<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.huaihua.mapper.BizEnteringHunanFilingMapper">

    <select id="queryEnteringHunanFilingInboxPage"
            resultType="com.ctsi.huaihua.entity.dto.BizQueryEnteringHunanFilingDTO">
        SELECT
        inbox.id,
        ehf.guest_company_id,
        ehf.guest_company_name,
        ehf.guest_user_name,
        ehf.guest_user_id,
        ehf.guest_post,
        ehf.in_hunan_start_date,
        ehf.in_hunan_end_date,
        ehf.In_hunan_days,
        ehf.create_by,
        ehf.create_name,
        ehf.contact_number,
        inbox.inbox_status
        FROM
        biz_entering_hunan_filing ehf
        RIGHT JOIN t_inbox inbox ON ehf.id = inbox.form_data_id
        WHERE
        inbox.deleted = 0
        AND ehf.deleted = 0
        AND inbox.source_business_name = 'enteringHunanFiling'
        <if test="null != enteringHunanCondition.receiveCompanyId">
            AND inbox.receive_company_id = #{enteringHunanCondition.receiveCompanyId}
        </if>
        <if test="null != enteringHunanCondition.createName">
            AND ehf.create_name LIKE CONCAT('%',#{enteringHunanCondition.createName},'%')
        </if>
        <if test="null != enteringHunanCondition.guestUserName">
            AND ehf.guest_user_name LIKE CONCAT('%',#{enteringHunanCondition.guestUserName},'%')
        </if>
    </select>
</mapper>
