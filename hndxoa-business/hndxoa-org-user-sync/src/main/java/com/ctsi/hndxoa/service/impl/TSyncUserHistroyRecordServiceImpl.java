package com.ctsi.hndxoa.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistoryRecordBackDTO;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.ITSyncUserHistroyRecordService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 同步机构历史记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Slf4j
@Service
@DS(DbConst.SHARDING)
public class TSyncUserHistroyRecordServiceImpl extends SysBaseServiceImpl<TSyncUserHistroyRecordMapper, TSyncUserHistroyRecord> implements ITSyncUserHistroyRecordService {

    @Autowired
    private TSyncUserHistroyRecordMapper tSyncUserHistroyRecordMapper;

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;

    @Autowired
    private CscpUserRepository cscpUserRepository;
    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSyncUserHistroyRecordDTO> queryListPage(TSyncUserHistroyRecordDTO entityDTO, BasePageForm basePageForm) {
        String loginUserName = SecurityUtils.getCurrentUserName();
        Integer recordCount;
        Integer startIndex = (basePageForm.getCurrentPage() - 1) * basePageForm.getPageSize();
        /*//设置条件
        if (SecurityUtils.isAdmin()) {
            recordCount = tSyncUserHistroyRecordMapper.selectRecordCount(entityDTO);
        } else {
            entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
            recordCount = tSyncUserHistroyRecordMapper.selectRecordCountNoAdd(entityDTO);
        }



        List<TSyncUserHistroyRecordDTO> recordDTOList = new ArrayList<>();
        if(recordCount > 0){
            if (SecurityUtils.isAdmin()) {
                recordDTOList =  tSyncUserHistroyRecordMapper.selectRecordList(entityDTO, startIndex, basePageForm.getPageSize());
            } else {
                entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
                recordDTOList = tSyncUserHistroyRecordMapper.selectRecordListNoAdd(entityDTO, startIndex, basePageForm.getPageSize());
            }
        }*/
        //改造第一步根据条件查询出APP数据
        if (!SecurityUtils.isAdmin()) {
            entityDTO.setCreateBy(SecurityUtils.getCurrentUserId());
        }
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapperApp = new LambdaQueryWrapper();
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppName()),TSyncAppSystemManage::getAppName,entityDTO.getAppName());
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppCode()),TSyncAppSystemManage::getAppCode,entityDTO.getAppCode());
        List<TSyncAppSystemManage> tSyncAppSystemManages = tSyncAppSystemManageService.selectListNoAdd(queryWrapperApp);
        Map<String, TSyncAppSystemManage> appMap = tSyncAppSystemManages.stream()
                .collect(Collectors.toMap(
                        app -> app.getId().toString(), // 提取 appId 作为 key
                        app -> app // value 就是对象本身
                ));
        //获取APPid集合
        List<String> appIdList = tSyncAppSystemManages.stream().map(i -> i.getId().toString()).collect(Collectors.toList());
        //组装数据
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapperRecord = new LambdaQueryWrapper();
        queryWrapperRecord.in(!CollectionUtils.isEmpty(appIdList),TSyncUserHistroyRecord::getAppId,appIdList)
                .eq(TSyncUserHistroyRecord::getDeleted,"0")
                .eq(StringUtils.isNotEmpty(entityDTO.getStrOperaType()),TSyncUserHistroyRecord::getStrOperaType,entityDTO.getStrOperaType())
                .eq(entityDTO.getUserId() != null,TSyncUserHistroyRecord::getUserId,entityDTO.getUserId())
                .eq(StringUtils.isNotEmpty(entityDTO.getSyncSuccess()),TSyncUserHistroyRecord::getSyncSuccess,entityDTO.getSyncSuccess())
                .like(StringUtils.isNotEmpty(entityDTO.getStrCname()),TSyncUserHistroyRecord::getStrCname,entityDTO.getStrCname())
                .like(StringUtils.isNotEmpty(entityDTO.getStrMobile()),TSyncUserHistroyRecord::getStrMobile,entityDTO.getStrMobile())
                .eq(!Objects.isNull(entityDTO.getCreateBy()),TSyncUserHistroyRecord::getCreateBy,entityDTO.getCreateBy())
                .ge(entityDTO.getCreateTimeStart()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeStart())
                .le(entityDTO.getCreateTimeEnd()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeEnd());
        if (StringUtils.isNotEmpty(entityDTO.getLoginName())) {
            queryWrapperRecord.and(qw ->
                    qw.like(StringUtils.isNotEmpty(entityDTO.getLoginName()), TSyncUserHistroyRecord::getLoginName,
                                    entityDTO.getLoginName())
                            .or()
                            .like(StringUtils.isNotEmpty(entityDTO.getLoginName()),
                                    TSyncUserHistroyRecord::getStrUserId, entityDTO.getLoginName()));
        }

        // 先执行COUNT查询（不包含ORDER BY）
        recordCount = Math.toIntExact(tSyncUserHistroyRecordMapper.selectCountWithAlias(queryWrapperRecord));

        List<TSyncUserHistroyRecord> recordList = new ArrayList<>();
        if (recordCount != null && recordCount > 0) {
            // 使用原生SQL方式绕过ShardingSphere分页问题
            TSyncUserHistroyRecordDTO tempDTO = new TSyncUserHistroyRecordDTO();
            tempDTO.setCreateTimeStart(entityDTO.getCreateTimeStart());
            tempDTO.setCreateTimeEnd(entityDTO.getCreateTimeEnd());
            recordList = queryWithNativePagination(queryWrapperRecord, basePageForm, tempDTO);
        }
        List<TSyncUserHistroyRecordDTO> recordDTOList = ListCopyUtil.copy(recordList, TSyncUserHistroyRecordDTO.class);
        // 姓名、手机号码脱敏处理
        recordDTOList.forEach(x -> {
            x.setAppName(appMap.get(x.getAppId().toString()).getAppName());
            x.setAppCode(appMap.get(x.getAppId().toString()).getAppCode());
            x.setLoginName(StringUtils.isNotEmpty(x.getLoginName()) ? x.getLoginName() : x.getStrUserId());
            String strMobile = x.getStrMobile();
            if (null != strMobile && !"".equals(strMobile)) {
                x.setStrMobile(DesensitizeUtil.desensitizedPhoneNumber(x.getStrMobile()));
            }
            String strCname = x.getStrCname();
            if (null != strCname && !"".equals(strCname)) {
                x.setStrCname(DesensitizeUtil.desensitizedName(x.getStrCname()));
            }
        });
        return new PageResult<TSyncUserHistroyRecordDTO>(recordDTOList,
                recordCount, basePageForm.getCurrentPage());
    }

    @Override
    @SuppressWarnings("all")
    public PageResult<TSyncUserHistroyRecordDTO> queryListPageByRole(TSyncUserHistroyRecordDTO entityDTO, BasePageForm basePageForm) {
        Integer recordCount;
        Integer startIndex = (basePageForm.getCurrentPage() - 1) * basePageForm.getPageSize();

        //改造第一步根据条件查询出APP数据
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapperApp = new LambdaQueryWrapper();
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppName()),TSyncAppSystemManage::getAppName,entityDTO.getAppName());
        queryWrapperApp.like(StringUtils.isNotEmpty(entityDTO.getAppCode()),TSyncAppSystemManage::getAppCode,entityDTO.getAppCode());
        List<TSyncAppSystemManage> tSyncAppSystemManages = tSyncAppSystemManageService.selectListNoAdd(queryWrapperApp);
        Map<String, TSyncAppSystemManage> appMap = tSyncAppSystemManages.stream()
                .collect(Collectors.toMap(
                        app -> app.getId().toString(), // 提取 appId 作为 key
                        app -> app // value 就是对象本身
                ));
        //获取APPid集合
        List<String> appIdList = tSyncAppSystemManages.stream().map(i -> i.getId().toString()).collect(Collectors.toList());
        //组装数据
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapperRecord = new LambdaQueryWrapper();
        queryWrapperRecord.in(!CollectionUtils.isEmpty(appIdList),TSyncUserHistroyRecord::getAppId,appIdList)
                .eq(StringUtils.isNotEmpty(entityDTO.getStrOperaType()),TSyncUserHistroyRecord::getStrOperaType,entityDTO.getStrOperaType())
                .eq(entityDTO.getUserId() != null,TSyncUserHistroyRecord::getUserId,entityDTO.getUserId())
                .eq(StringUtils.isNotEmpty(entityDTO.getSyncSuccess()),TSyncUserHistroyRecord::getSyncSuccess,entityDTO.getSyncSuccess())
                .like(StringUtils.isNotEmpty(entityDTO.getStrCname()),TSyncUserHistroyRecord::getStrCname,entityDTO.getStrCname())
                .like(StringUtils.isNotEmpty(entityDTO.getStrMobile()),TSyncUserHistroyRecord::getStrMobile,entityDTO.getStrMobile())
                .eq(!Objects.isNull(entityDTO.getCreateBy()),TSyncUserHistroyRecord::getCreateBy,entityDTO.getCreateBy())
                .ge(entityDTO.getCreateTimeStart()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeStart())
                .le(entityDTO.getCreateTimeEnd()!=null,TSyncUserHistroyRecord::getCreateTime,entityDTO.getCreateTimeEnd());
        if (StringUtils.isNotEmpty(entityDTO.getLoginName())) {
            queryWrapperRecord.and(qw ->
                    qw.like(StringUtils.isNotEmpty(entityDTO.getLoginName()), TSyncUserHistroyRecord::getLoginName,
                                    entityDTO.getLoginName())
                            .or()
                            .like(StringUtils.isNotEmpty(entityDTO.getLoginName()),
                                    TSyncUserHistroyRecord::getStrUserId, entityDTO.getLoginName()));
        }

        // 先执行COUNT查询（不包含ORDER BY）
        recordCount = Math.toIntExact(tSyncUserHistroyRecordMapper.selectCountWithAlias(queryWrapperRecord));

        List<TSyncUserHistroyRecord> recordList = new ArrayList<>();
        if (recordCount != null && recordCount > 0) {
            // 修复跨月分页问题：使用原生SQL方式绕过ShardingSphere分页问题
            recordList = queryWithNativePagination(queryWrapperRecord, basePageForm, entityDTO);
        }

        List<TSyncUserHistroyRecordDTO> recordDTOList = ListCopyUtil.copy(recordList, TSyncUserHistroyRecordDTO.class);
        // 姓名、手机号码脱敏处理
        recordDTOList.forEach(x -> {
            x.setAppName(appMap.get(x.getAppId().toString()).getAppName());
            x.setAppCode(appMap.get(x.getAppId().toString()).getAppCode());
            x.setLoginName(StringUtils.isNotEmpty(x.getLoginName()) ? x.getLoginName() : x.getStrUserId());
            String strMobile = x.getStrMobile();
            if (null != strMobile && !"".equals(strMobile)) {
                x.setStrMobile(DesensitizeUtil.desensitizedPhoneNumber(x.getStrMobile()));
            }
            String strCname = x.getStrCname();
            if (null != strCname && !"".equals(strCname)) {
                x.setStrCname(DesensitizeUtil.desensitizedName(x.getStrCname()));
            }
        });
        return new PageResult<TSyncUserHistroyRecordDTO>(recordDTOList,
                recordCount, basePageForm.getCurrentPage());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSyncUserHistroyRecordDTO> queryList(TSyncUserHistroyRecordDTO entityDTO) {
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = new LambdaQueryWrapper();
            List<TSyncUserHistroyRecord> listData = tSyncUserHistroyRecordMapper.selectList(queryWrapper);
            List<TSyncUserHistroyRecordDTO> TSyncUserHistroyRecordDTOList = ListCopyUtil.copy(listData, TSyncUserHistroyRecordDTO.class);
        return TSyncUserHistroyRecordDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSyncUserHistroyRecordDTO findOne(Long id) {
        TSyncUserHistroyRecord  tSyncUserHistroyRecord =  tSyncUserHistroyRecordMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tSyncUserHistroyRecord,TSyncUserHistroyRecordDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSyncUserHistroyRecordDTO create(TSyncUserHistroyRecordDTO entityDTO) {
       TSyncUserHistroyRecord tSyncUserHistroyRecord =  BeanConvertUtils.copyProperties(entityDTO,TSyncUserHistroyRecord.class);
       // 手动设置create_time，确保分表路由正确
       if (tSyncUserHistroyRecord.getCreateTime() == null) {
           tSyncUserHistroyRecord.setCreateTime(LocalDateTime.now());
       }
        save(tSyncUserHistroyRecord);
        return  BeanConvertUtils.copyProperties(tSyncUserHistroyRecord,TSyncUserHistroyRecordDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSyncUserHistroyRecordDTO entity) {
        TSyncUserHistroyRecord tSyncUserHistroyRecord = BeanConvertUtils.copyProperties(entity,TSyncUserHistroyRecord.class);
        return tSyncUserHistroyRecordMapper.updateById(tSyncUserHistroyRecord);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tSyncUserHistroyRecordMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSyncUserHistroyRecordId
     * @return
     */
    @Override
    public boolean existByTSyncUserHistroyRecordId(Long TSyncUserHistroyRecordId) {
        if (TSyncUserHistroyRecordId != null) {
            LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSyncUserHistroyRecord::getId, TSyncUserHistroyRecordId);
            List<TSyncUserHistroyRecord> result = tSyncUserHistroyRecordMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TSyncUserHistroyRecordDTO> dataList) {
        List<TSyncUserHistroyRecord> result = ListCopyUtil.copy(dataList, TSyncUserHistroyRecord.class);
        // 手动设置create_time，确保分表路由正确
        LocalDateTime now = LocalDateTime.now();
        for (TSyncUserHistroyRecord record : result) {
            if (record.getCreateTime() == null) {
                record.setCreateTime(now);
            }
        }
        return saveBatch(result);
    }

    @Override
    public void syncCallback(TSyncUserHistoryRecordBackDTO dto) {
        if (!"zwylz".equals(dto.getSourceKey())) {
            return;
        }
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSyncUserHistroyRecord::getEventId, dto.getEventId());
        List<TSyncUserHistroyRecord> result = tSyncUserHistroyRecordMapper.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        for (TSyncUserHistroyRecord record : result) {
            record.setSyncSuccess("false");
            record.setSyncMessage(dto.getSyncMessage());
            record.setSyncStatus(dto.getSyncStatus());
            if (StringUtils.isNotEmpty(dto.getPhone())) {
                List<String> imuUserids = Arrays.asList(dto.getPhone().split(","));
                if (imuUserids.contains(record.getStrMobile())) {
                    record.setSyncSuccess("true");
                    record.setSyncMessage("同步成功");
                    record.setSyncStatus("200");
                }
            }
        }
        this.updateBatchById(result);
    }

    /**
     * 处理跨月分表的分页查询
     * 解决ShardingSphere 4.1.1跨表分页时每个表都返回pageSize条记录的问题
     * 使用智能的应用层分页确保结果正确且性能可接受
     */
    private List<TSyncUserHistroyRecord> queryShardingDataWithPagination(
            LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper, BasePageForm basePageForm) {

        log.info("开始跨表分页查询，当前页：{}，页面大小：{}", basePageForm.getCurrentPage(), basePageForm.getPageSize());

        // 添加排序条件
        queryWrapper.orderByDesc(TSyncUserHistroyRecord::getCreateTime);

        // 计算分页参数
        int pageSize = basePageForm.getPageSize();
        int currentPage = basePageForm.getCurrentPage();
        int startIndex = (currentPage - 1) * pageSize;

        // 智能预估需要查询的数据量
        // 对于前几页，查询相对较少的数据；对于后面的页，查询更多数据
        int estimatedFetchSize;
        if (currentPage <= 5) {
            // 前5页，查询相对较少的数据
            estimatedFetchSize = Math.max(pageSize * 10, 500);
        } else {
            // 后面的页，需要查询更多数据
            estimatedFetchSize = pageSize * currentPage + pageSize * 5;
        }

        // 限制最大查询量，避免内存问题
        estimatedFetchSize = Math.min(estimatedFetchSize, 5000);

        log.info("预估查询数据量：{}", estimatedFetchSize);

        // 使用LIMIT限制查询数据量，但不用于精确分页
        queryWrapper.last("LIMIT " + estimatedFetchSize);
        List<TSyncUserHistroyRecord> fetchedRecords = tSyncUserHistroyRecordMapper.selectListNoAdd(queryWrapper);

        log.info("实际查询到记录数：{}", fetchedRecords.size());

        // 应用层分页处理
        if (fetchedRecords.isEmpty()) {
            return new ArrayList<>();
        }

        // 确保数据已按时间倒序排列（ShardingSphere可能没有正确排序跨表结果）
        fetchedRecords.sort((a, b) -> {
            if (a.getCreateTime() == null && b.getCreateTime() == null) return 0;
            if (a.getCreateTime() == null) return 1;
            if (b.getCreateTime() == null) return -1;
            return b.getCreateTime().compareTo(a.getCreateTime());
        });

        // 计算分页范围
        int endIndex = Math.min(startIndex + pageSize, fetchedRecords.size());

        if (startIndex >= fetchedRecords.size()) {
            log.warn("起始索引{}超出查询结果范围{}，可能需要增加查询数据量", startIndex, fetchedRecords.size());
            return new ArrayList<>();
        }

        List<TSyncUserHistroyRecord> pageResult = fetchedRecords.subList(startIndex, endIndex);
        log.info("分页结果：从索引{}到{}，返回{}条记录", startIndex, endIndex - 1, pageResult.size());

        return pageResult;
    }

    /**
     * 使用原生SQL方式进行分页查询，绕过ShardingSphere的分页问题
     * 采用限制查询数量的方式平衡性能和准确性
     */
    private List<TSyncUserHistroyRecord> queryWithNativePagination(
            LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper,
            BasePageForm basePageForm,
            TSyncUserHistroyRecordDTO entityDTO) {

        log.info("使用原生SQL分页查询，当前页：{}，页面大小：{}", basePageForm.getCurrentPage(), basePageForm.getPageSize());

        // 计算分页参数
        int pageSize = basePageForm.getPageSize();
        int currentPage = basePageForm.getCurrentPage();
        int startIndex = (currentPage - 1) * pageSize;

        // 智能计算需要查询的数据量
        // 为了确保分页准确，我们需要查询足够的数据，但也要控制性能
        int fetchLimit;
        if (currentPage <= 10) {
            // 前10页，查询相对较少的数据
            fetchLimit = Math.max(pageSize * 20, 1000);
        } else {
            // 后面的页，需要查询更多数据
            fetchLimit = pageSize * currentPage + pageSize * 10;
        }

        // 设置最大限制，避免查询过多数据
        fetchLimit = Math.min(fetchLimit, 10000);

        log.info("设置查询限制：{} 条记录", fetchLimit);

        // 添加排序和限制
        queryWrapper.orderByDesc(TSyncUserHistroyRecord::getCreateTime);
        queryWrapper.last("LIMIT " + fetchLimit);

        try {
            // 查询限定数量的记录
            List<TSyncUserHistroyRecord> fetchedRecords = tSyncUserHistroyRecordMapper.selectListNoAdd(queryWrapper);

            log.info("实际查询到记录数：{}", fetchedRecords.size());

            // 手动进行分页
            int endIndex = Math.min(startIndex + pageSize, fetchedRecords.size());

            if (startIndex >= fetchedRecords.size()) {
                log.warn("起始索引{}超出查询结果范围{}，可能需要增加查询限制", startIndex, fetchedRecords.size());
                return new ArrayList<>();
            }

            // 再次确保按时间倒序排列（防止ShardingSphere跨表排序问题）
            fetchedRecords.sort((a, b) -> {
                if (a.getCreateTime() == null && b.getCreateTime() == null) return 0;
                if (a.getCreateTime() == null) return 1;
                if (b.getCreateTime() == null) return -1;
                return b.getCreateTime().compareTo(a.getCreateTime());
            });

            List<TSyncUserHistroyRecord> pageResult = fetchedRecords.subList(startIndex, endIndex);
            log.info("分页结果：返回{}条记录（从第{}条到第{}条）", pageResult.size(), startIndex + 1, endIndex);

            return pageResult;

        } catch (Exception e) {
            log.error("原生SQL分页查询失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSyncUserHistroyRecordDTO generateUserHistoryRecord(Long userId, TSyncAppSystemManage appSystemManage, CscpUserDetail cscpUserDetail) {
        if (null == cscpUserDetail) {
            cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        }
        CscpUserDTO cscpUserDTO = cscpUserRepository.selectUserById(userId);
        if (null == cscpUserDTO) {
            throw new BusinessException("生成推送记录异常!");
        }
        TSyncUserHistroyRecord userRecord = new TSyncUserHistroyRecord();
        userRecord.setAppId(appSystemManage.getId());
        userRecord.setUserId(cscpUserDTO.getId());
        userRecord.setStrCname(cscpUserDTO.getRealName());
        userRecord.setStrUserId(cscpUserDTO.getLoginName());
        userRecord.setLoginName(cscpUserDTO.getLoginName());
        userRecord.setStrId(cscpUserDTO.getStrId());
        userRecord.setStrOperaType("add");
        userRecord.setRequestMode(appSystemManage.getRequestMode());
        userRecord.setCreateBy(cscpUserDetail.getId());
        userRecord.setCreateName(cscpUserDetail.getRealName());
        userRecord.setCreateTime(DateUtils.getLocalDateTime());
        userRecord.setDepartmentId(null != cscpUserDetail.getDepartmentId() ? cscpUserDetail.getDepartmentId() : null);
        userRecord.setCompanyId(null != cscpUserDetail.getCompanyId() ? cscpUserDetail.getCompanyId() : null);
        userRecord.setTenantId(null != cscpUserDetail.getTenantId() ? cscpUserDetail.getTenantId() : null);
        userRecord.setInSystemFlag(appSystemManage.getInSystemFlag());
        userRecord.setStrMobile(cscpUserDTO.getMobile());
        // 同步中
        userRecord.setSyncSuccess("ing");
        userRecord.setSyncMessage("推送中");
        userRecord.setSyncStatus("205");
        tSyncUserHistroyRecordMapper.insert(userRecord);
        return BeanConvertUtils.copyProperties(userRecord, TSyncUserHistroyRecordDTO.class);
    }
}
