package com.ctsi.hndxoa.pull.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.wps.WpsUtil;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.SyncModel;
import com.ctsi.hndxoa.entity.dto.SyncResponseModel;
import com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.hndxoa.mq.producer.PassiveSyncProducer;
import com.ctsi.hndxoa.pull.entity.dto.PassiveSyncMessage;
import com.ctsi.hndxoa.pull.service.ITSyncAppSystemManagePullService;
import com.ctsi.hndxoa.pull.utils.PassiveSyncCountTool;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.ITSyncOrgHistroyRecordService;
import com.ctsi.hndxoa.service.ITSyncUserHistroyRecordService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import com.ctsi.hndxoa.service.constant.SyncAppSystemEnum;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.security.CscpUserDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.StatusLine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 被动推送模式 - 实现
 */
@Slf4j
@Service
@DS(DbConst.SHARDING)
@SuppressWarnings("all")
public class TSyncAppSystemManagePullServiceImpl implements ITSyncAppSystemManagePullService {

    @Autowired
    private TSyncAppSystemManageMapper tSyncAppSystemManageMapper;

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;

    @Autowired
    private ITSyncOrgHistroyRecordService itSyncOrgHistroyRecordService;

    @Autowired
    private ITSyncUserHistroyRecordService itSyncUserHistroyRecordService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private PassiveSyncProducer passiveSyncProducer;

    @Autowired
    private PassiveSyncCountTool passiveSyncCountTool;


    @Override
    public void passiveSyncToApp(PassiveSyncMessage passiveSyncMessage) {
        List<PassiveSyncMessage> list = new ArrayList<>(1);
        list.add(passiveSyncMessage);
        pull(passiveSyncMessage.getAppSystemId(), list);
    }

    private void updateHistoryRecord(List<PassiveSyncMessage> passiveSyncMessages, String status, String message, String syncSuccess) {
        for (PassiveSyncMessage passiveSyncMessage : passiveSyncMessages) {
            if (passiveSyncMessage.getType() == OrgUserConstants.PushType.PUSH_UNIT) {
                TSyncOrgHistroyRecord record = new TSyncOrgHistroyRecord();
                record.setId(passiveSyncMessage.getSyncHistoryId());
                record.setSyncStatus(status);
                record.setSyncSuccess(syncSuccess);
                record.setSyncMessage(message);
                itSyncOrgHistroyRecordService.updateById(record);
            } else if (passiveSyncMessage.getType() == OrgUserConstants.PushType.PUSH_USER) {
                TSyncUserHistroyRecord record = new TSyncUserHistroyRecord();
                record.setId(passiveSyncMessage.getSyncHistoryId());
                record.setSyncStatus(status);
                record.setSyncSuccess(syncSuccess);
                record.setSyncMessage(message);
                itSyncUserHistroyRecordService.updateById(record);
            }
        }

    }

    private boolean httpSuccessResult(SyncResponseModel responseModel) {
        return responseModel != null && responseModel.getSuccess() != null && responseModel.getSuccess()
                && responseModel.getObj() != null && "200".equals(responseModel.getObj().getStatus());
    }

    private String getCodeType(Integer type) {
        if (type == null) {
            throw new BusinessException("passiveSyncMessage.type不能为空");
        } else if (type == OrgUserConstants.PushType.PUSH_UNIT) {
            return "PASSIVE_PUSH_UNIT";
        } else if (type == OrgUserConstants.PushType.PUSH_USER) {
            return "PASSIVE_PUSH_USER";
        } else {
            throw new BusinessException("passiveSyncMessage.type参数错误");
        }
    }

    private String getUrl(TSyncAppSystemManage appSystemManage) {
        if (OrgUserConstants.RequestMode.REQUEST_MODE_PULL_HTTP.equals(appSystemManage.getRequestMode())) {
            return appSystemManage.getPassiveUrl();
        } else if (OrgUserConstants.RequestMode.REQUEST_MODE_HTTP.equals(appSystemManage.getRequestMode())) {
            return SyncAppSystemEnum.WAI_BU.getCode() == appSystemManage.getInSystemFlag() ? appSystemManage.getSyncUrl() : appSystemManage.getInSystemUrl();
        }
        return null;
    }

    @Override
    public void batchPassiveSyncToApp(List<PassiveSyncMessage> passiveSyncMessages) {

        Map<Long, List<PassiveSyncMessage>> map = passiveSyncMessages.stream().collect(Collectors.groupingBy(PassiveSyncMessage::getAppSystemId));
        for (Map.Entry<Long, List<PassiveSyncMessage>> entry : map.entrySet()) {
            pull(entry.getKey(), entry.getValue());
        }
    }

    private void pull(Long key, List<PassiveSyncMessage> list) {
        try {
            // 1. SyncModel
            SyncModel syncModel = new SyncModel();
            syncModel.setStrCodeType(getCodeType(list.get(0).getType()));
            syncModel.setStrOperaType(OrgUserConstants.OperaType.ADD);

            // 2. TSyncAppSystemManage
            TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectById(key);
            String url = getUrl(appSystemManage);

            // 3. URL 校验
            if (!isValidHttpUrl(url)) {
                throw new BusinessException("无效URL:" + url);
            }
            // 4. 发起 HTTP 请求
            Map<String, Object> bodyParams = new HashMap<>();
            bodyParams.put("mode", syncModel);
            Map<String, Object> dataInfo = new HashMap<>();
            dataInfo.put("passiveInfo", list);
            bodyParams.put("dataInfo", dataInfo);
            Pair<StatusLine, String> resp = WpsUtil.sendPostRequestConfigTimeout(url, null, bodyParams, 10000, 10000);
            if (resp.getValue() != null) {
                JSONObject jsonObject = JSONObject.parseObject(resp.getValue());
                SyncResponseModel responseModel = JSONObject.toJavaObject(jsonObject, SyncResponseModel.class);
                if (httpSuccessResult(responseModel)) {
                    updateHistoryRecord(list, responseModel.getObj().getStatus(), "已通知同步", "true");
                } else {
                    updateHistoryRecord(list, responseModel.getObj().getStatus(), responseModel.getMessage(), "false");
                }
            }
        } catch (IOException e) {
            updateHistoryRecord(list, "500", StringUtils.substring(e.toString(), 0, 800), "false");
            log.error(e.toString());
        }
    }


    /**
     * 判断应用管理请求地址是为http
     * @param urlString
     * @return
     */
    public static boolean isValidHttpUrl(String urlString) {
        try {
            // 检查 URL 格式是否合法
            URL url = new URL(urlString);

            // 检查协议是否为 HTTP 或 HTTPS
            String protocol = url.getProtocol();
            if (!protocol.equals("http") && !protocol.equals("https")) {
                log.info("URL 协议必须是 HTTP 或 HTTPS");
                return false;
            }
            return true;
        } catch (MalformedURLException e) {
            log.error("URL 格式不合法: " + e.getMessage());
            return false;
        }
    }

    /**
     * 单个被动推送-共用
     *
     * @param appId
     * @param type
     * @param businessId
     * @param flag
     * @return
     */
    @Override
    public boolean sendPullManages(Long appId, Integer type, Long businessId, CscpUserDetail cscpUserDetail, String flag) {
        TSyncAppSystemManage tSyncAppSystemManage = tSyncAppSystemManageMapper.selectById(appId);
        if (tSyncAppSystemManage == null) {
            throw new BusinessException("获取应用失败!");
        }
        if (OrgUserConstants.RequestMode.REQUEST_MODE_PULL_HTTP.equals(tSyncAppSystemManage.getRequestMode())) {
            PassiveSyncMessage passiveSyncMessage = getPassiveSyncMessage(type, businessId, tSyncAppSystemManage, cscpUserDetail);
            passiveSyncProducer.sendOneMessage(JSON.toJSONString(passiveSyncMessage));
            // 单位管理员
            uintAdminPush(type, businessId, tSyncAppSystemManage, cscpUserDetail, flag);
            return true;
        }
        return false;
    }

    private void uintAdminPush(Integer type, Long businessId, TSyncAppSystemManage appSystemManage, CscpUserDetail cscpUserDetail, String flag) {
        if (OrgUserConstants.PushType.PUSH_UNIT == type) {
            CscpOrg cscpOrg = cscpOrgRepository.selectById(businessId);
            if (ObjectUtil.equals(cscpOrg.getType(), 2)) {
                // 根据单位id查询单位管理员
                LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper<>();
                userOrgQW.eq(CscpUserOrg::getOrgId, cscpOrg.getId());
                userOrgQW.apply("org_id = company_id");
                userOrgQW.last("limit 1");
                CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(userOrgQW);
                if ("add".equals(flag) || "add".equals(tSyncAppSystemManageService.setUserStrOperaType(appSystemManage, cscpUserOrg.getUserId()))) {
                    PassiveSyncMessage passiveSyncMessage = getPassiveSyncMessage(OrgUserConstants.PushType.PUSH_USER, cscpUserOrg.getUserId(), appSystemManage, cscpUserDetail);
                    passiveSyncProducer.sendOneMessage(JSON.toJSONString(passiveSyncMessage));
                }
            }
        }
    }


    /**
     * 批量被动推送-共用
     * @param tSyncAppSystemManage
     * @param type
     * @param ids
     * @return
     */
    @Override
    public boolean batchSendPullManages(TSyncAppSystemManage tSyncAppSystemManage, Long appId, Integer type, List<Long> ids) {
        if (null != appId) {
            tSyncAppSystemManage = tSyncAppSystemManageMapper.selectById(appId);
        }
        if (OrgUserConstants.RequestMode.REQUEST_MODE_PULL_HTTP.equals(tSyncAppSystemManage.getRequestMode())) {
            // 被动推送
            List<PassiveSyncMessage> passiveSyncMessageList = new ArrayList<>();
            if (OrgUserConstants.PushType.PUSH_UNIT == type) {
                for (Long orgId : ids) {
                    PassiveSyncMessage passiveSyncMessage = getPassiveSyncMessage(type, orgId, tSyncAppSystemManage, null);
                    passiveSyncMessageList.add(passiveSyncMessage);
                }
            }
            if (OrgUserConstants.PushType.PUSH_USER == type) {
                for (Long userId : ids) {
                    PassiveSyncMessage passiveSyncMessage = getPassiveSyncMessage(type, userId, tSyncAppSystemManage, null);
                    passiveSyncMessageList.add(passiveSyncMessage);
                }
            }
            passiveSyncProducer.sendManyMessage(JSON.toJSONString(passiveSyncMessageList));
            return true;
        }
        return false;
    }

    private PassiveSyncMessage getPassiveSyncMessage(Integer type, Long businessId, TSyncAppSystemManage tSyncAppSystemManage, CscpUserDetail cscpUserDetail) {
        PassiveSyncMessage passiveSyncMessage = new PassiveSyncMessage();
        passiveSyncMessage.setAppSystemId(tSyncAppSystemManage.getId());
        if (OrgUserConstants.PushType.PUSH_UNIT == type) {
            // 生成推送记录
            TSyncOrgHistroyRecordDTO tSyncOrgHistroyRecordDTO = itSyncOrgHistroyRecordService.generateOrgHistoryRecord(businessId, tSyncAppSystemManage, cscpUserDetail);
            // 生成消息类
            passiveSyncMessage.setOrgId(businessId);
            passiveSyncMessage.setType(OrgUserConstants.PushType.PUSH_UNIT);
            passiveSyncMessage.setSyncHistoryId(tSyncOrgHistroyRecordDTO.getId());
            passiveSyncCountTool.initOrgCount(passiveSyncMessage.getSyncHistoryId());
        }
        if (OrgUserConstants.PushType.PUSH_USER == type) {
            // 生成推送记录
            TSyncUserHistroyRecordDTO tSyncUserHistroyRecordDTO = itSyncUserHistroyRecordService.generateUserHistoryRecord(businessId, tSyncAppSystemManage, cscpUserDetail);
            passiveSyncMessage.setUserId(businessId);
            passiveSyncMessage.setType(OrgUserConstants.PushType.PUSH_USER);
            passiveSyncMessage.setSyncHistoryId(tSyncUserHistroyRecordDTO.getId());
            passiveSyncCountTool.initUserCount(passiveSyncMessage.getSyncHistoryId());
        }
        return passiveSyncMessage;
    }
}
