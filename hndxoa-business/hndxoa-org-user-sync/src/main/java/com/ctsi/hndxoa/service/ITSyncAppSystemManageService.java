package com.ctsi.hndxoa.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.SyncCommonHttpDTO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <p>
 * 同步应用系统管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface ITSyncAppSystemManageService extends SysBaseServiceI<TSyncAppSystemManage> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TSyncAppSystemManageDTO> queryListPage(TSyncAppSystemManageDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TSyncAppSystemManageDTO> queryList(TSyncAppSystemManageDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TSyncAppSystemManageDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TSyncAppSystemManageDTO create(TSyncAppSystemManageDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TSyncAppSystemManageDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTSyncAppSystemManageId
     * @param code
     * @return
     */
    boolean existByTSyncAppSystemManageId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TSyncAppSystemManageDTO> dataList);

    /**
     * 同步机构数据
     * @param syncOrgUserDTO
     * @return
     */
    boolean syncOrg(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 同步用户数据
     * @param syncOrgUserDTO
     * @return
     */
    boolean syncUser(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 内部系统同步机构数据
     * @param syncOrgUserDTO
     * @return Pair<Boolean, String> - 布尔值表示是否成功，字符串包含错误信息
     */
    Pair<Boolean, String> syncOrgInSystem(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 内部系统同步用户数据
     * @param syncOrgUserDTO
     * @return
     */
    Pair<Boolean, String>  syncUserInSystem(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 修改、禁用用户操作-回调推送业务系统
     * @param syncOrgUserDTO
     */
    void syncUserBusiness(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 修改、删除机构操作-回调推送业务系统
     * @param syncOrgUserDTO
     */
    void syncOrgBusiness(SyncOrgUserDTO syncOrgUserDTO);

    void mqSyncUser(SyncOrgUserDTO syncOrgUser);

    /**
     * 同步机构
     * @param syncOrgUser
     */
    Pair<String, String> mqSyncOrg(SyncOrgUserDTO syncOrgUser);

    /**
     * 同步用户所属机构
     * @param syncOrgUser
     */
    void mqSyncUserOfOrg(SyncOrgUserDTO syncOrgUser);

    List<TSyncAppSystemManage> getActiveProjectsUser();

    List<TSyncAppSystemManage> getActiveProjectsOrg();


    /**
     * 验证应用绑定的机构是否和推送机构/用户是否有关联
     * @param inOwnedOrgId    应用绑定的机构
     * @param syncOrgUserDTO  推送机构/用户
     */
    void ownedOrgValid(String inOwnedOrgId, SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 同步指定单位下的所有用户
     * @param syncOrgUserDTO 包含OrgId和appId
     * @return Pair<是否成功, 错误信息>
     */
    List<Long> getAllUserByCompanyId(SyncOrgUserDTO syncOrgUserDTO);

    Pair<Boolean, String> batchSyncOrgInSystem(List<SyncOrgUserDTO> syncOrgUserDTOList);

    Pair<Boolean, String> batchSyncUserInSystem(List<SyncOrgUserDTO> syncOrgUserDTOList);

    void commonHttpRequest(SyncCommonHttpDTO dto, boolean isMq);

    /**
     * 最新的同步用户方法
     * @param dto
     */
    void syncUserNew(SyncOrgUserDTO dto);

    /**
     * 最新的同步机构方法
     * @param dto
     */
    void syncOrgPush(SyncOrgUserDTO dto);

    void autoUserApp(CscpUserDTO cscpUserDTO, boolean syncUpdate);

    void autoOrgApp(CscpOrgDTO cscpOrgDTO, boolean syncUpdate);

    void autoUserAppDetail(CscpUserDTO cscpUserDTO, boolean syncUpdate);

    void autoOrgAppDetail(CscpOrgDTO cscpOrgDTO, boolean syncUpdate);

    void syncUserLz(SyncOrgUserDTO dto);

    PageResult<TSyncAppSystemManageDTO> unitAdminQueryAppPage(TSyncAppSystemManageDTO tSyncAppSystemManageDTO, BasePageForm basePageForm);

    void updateUserserPushAppCode(Long id, List<TSyncAppSystemManage> appSystemManages);

    String selectEnableAppCodeStr();

    List<TSyncAppSystemManage> getPassiveActiveApp();

    TSyncUserHistroyRecord insertUserSyncRecord(CscpUserDTO cscpUserDTO, TSyncAppSystemManage app, CscpUserDetail currentCscpUserDetail);

    boolean sendMqManages(Long appId, Integer type, Long businessId, CscpUserDetail cscpUserDetail);

    boolean batchSendMqManages(TSyncAppSystemManage tSyncAppSystemManage, Long appId, Integer type, List<Long> ids);

    List<Long> getOrgLevelList(List<Long> orgIds);

    String setOrgStrOperaType(TSyncAppSystemManage appSystemManage, Long orgId);

    String setUserStrOperaType(TSyncAppSystemManage appSystemManage, Long userId);
}
