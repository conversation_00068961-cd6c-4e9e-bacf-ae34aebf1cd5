<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>hndxoa-business</artifactId>
        <groupId>com.ctsi.hndxoa</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>hndxoa-huaihua-government-efficiency</artifactId>
    <description>怀化政务效能系统</description>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-base</artifactId>
        </dependency>

        <!--        引入系统数据字典-->
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-system</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-sms</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-userorg</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-file-operation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-import</artifactId>
        </dependency>


    </dependencies>


</project>
