package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizHomeTaskDTO;
import com.ctsi.huaihua.entity.dto.BizHomeTaskPackagingDTO;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.entity.dto.BizTaskFeedbackDTO;
import com.ctsi.huaihua.entity.BizTaskFeedback;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务成果反馈表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IBizTaskFeedbackService extends SysBaseServiceI<BizTaskFeedback> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskFeedbackDTO> queryListPage(BizTaskFeedbackDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskFeedbackDTO> queryList(BizTaskFeedbackDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskFeedbackDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskFeedbackDTO create(BizTaskFeedbackDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskFeedbackDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskFeedbackId
     * @param code
     * @return
     */
    boolean existByBizTaskFeedbackId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskFeedbackDTO> dataList);

    /**
     * 查询待审核记录
     * 查询已办结数据
     */
    PageResult<BizTaskDTO> queryBizTaskDecompose(BizTaskDTO bizTaskDTO, BasePageForm basePageForm);

    /**
     * 查询发布
     * 查询承办
     * @return
     */
    PageResult<BizHomeTaskPackagingDTO> queryBizHomeTaskDecompose(BizHomeTaskDTO bizHomeTaskDTO, BasePageForm basePageForm);


    /**
     * 根据分解任务ID查询最新反馈信息
     * @param decomposeIdList
     * @return
     */
    Map<Long, BizTaskFeedbackDTO> queryLastedBizTaskFeedback(List<Long> decomposeIdList);
}
