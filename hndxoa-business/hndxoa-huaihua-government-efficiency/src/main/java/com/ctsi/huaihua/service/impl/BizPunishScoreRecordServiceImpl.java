package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.huaihua.entity.BizUserPunishScoreRel;
import com.ctsi.huaihua.entity.dto.BizUserPunishScoreRelDTO;
import com.ctsi.huaihua.mapper.BizUserPunishScoreRelMapper;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizPunishScoreRecord;
import com.ctsi.huaihua.entity.dto.BizPunishScoreRecordDTO;
import com.ctsi.huaihua.mapper.BizPunishScoreRecordMapper;
import com.ctsi.huaihua.service.IBizPunishScoreRecordService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 奖分记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Slf4j
@Service
public class BizPunishScoreRecordServiceImpl extends SysBaseServiceImpl<BizPunishScoreRecordMapper, BizPunishScoreRecord> implements IBizPunishScoreRecordService {

    /**
     * 市政府办
     */
    private static final Integer BIZ_MUNICIPAL_UNIT = 1;

    /**
     * 督查室
     */
    private static final Integer BIZ_SUPERVISE_OFFICE = 2;

    /**
     * 市直单位
     */
    private static final Integer BIZ_STRAIGHT_UNIT = 3;

    @Autowired
    private BizPunishScoreRecordMapper bizPunishScoreRecordMapper;

    @Autowired
    private BizUserPunishScoreRelMapper bizUserPunishScoreRelMapper;

    @Autowired
    private BizUserPunishScoreRelServiceImpl bizUserPunishScoreRelService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPunishScoreRecordDTO> queryListPage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = new LambdaQueryWrapper();

        IPage<BizPunishScoreRecord> pageData = bizPunishScoreRecordMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizPunishScoreRecordDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizPunishScoreRecordDTO.class));

        return new PageResult<BizPunishScoreRecordDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizPunishScoreRecordDTO> queryList(BizPunishScoreRecordDTO entityDTO) {
        LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = new LambdaQueryWrapper();
            List<BizPunishScoreRecord> listData = bizPunishScoreRecordMapper.selectList(queryWrapper);
            List<BizPunishScoreRecordDTO> BizPunishScoreRecordDTOList = ListCopyUtil.copy(listData, BizPunishScoreRecordDTO.class);
        return BizPunishScoreRecordDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizPunishScoreRecordDTO findOne(Long id) {
        BizPunishScoreRecord  bizPunishScoreRecord =  bizPunishScoreRecordMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizPunishScoreRecord,BizPunishScoreRecordDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizPunishScoreRecordDTO create(BizPunishScoreRecordDTO entityDTO) {
        if (CollectionUtils.isEmpty(entityDTO.getBizUserPunishScoreRelDTOS())){
            throw new BusinessException("被处罚人不能为空");
        }
        if (Objects.isNull(entityDTO.getScoreYear())){
            entityDTO.setScoreYear(LocalDateTime.now().getYear());
        }
        //市政单位需要审核
        if (BIZ_MUNICIPAL_UNIT.equals(entityDTO.getPunishResource())) {
            entityDTO.setHasReviewer(0);
        }else if (BIZ_SUPERVISE_OFFICE.equals(entityDTO.getPunishResource())){
            //其他情况无需审核
            entityDTO.setHasReviewer(1);
            entityDTO.setStraightUnitHasRead(0);
            this.approvedBizPunishScoreRecord(entityDTO);
        }else if (BIZ_STRAIGHT_UNIT.equals(entityDTO.getPunishResource())){
            entityDTO.setHasReviewer(1);
            this.approvedBizPunishScoreRecord(entityDTO);
        }
        // 用户列表只能是同一单位
        Set<Long> companyIdSet = new HashSet<>(16);
        entityDTO.getBizUserPunishScoreRelDTOS().forEach(i -> {
            if (Objects.isNull(i.getPunishCompanyId())) {
                throw new BusinessException("被处罚的用户单位ID不允许为空");
            }
            companyIdSet.add(i.getPunishCompanyId());
        });
        if (companyIdSet.size() > 1) {
            throw new BusinessException("被处罚的用户必须为同一单位");
        }
        entityDTO.setCreateCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
        this.createBizUserPunishScoreRel(entityDTO.getBizUserPunishScoreRelDTOS(),entityDTO.getId());

        BizPunishScoreRecord bizPunishScoreRecord =  BeanConvertUtils.copyProperties(entityDTO,BizPunishScoreRecord.class);
        save(bizPunishScoreRecord);
        return  BeanConvertUtils.copyProperties(bizPunishScoreRecord,BizPunishScoreRecordDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizPunishScoreRecordDTO entity) {
        BizPunishScoreRecord bizPunishScoreRecord = BeanConvertUtils.copyProperties(entity,BizPunishScoreRecord.class);
        return bizPunishScoreRecordMapper.updateById(bizPunishScoreRecord);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizPunishScoreRecordMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizPunishScoreRecordId
     * @return
     */
    @Override
    public boolean existByBizPunishScoreRecordId(Long BizPunishScoreRecordId) {
        if (BizPunishScoreRecordId != null) {
            LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizPunishScoreRecord::getId, BizPunishScoreRecordId);
            List<BizPunishScoreRecord> result = bizPunishScoreRecordMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizPunishScoreRecordDTO> dataList) {
        List<BizPunishScoreRecord> result = ListCopyUtil.copy(dataList, BizPunishScoreRecord.class);
        return saveBatch(result);
    }

    /**
     * 市政府办分页展示
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPunishScoreRecordDTO> queryBizMunicipalUnitPage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQuery = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(entityDTO.getName())){
            lambdaQuery.like(BizUserPunishScoreRel::getUserName,entityDTO.getName());
        }
        if (StringUtils.isNotBlank(entityDTO.getCompanyName())){
            lambdaQuery.like(BizUserPunishScoreRel::getPunishCompanyName,entityDTO.getCompanyName());
        }
        List<BizUserPunishScoreRel> bizUserPunishScoreRelList = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQuery);
        List<Long> idList = bizUserPunishScoreRelList.stream().map(i -> i.getPunishScoreRecordId()).collect(Collectors.toList());

        LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = new LambdaQueryWrapper();

        if (idList.size() > 0){
            queryWrapper.in(BizPunishScoreRecord::getId,idList);
        }
        if (StringUtils.isNotBlank(entityDTO.getReductionReason())) {
            queryWrapper.eq(BizPunishScoreRecord::getReductionReason, entityDTO.getReductionReason());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getReductionReasonList())){
            queryWrapper.in(BizPunishScoreRecord::getReductionReason,entityDTO.getReductionReasonList());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getHasReviewerList())){
            queryWrapper.in(BizPunishScoreRecord::getHasReviewer,entityDTO.getHasReviewerList());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(BizPunishScoreRecord::getCreateTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(BizPunishScoreRecord::getCreateTime, entityDTO.getEndTime());
        }
        if (Objects.nonNull(entityDTO.getHasReviewer())){
            queryWrapper.eq(BizPunishScoreRecord::getHasReviewer, entityDTO.getHasReviewer());
        }
        //市政府办只能看到自己发出的减分通报
        queryWrapper.eq(BizPunishScoreRecord::getPunishResource,BIZ_MUNICIPAL_UNIT);
        queryWrapper.orderByDesc(BizPunishScoreRecord::getCreateTime);

        IPage<BizPunishScoreRecord> pageData = bizPunishScoreRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizPunishScoreRecordDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizPunishScoreRecordDTO.class));
        String sub = "-";
        data.getRecords().forEach(i ->{
            //设置分数
            i.setScoreStr(sub.concat(i.getScore().toString()));
            //设置姓名
            LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(BizUserPunishScoreRel::getPunishScoreRecordId,i.getId());

            List<BizUserPunishScoreRel> bizUserPunishScoreRels = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQueryWrapper);
            i.setBizUserPunishScoreRelDTOS(ListCopyUtil.copy(bizUserPunishScoreRels,BizUserPunishScoreRelDTO.class));
            String name = "";
            for (BizUserPunishScoreRel rel:bizUserPunishScoreRels){
                if (StringUtils.isNotBlank(rel.getUserName())){
                    name = name.concat(rel.getUserName()).concat(" (");
                }
                if (StringUtils.isNotBlank(rel.getPunishDepartmentName())){
                    name = name.concat(rel.getPunishDepartmentName());
                }
                if (StringUtils.isNotBlank(rel.getPost())){
                    name = name.concat("、");
                    name = name.concat(rel.getPost());
                }
                name = name.concat(");");
            }
            i.setName(name.substring(0,name.length()-1));
            i.setCompanyName(bizUserPunishScoreRels.get(0).getPunishCompanyName());
        });

        //改变阅读状态
//        List<BizPunishScoreRecordDTO> bizPunishScoreRecords = ListCopyUtil.copy(data.getRecords(),BizPunishScoreRecordDTO.class);
//        bizPunishScoreRecords.forEach(i ->{
//            i.setBizMunicipalUnitHasRead(1);
//            this.update(i);
//        });


        return new PageResult<BizPunishScoreRecordDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 督查室分页展示
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPunishScoreRecordDTO> queryBizSuperviseOfficePage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQuery = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(entityDTO.getName())){
            lambdaQuery.like(BizUserPunishScoreRel::getUserName,entityDTO.getName());
        }
        if (StringUtils.isNotBlank(entityDTO.getCompanyName())){
            lambdaQuery.like(BizUserPunishScoreRel::getPunishCompanyName,entityDTO.getCompanyName());
        }
        List<BizUserPunishScoreRel> bizUserPunishScoreRelList = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQuery);
        List<Long> idList = bizUserPunishScoreRelList.stream().map(i -> i.getPunishScoreRecordId()).collect(Collectors.toList());

        LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = new LambdaQueryWrapper();

        if (idList.size() > 0){
            queryWrapper.in(BizPunishScoreRecord::getId,idList);
        }
        if (StringUtils.isNotBlank(entityDTO.getReductionReason())) {
            queryWrapper.eq(BizPunishScoreRecord::getReductionReason, entityDTO.getReductionReason());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getReductionReasonList())){
            queryWrapper.in(BizPunishScoreRecord::getReductionReason,entityDTO.getReductionReasonList());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getHasReviewerList()) && entityDTO.getHasReviewerList().contains(0)){
            if (Objects.nonNull(entityDTO.getStartTime())) {
                queryWrapper.ge(BizPunishScoreRecord::getCreateTime, entityDTO.getStartTime());
            }
            if (Objects.nonNull(entityDTO.getEndTime())) {
                queryWrapper.le(BizPunishScoreRecord::getCreateTime, entityDTO.getEndTime());
            }
        }else {
            if (Objects.nonNull(entityDTO.getStartTime())) {
                queryWrapper.ge(BizPunishScoreRecord::getReviewerTime, entityDTO.getStartTime());
            }
            if (Objects.nonNull(entityDTO.getEndTime())) {
                queryWrapper.le(BizPunishScoreRecord::getReviewerTime, entityDTO.getEndTime());
            }
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getHasReviewerList())){
            queryWrapper.in(BizPunishScoreRecord::getHasReviewer,entityDTO.getHasReviewerList());
        }
        if (Objects.nonNull(entityDTO.getHasReviewer())){
            queryWrapper.eq(BizPunishScoreRecord::getHasReviewer, entityDTO.getHasReviewer());
        }
        if (Objects.nonNull(entityDTO.getPunishResource())){
            queryWrapper.eq(BizPunishScoreRecord::getPunishResource,entityDTO.getPunishResource());
        }
        queryWrapper.orderByDesc(BizPunishScoreRecord::getCreateTime);

        IPage<BizPunishScoreRecord> pageData = bizPunishScoreRecordMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizPunishScoreRecordDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizPunishScoreRecordDTO.class));
        String sub = "-";
        data.getRecords().forEach(i ->{
            //设置分数
            i.setScoreStr(sub.concat(i.getScore().toString()));
            //设置姓名
            LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(BizUserPunishScoreRel::getPunishScoreRecordId,i.getId());

            List<BizUserPunishScoreRel> bizUserPunishScoreRels = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQueryWrapper);
            i.setBizUserPunishScoreRelDTOS(ListCopyUtil.copy(bizUserPunishScoreRels,BizUserPunishScoreRelDTO.class));
            String name = "";
            for (BizUserPunishScoreRel rel:bizUserPunishScoreRels){
                if (StringUtils.isNotBlank(rel.getUserName())){
                    name = name.concat(rel.getUserName()).concat(" (");
                }
                if (StringUtils.isNotBlank(rel.getPunishDepartmentName())){
                    name = name.concat(rel.getPunishDepartmentName());
                }
                if (StringUtils.isNotBlank(rel.getPost())){
                    name = name.concat("、");
                    name = name.concat(rel.getPost());
                }
                name = name.concat(");");
            }
            i.setName(name.substring(0,name.length()-1));
            i.setCompanyName(bizUserPunishScoreRels.get(0).getPunishCompanyName());
        });

        return new PageResult<BizPunishScoreRecordDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 市直单位分页展示
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPunishScoreRecordDTO> queryBizStraightUnitPage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQuery = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(entityDTO.getName())){
            lambdaQuery.like(BizUserPunishScoreRel::getUserName,entityDTO.getName());
        }
        if (StringUtils.isNotBlank(entityDTO.getCompanyName())){
            lambdaQuery.like(BizUserPunishScoreRel::getPunishCompanyName,entityDTO.getCompanyName());
        }
        lambdaQuery.eq(BizUserPunishScoreRel::getPunishCompanyId,SecurityUtils.getCurrentCompanyId());
        List<BizUserPunishScoreRel> bizUserPunishScoreRelList = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQuery);
        List<Long> idList = bizUserPunishScoreRelList.stream().map(i -> i.getPunishScoreRecordId()).collect(Collectors.toList());

        LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = new LambdaQueryWrapper();

        if (idList.size() > 0){
            queryWrapper.in(BizPunishScoreRecord::getId,idList);
        }
        if (StringUtils.isNotBlank(entityDTO.getReductionReason())) {
            queryWrapper.eq(BizPunishScoreRecord::getReductionReason, entityDTO.getReductionReason());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getReductionReasonList())){
            queryWrapper.in(BizPunishScoreRecord::getReductionReason,entityDTO.getReductionReasonList());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(BizPunishScoreRecord::getReviewerTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(BizPunishScoreRecord::getReviewerTime, entityDTO.getEndTime());
        }
        queryWrapper.eq(BizPunishScoreRecord::getHasReviewer,1);
        queryWrapper.orderByDesc(BizPunishScoreRecord::getCreateTime);

        IPage<BizPunishScoreRecord> pageData = bizPunishScoreRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizPunishScoreRecordDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizPunishScoreRecordDTO.class));
        String sub = "-";
        data.getRecords().forEach(i ->{
            //设置分数
            i.setScoreStr(sub.concat(i.getScore().toString()));
            //设置姓名
            LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(BizUserPunishScoreRel::getPunishScoreRecordId,i.getId());

            List<BizUserPunishScoreRel> bizUserPunishScoreRels = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQueryWrapper);
            i.setBizUserPunishScoreRelDTOS(ListCopyUtil.copy(bizUserPunishScoreRels,BizUserPunishScoreRelDTO.class));
            String name = "";
            for (BizUserPunishScoreRel rel:bizUserPunishScoreRels){
                if (StringUtils.isNotBlank(rel.getUserName())){
                    name = name.concat(rel.getUserName()).concat(" (");
                }
                if (StringUtils.isNotBlank(rel.getPunishDepartmentName())){
                    name = name.concat(rel.getPunishDepartmentName());
                }
                if (StringUtils.isNotBlank(rel.getPost())){
                    name = name.concat("、");
                    name = name.concat(rel.getPost());
                }
                name = name.concat(");");
            }
            i.setName(name.substring(0,name.length()-1));
            i.setCompanyName(bizUserPunishScoreRels.get(0).getPunishCompanyName());
        });

        //改变阅读状态
//        List<BizPunishScoreRecordDTO> bizPunishScoreRecords = ListCopyUtil.copy(data.getRecords(),BizPunishScoreRecordDTO.class);
//        bizPunishScoreRecords.forEach(i ->{
//            i.setStraightUnitHasRead(1);
//            this.update(i);
//        });

        return new PageResult<BizPunishScoreRecordDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 个人减分分页展示
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPunishScoreRecordDTO> queryPunishScoreRecordPage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (Objects.isNull(currentUserId)) {
            throw new BusinessException("未找到当前用户ID");
        }
        LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizUserPunishScoreRel::getUserId,currentUserId);
        lambdaQueryWrapper.eq(BizUserPunishScoreRel::getPunishCompanyId,SecurityUtils.getCurrentCompanyId());
        List<BizUserPunishScoreRel> bizUserPunishScoreRels = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(bizUserPunishScoreRels)) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }
        List<Long> idList = bizUserPunishScoreRels.stream().map(i ->i.getPunishScoreRecordId()).collect(Collectors.toList());

        //设置条件
        LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = new LambdaQueryWrapper();

        if (StringUtils.isNotBlank(entityDTO.getReductionReason())) {
            queryWrapper.eq(BizPunishScoreRecord::getReductionReason, entityDTO.getReductionReason());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getReductionReasonList())){
            queryWrapper.in(BizPunishScoreRecord::getReductionReason,entityDTO.getReductionReasonList());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(BizPunishScoreRecord::getReviewerTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(BizPunishScoreRecord::getReviewerTime, entityDTO.getEndTime());
        }
        queryWrapper.eq(BizPunishScoreRecord::getHasReviewer,1);
        queryWrapper.in(BizPunishScoreRecord::getId,idList);
        queryWrapper.orderByDesc(BizPunishScoreRecord::getCreateTime);

        IPage<BizPunishScoreRecord> pageData = bizPunishScoreRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizPunishScoreRecordDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizPunishScoreRecordDTO.class));
        String sub = "-";

        data.getRecords().forEach(i ->{
            //设置分数
            i.setScoreStr(sub.concat(i.getScore().toString()));

            String name = "";
            for (BizUserPunishScoreRel rel:bizUserPunishScoreRels){
                if (i.getId().equals(rel.getPunishScoreRecordId())) {
                    //设置人员
                    List<BizUserPunishScoreRelDTO> relDTOS = new ArrayList<>();
                    relDTOS.add(BeanConvertUtils.copyProperties(rel,BizUserPunishScoreRelDTO.class));
                    i.setBizUserPunishScoreRelDTOS(relDTOS);
                    //设置姓名
                    if (StringUtils.isNotBlank(rel.getUserName())){
                        name = name.concat(rel.getUserName()).concat(" (");
                    }
                    if (StringUtils.isNotBlank(rel.getPunishDepartmentName())){
                        name = name.concat(rel.getPunishDepartmentName());
                    }
                    if (StringUtils.isNotBlank(rel.getPost())){
                        name = name.concat("、");
                        name = name.concat(rel.getPost());
                    }
                    name = name.concat(");");
                    //设置阅读状态
                    i.setHasRead(rel.getHasRead());
                    //改变阅读状态
//                    BizUserPunishScoreRelDTO bizUserPunishScoreRelDTO = BeanConvertUtils.copyProperties(rel, BizUserPunishScoreRelDTO.class);
//                    bizUserPunishScoreRelDTO.setHasRead(1);
//                    bizUserPunishScoreRelService.update(bizUserPunishScoreRelDTO);
                }
            }
            i.setName(name.substring(0,name.length()-1));
            i.setCompanyName(bizUserPunishScoreRels.get(0).getPunishCompanyName());
        });

        return new PageResult<BizPunishScoreRecordDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 批量保存被处罚人员
     * @param bizUserPunishScoreRelDTOS    用户集合
     */
        public void createBizUserPunishScoreRel(List<BizUserPunishScoreRelDTO> bizUserPunishScoreRelDTOS,Long id){
        bizUserPunishScoreRelDTOS.forEach(i ->{
            i.setHasRead(0);
            i.setPunishScoreRecordId(id);
            bizUserPunishScoreRelService.create(i);
        });
    }

    /**
     * 查询未审核惩分申请数量
     * @return
     */
    @Override
    public Integer queryPunishScoreRecordNeedReviewAmount(){
        LambdaQueryWrapper<BizPunishScoreRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizPunishScoreRecord::getHasReviewer,0);
        Integer count = bizPunishScoreRecordMapper.selectCountOnlyAddTenantId(queryWrapper);
        return count;
    }

    /**
     * 审核通过与驳回
     * @param bizPunishScoreRecord
     * @return
     */
    @Override
    public Boolean approvedBizPunishScoreRecord(BizPunishScoreRecordDTO bizPunishScoreRecord){
        //补充审核人信息
        bizPunishScoreRecord.setReviewerId(SecurityUtils.getCurrentUserId());
        bizPunishScoreRecord.setReviewerName(SecurityUtils.getCurrentRealName());
        bizPunishScoreRecord.setReviewerDepartmentId(SecurityUtils.getCurrentCscpUserDetail().getDepartmentId());
        bizPunishScoreRecord.setReviewerCompanyId(SecurityUtils.getCurrentCompanyId());
        bizPunishScoreRecord.setReviewerTime(java.time.LocalDateTime.now());
        bizPunishScoreRecord.setBizMunicipalUnitHasRead(0);
        //设置阅读状态
        bizPunishScoreRecord.setStraightUnitHasRead(0);
        //设置个人阅读状态
        LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizUserPunishScoreRel::getPunishScoreRecordId,bizPunishScoreRecord.getId());
        List<BizUserPunishScoreRel> bizUserPunishScoreRels = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQueryWrapper);
        bizUserPunishScoreRels.forEach(i ->{
            i.setHasRead(0);
            bizUserPunishScoreRelService.update(BeanConvertUtils.copyProperties(i,BizUserPunishScoreRelDTO.class));
        });

        this.update(bizPunishScoreRecord);
        return true;

    }

    /**
     * 查看减分详情
     * @param id
     * @param type 0:非个人查看详情  1:个人查看详情
     * @return
     */
    @Override
    public BizPunishScoreRecordDTO getPunishScoreRecordDetail(Long id,Integer type){
        BizPunishScoreRecord bizPunishScoreRecord = bizPunishScoreRecordMapper.selectById(id);
        BizPunishScoreRecordDTO bizPunishScoreRecordDTO = BeanConvertUtils.copyProperties(bizPunishScoreRecord,BizPunishScoreRecordDTO.class);
        //设置姓名
        LambdaQueryWrapper<BizUserPunishScoreRel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizUserPunishScoreRel::getPunishScoreRecordId,id);
        if (type == 1){
            lambdaQueryWrapper.eq(BizUserPunishScoreRel::getUserId,SecurityUtils.getCurrentUserId());
        }
        List<BizUserPunishScoreRel> bizUserPunishScoreRels = bizUserPunishScoreRelMapper.selectListNoAdd(lambdaQueryWrapper);
        String name = "";
        for (BizUserPunishScoreRel rel:bizUserPunishScoreRels){
            name = name.concat(rel.getUserName()).concat("--(").concat(rel.getPunishDepartmentName()).concat("、").concat(rel.getPost()).concat(");");
        }
        bizPunishScoreRecordDTO.setName(name);
        return bizPunishScoreRecordDTO;
    }

    @Override
    public Boolean updateRed(Long id,Integer type){
        BizPunishScoreRecord bizPunishScoreRecord = new BizPunishScoreRecord();
        if (type == 0){
            bizPunishScoreRecord = bizPunishScoreRecordMapper.selectById(id);
            bizPunishScoreRecord.setBizMunicipalUnitHasRead(1);
        }else if (type == 1){
            bizPunishScoreRecord = bizPunishScoreRecordMapper.selectById(id);
            bizPunishScoreRecord.setStraightUnitHasRead(1);
        }
        bizPunishScoreRecordMapper.updateById(bizPunishScoreRecord);
        if (type == 2){
            BizUserPunishScoreRel bizUserPunishScoreRel = bizUserPunishScoreRelMapper.selectById(id);
            bizUserPunishScoreRel.setHasRead(1);
            bizUserPunishScoreRelMapper.updateById(bizUserPunishScoreRel);
        }
        return true;
    }
}
