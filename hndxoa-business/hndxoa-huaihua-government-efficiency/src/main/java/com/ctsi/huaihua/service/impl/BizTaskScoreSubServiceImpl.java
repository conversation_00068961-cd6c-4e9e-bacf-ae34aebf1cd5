package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.BizTaskScoreSub;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.entity.dto.BizTaskScoreSubDTO;
import com.ctsi.huaihua.mapper.BizTaskDecomposeMapper;
import com.ctsi.huaihua.mapper.BizTaskScoreSubMapper;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.huaihua.service.IBizTaskScoreSubService;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务减分表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Slf4j
@Service
public class BizTaskScoreSubServiceImpl extends SysBaseServiceImpl<BizTaskScoreSubMapper, BizTaskScoreSub> implements IBizTaskScoreSubService {

    @Autowired
    private BizTaskScoreSubMapper bizTaskScoreSubMapper;

    @Autowired
    private BizTaskDecomposeMapper bizTaskDecomposeMapper;

    @Autowired
    private IBizTaskDecomposeService bizTaskDecomposeService;

    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileMapper;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;

    /**
     * 查询当前数据所有的减分列表
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageResult<BizTaskScoreSubDTO> queryListPage(BizTaskScoreSubDTO entityDTO, BasePageForm basePageForm) {
        if (Objects.isNull(entityDTO.getBizTaskDecompose()) && Objects.isNull(entityDTO.getPreview())) {
            throw new BusinessException("缺少参数!");
        }

        //设置条件
        LambdaQueryWrapper<BizTaskScoreSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(BizTaskScoreSub::getBizTaskDecompose, entityDTO.getBizTaskDecompose());
        queryWrapper.orderByAsc(BizTaskScoreSub::getCreateTime);

        IPage<BizTaskScoreSub> pageData = bizTaskScoreSubMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskScoreSubDTO> data = pageData.convert(entity -> {
            BizTaskScoreSubDTO bizTaskScoreSubDTO = BeanConvertUtils.copyProperties(entity, BizTaskScoreSubDTO.class);
            bizTaskScoreSubDTO.setBizTaskDecomposeId(entity.getBizTaskDecompose());
            return bizTaskScoreSubDTO;
        });

        //如果是接收方调用该接口，并且把未阅状态发送过来，则进行修改分解任务的预览状态
        if (TaskStatus.NOT_PREVIEW.equals(entityDTO.getPreview())) {
            BizTaskDecompose bizTaskDecompose = new BizTaskDecompose();
            bizTaskDecompose.setPreview(TaskStatus.PREVIEW);
            bizTaskDecomposeMapper.updateTenantId(bizTaskDecompose,
                    new LambdaQueryWrapper<BizTaskDecompose>().in(BizTaskDecompose::getId, entityDTO.getBizTaskDecompose()));
        }

        return new PageResult(data.getRecords(), data.getTotal(), data.getCurrent());
    }


    /**
     * 减分通报
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BizTaskScoreSubDTO> points(BizTaskScoreSubDTO entityDTO) {
        //被撤回无法进行减分
        List<BizTaskDecompose> bizTaskDecomposeList = bizTaskDecomposeMapper.selectListNoAdd(
                new LambdaQueryWrapper<BizTaskDecompose>().select(BizTaskDecompose::getHasSign).in(BizTaskDecompose::getId, entityDTO.getBizTaskDecompose()));
        for (BizTaskDecompose bizTaskDecompose : bizTaskDecomposeList) {
            if (!Objects.isNull(bizTaskDecompose) && bizTaskDecompose.getHasSign().equals(TaskStatus.TASK_HAS_SIGN_WITHDRAW)) {
                throw new BusinessException("存在分解任务已撤回,无法进行减分!");
            }
        }

        List<BizTaskScoreSub> bizTaskScoreSubList = new ArrayList<>();
        for (int i = 0; i < entityDTO.getBizTaskDecompose().size(); i++) {
            BizTaskScoreSub bizTaskScoreSub = BeanConvertUtils.copyProperties(entityDTO, BizTaskScoreSub.class);
            bizTaskScoreSub.setBizTaskDecompose(entityDTO.getBizTaskDecompose().get(i));
            bizTaskScoreSubList.add(bizTaskScoreSub);
        }
        saveBatch(bizTaskScoreSubList);
        //拷贝附件
        List<CscpEnclosureFile> cscpEnclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(
                new LambdaQueryWrapper<CscpEnclosureFile>().eq(CscpEnclosureFile::getFormDataId, entityDTO.getEnclosureFileId())
        );
        List<CscpEnclosureFile> enclosureFiles = new LinkedList<>();
        for (int i = 0; i < bizTaskScoreSubList.size(); i++) {
            BizTaskScoreSub bizTaskScoreSub = bizTaskScoreSubList.get(i);
            cscpEnclosureFiles.forEach(y -> {
                CscpEnclosureFile cscpEnclosureFile = BeanConvertUtils.copyProperties(y, CscpEnclosureFile.class);
                cscpEnclosureFile.setId(null);
                cscpEnclosureFile.setFormDataId(bizTaskScoreSub.getId());
                enclosureFiles.add(cscpEnclosureFile);
            });
        }
        cscpEnclosureFileMapper.delete(new LambdaQueryWrapper<CscpEnclosureFile>().eq(CscpEnclosureFile::getFormDataId, entityDTO.getEnclosureFileId()));

        enclosureFiles.forEach(i -> {
            cscpEnclosureFileMapper.insert(i);
        });

        //修改分解任务的预览状态
        BizTaskDecompose upBizTaskDecompose = new BizTaskDecompose();
        upBizTaskDecompose.setPreview(TaskStatus.NOT_PREVIEW);
        bizTaskDecomposeMapper.updateTenantId(upBizTaskDecompose,
                new LambdaQueryWrapper<BizTaskDecompose>().in(BizTaskDecompose::getId, entityDTO.getBizTaskDecompose()));

        return ListCopyUtil.copy(bizTaskScoreSubList, BizTaskScoreSubDTO.class);
    }

    /**
     * 查询角标
     *
     * @return
     */
    @Override
    public Integer getAngleMark() {
        BizTaskDTO bizTaskDTO = new BizTaskDTO();
        bizTaskDTO.setDutyPeopleId(SecurityUtils.getCurrentUserId());
        bizTaskDTO.setSortField("receive_time");
        bizTaskDTO.setSortType("desc");
        BasePageForm basePageForm = new BasePageForm(Integer.MAX_VALUE, 1);
        PageResult<BizTaskDTO> bizTaskDTOPageResult = bizTaskDecomposeService.queryNotificationTask(bizTaskDTO, basePageForm);

        List<BizTaskDTO> collect = bizTaskDTOPageResult.getData().stream().filter(i -> {
            return !Objects.isNull(i.getPreview()) && TaskStatus.NOT_PREVIEW.equals(i.getPreview());
        }).collect(Collectors.toList());

        return collect.size();
    }


    /**
     * 根据分解表主键ID查询任务减分
     *
     * @param decomposeId
     * @return
     */
    @Override
    public List<BizTaskScoreSubDTO> queryScoreSubByDecomposeId(Long decomposeId) {
        if (Objects.isNull(decomposeId)) {
            throw new BusinessException("分解表主键ID不允许为空");
        }
        LambdaQueryWrapper<BizTaskScoreSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskScoreSub::getBizTaskDecompose, decomposeId);
        List<BizTaskScoreSub> taskScoreSubList = bizTaskScoreSubMapper.selectListNoAdd(queryWrapper);
        List<BizTaskScoreSubDTO> bizTaskScoreSubDTOList = ListCopyUtil.copy(taskScoreSubList, BizTaskScoreSubDTO.class);
        return bizTaskScoreSubDTOList;
    }
}
