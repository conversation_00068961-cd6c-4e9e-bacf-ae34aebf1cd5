package com.ctsi.huaihua.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.tree.impl.BaseTreeCurdServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.SysTenantUtils;
import com.ctsi.huaihua.entity.BizTaskCoefficient;
import com.ctsi.huaihua.mapper.BizTaskCoefficientMapper;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizTaskLevel;
import com.ctsi.huaihua.entity.dto.BizTaskLevelDTO;
import com.ctsi.huaihua.mapper.BizTaskLevelMapper;
import com.ctsi.huaihua.service.IBizTaskLevelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Slf4j
@Service
public class BizTaskLevelServiceImpl extends BaseTreeCurdServiceImpl<BizTaskLevelDTO, BizTaskLevel, BizTaskLevelMapper> implements IBizTaskLevelService {

    @Autowired
    private BizTaskLevelMapper bizTaskLevelMapper;

    @Autowired
    private IBizTaskLevelService bizTaskLevelService;

    @Autowired
    private BizTaskCoefficientMapper bizTaskCoefficientMapper;
    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskLevelDTO> queryListPage(BizTaskLevelDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskLevel> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskLevel> pageData = bizTaskLevelMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskLevelDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskLevelDTO.class));

        return new PageResult<BizTaskLevelDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskLevelDTO> queryList(BizTaskLevelDTO entityDTO) {
        LambdaQueryWrapper<BizTaskLevel> queryWrapper = new LambdaQueryWrapper();
            List<BizTaskLevel> listData = bizTaskLevelMapper.selectList(queryWrapper);
            List<BizTaskLevelDTO> BizTaskLevelDTOList = ListCopyUtil.copy(listData, BizTaskLevelDTO.class);
        return BizTaskLevelDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskLevelDTO findOne(Long id) {
        BizTaskLevel bizTaskLevel =  bizTaskLevelMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizTaskLevel, BizTaskLevelDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskLevelDTO create(BizTaskLevelDTO entityDTO) {
        this.updateOrderBy(entityDTO);
        //同级别下不可出现相同的级别名称
        Long bizTaskLevelTenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        LambdaQueryWrapper<BizTaskLevel> bizTaskLevelLambdaQueryWrapper = Wrappers.lambdaQuery();
        bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getParentId,entityDTO.getParentId());
        bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getTypeName,entityDTO.getTypeName());
        bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getTenantId,bizTaskLevelTenantId);
        int count = bizTaskLevelMapper.selectCount(bizTaskLevelLambdaQueryWrapper);
        if (count > 0){
            throw new BusinessException("该级别已存在");
        }

        BizTaskLevel bizTaskLevel =  BeanConvertUtils.copyProperties(entityDTO, BizTaskLevel.class);
        save(bizTaskLevel);
        return  BeanConvertUtils.copyProperties(bizTaskLevel, BizTaskLevelDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskLevelDTO entity) {
        this.updateOrderBy(entity);
        //同级别下不可出现相同的级别名称
        LambdaQueryWrapper<BizTaskLevel> bizTaskLevelLambdaQueryWrapper = Wrappers.lambdaQuery();
        bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getParentId,entity.getParentId());
        bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getTypeName,entity.getTypeName());
        int count = bizTaskLevelMapper.selectCount(bizTaskLevelLambdaQueryWrapper);
        if (count > 0){
            throw new BusinessException("该级别已存在");
        }

        BizTaskLevel bizTaskLevel = BeanConvertUtils.copyProperties(entity,BizTaskLevel.class);
        return bizTaskLevelMapper.updateById(bizTaskLevel);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        log.debug("Request to delete BizTaskLevel : {}", id);
//        BizTaskLevel bizTaskLevel = bizTaskLevelMapper.selectById(id);
//        if (!bizTaskLevel.getCreateBy().equals(SecurityUtils.getCurrentUserId())){
//            throw new BusinessException("该用户无权删除该配置");
//        }
        //删除子节点数据
        LambdaQueryWrapper<BizTaskLevel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizTaskLevel::getParentId,id);
        List<BizTaskLevel> bizTaskLevelList = bizTaskLevelMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(bizTaskLevelList)){
            for (BizTaskLevel bizTaskLevels : bizTaskLevelList){
                delete(bizTaskLevels.getId());
            }
        }
        //删除任务难度系数表对应数据
        LambdaQueryWrapper<BizTaskCoefficient> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizTaskCoefficient::getCoefficientId,id);
        bizTaskCoefficientMapper.delete(queryWrapper);
        bizTaskLevelMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskLevelId
     * @return
     */
    @Override
    public boolean existByBizTaskLevelId(Long BizTaskLevelId) {
        if (BizTaskLevelId != null) {
            LambdaQueryWrapper<BizTaskLevel> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskLevel::getId, BizTaskLevelId);
            List<BizTaskLevel> result = bizTaskLevelMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskLevelDTO> dataList) {
        List<BizTaskLevel> result = ListCopyUtil.copy(dataList, BizTaskLevel.class);
        return saveBatch(result);
    }


    @Override
    public List<BizTaskLevelDTO> getDataDtOFromDomin(List<BizTaskLevel> list) {
        return ListCopyUtil.copy(list,BizTaskLevelDTO.class);
    }

    @Override
    public BizTaskLevelDTO copyDto(BizTaskLevel bizTaskLevel, BizTaskLevelDTO bizTaskLevelDTO) {
        bizTaskLevelDTO = new BizTaskLevelDTO();
        BeanUtils.copyProperties(bizTaskLevel, bizTaskLevelDTO);
        return bizTaskLevelDTO;
    }

    @Override
    public Boolean updateOrderBy(BizTaskLevelDTO bizTaskLevelDTO){
        LambdaQueryWrapper<BizTaskLevel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizTaskLevel::getOrderBy,bizTaskLevelDTO.getOrderBy());
        lambdaQueryWrapper.eq(BizTaskLevel::getParentId,bizTaskLevelDTO.getParentId());
        int count = bizTaskLevelMapper.selectCount(lambdaQueryWrapper);
        if (count > 0){
            bizTaskLevelMapper.updataSort(
                    SortEnum.builder()
                            .sort(bizTaskLevelDTO.getOrderBy())
                            .parentId(bizTaskLevelDTO.getParentId())
                            .id(bizTaskLevelDTO.getId())
                            .tableName("biz_task_level")
                            .sortName("order_by")
                            .additionOrsubtraction("+")
                            .build());
        }
        return true;
    }


    @Override
    public List<BizTaskLevelDTO> selectChildren(Long parentId) {
        Assert.notNull(parentId, "parentId is null");

        try {
            QueryWrapper<BizTaskLevel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", parentId);
            queryWrapper.orderByAsc("order_by");
            CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();

            Long taskCompanyId = this.longThreadLocal.get();
            if (taskCompanyId != null && !taskCompanyId.equals(cscpUserDetail.getCompanyId())){
                queryWrapper.eq("company_id", taskCompanyId);
                List<BizTaskLevel> list = baseMapper.selectListNoAdd(queryWrapper);
                return getDataDtOFromDomin(list);
            }else {
                List<BizTaskLevel> list = new ArrayList<>();
                list = baseMapper.selectList(queryWrapper);

                if ( list.size() <= 0 ){
                    // 找租户
                    queryWrapper.eq("tenant_id" , cscpUserDetail.getTenantId())
                            .isNull("company_id");
                    list = bizTaskLevelMapper.selectListNoAdd(queryWrapper);
                    if (list.size() <= 0) {
                        // 找顶级租户
                        Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(cscpUserDetail.getTenantId());
                        QueryWrapper<BizTaskLevel> queryWrapper2 = new QueryWrapper<>();
                        queryWrapper2.eq("parent_id", parentId);
                        queryWrapper2.orderByAsc("order_by");
                        queryWrapper2.eq("tenant_id" , topFloorTenamtId)
                                .isNull("company_id");
                        list = bizTaskLevelMapper.selectListNoAdd(queryWrapper2);
                    }
                }
                return getDataDtOFromDomin(list);
            }


        } catch (Exception e) {
            log.error("selectChildren occurs error, caused by: ", e);
            throw new RuntimeException("selectChildren occurs error", e);
        }
    }

    /**
     * 查询所有数据后组装树
     * @param tenantId
     * @return
     */
    @Override
    public List<Tree<String>> queryLevelByTenantId(Long tenantId){
        // 先查询本单位
        // 如果是单位登录查询单位的
        List<BizTaskLevel> bizTaskLevelList = new ArrayList<>();
        if(SecurityUtils.isGeneralName()) {
            LambdaQueryWrapper<BizTaskLevel> bizTaskLevelLambdaQueryWrapperCom = Wrappers.lambdaQuery();
            bizTaskLevelList = bizTaskLevelMapper.selectList(bizTaskLevelLambdaQueryWrapperCom);
        }else {
            LambdaQueryWrapper<BizTaskLevel> bizTaskLevelLambdaQueryWrapper = Wrappers.lambdaQuery();
            bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getTenantId,tenantId);
            bizTaskLevelLambdaQueryWrapper.isNull(BizTaskLevel::getCompanyId);
            bizTaskLevelList = bizTaskLevelMapper.selectListNoAdd(bizTaskLevelLambdaQueryWrapper);
            if (CollectionUtil.isEmpty(bizTaskLevelList)){
                Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(tenantId);
                LambdaQueryWrapper<BizTaskLevel> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(BizTaskLevel::getTenantId,topFloorTenamtId);
                bizTaskLevelLambdaQueryWrapper.isNull(BizTaskLevel::getCompanyId);
                bizTaskLevelList = bizTaskLevelMapper.selectListNoAdd(lambdaQueryWrapper);
            }

        }

        List<BizTaskLevelDTO> bizTaskLevelDTOS = ListCopyUtil.copy(bizTaskLevelList, BizTaskLevelDTO.class);
        return bizTaskLevelService.assembledTreeNodeLists(bizTaskLevelDTOS);

    }

    /**
     * 查询顶层数据
     * @return
     */
    @Override
    public List<BizTaskLevelDTO> getTopLevel(){
        List<BizTaskLevel> bizTaskLevelList = new ArrayList<>();
        if(SecurityUtils.isGeneralName()) {
            LambdaQueryWrapper<BizTaskLevel> bizTaskLevelLambdaQueryWrapperCom = Wrappers.lambdaQuery();
            bizTaskLevelLambdaQueryWrapperCom.eq(BizTaskLevel::getParentId,0);
            bizTaskLevelList = bizTaskLevelMapper.selectList(bizTaskLevelLambdaQueryWrapperCom);
        }else{
            Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
            LambdaQueryWrapper<BizTaskLevel> bizTaskLevelLambdaQueryWrapper = Wrappers.lambdaQuery();
            bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getTenantId,tenantId);
            bizTaskLevelLambdaQueryWrapper.isNull(BizTaskLevel::getCompanyId);
            bizTaskLevelLambdaQueryWrapper.eq(BizTaskLevel::getParentId,0);
            bizTaskLevelList = bizTaskLevelMapper.selectListNoAdd(bizTaskLevelLambdaQueryWrapper);
            if (bizTaskLevelList.size() <= 0){
                Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(tenantId);
                LambdaQueryWrapper<BizTaskLevel> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(BizTaskLevel::getTenantId,topFloorTenamtId);
                lambdaQueryWrapper.eq(BizTaskLevel::getParentId,0);
                lambdaQueryWrapper.isNull(BizTaskLevel::getCompanyId);
                bizTaskLevelList = bizTaskLevelMapper.selectListNoAdd(lambdaQueryWrapper);
            }
        }

        List<BizTaskLevelDTO> bizTaskLevelDTOS = ListCopyUtil.copy(bizTaskLevelList, BizTaskLevelDTO.class);
        return bizTaskLevelDTOS;
    }
}
