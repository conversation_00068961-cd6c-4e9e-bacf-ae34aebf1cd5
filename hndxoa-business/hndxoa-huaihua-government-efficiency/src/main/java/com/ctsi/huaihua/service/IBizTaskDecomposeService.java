package com.ctsi.huaihua.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 任务分解表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IBizTaskDecomposeService extends SysBaseServiceI<BizTaskDecompose> {


    /**
     * 分页查询 通用承办管理任务查看表
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskDecomposeDTO> queryListPage(BizTaskDecomposeDTO entityDTO, BasePageForm page);

    /**
     * 分页获取所有通用承办管理任务查看表
     *
     * @param entity
     * @return
     */
    PageResult<BizTaskDTO> queryList(BizTaskDTO entity, BasePageForm basePageForm);

    /**
     * 获取所有不分页 通用承办管理任务查看表
     *
     * @param entity
     * @return
     */
    List<BizTaskDTO> queryListNoPage(BizTaskDTO entity);


    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskDecomposeDTO findOne(Long id);

    /**
     * 任务接收详情页
     *
     * @param id 子任务id参数 task_decompose_id
     * @return
     */
    BizTaskDetailsDecomposeDTO findOneDetail(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskDecomposeDTO create(BizTaskDecomposeDTO entity);


    /**
     * 更新
     *
     * @param entity 承办管理：接收保存、任务转办
     * @return
     */
    int update(BizTaskDecomposeDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 是否存在
     * <p>
     * existByBizTaskDecomposeId
     *
     * @param code
     * @return
     */
    boolean existByBizTaskDecomposeId(Long code);

    /**
     * 批量新增
     * <p>
     * create batch
     *
     * @param dataList
     * @return
     */
    Boolean insertBatch(List<BizTaskDecomposeDTO> dataList);

    /**
     * 根据主表ID查询数据
     * <p>
     * create batch
     *
     * @param supervisionId
     * @return
     */
    List<BizTaskDecomposeDTO> queryListBySupervisionId(Long supervisionId);


    /**
     * 任务签收
     *
     * @param id
     */
    void updateHasSign(Long id);


    /**
     * 获取签收的可以选择的转办的任务，只能选择一次
     */
    List<BizTaskDTO> queryTaskTransferNumber(long currentUserId, Long taskDecomposeId);

    List<BizPersonLiableAndReceiverDTO> queryPersonLiableAndReceiver(Long subId);

    /**
     * 承办任务台账
     *
     * @param dto
     * @param basePageForm
     * @param taskDecomposeDueTime
     * @return
     */
    PageResult<BizTaskDTO> listReport(BizTaskDTO dto, BasePageForm basePageForm, int taskDecomposeDueTime);

    /**
     * 承办任务台账(查询主表记录数量)
     *
     * @param dto
     * @param basePageForm
     * @param taskDecomposeDueTime
     * @return
     */
    PageResult<BizTaskDTO> listSupReport(BizTaskDTO dto, BasePageForm basePageForm, int taskDecomposeDueTime);

    /**
     * 任务办结
     */
    int updateHasFinish(BizTaskDecomposeScoreDTO bizTaskDecomposeScoreDTO);

    BizTaskDetailsDecomposeDTO getDecIncludeFeedbackInfo(Long id);

    /**
     * 查询分解任务  根据主任务id
     * 右侧对应分解任务默认以截止时间升序排序；如有最新成果数据，则将最新分解任务置顶
     *
     * @return
     */
    PageResult<BizTaskDTO> getTaskListBySubId(BizTaskDTO dto, BasePageForm basePageForm);

    /**
     * 更新最新反馈的成果标记
     *
     * @return 1 标识需要 刷新主任务列表; 0 不需要刷新
     */
    int updateFeedbackSign(BizTaskDecomposeDTO bizTaskDecomposeDTO);

    /**
     * 首页角标
     *
     * @param type
     * @return
     */
    Integer cornerMarkData(Integer type);

    /**
     * 任务收件箱角标
     *
     * @return
     */
    Integer cornerMarkTaskInbox();

    /**
     * 成果收件箱角标接口
     */
    Integer cornerMarkResultsInbox();

    /**
     * 临期任务角标接口
     */
    Integer cornerMarkTemporaryTask();

    /**
     * 逾期任务角标接口
     */
    Integer cornerMarkOverdueTask();

    /**
     * @param id 分解任务id
     * @return
     */
    TaskImplementationDetailDTO queryImplementationDetail(Long id);

    /**
     * 查询所有子任务
     *
     * @param dto
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskDTO> getAllTheSubtasks(BizTaskDTO dto, BasePageForm basePageForm);

    /**
     * 查询通报任务
     *
     * @param bizTaskDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskDTO> queryNotificationTask(BizTaskDTO bizTaskDTO, BasePageForm basePageForm);

    /**
     * @param bizTaskSupervisionAndScoreAddDTO
     * @return
     */
    BizTaskScoreDetailDTO queryScoreDetailList(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm);


    /**
     * 根据分解任务id,查找该条分解任务转办 的所有任务的反馈记录
     *
     * @param bizTaskDecomposeDTO
     * @return
     */
    List<BizTaskFeedbackDTO> getDecomposeTaskTransferFeedback(BizTaskDecomposeDTO bizTaskDecomposeDTO);


    /**
     * 根据主表主键ID查询分解表任务(无隔离)
     *
     * @param supervisionId
     * @return
     */
    List<BizTaskDecomposeDTO> queryDecomposeBySupervisionId(Long supervisionId);

    /**
     * 查询没有签收的
     *
     * @param bizTaskDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskDTO> selectNoSignCommonTaskList(NoHasSignTaskDTO bizTaskDTO, BasePageForm basePageForm);

    /**
     * 查询分解表任务未办结的数量
     *
     * @param subTblFk
     */
    int selectTaskDecomposeCount(Long subTblFk, Long dutyPeopleId);

    /**
     * 根据主任务id获取所有子任务,并且标记哪个子任务是自己的
     *
     * @param id
     * @return
     */
    List<BizTaskDetailsDecomposeDTO> getTaskDecomposeAll(Long id);

    /**
     * 导出用
     * @param dto
     * @param basePageForm
     * @param dueTimeType
     * @return
     */
    PageResult<BizTaskDTO> listReportToExcel(BizTaskDTO dto, BasePageForm basePageForm, int dueTimeType);
}
