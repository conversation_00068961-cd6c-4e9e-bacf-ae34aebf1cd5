package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizScoreUserConfigDTO;
import com.ctsi.huaihua.entity.BizScoreUserConfig;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 免考核人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface IBizScoreUserConfigService extends SysBaseServiceI<BizScoreUserConfig> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizScoreUserConfigDTO> queryListPage(BizScoreUserConfigDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizScoreUserConfigDTO> queryList(BizScoreUserConfigDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizScoreUserConfigDTO findOne(Long id);

    /**
     * 根据用户id获取免考核人员对象
     *
     * @param userIds
     * @return
     */
    List<BizScoreUserConfigDTO> findUserByUserId(List<Long> userIds);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizScoreUserConfigDTO create(BizScoreUserConfigDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizScoreUserConfigDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(List<Long> id);

     /**
     * 是否存在
     *
     * existByBizScoreUserConfigId
     * @param code
     * @return
     */
    boolean existByBizScoreUserConfigId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizScoreUserConfigDTO> dataList);


}
