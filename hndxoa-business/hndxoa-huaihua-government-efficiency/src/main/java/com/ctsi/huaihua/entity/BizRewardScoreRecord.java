package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 奖分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_reward_score_record")
@ApiModel(value="BizRewardScoreRecord对象", description="奖分记录表")
public class BizRewardScoreRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;

    /**
     * 加分年份
     */
    @ApiModelProperty(value = "加分年份")
    private Integer scoreYear;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 加分原因
     */
    @ApiModelProperty(value = "加分原因")
    private String scoreReason;

    /**
     * 审核人ID
     */
    @ApiModelProperty(value = "审核人ID")
    private Long reviewerId;

    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    private String reviewerName;

    /**
     * 审核人部门ID
     */
    @ApiModelProperty(value = "审核人部门ID")
    private Long reviewerDepartmentId;

    /**
     * 审核人单位ID
     */
    @ApiModelProperty(value = "审核人单位ID")
    private Long reviewerCompanyId;

    /**
     * 审核人是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "审核人是否未读0: 未查看  1:已查看")
    private Integer reviewerHasRead;

    /**
     * 0：未审核 1 审核通过 2 驳回
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过 2 驳回")
    private Integer hasReviewer;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewerTime;

    /**
     * 驳回理由
     */
    @ApiModelProperty(value = "驳回理由")
    private String refuseReason;


    /**
     * 申请加分来源：1市直单位 2督查室
     */
    @ApiModelProperty(value = "申请加分来源：1市直单位 2督查室")
    private Integer rewardResource;

    @ApiModelProperty(value = "创建人单位名称")
    private String createCompanyName;


}
