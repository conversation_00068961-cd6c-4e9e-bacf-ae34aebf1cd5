package com.ctsi.huaihua.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.common.datapermission.DataFilterThreadLocal;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.*;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.huaihua.mapper.BizTaskDecomposeMapper;
import com.ctsi.huaihua.mapper.BizTaskScoreAddMapper;
import com.ctsi.huaihua.mapper.BizTaskScoreSubMapper;
import com.ctsi.huaihua.mapper.BizTaskSupervisionMapper;
import com.ctsi.huaihua.service.*;
import com.ctsi.sms.smssend.SmsSendEnum;
import com.ctsi.sms.smssend.SmsSendUtil;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.CscpUserRole;
import com.ctsi.ssdc.admin.domain.dto.CscpMenusDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpMenusRepository;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRoleRepository;
import com.ctsi.ssdc.admin.service.CscpMenusService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.workrestdate.service.ITSysWorkRestDateService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务分解表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Slf4j
@Service
public class BizTaskDecomposeServiceImpl extends SysBaseServiceImpl<BizTaskDecomposeMapper, BizTaskDecompose> implements IBizTaskDecomposeService {

    @Autowired
    private BizTaskDecomposeMapper bizTaskDecomposeMapper;

    @Autowired
    private IBizTaskSupervisionService bizTaskSupervisionService;


    @Autowired
    private IBizTaskFeedbackService bizTaskFeedbackService;


    @Autowired
    private IBizTaskScoreAddService bizTaskScoreAddService;

    @Autowired
    private IBizTaskScoreSubService bizTaskScoreSubService;


    @Autowired
    private IBizTaskScoreService bizTaskScoreService;

    @Autowired
    private IBizTaskLevelService bizTaskLevelService;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;


    @Autowired
    private BizTaskSupervisionMapper bizTaskSupervisionMapper;

    /**
     * 分页查询 通用承办管理任务查看表
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskDecomposeDTO> queryListPage(BizTaskDecomposeDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskDecompose> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskDecompose> pageData = bizTaskDecomposeMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskDecomposeDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskDecomposeDTO.class));

        return new PageResult<BizTaskDecomposeDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 通用承办管理任务列表查询(查询主表记录数量)
     *
     * @param entityDTO
     * @return
     */
    private PageResult<BizTaskDTO> querySupList(BizTaskDTO entityDTO, BasePageForm basePageForm) {
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectCommonTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        int count = bizTaskDecomposeMapper.selectCommonTaskListCount(entityDTO);
        PageResult<BizTaskDTO> bizTaskDTOPageResults = new PageResult<>(page.getRecords(), page.getTotal(),
                page.getCurrent());
        //bizTaskDTOPageResults.setSupTotal(count);
        // 增加字段 任务来源 1 本单位 2 外单位
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        List<BizTaskDTO> records = page.getRecords();
        for (BizTaskDTO record : records) {
            Long supCompanyId = record.getSupCompanyId();
            if (currentCompanyId.longValue() == supCompanyId.longValue()) {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OWMER_DEPT);
            } else {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OTHER_DEPT);
            }

            // 办结
            if (record.getHasFinish() == 1) {
                LambdaQueryWrapper<BizTaskScore> lambFdaQueryWrapper = new LambdaQueryWrapper<>();
                lambFdaQueryWrapper.in(BizTaskScore::getBizTaskDecompose, record.getTaskDecomposeId());
                BizTaskScore taskScoreList = bizTaskScoreService.selectOneNoAdd(lambFdaQueryWrapper);
                if(taskScoreList != null){
                    record.setActualScore(taskScoreList.getActualScore());
                }
            } else {
                if (StringUtils.isNotEmpty(record.getDecomposeTaskSource())) {
                    record.setActualScore(Double.valueOf(record.getDecomposeTaskSource()));
                } else {
                    record.setActualScore(0.0d);
                }
            }

        }

        // 获取分值(先取主任务，再取办结的)
        List<Long> bizComposeIds = new ArrayList<>();
        records.forEach(bizTaskDTO -> {

            //判断是否为牵头单位人
            if(bizTaskDTO.getLeadPeopleId()!=null){
                if(bizTaskDTO.getDutyPeopleId().longValue() == bizTaskDTO.getLeadPeopleId().longValue()){
                    Long userId = entityDTO.getSignPeopleId();
                    int num = this.selectTaskDecomposeCount(bizTaskDTO.getTaskSupervisionId(), userId);
                    if(num == 0){
                        bizTaskDTO.setIsFeedbackBtn(0);
                    }else{
                        bizTaskDTO.setIsFeedbackBtn(1);
                    }
                }else{
                    bizTaskDTO.setIsFeedbackBtn(0);
                }
            }else{
                bizTaskDTO.setIsFeedbackBtn(0);
            }
        });

        return bizTaskDTOPageResults;

    }

    /**
     * 通用承办管理任务列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public PageResult<BizTaskDTO> queryList(BizTaskDTO entityDTO, BasePageForm basePageForm) {
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectCommonTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        PageResult<BizTaskDTO> bizTaskDTOPageResults = new PageResult<>(page.getRecords(), page.getTotal(),
                page.getCurrent());
        // 增加字段 任务来源 1 本单位 2 外单位
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        List<BizTaskDTO> records = page.getRecords();
        for (BizTaskDTO record : records) {
            Long supCompanyId = record.getSupCompanyId();
            if (currentCompanyId.longValue() == supCompanyId.longValue()) {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OWMER_DEPT);
            } else {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OTHER_DEPT);
            }

            // 办结
            if (record.getHasFinish() == 1) {
                LambdaQueryWrapper<BizTaskScore> lambFdaQueryWrapper = new LambdaQueryWrapper<>();
                lambFdaQueryWrapper.in(BizTaskScore::getBizTaskDecompose, record.getTaskDecomposeId());
                BizTaskScore taskScoreList = bizTaskScoreService.selectOneNoAdd(lambFdaQueryWrapper);
                if(taskScoreList != null){
                    record.setActualScore(taskScoreList.getActualScore());
                }
            } else {
                if (StringUtils.isNotEmpty(record.getDecomposeTaskSource())) {
                    record.setActualScore(Double.valueOf(record.getDecomposeTaskSource()));
                } else {
                    record.setActualScore(0.0d);
                }
            }
            if (Objects.nonNull(record.getTaskTransferId())){
                record.setTopCompanyId(getTopCompanyId(record.getTaskTransferId()));
            }else {
                record.setTopCompanyId(record.getSupCompanyId());
            }

        }

        // 获取分值(先取主任务，再取办结的)
        List<Long> bizComposeIds = new ArrayList<>();
        records.forEach(bizTaskDTO -> {

            //判断是否为牵头单位人
            if(bizTaskDTO.getLeadPeopleId()!=null){
                if(bizTaskDTO.getDutyPeopleId().longValue() == bizTaskDTO.getLeadPeopleId().longValue()){
                    Long userId = entityDTO.getSignPeopleId();
                    int num = this.selectTaskDecomposeCount(bizTaskDTO.getTaskSupervisionId(), userId);
                    if(num == 0){
                        bizTaskDTO.setIsFeedbackBtn(0);
                    }else{
                        bizTaskDTO.setIsFeedbackBtn(1);
                    }
                }else{
                    bizTaskDTO.setIsFeedbackBtn(0);
                }
            }else{
                bizTaskDTO.setIsFeedbackBtn(0);
            }
        });

        return bizTaskDTOPageResults;

    }

    /**
     * 通用承办管理任务列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskDTO> queryListNoPage(BizTaskDTO entityDTO) {
        List<BizTaskDTO> bizTaskDTOS = bizTaskDecomposeMapper.selectCommonTaskList(entityDTO);
        // 增加字段 任务来源 1 本单位 2 外单位
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        for (BizTaskDTO record : bizTaskDTOS) {
            Long supCompanyId = record.getSupCompanyId();
            if (currentCompanyId.longValue() == supCompanyId.longValue()) {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OWMER_DEPT);
            } else {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OTHER_DEPT);
            }
        }
        return bizTaskDTOS;

    }

    /**
     * 单个查询
     * 任务接收  单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskDecomposeDTO findOne(Long id) {
        BizTaskDecompose bizTaskDecompose = bizTaskDecomposeMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizTaskDecompose, BizTaskDecomposeDTO.class);
    }

    /**
     * 任务接收详情页  单个查询
     * 反馈增加反馈结果 详情
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskDetailsDecomposeDTO findOneDetail(Long id) {
        BizTaskDecompose bizTaskDecompose = bizTaskDecomposeMapper.selectById(id);
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionService.getById(bizTaskDecompose.getSubTblFk());
        BizTaskDetailsDecomposeDTO bizTaskDetailsDecomposeDTO = BeanConvertUtils.copyProperties(bizTaskDecompose, BizTaskDetailsDecomposeDTO.class);
        bizTaskDetailsDecomposeDTO.setTitle(bizTaskSupervision.getTitle());
        bizTaskDetailsDecomposeDTO.setTaskTag(bizTaskSupervision.getTaskTag());
        bizTaskDetailsDecomposeDTO.setAnnex(bizTaskSupervision.getAnnex());
        bizTaskDetailsDecomposeDTO.setDegreeUrgency(bizTaskSupervision.getDegreeUrgency());
        bizTaskDetailsDecomposeDTO.setTaskSource(bizTaskSupervision.getTaskSource());
        bizTaskDetailsDecomposeDTO.setCreateName(bizTaskSupervision.getCreateName());

        bizTaskDetailsDecomposeDTO.setExamineMethod(bizTaskSupervision.getExamineMethod());
        bizTaskDetailsDecomposeDTO.setTaskDescription(bizTaskSupervision.getTaskDescription());
        return bizTaskDetailsDecomposeDTO;
    }

    /**
     * 任务接收详情页  单个查询
     * 反馈增加反馈结果 详情
     */
    @Override
    public BizTaskDetailsDecomposeDTO getDecIncludeFeedbackInfo(Long id) {
        BizTaskDetailsDecomposeDTO bizTaskDetailsDecomposeDTO = findOneDetail(id);
        // 增加反馈结果集合
        LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, id).orderByDesc(BizTaskFeedback::getCreateTime);
        List<BizTaskFeedback> bizTaskFeedbacks = bizTaskFeedbackService.selectListNoAdd(queryWrapper);

        List<BizTaskFeedbackDTO> collect = bizTaskFeedbacks.stream().map(bizTaskFeedback -> BeanConvertUtils.copyProperties(bizTaskFeedback,
                BizTaskFeedbackDTO.class)).collect(Collectors.toList());
        bizTaskDetailsDecomposeDTO.setBizTaskFeedbackDTOList(collect);
        return bizTaskDetailsDecomposeDTO;
    }

    @Override
    public PageResult<BizTaskDTO> getTaskListBySubId(BizTaskDTO dto, BasePageForm basePageForm) {
        IPage<BizTaskDTO> page = null;
        List<BizTaskDTO> records = null;
        // 截止时间升序

        Integer hasFinish = dto.getSupHasFinish();
        // 主任务已办结   排序：根据任务的办结时间降序排序。desc
        if (null != hasFinish && hasFinish.intValue() == 1) {
            dto.setSupHasFinish(1);
            page = bizTaskDecomposeMapper.selectFinishTaskBoxList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), dto);
            records = page.getRecords();
            Collections.sort(records, (o1, o2) -> o2.getHasFinishTime().compareTo(o1.getHasFinishTime()));

        } else { /* 收件箱排序 : 右侧对应分解任务默认以截止时间升序排序；如有最新成果数据，则将最新分解任务置顶。 */
//            dto.setIsLead(bizTaskSupervisionService.isLeadPeople(dto.getSupCreateById()));
            dto.setHasFinish(hasFinish);
            page = bizTaskDecomposeMapper.selectBoxTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), dto);
            records = page.getRecords();
            Collections.sort(records, (o1, o2) -> {
                int feedbackCompareto = o2.getNewFeedbackSign().compareTo(o1.getNewFeedbackSign());   // 降序
                if (feedbackCompareto == 0 && o1.getNewFeedbackSign() == 1) { // 有反馈 最新反馈时间 即 成果上报时间 降序
                    return o2.getNewFeedbackTime().compareTo(o1.getNewFeedbackTime());
                }
                if (o1.getDueTime() != null && o2.getDueTime() != null) {
                    if (feedbackCompareto == 0 && o1.getNewFeedbackSign() == 0) { // 没反馈 截止时间升序 排序
                        return o1.getDueTime().compareTo(o2.getDueTime());
                    }
                }
                return feedbackCompareto;
            });
        }
        // 获取分值(先取主任务，再取办结的)
        records = page.getRecords();
        List<Long> bizComposeIds = new ArrayList<>();
        records.forEach(bizTaskDTO -> {
//            //获取主任务分数
//            if (Objects.nonNull(bizTaskDTO.getDegreeUrgency()) && Objects.nonNull(bizTaskDTO.getDifficulty()) && Objects.nonNull(bizTaskDTO.getInspectorTaskLevel())) {
//                bizTaskDTO.setActualScore(bizTaskSupervisionService.getScoreById(bizTaskDTO.getTaskSupervisionId()));
//            } else {
//                bizTaskDTO.setActualScore(0D);
//            }

            //若任务已办结，获取办结后分数
            if (bizTaskDTO.getTaskDecomposeId() != null) {
                bizComposeIds.add(bizTaskDTO.getTaskDecomposeId());
            }
        });
        if (CollectionUtil.isNotEmpty(bizComposeIds)) {
            LambdaQueryWrapper<BizTaskScore> lambFdaQueryWrapper = new LambdaQueryWrapper<>();
            lambFdaQueryWrapper.in(BizTaskScore::getBizTaskDecompose, bizComposeIds);
            List<BizTaskScore> taskScoreList = bizTaskScoreService.selectListNoAdd(lambFdaQueryWrapper);
            List<BizTaskDTO> finalRecords = records;
            taskScoreList.forEach(bizTaskScore -> {
                long decomeId = bizTaskScore.getBizTaskDecompose().longValue();
                finalRecords.forEach(bizTaskDTO -> {
                    if (bizTaskDTO.getTaskDecomposeId() != null && bizTaskDTO.getTaskDecomposeId().longValue() == decomeId) {
                        bizTaskDTO.setActualScore(bizTaskScore.getActualScore());
                    }
                });
            });
        }

        PageResult<BizTaskDTO> bizTaskPageResults = new PageResult<>(records, page.getTotal(), page.getCurrent());
        return bizTaskPageResults;
    }

    @Override
    public int updateFeedbackSign(BizTaskDecomposeDTO entity) {
        //更新状态码
        Long id = entity.getId();
        BizTaskDecompose updatedec = BeanConvertUtils.copyProperties(entity, BizTaskDecompose.class);
        bizTaskDecomposeMapper.updateById(updatedec);
        int i = 0;
        Integer newFeedbackSign = entity.getNewFeedbackSign();
        if (ObjectUtil.isNotEmpty(newFeedbackSign) && ObjectUtil.isNotEmpty(id)) {
            BizTaskDecompose bizTaskDecompose = this.getById(id);
            // 主任务id
            Long subTblFk = bizTaskDecompose.getSubTblFk();
            LambdaQueryWrapper<BizTaskDecompose> eq = new LambdaQueryWrapper<BizTaskDecompose>().eq(BizTaskDecompose::getSubTblFk, subTblFk)
                    .eq(BizTaskDecompose::getNewFeedbackSign, 1);
            List<BizTaskDecompose> bizTaskDecomposes = bizTaskDecomposeMapper.selectListNoAdd(eq);
            if (bizTaskDecomposes.isEmpty()) { /* 分解任务 没有最新反馈,更新 主任务 反馈标记 */
                BizTaskSupervision bizTaskSupervision = bizTaskSupervisionService.getById(subTblFk);
                if (bizTaskSupervision.getNewSupFeedbackSign().intValue() == 1) {
                    bizTaskSupervision.setNewSupFeedbackSign(0);
                    bizTaskSupervisionService.updateById(bizTaskSupervision);
                    // 需要刷新
                    i = 1;
                }
            }
        }
        return i;
    }

    /**
     * type = 1  * 任务收件箱：任务承办角色使用，即【承办管理】-【任务收件箱】，角标展示最新收到的未接收任务数量。
     * <p>
     * type = 2  * 成果收件箱：任务管理角色使用，即【成果管理】-【成果收件箱】，角标展示最新未审核的成果数量
     * <p>
     * type = 3  * 成果办结：任务承办角色使用，即【反馈管理】-【已办结】，角标展示最新已办结任务数量，浏览页面后角标清零。
     */
    @Override
    public Integer cornerMarkData(Integer type) {
        long currentUserId = SecurityUtils.getCurrentUserId();
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        Integer counts = 0;
        switch (type.intValue()) {
            case (1):
                // 任务收件箱
                NoHasSignTaskDTO bizTaskDTO = NoHasSignTaskDTO.builder().dutyPeopleId(currentUserId)
                        .build();
                //   如果是办公室专职人员取自己的上级交办的但是剔除本单位其他人的
                if (SecurityUtils.hasPermissions(TaskStatus.TASK_SIGN_CODE)) {
                    bizTaskDTO.setQueryType(2);
                    bizTaskDTO.setCompanyId(currentCompanyId);
                } else {
                    bizTaskDTO.setQueryType(1);
                }
                IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectNoSignCommonTaskList(
                        PageHelperUtil.getMPlusPageByBasePage(new BasePageForm(1, 10000)), bizTaskDTO);

                counts = Math.toIntExact(page.getTotal());
                break;

            case (2):
                /*
                 成果收件箱
                 最新未审核的成果数量  -> 未办结 , 有反馈   任务管理角色
                 没有配置 管理员角色 按个人过滤   // 配置 管理员角色,按照单位过滤
                */
                boolean myFilter = DataFilterThreadLocal.get() == null;
              /*  LambdaQueryWrapper<BizTaskDecompose> queryWrapper2 = new LambdaQueryWrapper<BizTaskDecompose>()
                        .eq(BizTaskDecompose::getHasFinish, 0).eq(BizTaskDecompose::getNewFeedbackSign, 1);
                if (myFilter) {
                    queryWrapper2.eq(BizTaskDecompose::getCreateBy, currentUserId);
                } else { // 配置 管理员角色,按照单位过滤
                    queryWrapper2.eq(BizTaskDecompose::getCompanyId, currentCompanyId);
                }
                // 是否牵头单位
                int flag = bizTaskSupervisionService.isLeadPeople(currentUserId);
                counts = bizTaskDecomposeMapper.selectCount(queryWrapper2);*/
                BizTaskDTO dto = new BizTaskDTO();
                BasePageForm basePageForm = new BasePageForm(1, 2);
                if (myFilter) { // 个人
                    dto.setSupCreateById(currentUserId);
                } else { // 配置 管理员角色,按照单位过滤
                    dto.setSupCompanyId(currentCompanyId);
                }
                dto.setHasFeedback(1);
                dto.setNewFeedbackSign(1);
                dto.setSupHasFinish(0);
                PageResult<BizTaskDTO> list = this.getTaskListBySubId(dto, basePageForm);
                counts = Math.toIntExact(list.getRecordsTotal());
                break;
            case (3):
                /*
                 成果办结
                【反馈管理】-【已办结】 承办角色
                */
                LambdaQueryWrapper<BizTaskDecompose> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(BizTaskDecompose::getNewFinishSign, 1).and(i -> i.eq(BizTaskDecompose::getContactPeopleId,
                        currentUserId).or().eq(BizTaskDecompose::getDutyPeopleId, currentUserId)); /* 责任人 or 联系人 */

                counts = bizTaskDecomposeMapper.selectCount(queryWrapper);
                break;
            default:
                break;

        }

        return counts;
    }

    @Override
    public Integer cornerMarkTaskInbox() {
        return cornerMarkData(1);
    }

    @Override
    public Integer cornerMarkResultsInbox() {
        return cornerMarkData(2);
    }

    @Override
    public Integer cornerMarkTemporaryTask() {
        /* 临期 */
        BizTaskDTO bizTaskDTO =
                BizTaskDTO.builder().dutyPeopleId(SecurityUtils.getCurrentUserId())
                        .queryType("3")
                        .hasSign(1)
                        .supHasFinish(0)
                        .hasFinish(0)
                        .hasFeedback(0)
                        .hasPublish(1)
                        .sortField("due_time")
                        .sortType("asc")
                        .build();
        List<BizTaskDTO> bizTasks = bizTaskDecomposeMapper.selectCommonTaskList(bizTaskDTO);
        int size = bizTasks.size();
        return size;
    }

    @Override
    public Integer cornerMarkOverdueTask() {
        /* 临期 */
        BizTaskDTO bizTaskDTO =
                BizTaskDTO.builder().dutyPeopleId(SecurityUtils.getCurrentUserId())
                        .queryType("2")
                        .hasSign(1)
                        .supHasFinish(0)
                        .hasFinish(0)
                        .hasFeedback(0)
                        .hasPublish(1)
                        .sortField("due_time")
                        .sortType("asc")
                        .build();
        List<BizTaskDTO> bizTasks = bizTaskDecomposeMapper.selectCommonTaskList(bizTaskDTO);
        int size = bizTasks.size();
        return size;
    }

    @Override
    public TaskImplementationDetailDTO queryImplementationDetail(Long id) {

        // 分解任务
        BizTaskDecompose taskDecompose = getById(id);
        Long subTblFk = taskDecompose.getSubTblFk();

        // 主任务
        BizTaskSupervision taskSupervision = bizTaskSupervisionService.getById(subTblFk);
        LocalDateTime createTime = taskSupervision.getCreateTime();

        TaskImplementationDetailDTO taskImplementationDetailDTO = BeanConvertUtils.copyProperties(taskDecompose, TaskImplementationDetailDTO.class);
        taskImplementationDetailDTO.setSupCreateName(taskSupervision.getCreateName());
        taskImplementationDetailDTO.setSupCreateTime(createTime);
        taskImplementationDetailDTO.setSupCompanyName(taskSupervision.getCompanyName());
        taskImplementationDetailDTO.setSupDepartmentName(taskSupervision.getDepartmentName());


        // 增加反馈结果集合
        LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, id).orderByDesc(BizTaskFeedback::getCreateTime);
        List<BizTaskFeedback> bizTaskFeedbacks = bizTaskFeedbackService.selectListNoAdd(queryWrapper);

        List<BizTaskFeedbackDTO> feedbacks =
                bizTaskFeedbacks.stream().map(bizTaskFeedback -> BeanConvertUtils.copyProperties(bizTaskFeedback,
                        BizTaskFeedbackDTO.class)).collect(Collectors.toList());
        taskImplementationDetailDTO.setFeedBackList(feedbacks); // 分解任务反馈集合

        // 查找,转办任务
        LambdaQueryWrapper<BizTaskSupervision> wrapper = new LambdaQueryWrapper();
        wrapper.eq(BizTaskSupervision::getTaskTransferDecomposeId, id);
        List<BizTaskSupervision> bizTaskSupervisions = bizTaskSupervisionService.selectListNoAdd(wrapper);
        // 转办记录
        if (!bizTaskSupervisions.isEmpty()) {
            LambdaQueryWrapper<BizTaskDecompose> queryWrapper1 = new LambdaQueryWrapper();
            queryWrapper1.eq(BizTaskDecompose::getSubTblFk, bizTaskSupervisions.get(0).getId());
            List<BizTaskDecompose> decomposeList = selectListNoAdd(queryWrapper1);
            List<BizTaskDecomposeDTO> copy = ListCopyUtil.copy(decomposeList, BizTaskDecomposeDTO.class);
            for (BizTaskDecomposeDTO bizTaskDecomposeDTO : copy) {
                // 增加反馈结果集合
                LambdaQueryWrapper<BizTaskFeedback> wrapper2 = new LambdaQueryWrapper();
                wrapper2.eq(BizTaskFeedback::getBizTaskDecompose, bizTaskDecomposeDTO.getId()).orderByDesc(BizTaskFeedback::getCreateTime);
                List<BizTaskFeedback> feedbackList = bizTaskFeedbackService.selectListNoAdd(wrapper2);
                bizTaskDecomposeDTO.setBizTaskFeedbackDTOList(ListCopyUtil.copy(feedbackList, BizTaskFeedbackDTO.class));
            }
            taskImplementationDetailDTO.setTransformTaskList(copy);
        }

        return taskImplementationDetailDTO;
    }

    /**
     * 查询所有子任务
     *
     * @param dto
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskDTO> getAllTheSubtasks(BizTaskDTO dto, BasePageForm basePageForm) {
        dto.setHasSign(99);
        dto.setSortSub(1);

        //没有减过分的数据
        IPage<BizTaskDTO> page = page = bizTaskDecomposeMapper.selectReducedTask(PageHelperUtil.getMPlusPageByBasePage(basePageForm), dto);


        PageResult<BizTaskDTO> bizTaskPageResults = new PageResult<>(page.getRecords(), page.getTotal(), page.getCurrent());
        return bizTaskPageResults;
    }

    /**
     * 查询通报任务，分页
     *
     * @param bizTaskDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskDTO> queryNotificationTask(BizTaskDTO bizTaskDTO, BasePageForm basePageForm) {
        bizTaskDTO.setHasPublish(TaskStatus.TASK_RELEASE);
        bizTaskDTO.setHasSign(TaskStatus.HAS_SIGN);
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectCommonTaskSubList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizTaskDTO);

        return new PageResult<>(page.getRecords(), page.getTotal(), page.getCurrent());
    }

    @Override
    public List<BizTaskFeedbackDTO> getDecomposeTaskTransferFeedback(BizTaskDecomposeDTO bizTaskDecomposeDTO) {
        LambdaQueryWrapper<BizTaskSupervision> qwrapper = new LambdaQueryWrapper();
        // 转办的主任务
        qwrapper.eq(BizTaskSupervision::getTaskTransferDecomposeId, bizTaskDecomposeDTO.getId());
        List<BizTaskSupervision> bizTaskSupervisions = bizTaskSupervisionService.selectListNoAdd(qwrapper);

        List<BizTaskFeedbackDTO> result = new ArrayList<>();
        if (bizTaskSupervisions.size() > 0) {
            Long id = bizTaskSupervisions.get(0).getId();
            // 查分解任务
            List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = queryListBySupervisionId(id);
            // 增加反馈结果集合
            for (BizTaskDecomposeDTO bizTaskDecomposeTransfDTO : bizTaskDecomposeDTOList) {
                LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, bizTaskDecomposeTransfDTO.getId()).orderByDesc(BizTaskFeedback::getCreateTime);
                List<BizTaskFeedback> bizTaskFeedbacks = bizTaskFeedbackService.selectListNoAdd(queryWrapper);

                List<BizTaskFeedbackDTO> collect = bizTaskFeedbacks.stream().map(bizTaskFeedback -> BeanConvertUtils.copyProperties(bizTaskFeedback,
                        BizTaskFeedbackDTO.class)).collect(Collectors.toList());
                result.addAll(collect);
            }
        }
        return result;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskDecomposeDTO create(BizTaskDecomposeDTO entityDTO) {
        BizTaskDecompose bizTaskDecompose = BeanConvertUtils.copyProperties(entityDTO, BizTaskDecompose.class);
        save(bizTaskDecompose);
        return BeanConvertUtils.copyProperties(bizTaskDecompose, BizTaskDecomposeDTO.class);
    }

    /**
     * 修改
     *
     * @param entity 承办管理：接收保存、任务转办
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskDecomposeDTO entity) {
        BizTaskDecompose bizTaskDecompose = BeanConvertUtils.copyProperties(entity, BizTaskDecompose.class);
        return bizTaskDecomposeMapper.updateById(bizTaskDecompose);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskDecomposeMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param bizTaskDecomposeId
     * @return
     */
    @Override
    public boolean existByBizTaskDecomposeId(Long bizTaskDecomposeId) {
        if (bizTaskDecomposeId != null) {
            LambdaQueryWrapper<BizTaskDecompose> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskDecompose::getId, bizTaskDecomposeId);
            List<BizTaskDecompose> result = bizTaskDecomposeMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskDecomposeDTO> dataList) {
        // 先按照逻辑删除写数据
        // 默认不发布
        int hasPublish = 0;
        String title = "";
        boolean isTranSfer = false ;// 是否转办
        Double tranScore = 0.0d;
        BizTaskSupervision bizTaskSupervision = new BizTaskSupervision();
        if (CollectionUtil.isNotEmpty(dataList)) {
            BizTaskDecomposeDTO bizTaskDecomposeDTO = dataList.get(0);
            Long subTblFk = bizTaskDecomposeDTO.getSubTblFk();
            bizTaskSupervision = bizTaskSupervisionService.getById(subTblFk);
            if (bizTaskSupervision != null) {
                hasPublish = bizTaskSupervision.getHasPublish();
                title = bizTaskSupervision.getTitle();

                //  获取转办的id
                Long taskTranSferId = bizTaskSupervision.getTaskTransferDecomposeId();
                if (taskTranSferId != null){
                    BizTaskDecompose bizTaskDecomposeTran = bizTaskDecomposeMapper.selectById(taskTranSferId);
                    if (bizTaskDecomposeTran != null){
                        tranScore = bizTaskDecomposeTran.getTaskScore();
                        isTranSfer = true;
                    }
                }
            }

//            String subDomainId = bizTaskDecomposeDTO.getSubDomainId();
//            LambdaQueryWrapper<BizTaskDecompose> queryWrapper = new LambdaQueryWrapper();
//            queryWrapper.eq(BizTaskDecompose::getSubTblFk, subTblFk);
            bizTaskDecomposeMapper.deleteBySubTblFk(subTblFk);
        }


        Boolean l = false;

        //   获取主表的业务数据
        List<BizTaskDecomposeDTO> realDataList = new ArrayList<>();
        Set<String> phoneList = new HashSet<>();
        int finalHasPublish = hasPublish;
        for (BizTaskDecomposeDTO bizTaskDecomposeDTO : dataList) {
            String dutyPeople = bizTaskDecomposeDTO.getDutyPeople();
            if (StringUtils.isEmpty(dutyPeople)) {
                continue;
            }
            JSONArray jsonArray = JSONArray.fromObject(dutyPeople);
            if (!jsonArray.isEmpty()) {
                JSONObject jsonObject = jsonArray.getJSONObject(0);
                DutyPeopleDTO dutyPeopleDTO = (DutyPeopleDTO) JSONObject.toBean(jsonObject, DutyPeopleDTO.class);
                bizTaskDecomposeDTO.setDutyPeopleId(Long.valueOf(dutyPeopleDTO.getUserId()));
                bizTaskDecomposeDTO.setDutyPeopleCompanyId(StringUtils.isEmpty(dutyPeopleDTO.getCompanyId()) ? null : Long.valueOf(dutyPeopleDTO.getCompanyId()));
                bizTaskDecomposeDTO.setDutyPeopleDepartmentId(StringUtils.isEmpty(dutyPeopleDTO.getDepartmentId()) ? null : Long.valueOf(dutyPeopleDTO.getDepartmentId()));
                bizTaskDecomposeDTO.setDutyPeopleName(dutyPeopleDTO.getUserName());
                bizTaskDecomposeDTO.setDutyPeopleCompanyName(dutyPeopleDTO.getCompanyName());
                bizTaskDecomposeDTO.setDutyPeopleDepartmentName(dutyPeopleDTO.getDepartmentName());
                bizTaskDecomposeDTO.setDutyPeoplePostName(dutyPeopleDTO.getPost());
                bizTaskDecomposeDTO.setGroupName(dutyPeopleDTO.getGroupName());
                bizTaskDecomposeDTO.setDutyPeoplePhone(dutyPeopleDTO.getMobile());
                if (StringUtils.isNotEmpty(bizTaskSupervision.getTaskScore())){
                    bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()));
                }


              /*  Boolean b = !Objects.isNull(bizTaskSupervision.getLeadPeopleId()) && (bizTaskSupervision.getLeadPeopleId().equals(bizTaskDecomposeDTO.getDutyPeopleId()));
                if (b) {
                    l = true;
                }*/
                //如果当前责任人是牵头单位人的话，分值占比设置为100%，如果不是分值占比设置为50%
                //  如果转办，一直都是这个分值
                if (isTranSfer){
                    bizTaskDecomposeDTO.setTaskScore(tranScore);
                }else {
                    if (!Objects.isNull(bizTaskSupervision.getTaskScore()) && StringUtils.isNotEmpty(bizTaskSupervision.getTaskScore())) {
//                        Boolean b = !Objects.isNull(bizTaskSupervision.getLeadPeopleId()) && (bizTaskSupervision.getLeadPeopleId().equals(bizTaskDecomposeDTO.getDutyPeopleId()));
//                        if (b) {
//                            bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()));
//                        } else {
//                            bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()) * 0.5);
//                        }
                        if (bizTaskSupervision.getLeadPeopleId() != null) {
                            if (bizTaskSupervision.getLeadPeopleId().longValue() == bizTaskDecomposeDTO.getDutyPeopleId().longValue()) {
                                bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()));
                            } else {
                                bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()) * 0.5);
                            }
                        } else {
                            bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()));
                        }
                    }
                }

                //如果牵头单位ID和责任人ID相等任务办结人为创建人，否则任务办结人为牵头人
                if (bizTaskSupervision.getLeadPeopleId() != null) {
                    if (bizTaskDecomposeDTO.getDutyPeopleId().longValue() == bizTaskSupervision.getLeadPeopleId().longValue()) {
                        bizTaskDecomposeDTO.setConcludePeopleId(bizTaskSupervision.getCreateBy().toString());
                    }else{
                        String concludePeopleId = bizTaskSupervision.getLeadPeopleId().toString();
                        //获取牵头单位人单位ID
                        LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
                        queryWrapper.eq(CscpUserOrg::getUserId, bizTaskSupervision.getLeadPeopleId());
                        CscpUserOrg cscpUserOrg = cscpUserOrgRepository.selectOneNoAdd(queryWrapper);
                        //获取牵头单位专职人员ID
                        List<CscpUserDTO> cscpUserDTOS = cscpUserRepository.selectCodeUser(cscpUserOrg.getCompanyId(), TaskStatus.TASK_SIGN_CODE);
                        if (CollectionUtil.isNotEmpty(cscpUserDTOS)) {
                            concludePeopleId = concludePeopleId +","+ cscpUserDTOS.get(0).getId().toString();
                        }
                        bizTaskDecomposeDTO.setConcludePeopleId(concludePeopleId);
                    }
                } else {//如果任务没有选择牵头单位，办结人则是创建人
                    bizTaskDecomposeDTO.setConcludePeopleId(bizTaskSupervision.getCreateBy().toString());
                }

                CscpUserDTO contantCscpUserDTO = null;

                //phoneList.add(dutyPeopleDTO.getMobile());
                //  如果是同一个单位，直接发送的责任人
                if (bizTaskDecomposeDTO.getDutyPeopleCompanyId() != null
                        && bizTaskDecomposeDTO.getDutyPeopleCompanyId().longValue()
                        == SecurityUtils.getCurrentCompanyId().longValue()) {
                    if (finalHasPublish == 1) {
                        phoneList.add(bizTaskDecomposeDTO.getDutyPeoplePhone());
                    }
                } else {
                    Long dutyPeopleCompanyId = bizTaskDecomposeDTO.getDutyPeopleCompanyId();
                    // 找到责任人单位下面谁有权限接收任务 code吗为 TaskStatus.TASK_SIGN_CODE 在这个常量，
                    List<CscpUserDTO> cscpUserDTOS = cscpUserRepository.selectCodeUser(dutyPeopleCompanyId, TaskStatus.TASK_SIGN_CODE);
                    if (CollectionUtil.isNotEmpty(cscpUserDTOS)) {
                        if (finalHasPublish == 1) {
                            phoneList.add(cscpUserDTOS.get(0).getMobile());
                        }
                        contantCscpUserDTO = cscpUserDTOS.get(0);
                        bizTaskDecomposeDTO.setContactPeopleId(contantCscpUserDTO.getId());
                        bizTaskDecomposeDTO.setContactPeopleName(contantCscpUserDTO.getRealName());
                        bizTaskDecomposeDTO.setContactPeopleCompanyId(contantCscpUserDTO.getCompanyId());
                        bizTaskDecomposeDTO.setContactPeopleCompanyName(contantCscpUserDTO.getCompanyName());
                        bizTaskDecomposeDTO.setContactPeopleDepartmentId(contantCscpUserDTO.getDepartmentId());
                        bizTaskDecomposeDTO.setContactPeopleDepartmentName(contantCscpUserDTO.getDepartmentName());
                        bizTaskDecomposeDTO.setContactPeoplePostName(contantCscpUserDTO.getPost());
                        bizTaskDecomposeDTO.setContactPeoplePhone(contantCscpUserDTO.getMobile());

                    }
                    // 包含权限码的第一个人，发送短信
                }

                //如果没有配置专职人员联络员为责任人
                if(bizTaskDecomposeDTO.getContactPeopleId() == null){
                    bizTaskDecomposeDTO.setContactPeopleId(bizTaskDecomposeDTO.getDutyPeopleId());
                    bizTaskDecomposeDTO.setContactPeoplePhone(bizTaskDecomposeDTO.getDutyPeoplePhone());
                    bizTaskDecomposeDTO.setContactPeopleName(bizTaskDecomposeDTO.getDutyPeopleName());
                }

                realDataList.add(bizTaskDecomposeDTO);
            }

        }

    /*    //如果当前责任人是牵头单位人的话，分值占比设置为100%，如果不是分值占比设置为50%
        for (int i = 0; i < realDataList.size(); i++) {
            BizTaskDecomposeDTO bizTaskDecomposeDTO = realDataList.get(i);
            if (!Objects.isNull(bizTaskDecomposeDTO) && !Objects.isNull(bizTaskSupervision.getTaskScore()) && l) {
                Boolean b = !Objects.isNull(bizTaskSupervision.getLeadPeopleId()) && (bizTaskSupervision.getLeadPeopleId().equals(bizTaskDecomposeDTO.getDutyPeopleId()));
                if (b) {
                    bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()));
                } else {
                    bizTaskDecomposeDTO.setTaskScore(Double.valueOf(bizTaskSupervision.getTaskScore()) * 0.5);
                }
            }
        }*/


        List<BizTaskDecompose> result = ListCopyUtil.copy(realDataList, BizTaskDecompose.class);
        // 短信发送
        if (StringUtils.isNotEmpty(title) && CollectionUtil.isNotEmpty(phoneList)) {
            SmsSendUtil.batchSendSms(phoneList, title, SmsSendEnum.HH_TASK_SMS_SIGN);
        }
        return saveBatch(result);
    }


    @Override
    public List<BizTaskDecomposeDTO> queryListBySupervisionId(Long supervisionId) {
        if (Objects.isNull(supervisionId)) {
            throw new BusinessException("主表ID不允许为空");
        }
        LambdaQueryWrapper<BizTaskDecompose> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskDecompose::getSubTblFk, supervisionId).orderByAsc(BizTaskDecompose::getEndDate);
        // 右侧对应分解任务默认以截止时间升序排序；  如有最新成果数据，则将最新分解任务置顶 TODO


        List<BizTaskDecompose> listData = bizTaskDecomposeMapper.selectListNoAdd(queryWrapper);
        List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = ListCopyUtil.copy(listData, BizTaskDecomposeDTO.class);
        return bizTaskDecomposeDTOList;
    }


    @Override
    public void updateHasSign(Long id) {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        BizTaskDecompose bizTaskDecompose = bizTaskDecomposeMapper.selectById(id);
        bizTaskDecompose.setHasSign(1);
        bizTaskDecompose.setReceiveTime(LocalDateTime.now());
        bizTaskDecompose.setSignPeopleId(bizTaskDecompose.getDutyPeopleId());
        bizTaskDecompose.setSignPeopleName(bizTaskDecompose.getDutyPeopleName());
        bizTaskDecompose.setSignPeoplePhone(bizTaskDecompose.getDutyPeoplePhone());
        bizTaskDecompose.setSignPeopleCompanyName(bizTaskDecompose.getDutyPeopleCompanyName());
        bizTaskDecomposeMapper.updateById(bizTaskDecompose);
    }


    /**
     * 查询责任人和接收人
     *
     * @param subId
     * @return
     */
    @Override
    public List<BizPersonLiableAndReceiverDTO> queryPersonLiableAndReceiver(Long subId) {
        //获取所有责任人
        List<BizTaskDecompose> bizTaskDecomposes = bizTaskDecomposeMapper.selectListNoAdd(
                new LambdaQueryWrapper<BizTaskDecompose>().eq(BizTaskDecompose::getSubTblFk, subId));

        //转换
        List<BizTaskDecomposeDTO> bizTaskDecomposeList = ListCopyUtil.copy(bizTaskDecomposes,
                BizTaskDecomposeDTO.class);

        //根据群组名称分组
        Map<String, List<BizTaskDecomposeDTO>> collect = bizTaskDecomposeList.stream().collect(Collectors.groupingBy(s -> s.getGroupName()));

        //将map数据转成List
        List<BizPersonLiableAndReceiverDTO> collect1 = collect.keySet().stream().map(i -> {
            BizPersonLiableAndReceiverDTO bizPersonLiableAndReceiverDTO = new BizPersonLiableAndReceiverDTO();
            bizPersonLiableAndReceiverDTO.setGroupName(i);
            bizPersonLiableAndReceiverDTO.setBizTaskDecomposeList(collect.get(i));
            return bizPersonLiableAndReceiverDTO;
        }).collect(Collectors.toList());


        return collect1;
    }

    /**
     * // 已反馈 是绿的
     * // 未反馈 到截止时间3天内 黄   以天为单位
     * // 未反馈 超出截止时间 红
     */
    @Override
    public PageResult<BizTaskDTO> listReport(BizTaskDTO dto, BasePageForm basePageForm, int dueTimeType) {
        PageResult<BizTaskDTO> bizTasks = queryList(dto, basePageForm);
        List<BizTaskDTO> data = bizTasks.getData();
        data.forEach(bizTaskDTO -> {
            // 重要性
            if (org.apache.commons.lang3.StringUtils.isNotBlank(bizTaskDTO.getInspectorTaskLevel())) {
                BizTaskLevelDTO inspectorTaskLevelDTO = bizTaskLevelService.findOne(Long.parseLong(bizTaskDTO.getInspectorTaskLevel()));
                bizTaskDTO.setInspectorTaskLevelDTO(inspectorTaskLevelDTO);
            }

            // 交办事项
            if (org.apache.commons.lang3.StringUtils.isNotBlank(bizTaskDTO.getInspectorItems())) {
                BizTaskLevelDTO inspectorItemsDTO = bizTaskLevelService.findOne(Long.parseLong(bizTaskDTO.getInspectorItems()));
                bizTaskDTO.setInspectorItemsDTO(inspectorItemsDTO);
            }

            /* 标识 颜色 */
            Integer hasFeedback = bizTaskDTO.getHasFeedback();
            if (ObjectUtil.isNotEmpty(hasFeedback) && hasFeedback.intValue() == 1) { // 有反馈
                bizTaskDTO.setHasFeedback(1);
                bizTaskDTO.setWarningColorCode("3");
            } else {
                bizTaskDTO.setHasFeedback(0);
                LocalDate dueTime = null;
                switch (dueTimeType) {
                    case (TaskStatus.TASK_DECOMPOSE_DUE_TIME):
                        dueTime = bizTaskDTO.getDueTime();
                        break;
                    case (TaskStatus.TASK_SUPERVISION_DUE_TIME):
                        dueTime = bizTaskDTO.getSupDueTime();
                        break;
                    default:
                        dueTime = bizTaskDTO.getDueTime();
                        break;
                }
                if (dueTime == null) {
                    log.error("分解任务结束时间为空, 任务id为{}", bizTaskDTO.getTaskDecomposeId());
                } else {
                    LocalDateTime now = LocalDateTimeUtil.now();
                    LocalDateTime dueTimeStart = dueTime.atStartOfDay();
                    Duration duration = Duration.between(now, dueTimeStart);
                    long days = duration.toDays(); // 相差的天数
                    if (days >= 0 && days <= 1) {
                        bizTaskDTO.setWarningColorCode("2");
                    }
                    if (days < 0) {
                        bizTaskDTO.setWarningColorCode("1");
                    }
                }

            }
        });
        return bizTasks;
    }


    /**
     * 承办任务台账(查询主表记录数量)
     * @param dto
     * @param basePageForm
     * @param taskDecomposeDueTime
     * @return
     */
    @Override
    public PageResult<BizTaskDTO> listSupReport(BizTaskDTO dto, BasePageForm basePageForm, int taskDecomposeDueTime) {
        PageResult<BizTaskDTO> bizTasks = querySupList(dto, basePageForm);
        List<BizTaskDTO> data = bizTasks.getData();
        data.forEach(bizTaskDTO -> {
            // 重要性
            if (org.apache.commons.lang3.StringUtils.isNotBlank(bizTaskDTO.getInspectorTaskLevel())) {
                BizTaskLevelDTO inspectorTaskLevelDTO = bizTaskLevelService.findOne(Long.parseLong(bizTaskDTO.getInspectorTaskLevel()));
                bizTaskDTO.setInspectorTaskLevelDTO(inspectorTaskLevelDTO);
            }

            // 交办事项
            if (org.apache.commons.lang3.StringUtils.isNotBlank(bizTaskDTO.getInspectorItems())) {
                BizTaskLevelDTO inspectorItemsDTO = bizTaskLevelService.findOne(Long.parseLong(bizTaskDTO.getInspectorItems()));
                bizTaskDTO.setInspectorItemsDTO(inspectorItemsDTO);
            }

            /* 标识 颜色 */
            Integer hasFeedback = bizTaskDTO.getHasFeedback();
            if (ObjectUtil.isNotEmpty(hasFeedback) && hasFeedback.intValue() == 1) { // 有反馈
                bizTaskDTO.setHasFeedback(1);
                bizTaskDTO.setWarningColorCode("3");
            } else {
                bizTaskDTO.setHasFeedback(0);
                LocalDate dueTime = null;
                switch (taskDecomposeDueTime) {
                    case (TaskStatus.TASK_DECOMPOSE_DUE_TIME):
                        dueTime = bizTaskDTO.getDueTime();
                        break;
                    case (TaskStatus.TASK_SUPERVISION_DUE_TIME):
                        dueTime = bizTaskDTO.getSupDueTime();
                        break;
                    default:
                        dueTime = bizTaskDTO.getDueTime();
                        break;
                }
                if (dueTime == null) {
                    log.error("分解任务结束时间为空, 任务id为{}", bizTaskDTO.getTaskDecomposeId());
                } else {
                    LocalDateTime now = LocalDateTimeUtil.now();
                    LocalDateTime dueTimeStart = dueTime.atStartOfDay();
                    Duration duration = Duration.between(now, dueTimeStart);
                    long days = duration.toDays(); // 相差的天数
                    if (days >= 0 && days <= 1) {
                        bizTaskDTO.setWarningColorCode("2");
                    }
                    if (days < 0) {
                        bizTaskDTO.setWarningColorCode("1");
                    }
                }

            }
        });
        return bizTasks;
    }




    @Override
    public int updateHasFinish(BizTaskDecomposeScoreDTO bizTaskDecomposeScoreDTO) {
        Long id = bizTaskDecomposeScoreDTO.getId();
        Integer hasFinish = bizTaskDecomposeScoreDTO.getHasFinish();
        LocalDateTime now = LocalDateTimeUtil.now();
        BizTaskDecompose bizTaskDecompose = BeanConvertUtils.copyProperties(bizTaskDecomposeScoreDTO, BizTaskDecompose.class);
        bizTaskDecompose.setHasFinishTime(now);
        bizTaskDecompose.setNewFinishSign(1); /* 新办结标识 */
        bizTaskDecompose.setTaskScore(bizTaskDecomposeScoreDTO.getActualScore());
        bizTaskDecomposeMapper.updateById(bizTaskDecompose);
        int i = 0;
        // 办结
        if (ObjectUtil.isNotNull(hasFinish)) {
            // * 1 更新反馈表 不能反馈
            LambdaQueryWrapper<BizTaskFeedback> eqFeed =
                    new LambdaQueryWrapper<BizTaskFeedback>().eq(BizTaskFeedback::getBizTaskDecompose, id);
            List<BizTaskFeedback> list = bizTaskFeedbackService.selectListNoAdd(eqFeed);
            CscpUserDetail user = SecurityUtils.getCurrentUser().get();
            if (!list.isEmpty()) {
                List<BizTaskFeedback> collect = list.stream().map(item -> {
                    item.setHasReviewer(1);
                    item.setReviewerId(user.getId());
                    item.setReviewerCompanyId(user.getCompanyId());
                    item.setReviewerTime(now);
                    item.setReviewerDepartmentId(user.getDepartmentId());
                    return item;
                }).collect(Collectors.toList());
                bizTaskFeedbackService.saveOrUpdateBatch(collect);
            }
            // * 2 更新主表 分解表是否还有 未办结的任务
            BizTaskDecompose bizTaskDecomposeNew = this.getById(id);
            /* 主任务id */
            Long supid = bizTaskDecomposeNew.getSubTblFk();
            /* 未办结 */
            LambdaQueryWrapper<BizTaskDecompose> eq = new LambdaQueryWrapper<BizTaskDecompose>().eq(BizTaskDecompose::getSubTblFk, supid)
                    .eq(BizTaskDecompose::getHasFinish, 0);
            List<BizTaskDecompose> bizTaskDecomposes = bizTaskDecomposeMapper.selectListNoAdd(eq);
            // 分解任务 没有最新反馈,更新 主任务 反馈标记
            if (bizTaskDecomposes.isEmpty()) {
                BizTaskSupervision bizTaskSupervision = bizTaskSupervisionService.getById(supid);
                bizTaskSupervision.setHasFinish(1);
                bizTaskSupervision.setHasFinishTime(now);
                bizTaskSupervisionService.updateById(bizTaskSupervision);
                i = 1;
            }

            //  往分值表里面插入分值记录
            BizTaskScoreDTO bizTaskScoreDTO = new BizTaskScoreDTO();
            bizTaskScoreDTO.setActualScore(bizTaskDecomposeScoreDTO.getActualScore());
            bizTaskScoreDTO.setAdjustableReason(bizTaskDecomposeScoreDTO.getAdjustableReason());
            bizTaskScoreDTO.setShouldScore(bizTaskDecomposeScoreDTO.getShouldScore());
            bizTaskScoreDTO.setBizTaskDecompose(bizTaskDecomposeScoreDTO.getId());
            bizTaskScoreDTO.setBizTaskSupervisionId(supid);
            bizTaskScoreDTO.setScoreFormulas(bizTaskSupervisionService.getEquationById(supid));
            bizTaskScoreService.create(bizTaskScoreDTO);
        }


        return i;
    }


    @Override
    public List<BizTaskDTO> queryTaskTransferNumber(long currentUserId, Long taskDecomposeId) {
        BasePageForm basePageForm = new BasePageForm(1, 100);
        BizTaskDTO bizTaskDTO = new BizTaskDTO();
        //bizTaskDTO.setDutyPeopleId(currentUserId);
        bizTaskDTO.setHasSign(99);
        bizTaskDTO.setHasFinish(0);
        bizTaskDTO.setHasTransfer(0);
        if (taskDecomposeId != null) {
            bizTaskDTO.setTaskDecomposeId(taskDecomposeId);
        }
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectCommonTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizTaskDTO);

        return page.getRecords();
    }

    @Override
    public BizTaskScoreDetailDTO queryScoreDetailList(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        BizTaskScoreDetailDTO bizTaskScoreDetailDTO = new BizTaskScoreDetailDTO();
        BizTaskScoreSubDTO bizTaskScoreSubDTO = new BizTaskScoreSubDTO();
        List<Long> decomposeIdList = new ArrayList<>();
        decomposeIdList.add(bizTaskSupervisionAndScoreAddDTO.getBizTaskDecomposeId());
        bizTaskScoreSubDTO.setBizTaskDecompose(decomposeIdList);
        bizTaskScoreSubDTO.setPreview(1);
        PageResult<BizTaskScoreAdd> bizTaskScoreAddList = bizTaskScoreAddService.queryScoreDetailList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        PageResult<BizTaskScoreSubDTO> bizTaskScoreSubList = bizTaskScoreSubService.queryListPage(bizTaskScoreSubDTO, basePageForm);
        bizTaskScoreDetailDTO.setBizTaskScoreAddList(bizTaskScoreAddList.getData());
        bizTaskScoreDetailDTO.setBizTaskScoreSubList(bizTaskScoreSubList.getData());
        return bizTaskScoreDetailDTO;
    }

    /**
     * 根据主表主键ID查询分解表任务(无隔离)
     *
     * @param supervisionId
     * @return
     */
    @Override
    public List<BizTaskDecomposeDTO> queryDecomposeBySupervisionId(Long supervisionId) {
        if (Objects.isNull(supervisionId)) {
            throw new BusinessException("主表主键ID不允许为空");
        }

        LambdaQueryWrapper<BizTaskDecompose> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskDecompose::getSubTblFk, supervisionId);
        List<BizTaskDecompose> bizTaskDecomposeList = bizTaskDecomposeMapper.selectListNoAdd(queryWrapper);
        List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = ListCopyUtil.copy(bizTaskDecomposeList, BizTaskDecomposeDTO.class);
        return bizTaskDecomposeDTOList;
    }

    /**
     * 查询没有签收的
     *
     * @param bizTaskDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskDTO> selectNoSignCommonTaskList(NoHasSignTaskDTO bizTaskDTO, BasePageForm basePageForm) {
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectNoSignCommonTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizTaskDTO);
        PageResult<BizTaskDTO> bizTaskDTOPageResults = new PageResult<>(page.getRecords(), page.getTotal(),
                page.getCurrent());
        // 增加字段 任务来源 1 本单位 2 外单位
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        List<BizTaskDTO> records = page.getRecords();
        for (BizTaskDTO record : records) {

            // 办结
            if (record.getHasFinish() == 1) {
                LambdaQueryWrapper<BizTaskScore> lambFdaQueryWrapper = new LambdaQueryWrapper<>();
                lambFdaQueryWrapper.in(BizTaskScore::getBizTaskDecompose, record.getTaskDecomposeId());
                BizTaskScore taskScoreList = bizTaskScoreService.selectOneNoAdd(lambFdaQueryWrapper);
                record.setActualScore(taskScoreList.getActualScore());
            } else {
                if (StringUtils.isNotEmpty(record.getDecomposeTaskSource())) {
                    record.setActualScore(Double.valueOf(record.getDecomposeTaskSource()));
                } else {
                    record.setActualScore(0.0d);
                }
            }

            Long supCompanyId = record.getSupCompanyId();
            if (currentCompanyId.longValue() == supCompanyId.longValue()) {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OWMER_DEPT);
            } else {
                record.setTaskSourceDept(TaskStatus.TASK_SOURCE_OTHER_DEPT);
            }
        }
        return bizTaskDTOPageResults;
    }

    @Override
    public int selectTaskDecomposeCount(Long subTblFk, Long dutyPeopleId) {
//        LambdaQueryWrapper<BizTaskDecompose> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(BizTaskDecompose::getSubTblFk, subTblFk);
//        queryWrapper.eq(BizTaskDecompose::getHasFinish, 0);
//        queryWrapper.ne(BizTaskDecompose::getDutyPeopleId, dutyPeopleId);

//        queryWrapper.and((wrapper)->{
//            wrapper.ne(BizTaskDecompose::getDutyPeopleId,dutyPeopleId)
//            .or().ne(BizTaskDecompose::getContactPeopleId,dutyPeopleId);
//        });

//        List<BizTaskDecompose> bizTaskDecomposeList = bizTaskDecomposeMapper.selectListNoAdd(queryWrapper);
        return bizTaskDecomposeMapper.selectTaskDecomposeCount(dutyPeopleId,subTblFk);
    }

    /**
     * 根据主任务id获取所有子任务,并且标记哪个子任务是自己的
     *
     * @param id
     * @return
     */
    @Override
    public List<BizTaskDetailsDecomposeDTO> getTaskDecomposeAll(Long id) {
        if (Objects.isNull(id)) {
            throw new BusinessException("任务id不能为空!");
        }

        //根据主任务id查询对应的子任务
        List<BizTaskDecompose> bizTaskDecomposes = bizTaskDecomposeMapper.selectListNoAdd(
                new LambdaQueryWrapper<BizTaskDecompose>().eq(BizTaskDecompose::getSubTblFk, id));
        List<BizTaskDetailsDecomposeDTO> bizTaskDetailsDecomposes = ListCopyUtil.copy(bizTaskDecomposes, BizTaskDetailsDecomposeDTO.class);
        if (bizTaskDetailsDecomposes.isEmpty()) {
            return bizTaskDetailsDecomposes;
        }

        // 设置责任人单位ID
        bizTaskDetailsDecomposes.forEach(i -> {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(i.getDutyPeople())) {
                List<DutyPeopleDTO> dutyPeopleDTOList = JSON.parseArray(i.getDutyPeople(), DutyPeopleDTO.class);
                if (CollectionUtil.isNotEmpty(dutyPeopleDTOList)) {
                    i.setDutyPeopleCompanyId(Long.parseLong(dutyPeopleDTOList.get(0).getCompanyId()));
                }
            }
        });

        //通过map标记属于当前用户的子任务
        Map<Long, BizTaskDetailsDecomposeDTO> bizTaskDecomposeMap = bizTaskDetailsDecomposes.stream().collect(
                Collectors.toMap(BizTaskDetailsDecomposeDTO::getDutyPeopleId, BizTaskDetailsDecomposeDTO -> BizTaskDetailsDecomposeDTO, (v1, v2) -> v2));
        //标记自己的任务
        if (bizTaskDecomposeMap.containsKey(SecurityUtils.getCurrentUserId())) {
            bizTaskDecomposeMap.get(SecurityUtils.getCurrentUserId()).setOneselfTask(1);
        }

        return bizTaskDecomposeMap.values().stream().collect(Collectors.toList());
    }

    /**
     * 导出用(慎用，可能导致oom)
     *
     * @param dto
     * @param basePageForm
     * @param dueTimeType
     * @return
     */
    @Override
    public PageResult<BizTaskDTO> listReportToExcel(BizTaskDTO dto, BasePageForm basePageForm, int dueTimeType) {
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectCommonTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), dto);
        PageResult<BizTaskDTO> bizTaskDTOPageResults = new PageResult<>(page.getRecords(), page.getTotal(),
                page.getCurrent());
        List<BizTaskDTO> data = bizTaskDTOPageResults.getData();
        //获取重要性id集合
        List<Long> inspectorTaskLevels = data.stream().map(i -> {
            return Long.parseLong(i.getInspectorTaskLevel());
        }).distinct().collect(Collectors.toList());

        //获取交办事项id集合
        List<Long> inspectorItems = data.stream().map(i -> {
            return Long.parseLong(i.getInspectorItems());
        }).distinct().collect(Collectors.toList());

        inspectorTaskLevels.addAll(inspectorItems);

        LambdaQueryWrapper<BizTaskLevel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(BizTaskLevel::getId, inspectorTaskLevels);
        List<BizTaskLevel> bizTaskLevels = bizTaskLevelService.selectListNoAdd(lambdaQueryWrapper);
        List<BizTaskLevelDTO> bizTaskLevelDTOS = ListCopyUtil.copy(bizTaskLevels, BizTaskLevelDTO.class);
        Map<Long, BizTaskLevelDTO> bizTaskLevelMap = bizTaskLevelDTOS.stream().collect(Collectors.toMap(BizTaskLevelDTO::getId, Function.identity(), (key1, key2) -> key2));

        data.forEach(bizTaskDTO -> {
            // 重要性
            if (org.apache.commons.lang3.StringUtils.isNotBlank(bizTaskDTO.getInspectorTaskLevel())) {
                BizTaskLevelDTO inspectorTaskLevelDTO = bizTaskLevelMap.get(Long.parseLong(bizTaskDTO.getInspectorTaskLevel()));
                bizTaskDTO.setInspectorTaskLevelDTO(inspectorTaskLevelDTO);
            }

            // 交办事项
            if (org.apache.commons.lang3.StringUtils.isNotBlank(bizTaskDTO.getInspectorItems())) {
                BizTaskLevelDTO inspectorItemsDTO = bizTaskLevelMap.get(Long.parseLong(bizTaskDTO.getInspectorItems()));
                bizTaskDTO.setInspectorItemsDTO(inspectorItemsDTO);
            }

        });
        return bizTaskDTOPageResults;
    }

    /**
     * 获取未转办时的单位id
     * @param taskTransferId
     * @return
     */
    public Long getTopCompanyId(Long taskTransferId){
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(taskTransferId);
        if (Objects.nonNull(bizTaskSupervision.getTaskTransferId())){
            getTopCompanyId(bizTaskSupervision.getTaskTransferId());
        }
        return bizTaskSupervision.getCompanyId();
    }
}
