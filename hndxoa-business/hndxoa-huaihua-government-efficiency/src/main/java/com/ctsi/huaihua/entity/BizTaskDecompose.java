package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 任务分解表
 * 分解任务
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_decompose")
@ApiModel(value = "BizTaskDecompose对象", description = "任务分解表")
public class BizTaskDecompose extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 责任人的id
     */
    @ApiModelProperty(value = "责任人的id")
    private Long dutyPeopleId;

    /**
     * 责任人的姓名
     */
    @ApiModelProperty(value = "责任人的姓名")
    private String dutyPeopleName;

    /**
     * 责任人的部门名称
     */
    @ApiModelProperty(value = "责任人的部门名称")
    private String dutyPeopleDepartmentName;

    /**
     * 责任人的单位名称
     */
    @ApiModelProperty(value = "责任人的单位名称")
    private String dutyPeopleCompanyName;

    /**
     * 责任人的职务
     */
    @ApiModelProperty(value = "责任人的职务")
    private String dutyPeoplePostName;

    /**
     * 责任人的部门id
     */
    @ApiModelProperty(value = "责任人的部门id")
    private Long dutyPeopleDepartmentId;

    /**
     * 责任人的单位id
     */
    @ApiModelProperty(value = "责任人的单位id")
    private Long dutyPeopleCompanyId;

    /**
     * 联络员的id
     */
    @ApiModelProperty(value = "联络员的id")
    private Long contactPeopleId;

    /**
     * 联络员的姓名
     */
    @ApiModelProperty(value = "联络员的姓名")
    private String contactPeopleName;

    /**
     * 联络员的部门名称
     */
    @ApiModelProperty(value = "联络员的部门名称")
    private String contactPeopleDepartmentName;

    /**
     * 联络员的单位名称
     */
    @ApiModelProperty(value = "联络员的单位名称")
    private String contactPeopleCompanyName;

    /**
     * 联络员的职务
     */
    @ApiModelProperty(value = "联络员的职务")
    private String contactPeoplePostName;

    /**
     * 联络员的部门id
     */
    @ApiModelProperty(value = "联络员的部门id")
    private Long contactPeopleDepartmentId;

    /**
     * 联络员的单位id
     */
    @ApiModelProperty(value = "联络员的单位id")
    private Long contactPeopleCompanyId;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String content;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    private LocalDate startDate;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间")
    @NotBlank(message = "任务结束时间不能为空")
    private LocalDate endDate;

    /**
     * 接收时间，yyyy-MM-dd HH:mm
     */
    @ApiModelProperty(value = "接收时间，yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /**
     * 难度系数
     */
    @ApiModelProperty(value = "难度系数")
    private String degreeDifficulty;

    /**
     * 任务概叙
     */
    @ApiModelProperty(value = "任务概叙")
    private String taskDescription;

    /**
     * 短信提醒方式，对应数据字典sms_reminder
     * 1 -> 无,2->每周（提前一天提醒）,3->每月（提前1天提醒）
     */
    @ApiModelProperty(value = "短信提醒方式，对应数据字典sms_reminder")
    private String smsReminder;

    /**
     * 0：未办结 1 办结
     */
    @ApiModelProperty(value = "0：未办结 1 办结")
    private Integer hasFinish;
    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private LocalDateTime hasFinishTime;


    /**
     * 0：未签收 1 已签收  2 撤回
     */
    @ApiModelProperty(value = "0：未签收 1 已签收 2 撤回")
    private Integer hasSign;

    /**
     * 0：未转办 1 已转办
     */
    @ApiModelProperty(value = "0：未转办 1 已转办")
    private Integer hasTransfer;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键sub_tbl_fk")
    private Long subTblFk;


    /**
     * 子模型的排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer subTblNum;

    @ApiModelProperty(value = "签收人的id")
    private Long signPeopleId;

    @ApiModelProperty(value = "签收人的姓名")
    private String signPeopleName;

    @ApiModelProperty(value = "签收人的电话")
    private String signPeoplePhone;

    @ApiModelProperty(value = "签收人单位名称")
    private String signPeopleCompanyName;
    /**
     * 子表单的表单id
     */
    @ApiModelProperty(value = "子表单的表单id")
    private String subDomainId;

    @ApiModelProperty(value = "供表单使用的责任人")
    private String dutyPeople;

    @ApiModelProperty(value = "群名称")
    private String groupName;

    /**
     * 是否有反馈记录 0：没有 , 1 有
     */
    @ApiModelProperty(value = " 是否有反馈记录 0：没有, 1 有")
    private Integer hasFeedback;

    /**
     * 对于最新反馈的成果标记 0：没有 , 1 有
     * 对于最新反馈的成果，页面列表以小红点标识最新数据，用户点击成果审核后恢复默认样式。
     */
    @ApiModelProperty(value = " 对于最新反馈的成果，页面列表以小红点标识最新数据,用户点击成果审核后恢复默认样式  0：没有标识, 1 最新反馈成果,有小红点标识")
    private Integer newFeedbackSign;


    /**
     * 小红点标识最新数据  0：没有 , 1 有
     * 对于最新办结的任务，页面列表以小红点标识最新数据，用户浏览后 后恢复默认样式。
     */
    @ApiModelProperty(value = " 最新办结的任务,小红点标识最新数据,用户点击任务详情或办结详情 后恢复默认样式， 0：没有标识, 1 最新办结,有小红点标识")
    private Integer newFinishSign;


    /**
     * 最新反馈时间
     */
    @ApiModelProperty(value = "最新反馈时间")
    private LocalDateTime newFeedbackTime;

    @ApiModelProperty(value = "延期申请状态 0 无申请, 1 有申请(暂未通过), 2 申请通过, 3 通过并手动调整延期时间")
    private Integer taskDelayStatus;


    @ApiModelProperty(value = "(已发)短信提醒日期")
    private LocalDateTime smsSendTime;

    @ApiModelProperty(value = "责任人电话")
    private String dutyPeoplePhone;

    @ApiModelProperty(value = "联络员的电话")
    private String contactPeoplePhone;


    @ApiModelProperty(value = "是否主办单位，1表示主办单位，0表示协办单位")
    private String hostCompany;

    @ApiModelProperty(value = "是否预览(0:未预览 1:预览)")
    private Integer preview;

    @ApiModelProperty(value = "附件id")
    private Long enclosureFileId;


    @ApiModelProperty(value = "任务分值")
    private Double taskScore;

    @ApiModelProperty(value = "办结人id")
    private String concludePeopleId;
}
