package com.ctsi.huaihua.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.huaihua.entity.BizTaskScoreAdd;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.entity.dto.BizTaskScoreAddDTO;
import com.ctsi.huaihua.entity.dto.BizTaskScoreAddDetailDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionAndScoreAddDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 任务加分表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface IBizTaskScoreAddService extends SysBaseServiceI<BizTaskScoreAdd> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskScoreAddDTO> queryListPage(BizTaskScoreAddDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskScoreAddDTO> queryList(BizTaskScoreAddDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskScoreAddDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskScoreAddDTO create(BizTaskScoreAddDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskScoreAddDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskScoreAddId
     * @param code
     * @return
     */
    boolean existByBizTaskScoreAddId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskScoreAddDTO> dataList);

    /**
     * 任务加分审核
     * @param bizTaskScoreAddDTO
     * @return
     */
    Boolean approvedBizTaskScoreAdd(BizTaskScoreAddDTO bizTaskScoreAddDTO);

    /**
     * 分页查询加分申请列表
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskSupervisionAndScoreAddDTO> queryPageScoreAddList(BizTaskSupervisionAndScoreAddDTO entityDTO, BasePageForm page);


    /**
     * 查询加分详情
     * @param bizTaskScoreAddDTO
     * @return
     */
    BizTaskScoreAddDetailDTO queryScoreDetail(BizTaskScoreAddDTO bizTaskScoreAddDTO);

    /**
     * 需要延期审核数量(无单位隔离)
     * @param bizTaskScoreAddDTO
     * @return
     */
    Integer queryScoreAddNeedReviewAmount(BizTaskScoreAddDTO bizTaskScoreAddDTO);

    /**
     * 查询加分申请详情(会修改加分申请为已阅状态)
     * @param bizTaskScoreAddDTO
     * @return
     */
    BizTaskScoreAddDetailDTO queryAndReadScoreAddDetail(BizTaskScoreAddDTO bizTaskScoreAddDTO);

    /**
     * 查询加分申请列表(会修改加分申请为已阅状态)
     * @param bizTaskSupervisionAndScoreAddDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskSupervisionAndScoreAddDTO> queryAndReadPageScoreAddList(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm);


    /**
     * 提醒延期申请通过数量(无单位隔离)
     * @param bizTaskScoreAddDTO
     * @return
     */
    Integer queryScoreAddHasReviewedAmount(BizTaskScoreAddDTO bizTaskScoreAddDTO);

    /**
     * 提醒加分申请通过情况(按分解任务计算)
     * @return
     */
    Integer queryScoreAddHasReviewedAmountBySubTask();

    /**
     * 查询分解表对应的加分申请列表
     * @param bizTaskSupervisionAndScoreAddDTO
     * @return
     */
    PageResult<BizTaskScoreAdd> queryScoreDetailList(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm);

    /**
     * 查询分解表对应的加分申请列表(申请人)
     * @param bizTaskSupervisionAndScoreAddDTO
     * @return
     */
    PageResult<BizTaskScoreAdd> queryAndReadScoreDetailListCreate(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm);

    /**
     * 查询分解表对应的加分申请列表(审核人)
     * @param bizTaskSupervisionAndScoreAddDTO
     * @return
     */
    PageResult<BizTaskScoreAdd> queryAndReadScoreDetailListAudit(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm);

    /**
     * 查询分解表对应的加分申请列表(会修改加分申请为已阅状态)
     *
     * @param dto
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskDTO> getAllTheAddTasks(BizTaskDTO dto, BasePageForm basePageForm);

    /**
     * 修改加分申请为已阅状态(不按单位隔离)
     * @param bizTaskScoreAdds
     * @return
     */
    Boolean readBizTaskScoreAdd(List<BizTaskScoreAdd> bizTaskScoreAdds);

    /**
     * 根据主表主键ID查询主表任务、分解表任务和加分申请、任务减分
     * @param supervisionDTO
     * @return
     */
    BizTaskSupervisionDTO querySupervisionAndScoreBySupervisionId(BizTaskSupervisionDTO supervisionDTO);

    /**
     * 根据分解表ID查询加分申请
     * @param decomposeId
     * @param reviewedStatusList
     * @return
     */
    List<BizTaskScoreAddDTO> queryScoreAddByDecomposeId(Long decomposeId, List<Integer> reviewedStatusList);
}
