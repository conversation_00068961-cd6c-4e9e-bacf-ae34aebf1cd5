package com.ctsi.huaihua.controller;
import com.ctsi.huaihua.entity.dto.BizHomeTaskDTO;
import com.ctsi.huaihua.entity.dto.BizHomeTaskPackagingDTO;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.huaihua.entity.BizTaskFeedback;
import com.ctsi.huaihua.entity.dto.BizTaskFeedbackDTO;
import com.ctsi.huaihua.service.IBizTaskFeedbackService;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskFeedback")
@Api(value = "任务成果反馈表", tags = "任务成果反馈表接口")
public class BizTaskFeedbackController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskFeedback";

    @Autowired
    private IBizTaskFeedbackService bizTaskFeedbackService;
    @Autowired
    private IBizTaskDecomposeService bizTaskDecomposeService;
    @Autowired
    private IBizTaskDecomposeService bizTaskDecomposeMapper;


    /**
     *  新增任务成果反馈表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskFeedback.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务成果反馈表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskFeedback.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskFeedbackDTO> bizTaskFeedbackList) {
       Boolean  result = bizTaskFeedbackService.insertBatch(bizTaskFeedbackList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskFeedback.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务成果反馈表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskFeedback.add')")
    public ResultVO<BizTaskFeedbackDTO> create(@RequestBody BizTaskFeedbackDTO bizTaskFeedbackDTO)  {
        BizTaskFeedbackDTO result = bizTaskFeedbackService.create(bizTaskFeedbackDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskFeedback.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新任务成果反馈表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskFeedback.update')")
    public ResultVO update(@RequestBody BizTaskFeedbackDTO bizTaskFeedbackDTO) {
	    Assert.notNull(bizTaskFeedbackDTO.getId(), "general.IdNotNull");
        int count = bizTaskFeedbackService.update(bizTaskFeedbackDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除任务成果反馈表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskFeedback.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskFeedback.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskFeedbackService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskFeedbackDTO bizTaskFeedbackDTO = bizTaskFeedbackService.findOne(id);
        return ResultVO.success(bizTaskFeedbackDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskFeedbackPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskFeedbackDTO>> queryBizTaskFeedbackPage(BizTaskFeedbackDTO bizTaskFeedbackDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskFeedbackService.queryListPage(bizTaskFeedbackDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskFeedback")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskFeedbackDTO>> queryBizTaskFeedback(BizTaskFeedbackDTO bizTaskFeedbackDTO) {
       List<BizTaskFeedbackDTO> list = bizTaskFeedbackService.queryList(bizTaskFeedbackDTO);
       return ResultVO.success(new ResResult<BizTaskFeedbackDTO>(list));
   }



    /**
     * 查询待审核记录
     * 查询 待反馈
     * 查询已办结数据 自动清除角标
     */
    @GetMapping("/queryBizTaskDecompose")
    @ApiOperation(value = "反馈管理 任务查询通用接口 ", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDTO>> queryBizTaskDecompose(BizTaskDTO bizTaskDTO, BasePageForm basePageForm) {
        bizTaskDTO.setDutyPeopleId(SecurityUtils.getCurrentUserId());
        PageResult<BizTaskDTO> list = bizTaskFeedbackService.queryBizTaskDecompose(bizTaskDTO,basePageForm);
        return ResultVO.success(list);
    }


    /**
     *  分页查询发布和我的承办多条数据.
     */

    @GetMapping("/queryBizHomeTaskDTOPage")
    @ApiOperation(value = "翻页查询发布和我的承办多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizHomeTaskPackagingDTO>> queryBizHomeTaskDTOPage(BizHomeTaskDTO bizHomeTaskDTO, BasePageForm basePageForm) {
        bizHomeTaskDTO.setDutyPeopleId(SecurityUtils.getCurrentUserId());
        return ResultVO.success(bizTaskFeedbackService.queryBizHomeTaskDecompose(bizHomeTaskDTO, basePageForm));
    }


}
