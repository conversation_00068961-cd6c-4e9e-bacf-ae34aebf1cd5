package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskExportDTO;
import com.ctsi.huaihua.entity.dto.BizTaskImportDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname BizTaskImportAndExportService
 * @Description
 * @Date 2022/3/15/0015 14:56
 */
public interface BizTaskImportAndExportService {

    /**
     * 处理任务导入数据
     * @param dtoList
     * @return
     */
    List<BizTaskExportDTO> assemblyTaskImport(List<BizTaskImportDTO> dtoList);

    /**
     * 成果下载
     * @param id
     * @param response
     */
    void downloadFeedBackZip(Long id, HttpServletResponse response);
}
