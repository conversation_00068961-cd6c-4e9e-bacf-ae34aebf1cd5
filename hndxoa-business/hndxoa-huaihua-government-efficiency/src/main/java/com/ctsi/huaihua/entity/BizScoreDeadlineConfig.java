package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 日常工作打分截止时间设置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("biz_score_deadline_config")
@ApiModel(value="BizScoreDeadlineConfig对象", description="日常工作打分截止时间设置")
public class BizScoreDeadlineConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日常工作打分截止时间(日)
     */
    @ApiModelProperty(value = "日常工作打分截止时间(日)")
    private Integer day;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.UPDATE, select = false)
    private Long updateBy;

    @TableField(fill = FieldFill.UPDATE)
    private String updateName;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private String createName;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    //部门id
    @TableField(fill = FieldFill.INSERT)
    private Long departmentId;

    //单位id
    @TableField(fill = FieldFill.INSERT)
    private Long companyId;


    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    @TableLogic
    @TableField(select = false, fill = FieldFill.INSERT)
    private Integer deleted;


}
