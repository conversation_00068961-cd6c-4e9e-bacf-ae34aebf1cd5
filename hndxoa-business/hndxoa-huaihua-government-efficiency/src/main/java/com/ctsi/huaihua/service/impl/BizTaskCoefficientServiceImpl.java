package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SysTenantUtils;
import com.ctsi.huaihua.entity.BizTaskCoefficient;
import com.ctsi.huaihua.entity.BizTaskLevel;
import com.ctsi.huaihua.entity.dto.BizTaskCoefficientDTO;
import com.ctsi.huaihua.entity.dto.BizTaskEquationAndScoreDTO;
import com.ctsi.huaihua.mapper.BizTaskCoefficientMapper;
import com.ctsi.huaihua.mapper.BizTaskLevelMapper;
import com.ctsi.huaihua.service.IBizTaskCoefficientService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.system.entity.TSysDictRecord;
import com.ctsi.system.mapper.TSysDictRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *  任务难度系数服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Slf4j
@Service
public class BizTaskCoefficientServiceImpl extends SysBaseServiceImpl<BizTaskCoefficientMapper, BizTaskCoefficient> implements IBizTaskCoefficientService {

    @Autowired
    private BizTaskCoefficientMapper bizTaskCoefficientMapper;

    @Autowired
    private BizTaskLevelMapper bizTaskLevelMapper;

    @Autowired
    private TSysDictRecordMapper tSysDictRecordMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskCoefficientDTO> queryListPage(BizTaskCoefficientDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskCoefficient> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskCoefficient> pageData = bizTaskCoefficientMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //本单位没有找租户
        if (Objects.isNull(pageData)){
            Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
            queryWrapper.eq(BizTaskCoefficient::getTenantId, tenantId);
            queryWrapper.isNull(BizTaskCoefficient::getCompanyId);
             pageData = bizTaskCoefficientMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
             //上级租户没有找顶级租户
             if (Objects.isNull(pageData)){
                 Long topFloorTenantId = SysTenantUtils.getTopFloorTenamtId(tenantId);
                 LambdaQueryWrapper<BizTaskCoefficient> queryWrapper1 = Wrappers.lambdaQuery();
                 queryWrapper1.eq(BizTaskCoefficient::getTenantId, topFloorTenantId);
                 queryWrapper1.isNull(BizTaskCoefficient::getCompanyId);
                 pageData = bizTaskCoefficientMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
             }
        }
        //返回
        IPage<BizTaskCoefficientDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskCoefficientDTO.class));
        data.getRecords().forEach(i ->{
            if (i.getType().equals(0)){
                LambdaQueryWrapper<BizTaskLevel> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(BizTaskLevel::getId,i.getCoefficientId());
                BizTaskLevel bizTaskLevel = bizTaskLevelMapper.selectOneNoAdd(lambdaQueryWrapper);
                if (Objects.nonNull(bizTaskLevel)) {
                    i.setAttributeName(bizTaskLevel.getTypeName());
                }
            }else {
                LambdaQueryWrapper<TSysDictRecord> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(TSysDictRecord::getDictId,i.getCoefficientId());
                lambdaQueryWrapper.eq(TSysDictRecord::getCode,i.getDictCode());
                TSysDictRecord tSysDictRecord = tSysDictRecordMapper.selectOne(lambdaQueryWrapper);
                if (Objects.nonNull(tSysDictRecord)) {
                    i.setAttributeName(tSysDictRecord.getName());
                }
            }
        });

        List<BizTaskCoefficientDTO> result = data.getRecords().stream()
                .sorted(Comparator.comparing(BizTaskCoefficientDTO::getWeight,Comparator.reverseOrder()).thenComparing(BizTaskCoefficientDTO::getFactor,Comparator.reverseOrder()))
                .collect(Collectors.toList());

        return new PageResult<BizTaskCoefficientDTO>(result,
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskCoefficientDTO> queryList(BizTaskCoefficientDTO entityDTO) {
        LambdaQueryWrapper<BizTaskCoefficient> queryWrapper = new LambdaQueryWrapper();
            List<BizTaskCoefficient> listData = bizTaskCoefficientMapper.selectList(queryWrapper);
            List<BizTaskCoefficientDTO> BizTaskCoefficientDTOList = ListCopyUtil.copy(listData, BizTaskCoefficientDTO.class);
        return BizTaskCoefficientDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskCoefficientDTO findOne(Long id) {
        BizTaskCoefficient bizTaskCoefficient =  bizTaskCoefficientMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizTaskCoefficient, BizTaskCoefficientDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskCoefficientDTO create(BizTaskCoefficientDTO entityDTO) {
        BizTaskCoefficient bizTaskCoefficient =  BeanConvertUtils.copyProperties(entityDTO, BizTaskCoefficient.class);
        //名称校验
        LambdaQueryWrapper<BizTaskCoefficient> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizTaskCoefficient::getCoefficientId,entityDTO.getCoefficientId());
        if (!entityDTO.getType().equals(0)){
            lambdaQueryWrapper.eq(BizTaskCoefficient::getDictCode,entityDTO.getDictCode());
        }
        BizTaskCoefficient checkName = bizTaskCoefficientMapper.selectOne(lambdaQueryWrapper);
        if (Objects.nonNull(checkName)){
            throw new BusinessException("该属性已设置");
        }
        //权重校验
        LambdaQueryWrapper<BizTaskCoefficient> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizTaskCoefficient::getType,entityDTO.getType());
        List<BizTaskCoefficient> bizTaskCoefficients = bizTaskCoefficientMapper.selectList(queryWrapper);
        if (bizTaskCoefficients.size() > 0){
            if (!bizTaskCoefficients.get(0).getWeight().equals(entityDTO.getWeight())){
                throw new BusinessException("同一属性权重必须相同");
            }
        }else {
            LambdaQueryWrapper<BizTaskCoefficient> queryWrapper1 = Wrappers.lambdaQuery();
            List<BizTaskCoefficient> weightList = bizTaskCoefficientMapper.selectList(queryWrapper1);
            Double sum = weightList.stream().mapToDouble(BizTaskCoefficient::getWeight).distinct().sum();
            sum = add(sum,entityDTO.getWeight());
            Double dSum = 1.0;
            if (sum.compareTo(dSum) > 0) {
                throw new BusinessException("权重和不能超过1");
            }
        }



        save(bizTaskCoefficient);
        return  BeanConvertUtils.copyProperties(bizTaskCoefficient, BizTaskCoefficientDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskCoefficientDTO entity) {

        LambdaQueryWrapper<BizTaskCoefficient> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ne(BizTaskCoefficient::getType,entity.getType());
        List<BizTaskCoefficient> bizTaskCoefficients = bizTaskCoefficientMapper.selectList(queryWrapper);
        Double sum = bizTaskCoefficients.stream().mapToDouble(BizTaskCoefficient::getWeight).distinct().sum();
        sum = add(sum,entity.getWeight());
        Double dSum = 1.0;
        if (sum.compareTo(dSum) > 0) {
            throw new BusinessException("权重和不能超过1");
        }

        LambdaQueryWrapper<BizTaskCoefficient> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizTaskCoefficient::getType,entity.getType());
        lambdaQueryWrapper.ne(BizTaskCoefficient::getId,entity.getId());
        List<BizTaskCoefficient> bizTaskCoefficientList = bizTaskCoefficientMapper.selectList(lambdaQueryWrapper);
        if (bizTaskCoefficientList.size() > 0) {
            bizTaskCoefficientList.forEach(i -> {
                i.setWeight(entity.getWeight());
                bizTaskCoefficientMapper.updateById(i);
            });
        }
        BizTaskCoefficient bizTaskCoefficient = BeanConvertUtils.copyProperties(entity, BizTaskCoefficient.class);
        return bizTaskCoefficientMapper.updateById(bizTaskCoefficient);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskCoefficientMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskCoefficientId
     * @return
     */
    @Override
    public boolean existByBizTaskCoefficientId(Long BizTaskCoefficientId) {
        if (BizTaskCoefficientId != null) {
            LambdaQueryWrapper<BizTaskCoefficient> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskCoefficient::getId, BizTaskCoefficientId);
            List<BizTaskCoefficient> result = bizTaskCoefficientMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskCoefficientDTO> dataList) {
        List<BizTaskCoefficient> result = ListCopyUtil.copy(dataList, BizTaskCoefficient.class);
        return saveBatch(result);
    }

    /**
     * 计算分数
     * @param list
     * @return
     */
    @Override
    public BizTaskEquationAndScoreDTO getEquationAndScore(List<BizTaskCoefficientDTO> list){
        Double sum = 0.0;
        List<String> equations = new ArrayList<>();
        String equation = "";
        BizTaskEquationAndScoreDTO bizTaskEquationAndScoreDTO = new BizTaskEquationAndScoreDTO();
        for (BizTaskCoefficientDTO bizTaskCoefficientDTO : list){
            LambdaQueryWrapper<BizTaskCoefficient> queryWrapper = Wrappers.lambdaQuery();
            //找本单位的
            queryWrapper.eq(BizTaskCoefficient::getCoefficientId, bizTaskCoefficientDTO.getCoefficientId());
            if (Objects.nonNull(bizTaskCoefficientDTO.getDictCode()) && !bizTaskCoefficientDTO.getDictCode().equals("0")) {
                queryWrapper.eq(BizTaskCoefficient::getDictCode, bizTaskCoefficientDTO.getDictCode());
            }
            BizTaskCoefficient bizTaskCoefficient = bizTaskCoefficientMapper.selectOne(queryWrapper);
            //本单位没有找上级租户
           if (Objects.isNull(bizTaskCoefficient)){
                Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
                queryWrapper.isNull(BizTaskCoefficient::getCompanyId);
                bizTaskCoefficient = bizTaskCoefficientMapper.selectOneOnlyAddTenantId(queryWrapper);
                //上级租户没有找顶级租户
                if (Objects.isNull(bizTaskCoefficient)){
                    Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(tenantId);
                    LambdaQueryWrapper<BizTaskCoefficient> lambdaQueryWrapper = Wrappers.lambdaQuery();
                    lambdaQueryWrapper.eq(BizTaskCoefficient::getCoefficientId, bizTaskCoefficientDTO.getCoefficientId());
                    if (Objects.nonNull(bizTaskCoefficientDTO.getDictCode()) && !bizTaskCoefficientDTO.getDictCode().equals("0")) {
                        lambdaQueryWrapper.eq(BizTaskCoefficient::getDictCode, bizTaskCoefficientDTO.getDictCode());
                    }
                    lambdaQueryWrapper.eq(BizTaskCoefficient::getTenantId,topFloorTenamtId);
                    lambdaQueryWrapper.isNull(BizTaskCoefficient::getCompanyId);
                    bizTaskCoefficient = bizTaskCoefficientMapper.selectOneNoAdd(queryWrapper);
                }
            }
            if (bizTaskCoefficient != null){
                sum = add(sum, mul(bizTaskCoefficient.getWeight(), bizTaskCoefficient.getFactor()));
                equations.add(contains(bizTaskCoefficient.getFactor(), bizTaskCoefficient.getWeight()));
                equations.add("+");
            }

        }
        if (equations.size() >= 1){
            equations.remove(equations.size()-1);
        }
        equations.add("=");
        equations.add(sum.toString());

        for (String equ :equations){
            equation = equation.concat(equ);
        }

        bizTaskEquationAndScoreDTO.setEquation(equation);
        bizTaskEquationAndScoreDTO.setScore(sum);
        return bizTaskEquationAndScoreDTO;
    }

    @Override
    public Double getScore(List<BizTaskCoefficientDTO> list){
        return this.getEquationAndScore(list).getScore();
    }

    @Override
    public String getEquation(List<BizTaskCoefficientDTO> list){
        return this.getEquationAndScore(list).getEquation();
    }


    public static Double add(Double v1, Double v2) {
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.add(b2).doubleValue();
    }

    public static Double mul(Double v1, Integer v2) {
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.multiply(b2).doubleValue();
    }

    public static String contains(Integer v1, Double v2) {
        String b1 = v1.toString();
        String b2 = v2.toString();

        return b1.concat("*").concat(b2);
    }

}
