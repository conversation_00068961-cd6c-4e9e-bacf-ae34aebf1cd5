package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.BizTaskScoreAdd;
import com.ctsi.huaihua.entity.BizTaskScoreSub;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.huaihua.mapper.BizTaskDecomposeMapper;
import com.ctsi.huaihua.mapper.BizTaskScoreAddMapper;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.huaihua.service.IBizTaskScoreAddService;
import com.ctsi.huaihua.service.IBizTaskScoreSubService;
import com.ctsi.huaihua.service.IBizTaskSupervisionService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务加分表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Slf4j
@Service
public class BizTaskScoreAddServiceImpl extends SysBaseServiceImpl<BizTaskScoreAddMapper, BizTaskScoreAdd> implements IBizTaskScoreAddService {

    @Autowired
    private BizTaskScoreAddMapper bizTaskScoreAddMapper;

    @Autowired
    private IBizTaskDecomposeService iBizTaskDecomposeService;

    @Autowired
    private BizTaskDecomposeMapper bizTaskDecomposeMapper;

    @Autowired
    private IBizTaskSupervisionService bizTaskSupervisionService;

    @Autowired
    private IBizTaskScoreSubService bizTaskScoreSubService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskScoreAddDTO> queryListPage(BizTaskScoreAddDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskScoreAdd> pageData = bizTaskScoreAddMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskScoreAddDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizTaskScoreAddDTO.class));

        return new PageResult<BizTaskScoreAddDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskScoreAddDTO> queryList(BizTaskScoreAddDTO entityDTO) {
        LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();
            List<BizTaskScoreAdd> listData = bizTaskScoreAddMapper.selectList(queryWrapper);
            List<BizTaskScoreAddDTO> BizTaskScoreAddDTOList = ListCopyUtil.copy(listData, BizTaskScoreAddDTO.class);
        return BizTaskScoreAddDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskScoreAddDTO findOne(Long id) {
        BizTaskScoreAdd  bizTaskScoreAdd =  bizTaskScoreAddMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizTaskScoreAdd,BizTaskScoreAddDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskScoreAddDTO create(BizTaskScoreAddDTO entityDTO) {
        // 查询分解表数据
        BizTaskDecompose bizTaskDecomposeDTO = iBizTaskDecomposeService.getById(entityDTO.getBizTaskDecompose());
        if (Objects.isNull(bizTaskDecomposeDTO)) {
            throw new BusinessException("未找到任务分解表数据");
        }

        // 新增加分申请必须要在签收之后，撤回之前
        if (!SysConstant.BIZ_IS_SIGN.equals(bizTaskDecomposeDTO.getHasSign())) {
            throw new BusinessException("加分申请必须要在任务签收之后，撤回之前申请");
        }

        // 查询该分解表之前有没有加分申请
//        LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(BizTaskScoreAdd::getBizTaskDecompose, entityDTO.getBizTaskDecompose());
//        List<BizTaskScoreAdd> listData = bizTaskScoreAddMapper.selectList(queryWrapper);
//        // 如果该分解表之前有加分申请，需要判断之前的申请有没有被审核
//        if (CollectionUtils.isNotEmpty(listData)) {
//            for (BizTaskScoreAdd bizTaskScoreAdd : listData) {
//                if (SysConstant.BIZ_NOT_FINISHED.equals(bizTaskScoreAdd.getHasReviewer())) {
//                    throw new BusinessException("该任务的分解表已存在未被审核的加分申请");
//                }
//            }
//        }

        // 新增加分申请记录
        entityDTO.setReviewerCompanyId(bizTaskDecomposeDTO.getCompanyId());
        entityDTO.setReviewerDepartmentId(bizTaskDecomposeDTO.getDepartmentId());
        entityDTO.setReviewerId(bizTaskDecomposeDTO.getCreateBy());
        entityDTO.setHasReviewer(0);
        entityDTO.setAuditHasRead(0);

       BizTaskScoreAdd bizTaskScoreAdd = BeanConvertUtils.copyProperties(entityDTO,BizTaskScoreAdd.class);
        save(bizTaskScoreAdd);
        return  BeanConvertUtils.copyProperties(bizTaskScoreAdd,BizTaskScoreAddDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskScoreAddDTO entity) {
        BizTaskScoreAdd bizTaskScoreAdd = BeanConvertUtils.copyProperties(entity,BizTaskScoreAdd.class);
        return bizTaskScoreAddMapper.updateById(bizTaskScoreAdd);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskScoreAddMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskScoreAddId
     * @return
     */
    @Override
    public boolean existByBizTaskScoreAddId(Long BizTaskScoreAddId) {
        if (BizTaskScoreAddId != null) {
            LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskScoreAdd::getId, BizTaskScoreAddId);
            List<BizTaskScoreAdd> result = bizTaskScoreAddMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskScoreAddDTO> dataList) {
        List<BizTaskScoreAdd> result = ListCopyUtil.copy(dataList, BizTaskScoreAdd.class);
        return saveBatch(result);
    }


    /**
     * 任务加分审核
     * @param bizTaskScoreAddDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean approvedBizTaskScoreAdd(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        // 查询任务加分申请
        BizTaskScoreAddDTO taskScoreAddDTO = this.findOne(bizTaskScoreAddDTO.getId());
        if (Objects.isNull(taskScoreAddDTO)) {
            throw new BusinessException("未找到该条任务的加分申请");
        }
        if (1 == taskScoreAddDTO.getHasReviewer() || 2 == taskScoreAddDTO.getHasReviewer()) {
            throw new BusinessException("该条加分申请记录已审核通过或已驳回");
        }

        // 查询分解表数据
//        BizTaskDecomposeDTO bizTaskDecomposeDTO = iBizTaskDecomposeService.findOne(
//                taskScoreAddDTO.getBizTaskDecompose());
//        if (Objects.isNull(bizTaskDecomposeDTO)) {
//            throw new BusinessException("未找到任务分解表数据");
//        }

        // 修改加分申请审核状态和审核内容
        taskScoreAddDTO.setHasReviewer(bizTaskScoreAddDTO.getHasReviewer());
        taskScoreAddDTO.setRefuseReason(bizTaskScoreAddDTO.getRefuseReason());
        taskScoreAddDTO.setAuditId(SecurityUtils.getCurrentUserId());
        taskScoreAddDTO.setAuditName(SecurityUtils.getCurrentRealName());
        taskScoreAddDTO.setAuditTime(LocalDateTime.now());
        taskScoreAddDTO.setHasRead(0);
        this.update(taskScoreAddDTO);
        return true;
    }


    /**
     * 查询加分详情
     * @param bizTaskScoreAddDTO
     * @return
     */
    @Override
    public BizTaskScoreAddDetailDTO queryScoreDetail(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        // 加分详情对象
        BizTaskScoreAddDetailDTO bizTaskScoreAddDetailDTO = new BizTaskScoreAddDetailDTO();
        // 加分申请
        BizTaskScoreAddDTO taskScoreAddDTO = this.findOne(bizTaskScoreAddDTO.getId());
        bizTaskScoreAddDetailDTO.setBizTaskScoreAddDTO(taskScoreAddDTO);
        if (Objects.nonNull(taskScoreAddDTO.getBizTaskDecompose())) {
            // 分解表数据
            BizTaskDecomposeDTO bizTaskDecomposeDTO = iBizTaskDecomposeService.findOne(taskScoreAddDTO.getBizTaskDecompose());
            bizTaskScoreAddDetailDTO.setBizTaskDecomposeDTO(bizTaskDecomposeDTO);
        }
        return bizTaskScoreAddDetailDTO;
    }

    /**
     * 查询加分申请详情(会修改加分申请为已阅状态)
     * @param bizTaskScoreAddDTO
     * @return
     */
    @Override
    public BizTaskScoreAddDetailDTO queryAndReadScoreAddDetail(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        BizTaskScoreAddDetailDTO bizTaskScoreAddDetailDTO = this.queryScoreDetail(bizTaskScoreAddDTO);

        // 修改为加分申请为已阅状态
        LambdaUpdateWrapper<BizTaskScoreAdd> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(BizTaskScoreAdd::getId, bizTaskScoreAddDTO.getId()).set(BizTaskScoreAdd::getHasRead, 1);
        bizTaskScoreAddMapper.update(null, lambdaUpdateWrapper);

        return bizTaskScoreAddDetailDTO;
    }


    /**
     * 查询加分申请列表(会修改加分申请为已阅状态)
     * @param bizTaskSupervisionAndScoreAddDTO
     * @param basePageForm
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageResult<BizTaskSupervisionAndScoreAddDTO> queryAndReadPageScoreAddList(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        PageResult<BizTaskSupervisionAndScoreAddDTO> taskSupervisionAndScoreAddDTOPageResult = this.queryPageScoreAddList(bizTaskSupervisionAndScoreAddDTO, basePageForm);

        // 修改加分申请为已阅状态
        List<Long> scoreAddIdList = taskSupervisionAndScoreAddDTOPageResult.getData().stream().map(i ->
                i.getTaskScoreAddId()).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(scoreAddIdList)) {
            return taskSupervisionAndScoreAddDTOPageResult;
        }
        LambdaUpdateWrapper<BizTaskScoreAdd> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.in(BizTaskScoreAdd::getId, scoreAddIdList).set(BizTaskScoreAdd::getHasRead, 1);
        bizTaskScoreAddMapper.update(null, lambdaUpdateWrapper);

        return taskSupervisionAndScoreAddDTOPageResult;
    }

    /**
     * 分页查询加分申请列表
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizTaskSupervisionAndScoreAddDTO> queryPageScoreAddList(BizTaskSupervisionAndScoreAddDTO entityDTO, BasePageForm page) {
        // 查询该用户需要审核的任务加分数据
        LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();
        if (Objects.nonNull(entityDTO.getScoreAddReviewerId())) {
            queryWrapper.eq(BizTaskScoreAdd::getReviewerId, entityDTO.getScoreAddReviewerId());
        }
        if (Objects.nonNull(entityDTO.getScoreAddCreateId())) {
            queryWrapper.eq(BizTaskScoreAdd::getCreateBy, entityDTO.getScoreAddCreateId());
        }
        if (Objects.nonNull(entityDTO.getHasReviewer())) {
            queryWrapper.eq(BizTaskScoreAdd::getHasReviewer, entityDTO.getHasReviewer());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getHasReviewerList())) {
            queryWrapper.in(BizTaskScoreAdd::getHasReviewer, entityDTO.getHasReviewerList());
        }
        List<BizTaskScoreAdd> listData = bizTaskScoreAddMapper.selectListNoAdd(queryWrapper);
        List<BizTaskScoreAddDTO> bizTaskScoreAddDTOList = ListCopyUtil.copy(listData, BizTaskScoreAddDTO.class);
        if (CollectionUtils.isEmpty(bizTaskScoreAddDTOList)) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }

        // 获取加分申请主键ID集合
        List<Long> taskScoreAddIdList = bizTaskScoreAddDTOList.stream().map(i -> i.getId()).collect(Collectors.toList());
        entityDTO.setTaskScoreAddIdList(taskScoreAddIdList);

        // 主表和加分表联合查询
        IPage<BizTaskSupervisionAndScoreAddDTO> bizTaskSupervisionDTOIPage = bizTaskScoreAddMapper.queryPageTaskSupervisionAndScoreAddInfo(
                PageHelperUtil.getMPlusPageByBasePage(page), entityDTO);

        // 组转数据
        List<BizTaskSupervisionAndScoreAddDTO> collect = bizTaskSupervisionDTOIPage.getRecords().stream().map(i -> {
            // 分解表数据
            BizTaskDecomposeDTO bizTaskDecomposeDTO = iBizTaskDecomposeService.findOne(i.getBizTaskDecomposeId());
            if (Objects.nonNull(bizTaskDecomposeDTO)) {
                i.setDutyPeopleName(bizTaskDecomposeDTO.getDutyPeopleName());
                i.setDutyPeopleCompanyName(bizTaskDecomposeDTO.getDutyPeopleCompanyName());
                i.setEndDate(bizTaskDecomposeDTO.getEndDate());
                i.setBizTaskDecomposeId(bizTaskDecomposeDTO.getId());
                i.setHasFinish(bizTaskDecomposeDTO.getHasFinish());
            }
            return i;
        }).collect(Collectors.toList());

        PageResult<BizTaskSupervisionAndScoreAddDTO> bizTaskSupervisionDTOPageResult = new PageResult<>(collect,
                bizTaskSupervisionDTOIPage.getTotal(), bizTaskSupervisionDTOIPage.getCurrent());
        return bizTaskSupervisionDTOPageResult;
    }

    /**
     * 需要延期审核数量(无单位隔离)
     * @param bizTaskScoreAddDTO
     * @return
     */
    @Override
    public Integer queryScoreAddNeedReviewAmount(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();
        if (Objects.nonNull(bizTaskScoreAddDTO.getReviewerId())) {
            queryWrapper.eq(BizTaskScoreAdd::getReviewerId, bizTaskScoreAddDTO.getReviewerId());
        }
        if (Objects.nonNull(bizTaskScoreAddDTO.getCreateBy())) {
            queryWrapper.eq(BizTaskScoreAdd::getCreateBy, bizTaskScoreAddDTO.getCreateBy());
        }
        if (Objects.nonNull(bizTaskScoreAddDTO.getHasReviewer())) {
            queryWrapper.eq(BizTaskScoreAdd::getHasReviewer, bizTaskScoreAddDTO.getHasReviewer());
        }
        if (CollectionUtils.isNotEmpty(bizTaskScoreAddDTO.getHasReviewerList())) {
            queryWrapper.in(BizTaskScoreAdd::getHasReviewer, bizTaskScoreAddDTO.getHasReviewerList());
        }
        if (Objects.nonNull(bizTaskScoreAddDTO.getHasRead())) {
            queryWrapper.eq(BizTaskScoreAdd::getHasRead, bizTaskScoreAddDTO.getHasRead());
        }
        return bizTaskScoreAddMapper.selectCountNoAdd(queryWrapper);
    }

    /**
     * 提醒延期申请通过数量(无单位隔离)
     * @param bizTaskScoreAddDTO
     * @return
     */
    @Override
    public Integer queryScoreAddHasReviewedAmount(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        Integer count = this.queryScoreAddNeedReviewAmount(bizTaskScoreAddDTO);
        return count;
    }

    /**
     * 提醒加分申请通过情况(按分解任务计算)
     * @return
     */
    @Override
    public Integer queryScoreAddHasReviewedAmountBySubTask() {
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        // 查询未查看已审核的加分申请
        LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();
        if (Objects.isNull(SecurityUtils.getCurrentUserId())) {
            throw new BusinessException("未找到该用户ID");
        }
        queryWrapper.eq(BizTaskScoreAdd::getCreateBy, SecurityUtils.getCurrentUserId());
        queryWrapper.in(BizTaskScoreAdd::getHasReviewer, list);
        queryWrapper.eq(BizTaskScoreAdd::getHasRead, 0);
        List<BizTaskScoreAdd> bizTaskScoreAdds = bizTaskScoreAddMapper.selectListNoAdd(queryWrapper);

        Integer count = 0;
        if (CollectionUtils.isEmpty(bizTaskScoreAdds)) {
            return count;
        }
//        count = bizTaskScoreAdds.stream().collect(Collectors.groupingBy(BizTaskScoreAdd::getBizTaskDecompose)).size();
        count = bizTaskScoreAdds.stream().map(i -> i.getBizTaskDecompose()).distinct().collect(Collectors.toList()).size();
        return count;
    }

    /**
     * 查询分解表对应的加分申请列表
     * @param bizTaskSupervisionAndScoreAddDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskScoreAdd> queryScoreDetailList(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO,BasePageForm basePageForm){
        LambdaQueryWrapper<BizTaskScoreAdd> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BizTaskScoreAdd::getBizTaskDecompose,bizTaskSupervisionAndScoreAddDTO.getBizTaskDecomposeId());
        if (CollectionUtils.isNotEmpty(bizTaskSupervisionAndScoreAddDTO.getHasReviewerList())) {
            lambdaQueryWrapper.in(BizTaskScoreAdd::getHasReviewer, bizTaskSupervisionAndScoreAddDTO.getHasReviewerList());
        }
        lambdaQueryWrapper.orderByAsc(BizTaskScoreAdd::getCreateTime);
        IPage<BizTaskScoreAdd> bizTaskScoreAdd = bizTaskScoreAddMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm),lambdaQueryWrapper);
        PageResult<BizTaskScoreAdd> bizTaskSupervisionAndScoreAddDTOPageResult = new PageResult<>(bizTaskScoreAdd.getRecords(),
                bizTaskScoreAdd.getTotal(), bizTaskScoreAdd.getCurrent());
        return bizTaskSupervisionAndScoreAddDTOPageResult;
    }

    /**
     * 查询分解表对应的加分申请列表(会修改加分申请为已阅状态)(申请人)
     * @param bizTaskSupervisionAndScoreAddDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskScoreAdd> queryAndReadScoreDetailListCreate(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO,BasePageForm basePageForm){
        PageResult<BizTaskScoreAdd> taskScoreAddPageResult = this.queryScoreDetailList(bizTaskSupervisionAndScoreAddDTO, basePageForm);

        // 修改加分申请为已阅状态
        List<Long> scoreAddIdList = taskScoreAddPageResult.getData().stream().map(i ->
                i.getId()).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(scoreAddIdList)) {
            return taskScoreAddPageResult;
        }
        LambdaUpdateWrapper<BizTaskScoreAdd> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.in(BizTaskScoreAdd::getId, scoreAddIdList).set(BizTaskScoreAdd::getHasRead, 1);
        bizTaskScoreAddMapper.update(null, lambdaUpdateWrapper);

        return taskScoreAddPageResult;
    }

    /**
     * 查询分解表对应的加分申请列表(会修改加分申请为已阅状态)(审核人)
     * @param bizTaskSupervisionAndScoreAddDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskScoreAdd> queryAndReadScoreDetailListAudit(BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO,BasePageForm basePageForm){
        PageResult<BizTaskScoreAdd> taskScoreAddPageResult = this.queryScoreDetailList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        // 修改加分申请为已阅状态
        if (taskScoreAddPageResult.getData().isEmpty()) {
            return new PageResult<>(null, 0, 0);
        }
        List<BizTaskScoreAdd> bizTaskScoreAdd = taskScoreAddPageResult.getData();
        this.readBizTaskScoreAdd(bizTaskScoreAdd);

        return taskScoreAddPageResult;
    }

    /**
     * 修改加分申请为已阅状态(不按单位隔离)
     * @param bizTaskScoreAdds
     * @return
     */
    @Override
    public Boolean readBizTaskScoreAdd(List<BizTaskScoreAdd> bizTaskScoreAdds){

        bizTaskScoreAdds.forEach(i -> {
            BizTaskScoreAdd bizTaskScoreAdd = new BizTaskScoreAdd();
            BeanUtils.copyProperties(i, bizTaskScoreAdd);
            bizTaskScoreAdd.setAuditHasRead(1);
            bizTaskScoreAddMapper.updateById(bizTaskScoreAdd);
        });
        return true;
    }

    /**
     * 查询所有带加分申请子任务
     *
     * @param dto
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskDTO> getAllTheAddTasks(BizTaskDTO dto, BasePageForm basePageForm) {
        LambdaQueryWrapper<BizTaskScoreAdd> scoreAddLambdaQueryWrapper = Wrappers.lambdaQuery();
        // 申请人只显示用户自己提交的申请
        if (Objects.nonNull(dto.getDutyPeopleId())) {
            scoreAddLambdaQueryWrapper.eq(BizTaskScoreAdd::getCreateBy, dto.getDutyPeopleId());
        }
        // 审核人只显示自己要审核的申请
        if(Objects.nonNull(dto.getAuditId())){
            scoreAddLambdaQueryWrapper.eq(BizTaskScoreAdd::getAuditId, dto.getAuditId());
        }
        //过滤审核状态
        if (CollectionUtils.isNotEmpty(dto.getHasScoreAddReviewerList())) {
            scoreAddLambdaQueryWrapper.in(BizTaskScoreAdd::getHasReviewer, dto.getHasScoreAddReviewerList());
        }
        List<BizTaskScoreAdd> bizTaskScoreAddList = bizTaskScoreAddMapper.selectListNoAdd(scoreAddLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(bizTaskScoreAddList)) {
            return new PageResult<>(null, 0, 0);
        }
        //按分解表id进行分组
        Map<Long, List<BizTaskScoreAdd>> collect = bizTaskScoreAddList.stream().collect(Collectors.groupingBy(BizTaskScoreAdd::getBizTaskDecompose));

        //生成带有加分申请的分解表id集合
        List<Long> bizTaskDecomposeIdList = bizTaskScoreAddList.stream().map(i -> i.getBizTaskDecompose())
                .distinct().collect(Collectors.toList());
        dto.setBizTaskDecomposeIdList(bizTaskDecomposeIdList);
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectCommonTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), dto);

        if (page.getRecords().isEmpty()) {
            return new PageResult<>(null, 0, 0);
        }

        //遍历分解表id集合，设置阅读状态，设置加分和，设置加分申请数量
        page.getRecords().forEach(i -> {
            //设置加分申请数量
            List<BizTaskScoreAdd> bizTaskScoreAdds = collect.get(i.getTaskDecomposeId());
            i.setScoreAddCount(bizTaskScoreAdds.size());

            //设置分解表下加分申请分数和
            double doublesum = bizTaskScoreAdds.stream().mapToDouble(j -> {
                return j.getScore();
            }).sum();
            i.setScore(doublesum);

            // 设置申请方阅读状态
            if (Objects.nonNull(dto.getDutyPeopleId())) {
                List<Integer> hasRead = bizTaskScoreAdds.stream().map(j -> j.getHasRead()).collect(Collectors.toList());
                if (hasRead.contains(0)) {
                    i.setHasRead(0);
                } else {
                    i.setHasRead(1);
                }
            }

            // 设置审核方阅读状态
            if(Objects.nonNull(dto.getAuditId())) {
                List<Integer> auditHasReadList = bizTaskScoreAdds.stream().map(j -> j.getAuditHasRead()).collect(Collectors.toList());
                if (auditHasReadList.contains(0)) {
                    i.setAuditHasRead(0);
                } else {
                    i.setAuditHasRead(1);
                }
            }

            //设置分解表下最后一条加分申请创建时间
            List<LocalDateTime> endCreateTimeList = bizTaskScoreAdds.stream().map(j -> j.getCreateTime()).collect(Collectors.toList());
            i.setEndCreateTime(Collections.max(endCreateTimeList));

        });

        PageResult<BizTaskDTO> bizTaskPageResults = new PageResult<>(page.getRecords(), page.getTotal(), page.getCurrent());
        return bizTaskPageResults;
    }


    /**
     * 根据主表主键ID查询主表任务、分解表任务和加分申请、任务减分
     * @param supervisionDTO
     * @return
     */
    @Override
    public BizTaskSupervisionDTO querySupervisionAndScoreBySupervisionId(BizTaskSupervisionDTO supervisionDTO) {
        if (Objects.isNull(supervisionDTO.getId())) {
            throw new BusinessException("主表主键ID不允许为空");
        }
        // 查询主表数据
        BizTaskSupervisionDTO bizTaskSupervisionDTO = bizTaskSupervisionService.findOne(supervisionDTO.getId());
        if (Objects.isNull(bizTaskSupervisionDTO)) {
            return new BizTaskSupervisionDTO();
        }

        // 查询主表对应的分解表数据
        List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = iBizTaskDecomposeService.queryDecomposeBySupervisionId(supervisionDTO.getId());
        if (CollectionUtils.isEmpty(bizTaskDecomposeDTOList)) {
            return bizTaskSupervisionDTO;
        }
        bizTaskSupervisionDTO.setBizTaskDecomposeDTOList(bizTaskDecomposeDTOList);

        bizTaskDecomposeDTOList.forEach(i -> {
            // 查询分解表对应的加分申请数据
            List<BizTaskScoreAddDTO> taskScoreAddDTOList = this.queryScoreAddByDecomposeId(i.getId(), supervisionDTO.getScoreReviewedStatusList());
            i.setBizTaskScoreAddDTOList(taskScoreAddDTOList);

            // 查询分解表对应的减分
            List<BizTaskScoreSubDTO> bizTaskScoreSubDTOList = bizTaskScoreSubService.queryScoreSubByDecomposeId(i.getId());
            i.setBizTaskScoreSubDTOList(bizTaskScoreSubDTOList);

        });
        return bizTaskSupervisionDTO;
    }

    /**
     * 根据分解表ID查询加分申请
     * @param decomposeId
     * @param reviewedStatusList
     * @return
     */
    @Override
    public List<BizTaskScoreAddDTO> queryScoreAddByDecomposeId(Long decomposeId, List<Integer> reviewedStatusList) {
        if (Objects.isNull(decomposeId)) {
            throw new BusinessException("分解表主键ID不允许为空");
        }
        LambdaQueryWrapper<BizTaskScoreAdd> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskScoreAdd::getBizTaskDecompose, decomposeId);
        if (CollectionUtils.isNotEmpty(reviewedStatusList)) {
            queryWrapper.in(BizTaskScoreAdd::getHasReviewer, reviewedStatusList);
        }
        List<BizTaskScoreAdd> bizTaskScoreAddList = bizTaskScoreAddMapper.selectListNoAdd(queryWrapper);
        List<BizTaskScoreAddDTO> BizTaskScoreAddDTOList = ListCopyUtil.copy(bizTaskScoreAddList, BizTaskScoreAddDTO.class);
        return BizTaskScoreAddDTOList;
    }
}
