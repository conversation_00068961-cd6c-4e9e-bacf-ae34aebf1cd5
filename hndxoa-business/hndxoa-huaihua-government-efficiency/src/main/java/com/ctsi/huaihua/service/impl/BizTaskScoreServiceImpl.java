package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizTaskScore;
import com.ctsi.huaihua.entity.dto.BizTaskScoreDTO;
import com.ctsi.huaihua.mapper.BizTaskScoreMapper;
import com.ctsi.huaihua.service.IBizTaskScoreService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 任务得分 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Slf4j
@Service
public class BizTaskScoreServiceImpl extends SysBaseServiceImpl<BizTaskScoreMapper, BizTaskScore> implements IBizTaskScoreService {

    @Autowired
    private BizTaskScoreMapper bizTaskScoreMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskScoreDTO> queryListPage(BizTaskScoreDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskScore> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskScore> pageData = bizTaskScoreMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskScoreDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizTaskScoreDTO.class));

        return new PageResult<BizTaskScoreDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskScoreDTO> queryList(BizTaskScoreDTO entityDTO) {
        LambdaQueryWrapper<BizTaskScore> queryWrapper = new LambdaQueryWrapper();
            List<BizTaskScore> listData = bizTaskScoreMapper.selectList(queryWrapper);
            List<BizTaskScoreDTO> BizTaskScoreDTOList = ListCopyUtil.copy(listData, BizTaskScoreDTO.class);
        return BizTaskScoreDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskScoreDTO findOne(Long id) {
        BizTaskScore  bizTaskScore =  bizTaskScoreMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizTaskScore,BizTaskScoreDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskScoreDTO create(BizTaskScoreDTO entityDTO) {
        BizTaskScore bizTaskScore =  BeanConvertUtils.copyProperties(entityDTO,BizTaskScore.class);
        //同一条分解任务只能有一条分值记录
        LambdaQueryWrapper<BizTaskScore> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizTaskScore::getBizTaskDecompose,bizTaskScore.getBizTaskDecompose());
        BizTaskScore check = this.selectOneNoAdd(queryWrapper);
        if (Objects.nonNull(check)){
            throw new BusinessException("同一条分解任务只能有一条分值记录");
        }
        save(bizTaskScore);
        return  BeanConvertUtils.copyProperties(bizTaskScore,BizTaskScoreDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskScoreDTO entity) {
        BizTaskScore bizTaskScore = BeanConvertUtils.copyProperties(entity,BizTaskScore.class);
        return bizTaskScoreMapper.updateById(bizTaskScore);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskScoreMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskScoreId
     * @return
     */
    @Override
    public boolean existByBizTaskScoreId(Long BizTaskScoreId) {
        if (BizTaskScoreId != null) {
            LambdaQueryWrapper<BizTaskScore> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskScore::getId, BizTaskScoreId);
            List<BizTaskScore> result = bizTaskScoreMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskScoreDTO> dataList) {
        List<BizTaskScore> result = ListCopyUtil.copy(dataList, BizTaskScore.class);
        return saveBatch(result);
    }

    @Override
    public BizTaskScoreDTO getTaskScoreByDecomposeId(Long bizTaskDecompose) {
        LambdaQueryWrapper<BizTaskScore> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskScore::getBizTaskDecompose, bizTaskDecompose);
        BizTaskScore bizTaskScore = bizTaskScoreMapper.selectOneNoAdd(queryWrapper);
        return  BeanConvertUtils.copyProperties(bizTaskScore,BizTaskScoreDTO.class);
    }


}
