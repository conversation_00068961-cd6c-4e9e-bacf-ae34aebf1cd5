package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizRewardScoreRecordDTO;
import com.ctsi.huaihua.service.IBizRewardScoreRecordService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizRewardScoreRecord")
@Api(value = "奖分记录表", tags = "奖分记录表接口")
public class BizRewardScoreRecordController extends BaseController {

    private static final String ENTITY_NAME = "bizRewardScoreRecord";

    @Autowired
    private IBizRewardScoreRecordService bizRewardScoreRecordService;



    /**
     *  新增奖分记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizRewardScoreRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增奖分记录表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizRewardScoreRecord.add')")
    public ResultVO createBatch(@RequestBody List<BizRewardScoreRecordDTO> bizRewardScoreRecordList) {
       Boolean  result = bizRewardScoreRecordService.insertBatch(bizRewardScoreRecordList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizRewardScoreRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增奖分记录表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.bizRewardScoreRecord.add')")
    public ResultVO<BizRewardScoreRecordDTO> create(@RequestBody BizRewardScoreRecordDTO bizRewardScoreRecordDTO)  {
        BizRewardScoreRecordDTO result = bizRewardScoreRecordService.create(bizRewardScoreRecordDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizRewardScoreRecord.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新奖分记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizRewardScoreRecord.update')")
    public ResultVO update(@RequestBody BizRewardScoreRecordDTO bizRewardScoreRecordDTO) {
	    Assert.notNull(bizRewardScoreRecordDTO.getId(), "general.IdNotNull");
        int count = bizRewardScoreRecordService.update(bizRewardScoreRecordDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除奖分记录表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizRewardScoreRecord.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizRewardScoreRecord.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizRewardScoreRecordService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizRewardScoreRecordDTO bizRewardScoreRecordDTO = bizRewardScoreRecordService.findOne(id);
        return ResultVO.success(bizRewardScoreRecordDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizRewardScoreRecordPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizRewardScoreRecordDTO>> queryBizRewardScoreRecordPage(BizRewardScoreRecordDTO bizRewardScoreRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizRewardScoreRecordService.queryListPage(bizRewardScoreRecordDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizRewardScoreRecord")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizRewardScoreRecordDTO>> queryBizRewardScoreRecord(BizRewardScoreRecordDTO bizRewardScoreRecordDTO) {
       List<BizRewardScoreRecordDTO> list = bizRewardScoreRecordService.queryList(bizRewardScoreRecordDTO);
       return ResultVO.success(new ResResult<BizRewardScoreRecordDTO>(list));
   }

    @GetMapping("/pageQueryBizRewardScoreRecordMunicipal")
    @ApiOperation(value = "市直单位办公室专职人员分页查询加分记录", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizRewardScoreRecordDTO>> pageQueryBizRewardScoreRecordMunicipal(BizRewardScoreRecordDTO bizRewardScoreRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizRewardScoreRecordService.pageQueryBizRewardScoreRecordMunicipal(bizRewardScoreRecordDTO, basePageForm));
    }

    @PostMapping("/pageQueryBizRewardScoreRecordSupervision")
    @ApiOperation(value = "市政府办督查室分页查询加分记录", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizRewardScoreRecordDTO>> pageQueryBizRewardScoreRecordSupervision(@RequestBody BizRewardScoreRecordDTO bizRewardScoreRecordDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizRewardScoreRecordDTO.getCurrentPage());
        basePageForm.setPageSize(bizRewardScoreRecordDTO.getPageSize());
        return ResultVO.success(bizRewardScoreRecordService.pageQueryBizRewardScoreRecordSupervision(bizRewardScoreRecordDTO, basePageForm));
    }

    @GetMapping("/pageQueryBizRewardScoreRecordIndividual")
    @ApiOperation(value = "市直单位个人分页查询加分记录", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizRewardScoreRecordDTO>> pageQueryBizRewardScoreRecordIndividual(BizRewardScoreRecordDTO bizRewardScoreRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizRewardScoreRecordService.pageQueryBizRewardScoreRecordIndividual(bizRewardScoreRecordDTO, basePageForm));
    }

    @PostMapping("/approvedBizRewardScoreRecord")
    @ApiOperation(value = "加分审核与驳回", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "延期申请审核通过")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Boolean> approvedBizRewardScoreRecord(@RequestBody BizRewardScoreRecordDTO bizRewardScoreRecordDTO) {
        Boolean bool = bizRewardScoreRecordService.approvedBizRewardScoreRecord(bizRewardScoreRecordDTO);
        return ResultVO.success(bool);
    }

    @GetMapping("/queryReviewedRewardScoreNum")
    @ApiOperation(value = "查询待审核奖分记录数量(查本租户)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Integer> queryReviewedRewardScoreNum() {
        return ResultVO.success(bizRewardScoreRecordService.queryReviewedRewardScoreNum());
    }

    @GetMapping("/getAndUpdateStatus/{id}")
    @ApiOperation(value = "个人查询单条数据并修改为已阅状态", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO getAndUpdateStatus(@PathVariable Long id) {
        BizRewardScoreRecordDTO bizRewardScoreRecordDTO = bizRewardScoreRecordService.getAndUpdateStatus(id);
        return ResultVO.success(bizRewardScoreRecordDTO);
    }


    @PostMapping("/pageQueryBizRewardScoreRecordMunicipalByAndroid")
    @ApiOperation(value = "市直单位办公室专职人员分页查询加分记录(安卓)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizRewardScoreRecordDTO>> pageQueryBizRewardScoreRecordMunicipalByAndroid(@RequestBody BizRewardScoreRecordDTO bizRewardScoreRecordDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizRewardScoreRecordDTO.getCurrentPage());
        basePageForm.setPageSize(bizRewardScoreRecordDTO.getPageSize());
        return ResultVO.success(bizRewardScoreRecordService.pageQueryBizRewardScoreRecordMunicipalByAndroid(bizRewardScoreRecordDTO, basePageForm));
    }

    @PostMapping("/pageQueryBizRewardScoreRecordIndividualByAndroid")
    @ApiOperation(value = "市直单位个人分页查询加分记录(安卓)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizRewardScoreRecordDTO>> pageQueryBizRewardScoreRecordIndividualByAndroid(@RequestBody BizRewardScoreRecordDTO bizRewardScoreRecordDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizRewardScoreRecordDTO.getCurrentPage());
        basePageForm.setPageSize(bizRewardScoreRecordDTO.getPageSize());
        return ResultVO.success(bizRewardScoreRecordService.pageQueryBizRewardScoreRecordIndividual(bizRewardScoreRecordDTO, basePageForm));
    }

}
