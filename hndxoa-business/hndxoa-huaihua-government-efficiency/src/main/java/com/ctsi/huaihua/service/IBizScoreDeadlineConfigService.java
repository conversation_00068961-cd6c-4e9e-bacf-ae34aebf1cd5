package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizScoreDeadlineConfigDTO;
import com.ctsi.huaihua.entity.BizScoreDeadlineConfig;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 日常工作打分截止时间设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface IBizScoreDeadlineConfigService extends SysBaseServiceI<BizScoreDeadlineConfig> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizScoreDeadlineConfigDTO> queryListPage(BizScoreDeadlineConfigDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizScoreDeadlineConfigDTO> queryList(BizScoreDeadlineConfigDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizScoreDeadlineConfigDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizScoreDeadlineConfigDTO create(BizScoreDeadlineConfigDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizScoreDeadlineConfigDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 是否允许打分
     *
     * @param scoreMonth
     * @return
     */
    boolean enableMark(Integer scoreMonth);

     /**
     * 是否存在
     *
     * existByBizScoreDeadlineConfigId
     * @param code
     * @return
     */
    boolean existByBizScoreDeadlineConfigId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizScoreDeadlineConfigDTO> dataList);


}
