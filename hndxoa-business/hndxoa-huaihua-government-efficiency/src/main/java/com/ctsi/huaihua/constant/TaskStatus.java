package com.ctsi.huaihua.constant;



/**
 * @description
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2022-04-08
 **/
public class TaskStatus {


    /**
     * 主任务结束时间标志
     */
    public static final int TASK_SUPERVISION_DUE_TIME = 1;

    /**
     * 分解任务结束时间标志
     */
    public static final int TASK_DECOMPOSE_DUE_TIME = 2;

    /**
     * 短信提醒 每周
     */
    public static final int AUTO_TASK_REMIND_WEEK = 2;
    /**
     * 短信提醒 每月
     */
    public static final int AUTO_TASK_REMIND_MONTH = 3;

    /**
     * 本单位
     */
    public static final int TASK_SOURCE_OWMER_DEPT = 1;
    /**
     * 外单位
     */
    public static final int TASK_SOURCE_OTHER_DEPT = 2;

    /**
     * 该分解任务有减分
     */
    public static final Integer TASK_POINTS = 1;

    /**
     * 该分解任务没减分
     */
    public static final Integer TASK_NOT_POINTS = 0;

    /**
     * 该分解任务有加分
     */
    public static final Integer TASK_ADD_POINTS = 1;

    /**
     * 该分解任务没加分
     */
    public static final Integer TASK_ADD_NOT_POINTS = 0;

    /**
     * 分解任务撤回
     */
    public static final Integer TASK_HAS_SIGN_WITHDRAW = 2;

    /**
     * 督察任务发布
     */
    public static final Integer TASK_RELEASE = 1;

    /**
     * 已签收
     */
    public static final Integer HAS_SIGN = 1;

    /**
     * 已预览
     */
    public static final Integer PREVIEW = 1;

    /**
     * 未预览
     */
    public static final Integer NOT_PREVIEW = 0;

    /**
     * 已发布
     */
    public static final Integer RELEASE = 1;

    /**
     * 驳回
     */
    public static final Integer REJECT = 4;

    /**
     * 待审批
     */
    public static final Integer TO_BE_APPROVED = 3;


    /**
     * 办公室专职人员签收上级的code吗
     */
    public static final String TASK_SIGN_CODE = "cscp.taskdecompose.leader";

    /**
     * 子任务办结
     */
    public static final Integer DECOMSE_FINISH = 1;
    /**
     * 子任务没有办结
     */
    public static final Integer DECOMSE_NO_FINISH = 0;

    /**
     * 主任务办结
     */
    public static final Integer SUP_FINISH = 1;

    /**
     * 主任务未办结
     */
    public static final Integer SUP_NO_FINISH = 0;

    /**
     * 使用创建时间为条件
     */
    public static final Integer CREATE_TIME_QUERY_TYPE = 1;

    /**
     * 使用办结时间为条件
     */
    public static final Integer CONCLUDE_TIME_QUERY_TYPE = 2;

}
