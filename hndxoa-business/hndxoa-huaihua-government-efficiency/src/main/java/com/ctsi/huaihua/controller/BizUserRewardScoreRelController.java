package com.ctsi.huaihua.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.huaihua.entity.BizUserRewardScoreRel;
import com.ctsi.huaihua.entity.dto.BizUserRewardScoreRelDTO;
import com.ctsi.huaihua.service.IBizUserRewardScoreRelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizUserRewardScoreRel")
@Api(value = "用户奖励分记录关系表", tags = "用户奖励分记录关系表接口")
public class BizUserRewardScoreRelController extends BaseController {

    private static final String ENTITY_NAME = "bizUserRewardScoreRel";

    @Autowired
    private IBizUserRewardScoreRelService bizUserRewardScoreRelService;



    /**
     *  新增用户奖励分记录关系表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizUserRewardScoreRel.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增用户奖励分记录关系表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserRewardScoreRel.add')")
    public ResultVO createBatch(@RequestBody List<BizUserRewardScoreRelDTO> bizUserRewardScoreRelList) {
       Boolean  result = bizUserRewardScoreRelService.insertBatch(bizUserRewardScoreRelList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizUserRewardScoreRel.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增用户奖励分记录关系表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserRewardScoreRel.add')")
    public ResultVO<BizUserRewardScoreRelDTO> create(@RequestBody BizUserRewardScoreRelDTO bizUserRewardScoreRelDTO)  {
        BizUserRewardScoreRelDTO result = bizUserRewardScoreRelService.create(bizUserRewardScoreRelDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizUserRewardScoreRel.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新用户奖励分记录关系表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserRewardScoreRel.update')")
    public ResultVO update(@RequestBody BizUserRewardScoreRelDTO bizUserRewardScoreRelDTO) {
	    Assert.notNull(bizUserRewardScoreRelDTO.getId(), "general.IdNotNull");
        int count = bizUserRewardScoreRelService.update(bizUserRewardScoreRelDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除用户奖励分记录关系表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizUserRewardScoreRel.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserRewardScoreRel.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizUserRewardScoreRelService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizUserRewardScoreRelDTO bizUserRewardScoreRelDTO = bizUserRewardScoreRelService.findOne(id);
        return ResultVO.success(bizUserRewardScoreRelDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizUserRewardScoreRelPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizUserRewardScoreRelDTO>> queryBizUserRewardScoreRelPage(BizUserRewardScoreRelDTO bizUserRewardScoreRelDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizUserRewardScoreRelService.queryListPage(bizUserRewardScoreRelDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizUserRewardScoreRel")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizUserRewardScoreRelDTO>> queryBizUserRewardScoreRel(BizUserRewardScoreRelDTO bizUserRewardScoreRelDTO) {
       List<BizUserRewardScoreRelDTO> list = bizUserRewardScoreRelService.queryList(bizUserRewardScoreRelDTO);
       return ResultVO.success(new ResResult<BizUserRewardScoreRelDTO>(list));
   }

}
