package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.receive.entity.GroupPopulation;
import com.ctsi.hndx.receive.entity.TemplateFormModel;
import com.ctsi.hndx.receive.entity.TemplateGroup;
import com.ctsi.hndx.receive.entity.dto.GroupPopulationDTO;
import com.ctsi.hndx.receive.entity.dto.PopulationGroupDTO;
import com.ctsi.hndx.receive.mapper.GroupPopulationMapper;
import com.ctsi.hndx.receive.mapper.TemplateFormModelMapper;
import com.ctsi.hndx.receive.mapper.TemplateGroupMapper;
import com.ctsi.hndx.receive.service.IPopulationGroupService;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SysTenantUtils;
import com.ctsi.huaihua.entity.*;
import com.ctsi.huaihua.entity.dto.BizScoreDeadlineConfigDTO;
import com.ctsi.huaihua.entity.dto.BizScoreRecordDTO;
import com.ctsi.huaihua.entity.dto.BizScoreScopeDTO;
import com.ctsi.huaihua.entity.dto.BizUserScoreDTO;
import com.ctsi.huaihua.mapper.*;
import com.ctsi.huaihua.service.IBizScoreDeadlineConfigService;
import com.ctsi.huaihua.service.IBizScoreRecordService;
import com.ctsi.huaihua.service.IBizScoreScopeService;
import com.ctsi.ssdc.admin.domain.dto.CscpBaseUserDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 日常打分记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Slf4j
@Service
public class BizScoreRecordServiceImpl extends SysBaseServiceImpl<BizScoreRecordMapper, BizScoreRecord> implements IBizScoreRecordService {

    @Autowired
    private IBizScoreDeadlineConfigService bizScoreDeadlineConfigService;

    @Autowired
    private IBizScoreScopeService bizScoreScopeService;

    @Autowired
    private BizScoreRecordMapper bizScoreRecordMapper;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IPopulationGroupService tThisUnitGroupService;

    @Autowired
    private BizTaskDecomposeMapper bizTaskDecomposeMapper;

    @Autowired
    private BizTaskScoreMapper bizTaskScoreMapper;

    @Autowired
    private BizTaskScoreAddMapper bizTaskScoreAddMapper;

    @Autowired
    private BizTaskScoreSubMapper bizTaskScoreSubMapper;

    @Autowired
    private TemplateFormModelMapper templateFormModelMapper;

    @Autowired
    private TemplateGroupMapper templateGroupMapper;

    @Autowired
    private GroupPopulationMapper groupPopulationMapper;

    @Value("${model.cFormModelTable:}")
    private String cFormModelTable;

    @Value("${model.companyId:}")
    private Long modelCompanyId;


    /**
     * 查询历史打分记录
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizScoreRecordDTO> queryListPage(BizScoreRecordDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizScoreRecord> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(entityDTO.getScoreYear() != null, BizScoreRecord::getScoreYear, entityDTO.getScoreYear());
        queryWrapper.eq(entityDTO.getScoreYear() != null, BizScoreRecord::getScoreMonth, entityDTO.getScoreMonth());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getUserName()), BizScoreRecord::getUserName, entityDTO.getUserName());
        // 根据年、月、打分时间降序排序
        queryWrapper.orderByDesc(BizScoreRecord::getScoreYear, BizScoreRecord::getScoreMonth, BizScoreRecord::getCreateTime);

        IPage<BizScoreRecord> pageData = bizScoreRecordMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizScoreRecordDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizScoreRecordDTO.class));

        return new PageResult<BizScoreRecordDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }


    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizScoreRecordDTO> queryList(BizScoreRecordDTO entityDTO) {
        LambdaQueryWrapper<BizScoreRecord> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(entityDTO.getScoreYear() != null, BizScoreRecord::getScoreYear, entityDTO.getScoreYear());
        queryWrapper.eq(entityDTO.getScoreYear() != null, BizScoreRecord::getScoreMonth, entityDTO.getScoreMonth());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getUserName()), BizScoreRecord::getUserName, entityDTO.getUserName());
        // 根据年、月、打分时间降序排序
        queryWrapper.orderByDesc(BizScoreRecord::getScoreYear, BizScoreRecord::getScoreMonth, BizScoreRecord::getCreateTime);
        List<BizScoreRecord> listData = bizScoreRecordMapper.selectList(queryWrapper);
        List<BizScoreRecordDTO> BizScoreRecordDTOList = ListCopyUtil.copy(listData, BizScoreRecordDTO.class);
        return BizScoreRecordDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizScoreRecordDTO findOne(Long id) {
        BizScoreRecord  bizScoreRecord =  bizScoreRecordMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizScoreRecord,BizScoreRecordDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizScoreRecordDTO create(BizScoreRecordDTO entityDTO) {
        BizScoreRecord bizScoreRecord =  BeanConvertUtils.copyProperties(entityDTO,BizScoreRecord.class);
        save(bizScoreRecord);
        return  BeanConvertUtils.copyProperties(bizScoreRecord,BizScoreRecordDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizScoreRecordDTO entity) {
        BizScoreRecord bizScoreRecord = BeanConvertUtils.copyProperties(entity,BizScoreRecord.class);
        return bizScoreRecordMapper.updateById(bizScoreRecord);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizScoreRecordMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizScoreRecordId
     * @return
     */
    @Override
    public boolean existByBizScoreRecordId(Long BizScoreRecordId) {
        if (BizScoreRecordId != null) {
            LambdaQueryWrapper<BizScoreRecord> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizScoreRecord::getId, BizScoreRecordId);
            List<BizScoreRecord> result = bizScoreRecordMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量打分
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizScoreRecordDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return true;
        }
        // 判断当前时间是否允许打分
        boolean bool = bizScoreDeadlineConfigService.enableMark(dataList.get(0).getScoreMonth());
        if (!bool) {
            throw new BusinessException("已经错过打分时间，不允许打分操作");
        }
        List<BizScoreRecord> bizScoreRecordList = ListCopyUtil.copy(dataList, BizScoreRecord.class);
        bizScoreRecordList.forEach(i -> updateById(i));
        return true;
    }


    /**
     * 领导日常打分
     * @param bizScoreRecordDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizScoreRecordDTO insertBizScoreRecord(BizScoreRecordDTO bizScoreRecordDTO) {
        // 判断当前时间是否允许打分
        boolean bool = bizScoreDeadlineConfigService.enableMark(bizScoreRecordDTO.getScoreMonth());
        if (!bool) {
            throw new BusinessException("已经错过打分时间，不允许打分操作");
        }
        BizScoreRecord bizScoreRecord = BeanConvertUtils.copyProperties(bizScoreRecordDTO, BizScoreRecord.class);
        this.updateById(bizScoreRecord);
        return bizScoreRecordDTO;
    }


    /**
     * 领导查询当月打分记录
     * @param bizScoreRecordDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizScoreRecordDTO> queryCurrentBizScoreRecord(BizScoreRecordDTO bizScoreRecordDTO, BasePageForm basePageForm) {
        BizScoreDeadlineConfigDTO bizScoreDeadlineConfigDTO = bizScoreDeadlineConfigService.queryList(null).get(0);
        if (Objects.isNull(bizScoreDeadlineConfigDTO) || Objects.isNull(bizScoreDeadlineConfigDTO.getDay())){
            throw new BusinessException("未设置日常工作打分截止时间");
        }

        LambdaQueryWrapper<BizScoreRecord> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizScoreRecord::getCreateBy, SecurityUtils.getCurrentUserId());
        if (StringUtils.isNotBlank(bizScoreRecordDTO.getUserName())) {
            queryWrapper.like(BizScoreRecord::getUserName, bizScoreRecordDTO.getUserName());
        }
        if (Objects.nonNull(bizScoreRecordDTO.getHasModify())) {
            queryWrapper.eq(BizScoreRecord::getHasModify, bizScoreRecordDTO.getHasModify());
        }
        queryWrapper.eq(BizScoreRecord::getCreateBy, SecurityUtils.getCurrentUserId());
        // 如果前端传了指定年份和月份，则直接查指定的时间的分数
        LocalDate now = LocalDate.now();
        if (Objects.nonNull(bizScoreRecordDTO.getScoreYear()) || Objects.nonNull(bizScoreRecordDTO.getScoreMonth())) {
            // 如果前端没有传年份，默认表示查今年
            if (Objects.nonNull(bizScoreRecordDTO.getScoreYear())) {
                queryWrapper.eq(BizScoreRecord::getScoreYear, bizScoreRecordDTO.getScoreYear());
            } else {
                queryWrapper.eq(BizScoreRecord::getScoreYear, now.getYear());
            }
            queryWrapper.eq(Objects.nonNull(bizScoreRecordDTO.getScoreMonth()), BizScoreRecord::getScoreMonth, bizScoreRecordDTO.getScoreMonth());
        } else {
            // 如果前端没有传，则表明查上个月的
            LocalDate lastMonthDate = now.minusMonths(1);
            queryWrapper.eq(BizScoreRecord::getScoreYear, lastMonthDate.getYear());
            queryWrapper.eq(BizScoreRecord::getScoreMonth, lastMonthDate.getMonth());
        }
        IPage<BizScoreRecord> pageData = bizScoreRecordMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        IPage<BizScoreRecordDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizScoreRecordDTO.class));
        if (CollectionUtils.isEmpty(pageData.getRecords())) {
            return new PageResult<>(new ArrayList<>(), data.getTotal(), data.getCurrent());
        }

        // 这里前端传入的每页条数为1000，可以直接不需要按分页处理
        List<Long> userIdList = data.getRecords().stream().map(i -> i.getUserId()).collect(Collectors.toList());
        CscpBaseUserDTO cscpBaseUserDTO = new CscpBaseUserDTO();
        cscpBaseUserDTO.setUserIdList(userIdList);
        Map<Long, List<CscpBaseUserDTO>> userMap = cscpUserService.queryBaseUserInfo(cscpBaseUserDTO).stream()
                .collect(Collectors.groupingBy(CscpBaseUserDTO::getUserId));
        data.getRecords().stream().forEach(record -> {
            if (CollectionUtils.isNotEmpty(userMap.get(record.getUserId()))) {
                CscpBaseUserDTO baseUserDTO = userMap.get(record.getUserId()).get(0);
                record.setPost(baseUserDTO.getPost());
                record.setDepartmentId(baseUserDTO.getDepartmentId());
                record.setDepartmentName(baseUserDTO.getDepartmentName());
                record.setCompanyId(baseUserDTO.getCompanyId());
                record.setCompanyName(baseUserDTO.getCompanyName());
            }
        });

        // 条件过滤
        List<BizScoreRecordDTO> bizScoreRecordDTOList = data.getRecords().stream().filter(i -> {
            if (StringUtils.isNotBlank(bizScoreRecordDTO.getDepartmentName())) {
                return i.getDepartmentName().contains(bizScoreRecordDTO.getDepartmentName());
            }
            return true;
        }).filter(i -> {
            if (StringUtils.isNotBlank(bizScoreRecordDTO.getCompanyName())) {
                return i.getCompanyName().contains(bizScoreRecordDTO.getCompanyName());
            }
            return true;
        }).collect(Collectors.toList());
        return new PageResult<>(bizScoreRecordDTOList, data.getTotal(), data.getCurrent());
    }


    /**
     * 定时任务: 自动生成默认打分记录
     * @param bizScoreRecordDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean autoGenerateScoreRecord(BizScoreRecordDTO bizScoreRecordDTO) {
        // 获取设置的领导日常打分基础分数
        String value = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DAILY_SCORE_BASE_LINE);
        if (StringUtils.isBlank(value)) {
            throw new BusinessException("系统配置表中未设置领导日常打分基础分数: biz:score:base:line");
        }

        // 获取所有打分范围记录的领导ID
        LambdaQueryWrapper<BizScoreScope> queryWrapper = new LambdaQueryWrapper();
        List<Long> leaderIdList = bizScoreScopeService.selectListNoAdd(queryWrapper).stream().map(i ->
                i.getLeaderId()).distinct().collect(Collectors.toList());

        // 获取该领导所有被打分用户，并组装数据
        leaderIdList.stream().forEach(i -> {
            BizScoreScopeDTO bizScoreScopeDTO = new BizScoreScopeDTO();
            bizScoreScopeDTO.setLeaderId(i);
            List<Long> idList = new ArrayList<>();
            idList.add(i);
            CscpUserDTO leaderUserDTO = cscpUserService.listQueryUserDTO(idList, true).get(0);
            // 按单个领导范围批量新增数据
            List<BizScoreRecord> collect = bizScoreScopeService.queryBizLeaderScoreScopeList(bizScoreScopeDTO).stream().map(userDTO -> {
                BizScoreRecord scoreRecord = new BizScoreRecord();
                // 要求当月生成上月打分记录
                LocalDate now = LocalDate.now().minusMonths(1);
                scoreRecord.setUserId(userDTO.getId());
                scoreRecord.setUserName(userDTO.getRealName());

                scoreRecord.setScoreYear(now.getYear());
                if (Objects.nonNull(bizScoreRecordDTO.getScoreYear())) {
                    scoreRecord.setScoreYear(bizScoreRecordDTO.getScoreYear());
                }

                scoreRecord.setScoreMonth(now.getMonthValue());
                if (Objects.nonNull(bizScoreRecordDTO.getScoreMonth())) {
                    scoreRecord.setScoreMonth(bizScoreRecordDTO.getScoreMonth());
                }
//                scoreRecord.setScore(60);
                scoreRecord.setScore(Integer.parseInt(value));
                scoreRecord.setHasModify(0);
                scoreRecord.setCreateBy(i);
                scoreRecord.setCreateName(leaderUserDTO.getRealName());
                scoreRecord.setCreateTime(LocalDateTime.now());
                return scoreRecord;
            }).filter(record -> !this.checkBizScoreRecordExist(record)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                this.saveBatch(collect);
            }
        });
        return true;
    }


    /**
     * 判断该用户当月打分记录是否存在
     * @param bizScoreRecord
     */
    private Boolean checkBizScoreRecordExist(BizScoreRecord bizScoreRecord) {
        LambdaQueryWrapper<BizScoreRecord> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(BizScoreRecord::getUserId, bizScoreRecord.getUserId());
        lambdaQueryWrapper.eq(BizScoreRecord::getScoreYear, bizScoreRecord.getScoreYear());
        lambdaQueryWrapper.eq(BizScoreRecord::getScoreMonth, bizScoreRecord.getScoreMonth());
        lambdaQueryWrapper.eq(BizScoreRecord::getCreateBy, bizScoreRecord.getCreateBy());
        List<BizScoreRecord> bizScoreRecords = bizScoreRecordMapper.selectListNoAdd(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(bizScoreRecords)) {
            return false;
        }
        return true;
    }

    @Override
    public List<BizUserScoreDTO> averageAnnualScores(){
        //获取人员
//        不使用直接取分阅群组的方法
//        PopulationGroupDTO populationGroupDTO = new PopulationGroupDTO();
//        BasePageForm basePageForm = new BasePageForm();
//        basePageForm.setCurrentPage(1);
//        basePageForm.setPageSize(100);
//        PageResult<PopulationGroupDTO> userDate = tThisUnitGroupService.queryListPage(populationGroupDTO, basePageForm);
//        for (PopulationGroupDTO i :userDate.getData()){
//            if (i.getGroupName().equals("市直单位领导")){
//                groupUser.addAll(i.getGroupUsers());
//            }
//        }
        //查找模型表
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        Long topFloorTenantId = SysTenantUtils.getTopFloorTenamtId(tenantId);
        LambdaQueryWrapper<TemplateFormModel> templateFormModelLambdaQueryWrapper = Wrappers.lambdaQuery();
        templateFormModelLambdaQueryWrapper.eq(TemplateFormModel::getTenantId,topFloorTenantId);
        templateFormModelLambdaQueryWrapper.eq(TemplateFormModel::getCompanyId,modelCompanyId);
        templateFormModelLambdaQueryWrapper.eq(TemplateFormModel::getModelTable,cFormModelTable);
        List<TemplateFormModel> templateFormModels = templateFormModelMapper.selectListNoAdd(templateFormModelLambdaQueryWrapper);
        List<Long> templateIds = templateFormModels.stream().map(i -> i.getTemplateId()).collect(Collectors.toList());
        //查找模型分组表
        LambdaQueryWrapper<TemplateGroup> templateGroupLambdaQueryWrapper = Wrappers.lambdaQuery();
        templateGroupLambdaQueryWrapper.in(TemplateGroup::getTemplateId,templateIds);
        List<TemplateGroup> templateGroups = templateGroupMapper.selectListNoAdd(templateGroupLambdaQueryWrapper);
        List<Long> groupIds = templateGroups.stream().map(i -> i.getGroupId()).collect(Collectors.toList());
        //查找分组表
        LambdaQueryWrapper<GroupPopulation> groupPopulationLambdaQueryWrapper = Wrappers.lambdaQuery();
        groupPopulationLambdaQueryWrapper.select(GroupPopulation::getTemplateBusinessId,GroupPopulation::getUnitName);
        groupPopulationLambdaQueryWrapper.in(GroupPopulation::getGroupId,groupIds);
        List<GroupPopulation> groupUser = groupPopulationMapper.selectListNoAdd(groupPopulationLambdaQueryWrapper);

        List<BizUserScoreDTO> bizUserScoreDTOList = ListCopyUtil.copy(groupUser,BizUserScoreDTO.class);
        Set<BizUserScoreDTO> bizUserScoreDTOS = new HashSet<>(bizUserScoreDTOList);

        bizUserScoreDTOS.forEach(i -> {
            //获取基础分
            LambdaQueryWrapper<BizScoreRecord> bizScoreRecordLambdaQueryWrapper = Wrappers.lambdaQuery();
            bizScoreRecordLambdaQueryWrapper.eq(BizScoreRecord::getUserId,i.getTemplateBusinessId());
            bizScoreRecordLambdaQueryWrapper.eq(BizScoreRecord::getScoreYear, org.joda.time.LocalDateTime.now().getYear());
            List<BizScoreRecord> bizScoreRecords = bizScoreRecordMapper.selectListNoAdd(bizScoreRecordLambdaQueryWrapper);
            Double sum = bizScoreRecords.stream().mapToDouble(BizScoreRecord::getScore).sum();
            //拼接当年分解任务开始时间与结束时间
            Integer year = org.joda.time.LocalDateTime.now().getYear();
            String start = year.toString().concat("-01-01 00:00:00");
            String end = year.toString().concat("-12-31 23:59:59");
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startTime = LocalDateTime.parse(start, df);
            LocalDateTime endTime = LocalDateTime.parse(end,df);
            //获取分解任务id
            LambdaQueryWrapper<BizTaskDecompose> bizTaskDecomposeLambdaQueryWrapper = Wrappers.lambdaQuery();
            bizTaskDecomposeLambdaQueryWrapper.eq(BizTaskDecompose::getDutyPeopleId,i.getTemplateBusinessId());
            bizTaskDecomposeLambdaQueryWrapper.eq(BizTaskDecompose::getHasFinish,1);
            bizTaskDecomposeLambdaQueryWrapper.ge(BizTaskDecompose::getHasFinishTime,startTime);
            bizTaskDecomposeLambdaQueryWrapper.le(BizTaskDecompose::getHasFinishTime,endTime);
            List<BizTaskDecompose> bizTaskDecomposes = bizTaskDecomposeMapper.selectListNoAdd(bizTaskDecomposeLambdaQueryWrapper);
            List<Long> decomposeId = bizTaskDecomposes.stream().map(j -> j.getId()).collect(Collectors.toList());
            //获取难度分
            if (CollectionUtils.isNotEmpty(decomposeId)){
                LambdaQueryWrapper<BizTaskScore> bizTaskScoreLambdaQueryWrapper = Wrappers.lambdaQuery();
                bizTaskScoreLambdaQueryWrapper.in(BizTaskScore::getBizTaskDecompose,decomposeId);
                List<BizTaskScore> bizTaskScores = bizTaskScoreMapper.selectListNoAdd(bizTaskScoreLambdaQueryWrapper);
                sum += bizTaskScores.stream().mapToDouble(BizTaskScore::getActualScore).sum();
                //获取加分
                LambdaQueryWrapper<BizTaskScoreAdd> bizTaskScoreAddLambdaQueryWrapper = Wrappers.lambdaQuery();
                bizTaskScoreAddLambdaQueryWrapper.in(BizTaskScoreAdd::getBizTaskDecompose,decomposeId);
                List<BizTaskScoreAdd> bizTaskScoreAddList = bizTaskScoreAddMapper.selectListNoAdd(bizTaskScoreAddLambdaQueryWrapper);
                sum += bizTaskScoreAddList.stream().mapToDouble(BizTaskScoreAdd::getScore).sum();
                //获取减分
                LambdaQueryWrapper<BizTaskScoreSub> bizTaskScoreSubLambdaQueryWrapper = Wrappers.lambdaQuery();
                bizTaskScoreSubLambdaQueryWrapper.in(BizTaskScoreSub::getBizTaskDecompose,decomposeId);
                List<BizTaskScoreSub> bizTaskScoreSubList = bizTaskScoreSubMapper.selectListNoAdd(bizTaskScoreSubLambdaQueryWrapper);
                sum -= bizTaskScoreSubList.stream().mapToDouble(BizTaskScoreSub::getSores).sum();
            }
            //平均月份
            Integer max = bizScoreRecords.stream().mapToInt(BizScoreRecord::getScoreMonth).max().orElse(0);
            Integer min = bizScoreRecords.stream().mapToInt(BizScoreRecord::getScoreMonth).min().orElse(0);
            Integer average = max - min + 1;
            i.setScore(division(sum,average,3));
        });

        return bizUserScoreDTOS.stream().sorted(Comparator.comparing(BizUserScoreDTO::getScore).reversed()).collect(Collectors.toList());
    }

    public static double division(double a, Integer b,Integer accutate){
        BigDecimal b1 = new BigDecimal(a);
        BigDecimal b2 = new BigDecimal(b);
        return b1.divide(b2,accutate,BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
