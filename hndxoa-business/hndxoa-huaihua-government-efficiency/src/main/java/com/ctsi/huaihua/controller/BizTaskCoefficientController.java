package com.ctsi.huaihua.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import com.ctsi.huaihua.entity.dto.BizTaskCoefficientDTO;
import com.ctsi.huaihua.service.IBizTaskCoefficientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 任务难度系数表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskCoefficient")
@Api(value = "任务难度系数表", tags = "任务难度系数表接口")
public class BizTaskCoefficientController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskCoefficient";

    @Autowired
    private IBizTaskCoefficientService bizTaskCoefficientService;



    /**
     *  新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskCoefficient.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskCoefficient.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskCoefficientDTO> bizTaskCoefficientList) {
       Boolean  result = bizTaskCoefficientService.insertBatch(bizTaskCoefficientList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskCoefficient.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增数据")
  //  @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskCoefficient.add')")
    public ResultVO<BizTaskCoefficientDTO> create(@RequestBody BizTaskCoefficientDTO bizTaskCoefficientDTO)  {
        BizTaskCoefficientDTO result = bizTaskCoefficientService.create(bizTaskCoefficientDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskCoefficient.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新数据")
 //   @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskCoefficient.update')")
    public ResultVO update(@RequestBody BizTaskCoefficientDTO bizTaskCoefficientDTO) {
	    Assert.notNull(bizTaskCoefficientDTO.getId(), "general.IdNotNull");
        int count = bizTaskCoefficientService.update(bizTaskCoefficientDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskCoefficient.delete)", notes = "传入参数")
 //   @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskCoefficient.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskCoefficientService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskCoefficientDTO bizTaskCoefficientDTO = bizTaskCoefficientService.findOne(id);
        return ResultVO.success(bizTaskCoefficientDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskCoefficientPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskCoefficientDTO>> queryBizTaskCoefficientPage(BizTaskCoefficientDTO bizTaskCoefficientDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskCoefficientService.queryListPage(bizTaskCoefficientDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskCoefficient")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskCoefficientDTO>> queryBizTaskCoefficient(BizTaskCoefficientDTO bizTaskCoefficientDTO) {
       List<BizTaskCoefficientDTO> list = bizTaskCoefficientService.queryList(bizTaskCoefficientDTO);
       return ResultVO.success(new ResResult<BizTaskCoefficientDTO>(list));
   }

    /**
     * 计算分数
     * @param list
     * @return
     */
   @PostMapping("/getScore")
   @ApiOperation(value = "获取分数", notes = "传入参数")
   public ResultVO<Double> getScore(@RequestBody List<BizTaskCoefficientDTO> list){
       Double result = bizTaskCoefficientService.getScore(list);
       return ResultVO.success(result);
   }

    /**
     * 获取公式
     * @param list
     * @return
     */
    @PostMapping("/getEquation")
    @ApiOperation(value = "获取公式", notes = "传入参数")
    public ResultVO<String> getEquation(@RequestBody List<BizTaskCoefficientDTO> list){
        String result = bizTaskCoefficientService.getEquation(list);
        return ResultVO.success(result);
    }
}
