package com.ctsi.huaihua.controller;

import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Optional;

import com.ctsi.huaihua.entity.BizTaskLeaderLiaison;
import com.ctsi.huaihua.entity.dto.BizTaskLeaderLiaisonDTO;
import com.ctsi.huaihua.service.IBizTaskLeaderLiaisonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskLeaderLiaison")
@Api(value = "任务领导对应的联络员表", tags = "任务领导对应的联络员表接口")
public class BizTaskLeaderLiaisonController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskLeaderLiaison";

    @Autowired
    private IBizTaskLeaderLiaisonService bizTaskLeaderLiaisonService;


    /**
     * 新增任务领导对应的联络员表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskLeaderLiaison.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增任务领导对应的联络员表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLeaderLiaison.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskLeaderLiaisonDTO> bizTaskLeaderLiaisonList) {
        Boolean result = bizTaskLeaderLiaisonService.insertBatch(bizTaskLeaderLiaisonList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskLeaderLiaison.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增任务领导对应的联络员表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLeaderLiaison.add')")
    public ResultVO<BizTaskLeaderLiaisonDTO> create(@RequestBody BizTaskLeaderLiaisonDTO bizTaskLeaderLiaisonDTO) {
        BizTaskLeaderLiaisonDTO result = bizTaskLeaderLiaisonService.create(bizTaskLeaderLiaisonDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskLeaderLiaison.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新任务领导对应的联络员表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLeaderLiaison.update')")
    public ResultVO update(@RequestBody BizTaskLeaderLiaisonDTO bizTaskLeaderLiaisonDTO) {
        Assert.notNull(bizTaskLeaderLiaisonDTO.getId(), "general.IdNotNull");
        int count = bizTaskLeaderLiaisonService.update(bizTaskLeaderLiaisonDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除任务领导对应的联络员表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskLeaderLiaison.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLeaderLiaison.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskLeaderLiaisonService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskLeaderLiaisonDTO bizTaskLeaderLiaisonDTO = bizTaskLeaderLiaisonService.findOne(id);
        return ResultVO.success(bizTaskLeaderLiaisonDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizTaskLeaderLiaisonPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskLeaderLiaisonDTO>> queryBizTaskLeaderLiaisonPage(BizTaskLeaderLiaisonDTO bizTaskLeaderLiaisonDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskLeaderLiaisonService.queryListPage(bizTaskLeaderLiaisonDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryBizTaskLeaderLiaison")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizTaskLeaderLiaisonDTO>> queryBizTaskLeaderLiaison(BizTaskLeaderLiaisonDTO bizTaskLeaderLiaisonDTO) {
        List<BizTaskLeaderLiaisonDTO> list = bizTaskLeaderLiaisonService.queryList(bizTaskLeaderLiaisonDTO);
        return ResultVO.success(new ResResult<BizTaskLeaderLiaisonDTO>(list));
    }

}
