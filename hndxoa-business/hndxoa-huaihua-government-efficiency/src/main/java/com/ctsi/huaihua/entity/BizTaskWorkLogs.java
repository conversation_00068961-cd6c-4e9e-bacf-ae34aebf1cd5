package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import java.time.LocalDate;

/**
 * <p>
 * 工作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_work_logs")
@ApiModel(value="BizTaskWorkLogs对象", description="工作日志")
public class BizTaskWorkLogs extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 是否为待办任务，1：表示待办任务，0：表示自建工作任务
     */
    @ApiModelProperty(value = "是否为待办任务，1：表示待办任务，0：表示自建工作任务")
    private Integer isTodoTask;

    /**
     * 待办任务的id
     */
    @ApiModelProperty(value = "待办任务的id")
    private Long todoTaskId;


    @ApiModelProperty(value = "表单项的id")
    private Long formId;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String post;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间  yyyy-MM-dd")

    private LocalDate startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间  yyyy-MM-dd")
    private LocalDate endTime;

    /**
     * 时长
     */
    @ApiModelProperty(value = "时长")
    @DecimalMin(message="时长不能小于0", value = "0.0")
    private Float costTime;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String content;

    /**
     * 附件：1表示有附件，0表示无附件
     */
    @ApiModelProperty(value = "附件：1表示有附件，0表示无附件")
    private Integer annex;

    /**
     * 工作类型
     */
    @ApiModelProperty(value = "工作类型")
    private String workType;

    /**
     * 工作来源
     */
    @ApiModelProperty(value = "工作来源")
    private String workSource;
}
