package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizUserPunishScoreRel;
import com.ctsi.huaihua.entity.dto.BizUserPunishScoreRelDTO;
import com.ctsi.huaihua.mapper.BizUserPunishScoreRelMapper;
import com.ctsi.huaihua.service.IBizUserPunishScoreRelService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 用户惩罚分记录关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Slf4j
@Service
public class BizUserPunishScoreRelServiceImpl extends SysBaseServiceImpl<BizUserPunishScoreRelMapper, BizUserPunishScoreRel> implements IBizUserPunishScoreRelService {

    @Autowired
    private BizUserPunishScoreRelMapper bizUserPunishScoreRelMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizUserPunishScoreRelDTO> queryListPage(BizUserPunishScoreRelDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizUserPunishScoreRel> queryWrapper = new LambdaQueryWrapper();

        IPage<BizUserPunishScoreRel> pageData = bizUserPunishScoreRelMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizUserPunishScoreRelDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizUserPunishScoreRelDTO.class));

        return new PageResult<BizUserPunishScoreRelDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizUserPunishScoreRelDTO> queryList(BizUserPunishScoreRelDTO entityDTO) {
        LambdaQueryWrapper<BizUserPunishScoreRel> queryWrapper = new LambdaQueryWrapper();
            List<BizUserPunishScoreRel> listData = bizUserPunishScoreRelMapper.selectList(queryWrapper);
            List<BizUserPunishScoreRelDTO> BizUserPunishScoreRelDTOList = ListCopyUtil.copy(listData, BizUserPunishScoreRelDTO.class);
        return BizUserPunishScoreRelDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizUserPunishScoreRelDTO findOne(Long id) {
        BizUserPunishScoreRel  bizUserPunishScoreRel =  bizUserPunishScoreRelMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizUserPunishScoreRel,BizUserPunishScoreRelDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizUserPunishScoreRelDTO create(BizUserPunishScoreRelDTO entityDTO) {
       BizUserPunishScoreRel bizUserPunishScoreRel =  BeanConvertUtils.copyProperties(entityDTO,BizUserPunishScoreRel.class);
        save(bizUserPunishScoreRel);
        return  BeanConvertUtils.copyProperties(bizUserPunishScoreRel,BizUserPunishScoreRelDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizUserPunishScoreRelDTO entity) {
        BizUserPunishScoreRel bizUserPunishScoreRel = BeanConvertUtils.copyProperties(entity,BizUserPunishScoreRel.class);
        return bizUserPunishScoreRelMapper.updateById(bizUserPunishScoreRel);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizUserPunishScoreRelMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizUserPunishScoreRelId
     * @return
     */
    @Override
    public boolean existByBizUserPunishScoreRelId(Long BizUserPunishScoreRelId) {
        if (BizUserPunishScoreRelId != null) {
            LambdaQueryWrapper<BizUserPunishScoreRel> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizUserPunishScoreRel::getId, BizUserPunishScoreRelId);
            List<BizUserPunishScoreRel> result = bizUserPunishScoreRelMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizUserPunishScoreRelDTO> dataList) {
        List<BizUserPunishScoreRel> result = ListCopyUtil.copy(dataList, BizUserPunishScoreRel.class);
        return saveBatch(result);
    }


}
