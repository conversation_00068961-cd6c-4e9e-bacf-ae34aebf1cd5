package com.ctsi.huaihua.controller;

import com.ctsi.ssdc.model.PageResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.ctsi.huaihua.entity.BizTaskScoreSub;
import com.ctsi.huaihua.entity.dto.BizTaskScoreSubDTO;
import com.ctsi.huaihua.service.IBizTaskScoreSubService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskScoreSub")
@Api(value = "任务减分表", tags = "任务减分表接口")
public class BizTaskScoreSubController extends BaseController {

    @Autowired
    private IBizTaskScoreSubService bizTaskScoreSubService;


    /**
     * 减分通报.
     */
    @PostMapping("/points")
    @ApiOperation(value = "减分通报(权限code码为：cscp.bizTaskScoreSub.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增任务减分表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScoreSub.add')")
    public ResultVO<List<BizTaskScoreSubDTO>> points(@RequestBody BizTaskScoreSubDTO bizTaskScoreSubDTO) {
        List<BizTaskScoreSubDTO> result = bizTaskScoreSubService.points(bizTaskScoreSubDTO);
        return ResultVO.success(result);
    }


    /**
     * 查询当前数据所有的减分列表.
     */
    @GetMapping("/queryBizTaskScoreSubPage")
    @ApiOperation(value = "查询当前数据所有的减分列表", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskScoreSubDTO>> queryBizTaskScoreSubPage(BizTaskScoreSubDTO bizTaskScoreSubDTO, BasePageForm basePageForm) {
        List<Long> bizTaskDecomposeIdList = new ArrayList<>();
        bizTaskDecomposeIdList.add(bizTaskScoreSubDTO.getBizTaskDecomposeId());
        bizTaskScoreSubDTO.setBizTaskDecompose(bizTaskDecomposeIdList);
        return ResultVO.success(bizTaskScoreSubService.queryListPage(bizTaskScoreSubDTO, basePageForm));
    }


    /**
     * 查询角标
     */
    @GetMapping("/getAngleMark")
    @ApiOperation(value = "app查询角标（未看公告）")
    public ResultVO<Integer> getAngleMark() {
        return ResultVO.success(bizTaskScoreSubService.getAngleMark());
    }


}
