package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskScoreSubDTO;
import com.ctsi.huaihua.entity.BizTaskScoreSub;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 任务减分表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface IBizTaskScoreSubService extends SysBaseServiceI<BizTaskScoreSub> {


    /**
     * 查询当前数据所有的减分列表
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskScoreSubDTO> queryListPage(BizTaskScoreSubDTO entityDTO, BasePageForm page);


    /**
     * 减分通报
     *
     * @param entity
     * @return
     */
    List<BizTaskScoreSubDTO> points(BizTaskScoreSubDTO entity);

    /**
     * 查看角标
     * @return
     */
    Integer getAngleMark();


    /**
     * 根据分解表主键ID查询任务减分
     * @param decomposeId
     * @return
     */
    List<BizTaskScoreSubDTO> queryScoreSubByDecomposeId(Long decomposeId);
}
