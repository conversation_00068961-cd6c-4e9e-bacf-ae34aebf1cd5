package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskLeaderLiaisonDTO;
import com.ctsi.huaihua.entity.BizTaskLeaderLiaison;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 任务领导对应的联络员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IBizTaskLeaderLiaisonService extends SysBaseServiceI<BizTaskLeaderLiaison> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskLeaderLiaisonDTO> queryListPage(BizTaskLeaderLiaisonDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskLeaderLiaisonDTO> queryList(BizTaskLeaderLiaisonDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskLeaderLiaisonDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskLeaderLiaisonDTO create(BizTaskLeaderLiaisonDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskLeaderLiaisonDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskLeaderLiaisonId
     * @param code
     * @return
     */
    boolean existByBizTaskLeaderLiaisonId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskLeaderLiaisonDTO> dataList);


}
