package com.ctsi.huaihua.service.impl;

import com.ctsi.hndx.service.impl.BaseUserInfoChangeSubjectServiceImpl;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/8/12
 */
@Service
public class BizScoreUserConfigSubjectServiceImpl extends BaseUserInfoChangeSubjectServiceImpl {

    @Override
    public void update(CscpUserDTO userDTO) {

        // 触发观察者信息更新
        this.notifyObservers(userDTO);
    }
}
