package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.service.BizTaskExportToExcelService;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname BizTaskOutputToExcelController
 * @Description
 * @Date 2022/9/27/0027 9:22
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskExportToExcel")
@Api(value = "任务导出至Excel", tags = "任务导出至Excel接口")
public class BizTaskExportToExcelController {

    @Autowired
    private BizTaskExportToExcelService bizTaskExportToExcelService;

    @Autowired
    private IBizTaskDecomposeService bizTaskDecomposeService;

    @PostMapping("/exportTaskPreview")
    @ApiOperation(value = "任务一览表导出接口", notes = "传入参数")
    public ResultVO exportTaskPreview(@RequestBody List<BizTaskDTO> bizTaskDTOList, HttpServletResponse response) throws IOException {
        if (CollectionUtils.isEmpty(bizTaskDTOList)) {
            throw new BusinessException("导出数据不允许为空");
        }
        Boolean bool = bizTaskExportToExcelService.exportTaskPreview(bizTaskDTOList, response);
        return ResultVO.success(bool);
    }

    @GetMapping("/exportAllTaskPreview")
    @ApiOperation(value = "任务一览表全部导出接口", notes = "")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO exportAllTaskPreview(BizTaskDTO dto, BasePageForm basePageForm, HttpServletResponse response) throws IOException{
        dto.setHasPublish(1); // 已发布的任务台账
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        dto.setSupCompanyId(currentCompanyId);
        basePageForm.setPageSize(10000);
        basePageForm.setCurrentPage(1);
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.listReportToExcel(dto, basePageForm, TaskStatus.TASK_DECOMPOSE_DUE_TIME);
        Boolean bool = bizTaskExportToExcelService.exportTaskPreview(list.getData(), response);
        return ResultVO.success(bool);
    }

}
