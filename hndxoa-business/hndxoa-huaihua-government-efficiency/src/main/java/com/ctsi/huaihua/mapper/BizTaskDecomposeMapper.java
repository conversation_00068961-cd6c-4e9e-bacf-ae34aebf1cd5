package com.ctsi.huaihua.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.dto.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 任务分解表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */

public interface BizTaskDecomposeMapper extends MybatisBaseMapper<BizTaskDecompose> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<BizTaskDTO> selectCommonTaskList(IPage<BizTaskDTO> page, @Param("bizTask") BizTaskDTO bizTaskDTO);


    @InterceptorIgnore(tenantLine = "true")
    int selectCommonTaskListCount(@Param("bizTask") BizTaskDTO bizTaskDTO);

    /**
     * 查询待签收的任务的集合
     *
     * @param page
     * @param bizTaskDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizTaskDTO> selectNoSignCommonTaskList(IPage<BizTaskDTO> page, @Param("bizTask") NoHasSignTaskDTO bizTaskDTO);

    /**
     * 查询自己的减分列表
     *
     * @param page
     * @param bizTaskDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizTaskDTO> selectCommonTaskSubList(IPage<BizTaskDTO> page, @Param("bizTask") BizTaskDTO bizTaskDTO);

    /**
     * 反馈管理
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizTaskDTO> selectFeedbackTaskList(IPage<BizTaskDTO> page, @Param("bizTask") BizTaskDTO bizTaskDTO);


    /**
     * 成果收件箱查询
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizTaskDTO> selectBoxTaskList(IPage<BizTaskDTO> page, @Param("bizTask") BizTaskDTO bizTaskDTO);

    /**
     * 成果管理 办结
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizTaskDTO> selectFinishTaskBoxList(IPage<BizTaskDTO> page, @Param("bizTask") BizTaskDTO bizTaskDTO);


    /**
     * 分页查询已减的子任务
     *
     * @param page
     * @param bizTaskDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizTaskDTO> selectReducedTask(IPage<BizTaskDTO> page, @Param("bizTask") BizTaskDTO bizTaskDTO);

    /**
     * mysql获取发布和我的承办
     *
     * @param page
     * @param bizTaskDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizHomeTaskDTO> selectSupAndDecomposeTaskList(IPage<BizHomeTaskDTO> page, @Param("bizTask") BizHomeTaskDTO bizTaskDTO);

    /**
     * 达梦数据库获取发布和我的承办
     *
     * @param page
     * @param bizTaskDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizHomeTaskDTO> selectDMSupAndDecomposeTaskList(IPage<BizHomeTaskDTO> page, @Param("bizTask") BizHomeTaskDTO bizTaskDTO);

    /**
     * 人大金仓数据库获取发布和我的承办
     *
     * @param page
     * @param bizTaskDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizHomeTaskDTO> selectKingbaseSupAndDecomposeTaskList(IPage<BizHomeTaskDTO> page, @Param("bizTask") BizHomeTaskDTO bizTaskDTO);

    /**
     * 不分页查询
     */
    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskDTO> selectCommonTaskList(@Param("bizTask") BizTaskDTO bizTaskDTO);


    @InterceptorIgnore(tenantLine = "true")
    List<BizCompanyNumberOfTasksDTO> selectTasks(@Param("cnot") BizCompanyNumberOfTasksDTO bizCompanyNumberOfTasksDTO);


    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskDTO> receiveTask(@Param("companyIds") Set<Long> companyIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("hasFinish") Integer hasFinish, @Param("queryType") Integer queryType);


    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskDTO> selectBizTaskScores(@Param("userIds") Set<Long> userIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询任务
     *
     * @param bizTaskStatisticsDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskDTO> selectUserTasks(@Param("condition") BizTaskStatisticsDTO bizTaskStatisticsDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskDTO> received(@Param("companyIds") Set<Long> companyIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("hasFinish") Integer hasFinish, @Param("queryType") Integer queryType);

    @InterceptorIgnore(tenantLine = "true")
    int selectTaskDecomposeCount(@Param("userId") Long userId, @Param("subTblFk") Long subTblFk);

    @InterceptorIgnore(tenantLine = "true")
    void deleteBySubTblFk(@Param("subTblFk") Long subTblFk);
}
