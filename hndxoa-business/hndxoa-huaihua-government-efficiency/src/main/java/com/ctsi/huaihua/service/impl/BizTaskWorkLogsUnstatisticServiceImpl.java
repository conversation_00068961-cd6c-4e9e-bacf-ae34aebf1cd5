package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.entity.BizTaskWorkLogsUnstatistic;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsUnstatisticDTO;
import com.ctsi.huaihua.mapper.BizTaskWorkLogsUnstatisticMapper;
import com.ctsi.huaihua.service.IBizTaskWorkLogsUnstatisticService;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 免统计工作日志的用户列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Slf4j
@Service
public class BizTaskWorkLogsUnstatisticServiceImpl extends SysBaseServiceImpl<BizTaskWorkLogsUnstatisticMapper, BizTaskWorkLogsUnstatistic> implements IBizTaskWorkLogsUnstatisticService {

    @Autowired
    private BizTaskWorkLogsUnstatisticMapper bizTaskWorkLogsUnstatisticMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskWorkLogsUnstatisticDTO> queryListPage(BizTaskWorkLogsUnstatisticDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskWorkLogsUnstatistic> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskWorkLogsUnstatistic> pageData = bizTaskWorkLogsUnstatisticMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskWorkLogsUnstatisticDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizTaskWorkLogsUnstatisticDTO.class));

        return new PageResult<BizTaskWorkLogsUnstatisticDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskWorkLogsUnstatisticDTO> queryList(BizTaskWorkLogsUnstatisticDTO entityDTO) {
        LambdaQueryWrapper<BizTaskWorkLogsUnstatistic> queryWrapper = new LambdaQueryWrapper();
            List<BizTaskWorkLogsUnstatistic> listData = bizTaskWorkLogsUnstatisticMapper.selectList(queryWrapper);
            List<BizTaskWorkLogsUnstatisticDTO> BizTaskWorkLogsUnstatisticDTOList = ListCopyUtil.copy(listData, BizTaskWorkLogsUnstatisticDTO.class);
        return BizTaskWorkLogsUnstatisticDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskWorkLogsUnstatisticDTO findOne(Long id) {
        BizTaskWorkLogsUnstatistic  bizTaskWorkLogsUnstatistic =  bizTaskWorkLogsUnstatisticMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizTaskWorkLogsUnstatistic,BizTaskWorkLogsUnstatisticDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskWorkLogsUnstatisticDTO create(BizTaskWorkLogsUnstatisticDTO entityDTO) {
       BizTaskWorkLogsUnstatistic bizTaskWorkLogsUnstatistic =  BeanConvertUtils.copyProperties(entityDTO,BizTaskWorkLogsUnstatistic.class);
        save(bizTaskWorkLogsUnstatistic);
        return  BeanConvertUtils.copyProperties(bizTaskWorkLogsUnstatistic,BizTaskWorkLogsUnstatisticDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskWorkLogsUnstatisticDTO entity) {
        BizTaskWorkLogsUnstatistic bizTaskWorkLogsUnstatistic = BeanConvertUtils.copyProperties(entity,BizTaskWorkLogsUnstatistic.class);
        return bizTaskWorkLogsUnstatisticMapper.updateById(bizTaskWorkLogsUnstatistic);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskWorkLogsUnstatisticMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskWorkLogsUnstatisticId
     * @return
     */
    @Override
    public boolean existByBizTaskWorkLogsUnstatisticId(Long BizTaskWorkLogsUnstatisticId) {
        if (BizTaskWorkLogsUnstatisticId != null) {
            LambdaQueryWrapper<BizTaskWorkLogsUnstatistic> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskWorkLogsUnstatistic::getId, BizTaskWorkLogsUnstatisticId);
            List<BizTaskWorkLogsUnstatistic> result = bizTaskWorkLogsUnstatisticMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskWorkLogsUnstatisticDTO> dataList) {
        List<BizTaskWorkLogsUnstatistic> result = ListCopyUtil.copy(dataList, BizTaskWorkLogsUnstatistic.class);
        return saveBatch(result);
    }


}
