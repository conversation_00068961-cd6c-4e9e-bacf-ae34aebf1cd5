package com.ctsi.huaihua.controller;

import com.alibaba.excel.EasyExcel;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizTaskExportDTO;
import com.ctsi.huaihua.entity.dto.BizTaskImportDTO;
import com.ctsi.huaihua.service.BizTaskImportAndExportService;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname BizTaskImportAndExportController
 * @Description
 * @Date 2022/3/15/0015 14:40
 */
@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskImportAndExport")
@Api(value = "任务导入导出", tags = "任务导入导出接口")
public class BizTaskImportAndExportController {

    @Autowired
    private BizTaskImportAndExportService bizTaskImportAndExportService;

    @PostMapping("/uploadBizTask")
    @ApiOperation(value = "任务导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "任务导入")
    public ResultVO uploadDepartment(MultipartFile file) throws IOException {
        // 读取Excel数据
        List<BizTaskImportDTO> list = EasyExcel.read(file.getInputStream()).head(BizTaskImportDTO.class).sheet().doReadSync();
        // 处理校验读取的数据
        List<BizTaskExportDTO> bizTaskExportDTOList = bizTaskImportAndExportService.assemblyTaskImport(list);

        if (list.size() > bizTaskExportDTOList.size()) {
            return ResultVO.error("任务导入失败：总共" + list.size() + "条, 成功" + bizTaskExportDTOList.size() + "条", bizTaskExportDTOList);
        }
        return ResultVO.success(bizTaskExportDTOList);
    }

    @GetMapping("/downloadFeedBackZip/{id}")
    @ApiOperation(value = "成果下载", notes = "传入分解任务ID")
    public ResultVO downloadFeedBackZip(@PathVariable Long id, HttpServletResponse response) {
        bizTaskImportAndExportService.downloadFeedBackZip(id, response);
        return ResultVO.success();
    }

}
