package com.ctsi.huaihua.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.huaihua.entity.BizTaskWorkLogs;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsAnnexDTO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsDTO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsPageDTO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsSourceDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 工作日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
public interface IBizTaskWorkLogsService extends SysBaseServiceI<BizTaskWorkLogs> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskWorkLogsDTO> queryListPage(BizTaskWorkLogsPageDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskWorkLogsDTO> queryList(BizTaskWorkLogsPageDTO entity);

    /**
     * 查询承办中已接收任务数据.不分页
     *
     * @param id 已接收任务id，分解任务的id
     * @param companyId 单位id
     * @return
     */
    List<BizTaskWorkLogsSourceDTO> getBizTasksReceivedTodoList(Long id, Long companyId);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskWorkLogsAnnexDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskWorkLogsAnnexDTO create(BizTaskWorkLogsDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskWorkLogsDTO entity);

    /**
     * 删除工作日志
     *
     * @param id 工作日志id
     * @return
     */
    int delete(Long id);

    /**
     * 逻辑删除工作日志附件
     *
     * @param annexId 工作日志附件id
     * @return
     */
    int deleteAnnex(Long annexId);

     /**
     * 是否存在
     *
     * existByBizTaskWorkLogsId
     * @param code
     * @return
     */
    boolean existByBizTaskWorkLogsId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskWorkLogsDTO> dataList);


}
