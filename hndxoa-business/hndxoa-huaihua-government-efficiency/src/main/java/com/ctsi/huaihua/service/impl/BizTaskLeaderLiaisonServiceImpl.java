package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizTaskLeaderLiaison;
import com.ctsi.huaihua.entity.dto.BizTaskLeaderLiaisonDTO;
import com.ctsi.huaihua.mapper.BizTaskLeaderLiaisonMapper;
import com.ctsi.huaihua.service.IBizTaskLeaderLiaisonService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 任务领导对应的联络员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Slf4j
@Service
public class BizTaskLeaderLiaisonServiceImpl extends SysBaseServiceImpl<BizTaskLeaderLiaisonMapper, BizTaskLeaderLiaison> implements IBizTaskLeaderLiaisonService {

    @Autowired
    private BizTaskLeaderLiaisonMapper bizTaskLeaderLiaisonMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskLeaderLiaisonDTO> queryListPage(BizTaskLeaderLiaisonDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskLeaderLiaison> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskLeaderLiaison> pageData = bizTaskLeaderLiaisonMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskLeaderLiaisonDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskLeaderLiaisonDTO.class));

        return new PageResult<BizTaskLeaderLiaisonDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskLeaderLiaisonDTO> queryList(BizTaskLeaderLiaisonDTO entityDTO) {
        LambdaQueryWrapper<BizTaskLeaderLiaison> queryWrapper = new LambdaQueryWrapper();
        List<BizTaskLeaderLiaison> listData = bizTaskLeaderLiaisonMapper.selectList(queryWrapper);
        List<BizTaskLeaderLiaisonDTO> BizTaskLeaderLiaisonDTOList = ListCopyUtil.copy(listData, BizTaskLeaderLiaisonDTO.class);
        return BizTaskLeaderLiaisonDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskLeaderLiaisonDTO findOne(Long id) {
        BizTaskLeaderLiaison bizTaskLeaderLiaison = bizTaskLeaderLiaisonMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizTaskLeaderLiaison, BizTaskLeaderLiaisonDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskLeaderLiaisonDTO create(BizTaskLeaderLiaisonDTO entityDTO) {
        BizTaskLeaderLiaison bizTaskLeaderLiaison = BeanConvertUtils.copyProperties(entityDTO, BizTaskLeaderLiaison.class);
        save(bizTaskLeaderLiaison);
        return BeanConvertUtils.copyProperties(bizTaskLeaderLiaison, BizTaskLeaderLiaisonDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskLeaderLiaisonDTO entity) {
        BizTaskLeaderLiaison bizTaskLeaderLiaison = BeanConvertUtils.copyProperties(entity, BizTaskLeaderLiaison.class);
        return bizTaskLeaderLiaisonMapper.updateById(bizTaskLeaderLiaison);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskLeaderLiaisonMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskLeaderLiaisonId
     * @return
     */
    @Override
    public boolean existByBizTaskLeaderLiaisonId(Long BizTaskLeaderLiaisonId) {
        if (BizTaskLeaderLiaisonId != null) {
            LambdaQueryWrapper<BizTaskLeaderLiaison> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskLeaderLiaison::getId, BizTaskLeaderLiaisonId);
            List<BizTaskLeaderLiaison> result = bizTaskLeaderLiaisonMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskLeaderLiaisonDTO> dataList) {
        List<BizTaskLeaderLiaison> result = ListCopyUtil.copy(dataList, BizTaskLeaderLiaison.class);
        return saveBatch(result);
    }


}
