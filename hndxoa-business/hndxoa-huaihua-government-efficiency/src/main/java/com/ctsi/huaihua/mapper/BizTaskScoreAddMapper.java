package com.ctsi.huaihua.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.huaihua.entity.BizTaskScoreAdd;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionAndScoreAddDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 任务加分表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface BizTaskScoreAddMapper extends MybatisBaseMapper<BizTaskScoreAdd> {

    /**
     * 查询根据加分申请的信息查询主表信息(无单位限制)
     * 查询加分列表专用(慎用)
     * @param iPage
     * @param bizTaskSupervisionAndScoreAddDTO
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<BizTaskSupervisionAndScoreAddDTO> queryPageTaskSupervisionAndScoreAddInfo(IPage iPage, @Param("dto") BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO);

}
