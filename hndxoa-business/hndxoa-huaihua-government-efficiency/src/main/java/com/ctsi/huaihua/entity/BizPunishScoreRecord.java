package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 奖分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_punish_score_record")
@ApiModel(value="BizPunishScoreRecord对象", description="奖分记录表")
public class BizPunishScoreRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;

    /**
     * 减分年份
     */
    @ApiModelProperty(value = "减分年份")
    private Integer scoreYear;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 减分原因
     */
    @ApiModelProperty(value = "减分原因")
    private String reductionReason;

    /**
     * 审核人ID
     */
    @ApiModelProperty(value = "审核人ID")
    private Long reviewerId;

    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    private String reviewerName;

    /**
     * 审核人部门ID
     */
    @ApiModelProperty(value = "审核人部门ID")
    private Long reviewerDepartmentId;

    /**
     * 审核人单位ID
     */
    @ApiModelProperty(value = "审核人单位ID")
    private Long reviewerCompanyId;

    /**
     * 市政府办是否未读 0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "市政府办是否未读0: 未查看  1:已查看")
    private Integer bizMunicipalUnitHasRead;

    /**
     * 0：未审核 1 审核通过 2 驳回
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过 2 驳回")
    private Integer hasReviewer;

    /**
     * 驳回理由
     */
    @ApiModelProperty(value = "驳回理由")
    private String refuseReason;

    /**
     * 市直单位是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "市直单位是否未读0: 未查看  1:已查看")
    private Integer straightUnitHasRead;

    /**
     * 申请减分来源：1市政府办业务科室 2督查室 3市直单位
     */
    @ApiModelProperty(value = "申请减分来源：1市政府办业务科室 2督查室 3市直单位")
    private Integer punishResource;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewerTime;

    @ApiModelProperty(value = "创建人单位名称")
    private String createCompanyName;

}
