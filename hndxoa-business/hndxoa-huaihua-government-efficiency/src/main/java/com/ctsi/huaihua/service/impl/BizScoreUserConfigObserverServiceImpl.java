package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.service.UserInfoChangeObserverService;
import com.ctsi.huaihua.entity.BizScoreUserConfig;
import com.ctsi.huaihua.mapper.BizScoreUserConfigMapper;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/8/12
 */
@Service
public class BizScoreUserConfigObserverServiceImpl implements UserInfoChangeObserverService {

    @Autowired
    private BizScoreUserConfigMapper bizScoreUserConfigMapper;


    @Override
    public Boolean update(CscpUserDTO userDTO) {
        // 修改怀化是否考核人员姓名
        BizScoreUserConfig bizScoreUserConfig = new BizScoreUserConfig();
        bizScoreUserConfig.setUserName(userDTO.getRealName());
        bizScoreUserConfigMapper.updateTenantId(bizScoreUserConfig,
                new LambdaQueryWrapper<BizScoreUserConfig>().eq(BizScoreUserConfig::getUserId,userDTO.getId()));
        return true;
    }
}
