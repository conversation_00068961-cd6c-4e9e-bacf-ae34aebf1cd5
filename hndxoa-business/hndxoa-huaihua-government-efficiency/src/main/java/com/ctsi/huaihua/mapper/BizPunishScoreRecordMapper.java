package com.ctsi.huaihua.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.huaihua.entity.BizPunishScoreRecord;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.huaihua.entity.dto.BizPunishScoreRecordDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 奖分记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
public interface BizPunishScoreRecordMapper extends MybatisBaseMapper<BizPunishScoreRecord> {

    /**
     * @param userIds
     * @param startTime
     * @param endTime
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<BizPunishScoreRecordDTO> selectBizTaskSupervisions(@Param("userIds") Set<Long> userIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
