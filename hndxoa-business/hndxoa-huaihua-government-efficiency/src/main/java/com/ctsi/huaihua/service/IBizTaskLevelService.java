package com.ctsi.huaihua.service;

import cn.hutool.core.lang.tree.Tree;
import com.ctsi.hndx.tree.TreeSelectService;
import com.ctsi.huaihua.entity.dto.BizTaskLevelDTO;
import com.ctsi.huaihua.entity.BizTaskLevel;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface IBizTaskLevelService extends TreeSelectService<BizTaskLevelDTO,BizTaskLevel> {

    //用于区分查询的是否是本单位的数据还是其他单位的数据
      ThreadLocal<Long> longThreadLocal = new ThreadLocal<>();
    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskLevelDTO> queryListPage(BizTaskLevelDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskLevelDTO> queryList(BizTaskLevelDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskLevelDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskLevelDTO create(BizTaskLevelDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskLevelDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskLevelId
     * @param code
     * @return
     */
    boolean existByBizTaskLevelId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskLevelDTO> dataList);

    /**
     * 排序号自增
     * @param bizTaskLevelDTO
     * @return
     */
    Boolean updateOrderBy(BizTaskLevelDTO bizTaskLevelDTO);

    /**
     * 查询所有数据后组装树
     * @param tenantId
     * @return
     */
    List<Tree<String>> queryLevelByTenantId(Long tenantId);

    /**
     * 查询顶层数据
     * @return
     */
    List<BizTaskLevelDTO> getTopLevel();

}
