package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizScoreUserConfigDTO;
import com.ctsi.huaihua.service.IBizScoreUserConfigService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizScoreUserConfig")
@Api(value = "免考核人员表", tags = "免考核人员表接口")
public class BizScoreUserConfigController extends BaseController {

    private static final String ENTITY_NAME = "bizScoreUserConfig";

    @Autowired
    private IBizScoreUserConfigService bizScoreUserConfigService;


    /**
     * 新增免考核人员表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizScoreUserConfig.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增免考核人员表批量数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreUserConfig.add')")
    public ResultVO createBatch(@RequestBody List<BizScoreUserConfigDTO> bizScoreUserConfigList) {
        Boolean result = bizScoreUserConfigService.insertBatch(bizScoreUserConfigList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 是否考核设置（0：不考核 1：考核）.
     */
    @PostMapping("/create")
    @ApiOperation(value = "是否考核设置（0：不考核 1：考核） (权限code码为：cscp.bizScoreUserConfig.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增免考核人员表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreUserConfig.add')")
    public ResultVO<BizScoreUserConfigDTO> create(@RequestBody BizScoreUserConfigDTO bizScoreUserConfigDTO) {
        BizScoreUserConfigDTO result = bizScoreUserConfigService.create(bizScoreUserConfigDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizScoreUserConfig.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新免考核人员表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreUserConfig.update')")
    public ResultVO update(@RequestBody BizScoreUserConfigDTO bizScoreUserConfigDTO) {
        Assert.notNull(bizScoreUserConfigDTO.getId(), "general.IdNotNull");
        int count = bizScoreUserConfigService.update(bizScoreUserConfigDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @PostMapping("/delete")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除免考核人员表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizScoreUserConfig.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreUserConfig.delete')")
    public ResultVO delete(@RequestBody List<Long> ids) {
        int count = bizScoreUserConfigService.delete(ids);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizScoreUserConfigDTO bizScoreUserConfigDTO = bizScoreUserConfigService.findOne(id);
        return ResultVO.success(bizScoreUserConfigDTO);
    }

    /**
     * 根据用户id获取免考核人员对象.
     */
    @GetMapping("/findUserByUserIds")
    @ApiOperation(value = "根据用户id获取免考核人员对象", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizScoreUserConfigDTO>> findUserByUserIds(@RequestBody List<Long> userIds) {
        if (userIds.isEmpty()) {
            throw new BusinessException("用户id不能为空");
        }
        List<BizScoreUserConfigDTO> bizScoreUserConfigDTOList = bizScoreUserConfigService.findUserByUserId(userIds);
        return ResultVO.success(new ResResult<BizScoreUserConfigDTO>(bizScoreUserConfigDTOList));
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizScoreUserConfigPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizScoreUserConfigDTO>> queryBizScoreUserConfigPage(BizScoreUserConfigDTO bizScoreUserConfigDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizScoreUserConfigService.queryListPage(bizScoreUserConfigDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryBizScoreUserConfig")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizScoreUserConfigDTO>> queryBizScoreUserConfig(BizScoreUserConfigDTO bizScoreUserConfigDTO) {
        List<BizScoreUserConfigDTO> list = bizScoreUserConfigService.queryList(bizScoreUserConfigDTO);
        return ResultVO.success(new ResResult<BizScoreUserConfigDTO>(list));
    }

}
