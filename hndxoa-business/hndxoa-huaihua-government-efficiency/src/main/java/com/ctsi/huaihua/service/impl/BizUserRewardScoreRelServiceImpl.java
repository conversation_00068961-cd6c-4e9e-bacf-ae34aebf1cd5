package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizUserRewardScoreRel;
import com.ctsi.huaihua.entity.dto.BizUserRewardScoreRelDTO;
import com.ctsi.huaihua.mapper.BizUserRewardScoreRelMapper;
import com.ctsi.huaihua.service.IBizUserRewardScoreRelService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 用户奖励分记录关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Slf4j
@Service
public class BizUserRewardScoreRelServiceImpl extends SysBaseServiceImpl<BizUserRewardScoreRelMapper, BizUserRewardScoreRel> implements IBizUserRewardScoreRelService {

    @Autowired
    private BizUserRewardScoreRelMapper bizUserRewardScoreRelMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizUserRewardScoreRelDTO> queryListPage(BizUserRewardScoreRelDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizUserRewardScoreRel> queryWrapper = new LambdaQueryWrapper();

        IPage<BizUserRewardScoreRel> pageData = bizUserRewardScoreRelMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizUserRewardScoreRelDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizUserRewardScoreRelDTO.class));

        return new PageResult<BizUserRewardScoreRelDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizUserRewardScoreRelDTO> queryList(BizUserRewardScoreRelDTO entityDTO) {
        LambdaQueryWrapper<BizUserRewardScoreRel> queryWrapper = new LambdaQueryWrapper();
        List<BizUserRewardScoreRel> listData = bizUserRewardScoreRelMapper.selectList(queryWrapper);
        List<BizUserRewardScoreRelDTO> BizUserRewardScoreRelDTOList = ListCopyUtil.copy(listData, BizUserRewardScoreRelDTO.class);
        return BizUserRewardScoreRelDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizUserRewardScoreRelDTO findOne(Long id) {
        BizUserRewardScoreRel  bizUserRewardScoreRel =  bizUserRewardScoreRelMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizUserRewardScoreRel,BizUserRewardScoreRelDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizUserRewardScoreRelDTO create(BizUserRewardScoreRelDTO entityDTO) {
        BizUserRewardScoreRel bizUserRewardScoreRel =  BeanConvertUtils.copyProperties(entityDTO,BizUserRewardScoreRel.class);
        save(bizUserRewardScoreRel);
        return  BeanConvertUtils.copyProperties(bizUserRewardScoreRel,BizUserRewardScoreRelDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizUserRewardScoreRelDTO entity) {
        BizUserRewardScoreRel bizUserRewardScoreRel = BeanConvertUtils.copyProperties(entity,BizUserRewardScoreRel.class);
        return bizUserRewardScoreRelMapper.updateById(bizUserRewardScoreRel);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizUserRewardScoreRelMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizUserRewardScoreRelId
     * @return
     */
    @Override
    public boolean existByBizUserRewardScoreRelId(Long BizUserRewardScoreRelId) {
        if (BizUserRewardScoreRelId != null) {
            LambdaQueryWrapper<BizUserRewardScoreRel> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizUserRewardScoreRel::getId, BizUserRewardScoreRelId);
            List<BizUserRewardScoreRel> result = bizUserRewardScoreRelMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizUserRewardScoreRelDTO> dataList) {
        List<BizUserRewardScoreRel> result = ListCopyUtil.copy(dataList, BizUserRewardScoreRel.class);
        return saveBatch(result);
    }

    /**
     * 根据被申请人的单位ID查询数据
     * @param companyId
     * @return
     */
    @Override
    public List<BizUserRewardScoreRelDTO> queryListByCompanyId(Long companyId) {
        if (Objects.isNull(companyId)) {
            throw new BusinessException("查询被奖励人的单位ID不允许为空");
        }
        LambdaQueryWrapper<BizUserRewardScoreRel> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizUserRewardScoreRel::getCompanyId, companyId);
        List<BizUserRewardScoreRel> listData = bizUserRewardScoreRelMapper.selectListNoAdd(queryWrapper);
        List<BizUserRewardScoreRelDTO> BizUserRewardScoreRelDTOList = ListCopyUtil.copy(listData, BizUserRewardScoreRelDTO.class);
        return BizUserRewardScoreRelDTOList;
    }


    /**
     * 根据被奖励人的单位ID查询数据
     * @param companyId
     * @return
     */
    @Override
    public List<BizUserRewardScoreRelDTO> queryListByRewardCompanyId(Long companyId) {
        if (Objects.isNull(companyId)) {
            throw new BusinessException("查询被奖励人的单位ID不允许为空");
        }
        LambdaQueryWrapper<BizUserRewardScoreRel> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizUserRewardScoreRel::getRewardCompanyId, companyId);
        List<BizUserRewardScoreRel> listData = bizUserRewardScoreRelMapper.selectListNoAdd(queryWrapper);
        List<BizUserRewardScoreRelDTO> BizUserRewardScoreRelDTOList = ListCopyUtil.copy(listData, BizUserRewardScoreRelDTO.class);
        return BizUserRewardScoreRelDTOList;
    }


    /**
     * 根据被奖励人的用户ID查询数据
     * @param userId
     * @return
     */
    @Override
    public List<BizUserRewardScoreRelDTO> queryListByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("查询被奖励人的用户ID不允许为空");
        }
        LambdaQueryWrapper<BizUserRewardScoreRel> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizUserRewardScoreRel::getUserId, userId);
        List<BizUserRewardScoreRel> listData = bizUserRewardScoreRelMapper.selectListNoAdd(queryWrapper);
        List<BizUserRewardScoreRelDTO> BizUserRewardScoreRelDTOList = ListCopyUtil.copy(listData, BizUserRewardScoreRelDTO.class);
        return BizUserRewardScoreRelDTOList;
    }
}
