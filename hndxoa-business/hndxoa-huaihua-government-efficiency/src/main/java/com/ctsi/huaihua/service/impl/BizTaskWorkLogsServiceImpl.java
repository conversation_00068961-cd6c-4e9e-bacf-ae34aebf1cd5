package com.ctsi.huaihua.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.huaihua.entity.BizTaskWorkLogs;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.huaihua.mapper.BizTaskWorkLogsMapper;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.huaihua.service.IBizTaskWorkLogsService;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserOrgDTO;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.system.entity.dto.TSysDictRecordDTO;
import com.ctsi.system.service.ITSysDictRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Slf4j
@Service
public class BizTaskWorkLogsServiceImpl extends SysBaseServiceImpl<BizTaskWorkLogsMapper, BizTaskWorkLogs> implements IBizTaskWorkLogsService {

    @Autowired
    private BizTaskWorkLogsMapper bizTaskWorkLogsMapper;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;

    @Autowired
    private IBizTaskDecomposeService bizTaskDecomposeService;

    @Autowired
    private ITSysDictRecordService tSysDictRecordService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskWorkLogsDTO> queryListPage(BizTaskWorkLogsPageDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskWorkLogs> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskWorkLogs::getCreateBy, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(entityDTO.getWorkType()!=null && StringUtils.isNotEmpty(entityDTO.getWorkType()),BizTaskWorkLogs::getWorkType, entityDTO.getWorkType() );
        queryWrapper.eq(entityDTO.getWorkSource()!=null && StringUtils.isNotEmpty(entityDTO.getWorkSource()),BizTaskWorkLogs::getWorkSource, entityDTO.getWorkSource() );
        queryWrapper.eq(entityDTO.getIsTodoTask()!=null,BizTaskWorkLogs::getIsTodoTask, entityDTO.getIsTodoTask() );
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getContent()),BizTaskWorkLogs::getContent, entityDTO.getContent());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizTaskWorkLogs::getStartTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizTaskWorkLogs::getEndTime, entityDTO.getEndTime());
        queryWrapper.orderByDesc(BizTaskWorkLogs::getCreateTime);
        IPage<BizTaskWorkLogs> pageData = bizTaskWorkLogsMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskWorkLogsDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskWorkLogsDTO.class));

        return new PageResult<BizTaskWorkLogsDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskWorkLogsDTO> queryList(BizTaskWorkLogsPageDTO entityDTO) {
        LambdaQueryWrapper<BizTaskWorkLogs> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskWorkLogs::getCreateBy, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(entityDTO.getWorkType()!=null && StringUtils.isNotEmpty(entityDTO.getWorkType()),BizTaskWorkLogs::getWorkType, entityDTO.getWorkType() );
        queryWrapper.eq(entityDTO.getWorkSource()!=null && StringUtils.isNotEmpty(entityDTO.getWorkSource()),BizTaskWorkLogs::getWorkSource, entityDTO.getWorkSource() );
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getContent()),BizTaskWorkLogs::getContent, entityDTO.getContent());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizTaskWorkLogs::getStartTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizTaskWorkLogs::getEndTime, entityDTO.getEndTime());
        queryWrapper.orderByDesc(BizTaskWorkLogs::getCreateTime);
        List<BizTaskWorkLogs> listData = bizTaskWorkLogsMapper.selectList(queryWrapper);
        List<BizTaskWorkLogsDTO> BizTaskWorkLogsDTOList = ListCopyUtil.copy(listData, BizTaskWorkLogsDTO.class);
        return BizTaskWorkLogsDTOList;
    }

    /**
     * 查询承办中已接收任务数据
     *
     * @param id 已接收任务id，分解任务的id
     * @param companyId 单位id
     * @return
     */
    @Override
    public List<BizTaskWorkLogsSourceDTO> getBizTasksReceivedTodoList(Long id, Long companyId) {
        long currentUserId = SecurityUtils.getCurrentUserId();
        CscpUserDetail userDetail = SecurityUtils.getCurrentCscpUserDetail();
        List<BizTaskWorkLogsSourceDTO> bizTaskWorkLogsSourceDTOList = new ArrayList<>();
        // 查询承办任务中已接受任务列表
        BizTaskDTO bizTaskDTO = new BizTaskDTO();
        bizTaskDTO.setDutyPeopleId(currentUserId);
        bizTaskDTO.setTaskDecomposeId(id);
        bizTaskDTO.setSortField("receive_time");
        bizTaskDTO.setSortType("desc");
        bizTaskDTO.setHasSign(1);
        List<BizTaskDTO> taskList = bizTaskDecomposeService.queryListNoPage(bizTaskDTO);
        List<TSysDictRecordDTO> taskSource = tSysDictRecordService.getDictRecordListByDictCode("taskSource", userDetail.getCompanyId());
        List<TSysDictRecordDTO> taskType = tSysDictRecordService.getDictRecordListByDictCode("taskType", userDetail.getCompanyId());
        for (BizTaskDTO entity : taskList) {
            BizTaskWorkLogsSourceDTO taskWorkLogSource = new BizTaskWorkLogsSourceDTO();
            taskWorkLogSource.setId(entity.getTaskDecomposeId());
            taskWorkLogSource.setValue(entity.getTaskDecomposeId());
            taskWorkLogSource.setIsTodoTask(1);
            taskType.stream().forEach(i -> {
                if (i.getCode().equals(entity.getTaskType())) {
                    taskWorkLogSource.setWorkType(i.getCode());
                }
            });
            taskSource.stream().forEach(i -> {
                if (i.getCode().equals(entity.getTaskSource())) {
                    taskWorkLogSource.setWorkSource(i.getCode());
                }
            });
            taskWorkLogSource.setContent(entity.getContent());
            taskWorkLogSource.setLabel(entity.getContent());
            taskWorkLogSource.setDepartmentName(userDetail.getDepartmentName());
            taskWorkLogSource.setCreateName(userDetail.getRealName());
            taskWorkLogSource.setCreateBy(currentUserId);
            // taskWorkLogSource.setEnclosureFileList(cscpEnclosureFileService.getFormFiles(entity.getTaskSupervisionId()));
            bizTaskWorkLogsSourceDTOList.add(taskWorkLogSource);
        }
        return bizTaskWorkLogsSourceDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskWorkLogsAnnexDTO findOne(Long id) {
        BizTaskWorkLogs bizTaskWorkLogs = bizTaskWorkLogsMapper.selectById(id);
        BizTaskWorkLogsAnnexDTO bizTaskWorkLogsAnnexDTO = BeanConvertUtils.copyProperties(bizTaskWorkLogs, BizTaskWorkLogsAnnexDTO.class);
        // 查询所有附件
        List<CscpEnclosureFile> formFiles = cscpEnclosureFileService.getFormFiles(id);
        bizTaskWorkLogsAnnexDTO.setEnclosureFileList(formFiles);
        return bizTaskWorkLogsAnnexDTO;
    }


    /**
     * 补充工作日志附件信息
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    public BizTaskWorkLogsAnnexDTO create(BizTaskWorkLogsDTO entityDTO) {
        BizTaskWorkLogs bizTaskWorkLogs = BeanConvertUtils.copyProperties(entityDTO, BizTaskWorkLogs.class);
        // 查询用户组织机构表，获取用户信息
        CscpUserDetail currentUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        List<CscpUserOrgDTO> cscpUserOrgDTOS = cscpUserOrgService.qryUserOrgByUserId(currentUserDetail.getId());
        List<String> userPosts = cscpUserOrgDTOS.stream().filter(i -> i.getCompanyId().equals(currentUserDetail.getCompanyId())).map(i -> i.getPost()).collect(Collectors.toList());
        // 查询用户职务
        String userPost = CollectionUtil.isNotEmpty(userPosts) ? userPosts.get(0) : "";

        // 添加用户职务、单位名称、部门名称
        bizTaskWorkLogs.setPost(userPost);
        bizTaskWorkLogs.setCompanyName(currentUserDetail.getCompanyName());
        bizTaskWorkLogs.setDepartmentName(currentUserDetail.getDepartmentName());

        // 保存工作日志
        BizTaskWorkLogsAnnexDTO bizTaskWorkLogsAnnexDTO = this.saveTO(bizTaskWorkLogs);

        // 查询附件，当 annex 不为0
        List<CscpEnclosureFile> formFiles = null;
        if (entityDTO.getAnnex()==1) {
            // 关联工作日志和附件
            formFiles = cscpEnclosureFileService.getFormFiles(entityDTO.getId());
        }
        bizTaskWorkLogsAnnexDTO.setEnclosureFileList(formFiles);
        return bizTaskWorkLogsAnnexDTO;
    }


    /**
     * 保存新增的工作日志
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizTaskWorkLogsAnnexDTO saveTO(BizTaskWorkLogs entityDTO) {
        save(entityDTO);
        return BeanConvertUtils.copyProperties(entityDTO, BizTaskWorkLogsAnnexDTO.class);
    }


    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskWorkLogsDTO entity) {
        BizTaskWorkLogs bizTaskWorkLogs = BeanConvertUtils.copyProperties(entity, BizTaskWorkLogs.class);
        return bizTaskWorkLogsMapper.updateById(bizTaskWorkLogs);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskWorkLogsMapper.deleteById(id);
    }

    /**
     * 逻辑删除工作日志附件
     *
     * @param annexId 工作日志附件id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAnnex(Long annexId) {
        CscpEnclosureFile enclosureFile = cscpEnclosureFileService.getById(annexId);
        if (enclosureFile == null) {
            throw new BusinessException("附件不存在");
        }
        List<CscpEnclosureFile> formFiles = cscpEnclosureFileService.getFormFiles(enclosureFile.getFormDataId());
        List<Long> annexIds = formFiles.stream().filter(i -> !i.getId().equals(annexId)).map(i -> i.getId()).collect(Collectors.toList());
        BizTaskWorkLogs bizTaskWorkLogs = new BizTaskWorkLogs();
        bizTaskWorkLogs.setId(enclosureFile.getFormDataId());
        bizTaskWorkLogs.setAnnex(CollectionUtil.isEmpty(annexIds) ? 0 : 1);
        cscpEnclosureFileService.delete(annexId);
        return updateById(bizTaskWorkLogs) ? 1 : 0;
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskWorkLogsId
     * @return
     */
    @Override
    public boolean existByBizTaskWorkLogsId(Long BizTaskWorkLogsId) {
        if (BizTaskWorkLogsId != null) {
            LambdaQueryWrapper<BizTaskWorkLogs> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskWorkLogs::getId, BizTaskWorkLogsId);
            List<BizTaskWorkLogs> result = bizTaskWorkLogsMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskWorkLogsDTO> dataList) {
        List<BizTaskWorkLogs> result = ListCopyUtil.copy(dataList, BizTaskWorkLogs.class);
        return saveBatch(result);
    }


}
