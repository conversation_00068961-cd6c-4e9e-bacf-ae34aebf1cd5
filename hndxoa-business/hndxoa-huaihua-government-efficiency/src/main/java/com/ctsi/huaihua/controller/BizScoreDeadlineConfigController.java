package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizScoreDeadlineConfigDTO;
import com.ctsi.huaihua.service.IBizScoreDeadlineConfigService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizScoreDeadlineConfig")
@Api(value = "日常工作打分截止时间设置", tags = "日常工作打分截止时间设置接口")
public class BizScoreDeadlineConfigController extends BaseController {

    private static final String ENTITY_NAME = "bizScoreDeadlineConfig";

    @Autowired
    private IBizScoreDeadlineConfigService bizScoreDeadlineConfigService;



    /**
     *  新增日常工作打分截止时间设置批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizScoreDeadlineConfig.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增日常工作打分截止时间设置批量数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreDeadlineConfig.add')")
    public ResultVO createBatch(@RequestBody List<BizScoreDeadlineConfigDTO> bizScoreDeadlineConfigList) {
       Boolean  result = bizScoreDeadlineConfigService.insertBatch(bizScoreDeadlineConfigList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizScoreDeadlineConfig.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增日常工作打分截止时间设置数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreDeadlineConfig.add')")
    public ResultVO<BizScoreDeadlineConfigDTO> create(@Valid @RequestBody BizScoreDeadlineConfigDTO bizScoreDeadlineConfigDTO)  {
        BizScoreDeadlineConfigDTO result = bizScoreDeadlineConfigService.create(bizScoreDeadlineConfigDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizScoreDeadlineConfig.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新日常工作打分截止时间设置数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreDeadlineConfig.update')")
    public ResultVO update(@Valid @RequestBody BizScoreDeadlineConfigDTO bizScoreDeadlineConfigDTO) {
	    Assert.notNull(bizScoreDeadlineConfigDTO.getId(), "general.IdNotNull");
        int count = bizScoreDeadlineConfigService.update(bizScoreDeadlineConfigDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除日常工作打分截止时间设置数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizScoreDeadlineConfig.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreDeadlineConfig.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizScoreDeadlineConfigService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  是否允许打分.
     */
    @GetMapping("/enableMark/{scoreMonth}")
    @ApiOperation(value = "是否允许打分", notes = "传入鱼粉参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizScoreDeadlineConfig.edit')")
    public ResultVO enableMark(@PathVariable Integer scoreMonth) {
        return ResultVO.success(bizScoreDeadlineConfigService.enableMark(scoreMonth));
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizScoreDeadlineConfigDTO bizScoreDeadlineConfigDTO = bizScoreDeadlineConfigService.findOne(id);
        return ResultVO.success(bizScoreDeadlineConfigDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizScoreDeadlineConfigPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizScoreDeadlineConfigDTO>> queryBizScoreDeadlineConfigPage(BizScoreDeadlineConfigDTO bizScoreDeadlineConfigDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizScoreDeadlineConfigService.queryListPage(bizScoreDeadlineConfigDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizScoreDeadlineConfig")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizScoreDeadlineConfigDTO>> queryBizScoreDeadlineConfig(BizScoreDeadlineConfigDTO bizScoreDeadlineConfigDTO) {
       List<BizScoreDeadlineConfigDTO> list = bizScoreDeadlineConfigService.queryList(bizScoreDeadlineConfigDTO);
       return ResultVO.success(new ResResult<BizScoreDeadlineConfigDTO>(list));
   }

}
