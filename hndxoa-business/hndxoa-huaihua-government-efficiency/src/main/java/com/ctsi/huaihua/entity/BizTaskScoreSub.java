package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务减分表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_score_sub")
@ApiModel(value = "BizTaskScoreSub对象", description = "任务减分表")
public class BizTaskScoreSub extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 申请减分的内容
     */
    @ApiModelProperty(value = "申请减分的内容")
    private String soreSubContent;

    /**
     * 申请减分的理由
     */
    @ApiModelProperty(value = "申请减分的理由")
    private String soreSubReason;

    /**
     * 申请减分的分数
     */
    @ApiModelProperty(value = "申请减分的分数")
    private Double sores;

    /**
     * 减回理由
     */
    @ApiModelProperty(value = "减回理由")
    private String refuseReason;

    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecompose;


}
