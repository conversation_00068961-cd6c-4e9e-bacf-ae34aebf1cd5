package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizUserPunishScoreRelDTO;
import com.ctsi.huaihua.entity.BizUserPunishScoreRel;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 用户惩罚分记录关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
public interface IBizUserPunishScoreRelService extends SysBaseServiceI<BizUserPunishScoreRel> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizUserPunishScoreRelDTO> queryListPage(BizUserPunishScoreRelDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizUserPunishScoreRelDTO> queryList(BizUserPunishScoreRelDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizUserPunishScoreRelDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizUserPunishScoreRelDTO create(BizUserPunishScoreRelDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizUserPunishScoreRelDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizUserPunishScoreRelId
     * @param code
     * @return
     */
    boolean existByBizUserPunishScoreRelId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizUserPunishScoreRelDTO> dataList);


}
