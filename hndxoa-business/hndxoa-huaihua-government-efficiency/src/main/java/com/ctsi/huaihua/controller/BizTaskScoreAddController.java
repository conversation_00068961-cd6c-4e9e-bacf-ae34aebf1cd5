package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.BizTaskScoreAdd;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.entity.dto.BizTaskScoreAddDTO;
import com.ctsi.huaihua.entity.dto.BizTaskScoreAddDetailDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionAndScoreAddDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import com.ctsi.huaihua.service.IBizTaskScoreAddService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskScoreAdd")
@Api(value = "任务加分表", tags = "任务加分表接口")
public class BizTaskScoreAddController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskScoreAdd";

    @Autowired
    private IBizTaskScoreAddService bizTaskScoreAddService;



    /**
     *  新增任务加分表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskScoreAdd.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务加分表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScoreAdd.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskScoreAddDTO> bizTaskScoreAddList) {
       Boolean  result = bizTaskScoreAddService.insertBatch(bizTaskScoreAddList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskScoreAdd.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务加分表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScoreAdd.add')")
    public ResultVO<BizTaskScoreAddDTO> create(@RequestBody BizTaskScoreAddDTO bizTaskScoreAddDTO)  {
        BizTaskScoreAddDTO result = bizTaskScoreAddService.create(bizTaskScoreAddDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskScoreAdd.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新任务加分表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScoreAdd.update')")
    public ResultVO update(@RequestBody BizTaskScoreAddDTO bizTaskScoreAddDTO) {
	    Assert.notNull(bizTaskScoreAddDTO.getId(), "general.IdNotNull");
        int count = bizTaskScoreAddService.update(bizTaskScoreAddDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除任务加分表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskScoreAdd.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScoreAdd.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskScoreAddService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskScoreAddDTO bizTaskScoreAddDTO = bizTaskScoreAddService.findOne(id);
        return ResultVO.success(bizTaskScoreAddDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskScoreAddPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskScoreAddDTO>> queryBizTaskScoreAddPage(BizTaskScoreAddDTO bizTaskScoreAddDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskScoreAddService.queryListPage(bizTaskScoreAddDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskScoreAdd")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskScoreAddDTO>> queryBizTaskScoreAdd(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
       List<BizTaskScoreAddDTO> list = bizTaskScoreAddService.queryList(bizTaskScoreAddDTO);
       return ResultVO.success(new ResResult<BizTaskScoreAddDTO>(list));
   }

    @PostMapping("/approvedBizTaskScoreAdd")
    @ApiOperation(value = "任务加分审核", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "延期申请审核通过")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Boolean> approvedBizTaskScoreAdd(@RequestBody BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        Boolean bool = bizTaskScoreAddService.approvedBizTaskScoreAdd(bizTaskScoreAddDTO);
        return ResultVO.success(bool);
    }

    @PostMapping("/batchApprovedBizTaskScoreAdd")
    @ApiOperation(value = "批量任务加分审核", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "延期申请审核通过")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Boolean> batchApprovedBizTaskScoreAdd(@RequestBody BizTaskScoreAddDTO bizTaskScoreAddDTO) {
       List<Long> idList = bizTaskScoreAddDTO.getIdList();
       for (Long id : idList) {
           BizTaskScoreAddDTO dto = new BizTaskScoreAddDTO();
           dto.setId(id);
           dto.setHasReviewer(bizTaskScoreAddDTO.getHasReviewer());
           dto.setRefuseReason(bizTaskScoreAddDTO.getRefuseReason());
           bizTaskScoreAddService.approvedBizTaskScoreAdd(dto);
       }
       return ResultVO.success(true);
    }

    @PostMapping("/queryPageScoreAddList")
    @ApiOperation(value = "查询加分申请列表",notes = "传入参数")
    public ResultVO<PageResult<BizTaskSupervisionAndScoreAddDTO>> queryPageScoreAddList(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        PageResult<BizTaskSupervisionAndScoreAddDTO> data = bizTaskScoreAddService.queryPageScoreAddList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        return ResultVO.success(data);
    }

    @PostMapping("/queryPageScoreAddReviewerListApp")
    @ApiOperation(value = "查询加分申请列表(手机端用(审核人))",notes = "传入参数")
    public ResultVO<PageResult<BizTaskSupervisionAndScoreAddDTO>> queryPageScoreAddReviewerListApp(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        bizTaskSupervisionAndScoreAddDTO.setScoreAddReviewerId(SecurityUtils.getCurrentUserId());
        PageResult<BizTaskSupervisionAndScoreAddDTO> data = bizTaskScoreAddService.queryPageScoreAddList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        return ResultVO.success(data);
    }

    @PostMapping("/queryPageScoreAddCreateListApp")
    @ApiOperation(value = "查询加分申请列表(手机端用(申请人))",notes = "传入参数")
    public ResultVO<PageResult<BizTaskSupervisionAndScoreAddDTO>> queryPageScoreAddCreateListApp(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        bizTaskSupervisionAndScoreAddDTO.setScoreAddCreateId(SecurityUtils.getCurrentUserId());
        PageResult<BizTaskSupervisionAndScoreAddDTO> data = bizTaskScoreAddService.queryPageScoreAddList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        return ResultVO.success(data);
    }

    @GetMapping("/queryScoreDetail")
    @ApiOperation(value = "查询加分详情", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<BizTaskScoreAddDetailDTO> queryScoreDetail(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        BizTaskScoreAddDetailDTO bizTaskScoreAddDetailDTO = bizTaskScoreAddService.queryScoreDetail(bizTaskScoreAddDTO);
        return ResultVO.success(bizTaskScoreAddDetailDTO);
    }

    @GetMapping("/queryAndReadScoreAddDetail")
    @ApiOperation(value = "查询加分申请详情(会修改加分申请为已阅状态)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<BizTaskScoreAddDetailDTO> queryAndReadScoreAddDetail(BizTaskScoreAddDTO bizTaskScoreAddDTO) {
        BizTaskScoreAddDetailDTO bizTaskScoreAddDetailDTO = bizTaskScoreAddService.queryAndReadScoreAddDetail(bizTaskScoreAddDTO);
        return ResultVO.success(bizTaskScoreAddDetailDTO);
    }

    /**
     * 查询加分申请列表(会修改加分申请为已阅状态)
     */
    @PostMapping("/queryAndReadPageScoreAddList")
    @ApiOperation(value = "查询加分申请列表(会修改加分申请为已阅状态)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskSupervisionAndScoreAddDTO>> queryAndReadPageScoreAddList(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        PageResult<BizTaskSupervisionAndScoreAddDTO> data = bizTaskScoreAddService.queryAndReadPageScoreAddList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        return ResultVO.success(data);
    }

    /**
     * 查询加分申请列表(会修改加分申请为已阅状态)
     */
    @PostMapping("/queryAndReadPageScoreAddCreateList")
    @ApiOperation(value = "查询加分申请列表(会修改加分申请为已阅状态)(申请人)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskSupervisionAndScoreAddDTO>> queryAndReadPageScoreAddCreateList(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        bizTaskSupervisionAndScoreAddDTO.setScoreAddCreateId(SecurityUtils.getCurrentUserId());
        PageResult<BizTaskSupervisionAndScoreAddDTO> data = bizTaskScoreAddService.queryAndReadPageScoreAddList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        return ResultVO.success(data);
    }

    /**
     * 查询加分申请列表(会修改加分申请为已阅状态)
     */
    @PostMapping("/queryAndReadPageScoreAddReviewerList")
    @ApiOperation(value = "查询加分申请列表(会修改加分申请为已阅状态)(审核人)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskSupervisionAndScoreAddDTO>> queryAndReadPageScoreAddReviewerList(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        bizTaskSupervisionAndScoreAddDTO.setScoreAddReviewerId(SecurityUtils.getCurrentUserId());
        PageResult<BizTaskSupervisionAndScoreAddDTO> data = bizTaskScoreAddService.queryAndReadPageScoreAddList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        return ResultVO.success(data);
    }

    @GetMapping("/queryScoreAddNeedReviewAmount")
    @ApiOperation(value = "需要加分审核数量", notes = "传入参数")
    public ResultVO<Integer> queryScoreAddNeedReviewAmount() {
        BizTaskScoreAddDTO bizTaskScoreAddDTO = new BizTaskScoreAddDTO();
        bizTaskScoreAddDTO.setReviewerId(SecurityUtils.getCurrentUserId());
        bizTaskScoreAddDTO.setHasReviewer(0);
        Integer count = bizTaskScoreAddService.queryScoreAddNeedReviewAmount(bizTaskScoreAddDTO);
        return ResultVO.success(count);
    }

    @GetMapping("/queryScoreAddHasReviewedAmount")
    @ApiOperation(value = "提醒加分申请通过情况(按加分申请计算)", notes = "传入参数")
    public ResultVO<Integer> queryScoreAddHasReviewedAmount() {
        BizTaskScoreAddDTO bizTaskScoreAddDTO = new BizTaskScoreAddDTO();
        bizTaskScoreAddDTO.setCreateBy(SecurityUtils.getCurrentUserId());
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        bizTaskScoreAddDTO.setHasReviewerList(list);
        bizTaskScoreAddDTO.setHasRead(0);
        Integer count = bizTaskScoreAddService.queryScoreAddHasReviewedAmount(bizTaskScoreAddDTO);
        return ResultVO.success(count);
    }

    @GetMapping("/queryScoreAddHasReviewedAmountBySubTask")
    @ApiOperation(value = "提醒加分申请通过情况(按分解任务计算)", notes = "传入参数")
    public ResultVO<Integer> queryScoreAddHasReviewedAmountBySubTask() {
        Integer count = bizTaskScoreAddService.queryScoreAddHasReviewedAmountBySubTask();
        return ResultVO.success(count);
    }

    @PostMapping("/queryScoreDetailList")
    @ApiOperation(value = "查询分解表对应的加分申请列表", notes = "传入参数")
    public ResultVO<PageResult<BizTaskScoreAdd>> queryScoreDetailList(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        PageResult<BizTaskScoreAdd> result = bizTaskScoreAddService.queryScoreDetailList(bizTaskSupervisionAndScoreAddDTO,basePageForm);
        return ResultVO.success(result);
    }

    @PostMapping("/queryAndReadScoreDetailListCreate")
    @ApiOperation(value = "查询分解表对应的加分申请列表(会修改加分申请为已阅状态)(申请人)", notes = "传入参数")
    public ResultVO<PageResult<BizTaskScoreAdd>> queryAndReadScoreDetailListCreate(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        PageResult<BizTaskScoreAdd> result = bizTaskScoreAddService.queryAndReadScoreDetailListCreate(bizTaskSupervisionAndScoreAddDTO,basePageForm);
        return ResultVO.success(result);
    }

    @PostMapping("/queryAndReadScoreDetailListAudit")
    @ApiOperation(value = "查询分解表对应的加分申请列表(会修改加分申请为已阅状态)(审核人)", notes = "传入参数")
    public ResultVO<PageResult<BizTaskScoreAdd>> queryAndReadScoreDetailListCreateAudit(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        PageResult<BizTaskScoreAdd> result = bizTaskScoreAddService.queryAndReadScoreDetailListAudit(bizTaskSupervisionAndScoreAddDTO,basePageForm);
        return ResultVO.success(result);
    }

    /**
     * 查询带加分申请的分解任务(申请人)
     */
    @PostMapping("/getAllTheAddTasksCreate")
    @ApiOperation(value = "查询分解任务,分页", notes = "请传入主任务id参数")
    public ResultVO<PageResult<BizTaskDTO>> getAllTheAddTasksCreate(@RequestBody BizTaskDTO dto, BasePageForm basePageForm) {
        dto.setDutyPeopleId(SecurityUtils.getCurrentUserId());
        basePageForm.setPageSize(dto.getPageSize());
        basePageForm.setCurrentPage(dto.getCurrentPage());
        PageResult<BizTaskDTO> list = bizTaskScoreAddService.getAllTheAddTasks(dto, basePageForm);
        return ResultVO.success(list);
    }

    /**
     * 查询带加分申请的分解任务(审核人)
     */
    @PostMapping("/getAllTheAddTasksReviewer")
    @ApiOperation(value = "查询分解任务,分页", notes = "请传入主任务id参数")
    public ResultVO<PageResult<BizTaskDTO>> getAllTheAddTasksReviewer(@RequestBody BizTaskDTO dto, BasePageForm basePageForm) {
        dto.setAuditId(SecurityUtils.getCurrentUserId());
        basePageForm.setPageSize(dto.getPageSize());
        basePageForm.setCurrentPage(dto.getCurrentPage());
        PageResult<BizTaskDTO> list = bizTaskScoreAddService.getAllTheAddTasks(dto, basePageForm);
        return ResultVO.success(list);
    }


    /**
     * 根据主表主键ID查询主表任务、分解表任务和加分申请、任务减分
     */
    @PostMapping("/querySupervisionAndScoreBySupervisionId")
    @ApiOperation(value = "根据主表主键ID查询主表任务、分解表任务和加分申请、任务减分", notes = "请传入主任务id参数")
    public ResultVO<BizTaskSupervisionDTO> querySupervisionAndScoreBySupervisionId(@RequestBody BizTaskSupervisionDTO supervisionDTO) {
        BizTaskSupervisionDTO bizTaskSupervisionDTO = bizTaskScoreAddService.querySupervisionAndScoreBySupervisionId(supervisionDTO);
        return ResultVO.success(bizTaskSupervisionDTO);
    }

    /**
     * 根据主表主键ID查询主表任务、分解表任务和待审核的加分申请
     */
    @PostMapping("/querySupervisionAndNotReviewedScoreBySupervisionId")
    @ApiOperation(value = "根据主表主键ID查询主表任务、分解表任务和待审核的加分申请", notes = "请传入主任务id参数")
    public ResultVO<BizTaskSupervisionDTO> querySupervisionAndNotReviewedScoreBySupervisionId(@RequestBody BizTaskSupervisionDTO supervisionDTO) {
        List<Integer> scoreReviewedStatusList = new ArrayList<>();
        scoreReviewedStatusList.add(0);
        supervisionDTO.setScoreReviewedStatusList(scoreReviewedStatusList);
        BizTaskSupervisionDTO bizTaskSupervisionDTO = bizTaskScoreAddService.querySupervisionAndScoreBySupervisionId(supervisionDTO);
        return ResultVO.success(bizTaskSupervisionDTO);
    }
}
