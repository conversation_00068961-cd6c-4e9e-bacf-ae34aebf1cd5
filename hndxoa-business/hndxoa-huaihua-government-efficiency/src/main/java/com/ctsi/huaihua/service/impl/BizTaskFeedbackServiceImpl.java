package com.ctsi.huaihua.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.DatabaseTypeUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.BizTaskFeedback;
import com.ctsi.huaihua.entity.BizTaskScore;
import com.ctsi.huaihua.entity.BizTaskSupervision;
import com.ctsi.huaihua.entity.dto.BizHomeTaskDTO;
import com.ctsi.huaihua.entity.dto.BizHomeTaskPackagingDTO;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.entity.dto.BizTaskFeedbackDTO;
import com.ctsi.huaihua.mapper.BizTaskDecomposeMapper;
import com.ctsi.huaihua.mapper.BizTaskFeedbackMapper;
import com.ctsi.huaihua.mapper.BizTaskSupervisionMapper;
import com.ctsi.huaihua.service.IBizTaskFeedbackService;
import com.ctsi.huaihua.service.IBizTaskScoreService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务成果反馈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Slf4j
@Service
public class BizTaskFeedbackServiceImpl extends SysBaseServiceImpl<BizTaskFeedbackMapper, BizTaskFeedback> implements IBizTaskFeedbackService {

    @Autowired
    private BizTaskFeedbackMapper bizTaskFeedbackMapper;

    @Autowired
    private BizTaskDecomposeMapper bizTaskDecomposeMapper;

    @Autowired
    private com.ctsi.huaihua.service.IBizTaskSupervisionService bizTaskSupervisionService;


    @Autowired
    private com.ctsi.huaihua.service.IBizTaskDecomposeService bizTaskDecomposeService;

    @Autowired
    private com.ctsi.huaihua.service.IBizTaskFeedbackTempService bizTaskFeedbackTempService;

    @Autowired
    private IBizTaskScoreService bizTaskScoreService;

    @Autowired
    private BizTaskSupervisionMapper bizTaskSupervisionMapper;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private DatabaseTypeUtils databaseTypeUtils;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskFeedbackDTO> queryListPage(BizTaskFeedbackDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskFeedback> pageData = bizTaskFeedbackMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskFeedbackDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizTaskFeedbackDTO.class));

        return new PageResult<BizTaskFeedbackDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskFeedbackDTO> queryList(BizTaskFeedbackDTO entityDTO) {
        LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
            List<BizTaskFeedback> listData = bizTaskFeedbackMapper.selectList(queryWrapper);
            List<BizTaskFeedbackDTO> BizTaskFeedbackDTOList = ListCopyUtil.copy(listData, BizTaskFeedbackDTO.class);
        return BizTaskFeedbackDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskFeedbackDTO findOne(Long id) {
        BizTaskFeedback  bizTaskFeedback =  bizTaskFeedbackMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizTaskFeedback,BizTaskFeedbackDTO.class);
    }


    /**
     * 新增
     * 办结的任务不能反馈
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskFeedbackDTO create(BizTaskFeedbackDTO entityDTO) {
        // 办结的任务不能反馈 校验;

        Long bizTaskDecomposeId = entityDTO.getBizTaskDecompose();
        BizTaskDecompose decompose = bizTaskDecomposeMapper.selectById(bizTaskDecomposeId);
        if(decompose == null   ){ // 办结
            throw new BusinessException(" 请输入分解表的主键id ");
        }
        if(ObjectUtil.equal(decompose.getHasFinish(),1)){ // 已办结
            throw new BusinessException(" 该任务已经办结,无法反馈! ");
        }
        Long id = entityDTO.getId();
        if(id != null ){ // 删除临时表数据
            bizTaskFeedbackTempService.delete(id);
        }

        BizTaskFeedback bizTaskFeedback =  BeanConvertUtils.copyProperties(entityDTO,BizTaskFeedback.class);
        Long deid = entityDTO.getBizTaskDecompose();
        Long bizTaskSupervisionId = entityDTO.getBizTaskSupervisionId();

        BizTaskDecompose bizTaskDecompose = new BizTaskDecompose();
        bizTaskDecompose.setId(deid);
        bizTaskDecompose.setHasFeedback(1);
        bizTaskDecompose.setNewFeedbackSign(1); // 最新反馈数据 标记

        bizTaskDecompose.setNewFeedbackTime(LocalDateTime.now()); // 最新反馈时间
        bizTaskDecomposeService.saveOrUpdate(bizTaskDecompose); // 更新 分解任务
        BizTaskSupervision supervision =new BizTaskSupervision();
        supervision.setId(bizTaskSupervisionId);
        supervision.setNewSupFeedbackSign(1);
        supervision.setSupHasFeedback(1);   // 任务有反馈
        bizTaskSupervisionService.saveOrUpdate(supervision); // 更新 主任务
        save(bizTaskFeedback);
        return  BeanConvertUtils.copyProperties(bizTaskFeedback,BizTaskFeedbackDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskFeedbackDTO entity) {
        BizTaskFeedback bizTaskFeedback = BeanConvertUtils.copyProperties(entity,BizTaskFeedback.class);
        return bizTaskFeedbackMapper.updateById(bizTaskFeedback);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskFeedbackMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskFeedbackId
     * @return
     */
    @Override
    public boolean existByBizTaskFeedbackId(Long BizTaskFeedbackId) {
        if (BizTaskFeedbackId != null) {
            LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskFeedback::getId, BizTaskFeedbackId);
            List<BizTaskFeedback> result = bizTaskFeedbackMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskFeedbackDTO> dataList) {
        List<BizTaskFeedback> result = ListCopyUtil.copy(dataList, BizTaskFeedback.class);
        return saveBatch(result);
    }

    @Override
    public PageResult<BizTaskDTO> queryBizTaskDecompose(BizTaskDTO entityDTO , BasePageForm basePageForm) {
        IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectFeedbackTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        List<BizTaskDTO> records = page.getRecords();
        PageResult<BizTaskDTO> bizTaskDTOPageResult = new PageResult<>(page.getRecords(), page.getTotal(), page.getCurrent());

        // 已办结 清除角标
        Integer hasFinish = entityDTO.getHasFinish();
        List<BizTaskDecompose> decomposeList = new ArrayList<>();
        if(hasFinish != null && hasFinish.intValue() == 1){ // 已办结 更新红点点标识
            records.forEach(rec ->{
                Integer newFinishSign = rec.getNewFinishSign();
                if(newFinishSign != null && newFinishSign.intValue() == 1){
                    BizTaskDecompose build = BizTaskDecompose.builder().newFinishSign(0).build();
                    build.setId(rec.getTaskDecomposeId());
                    decomposeList.add(build);
                }
            });
            bizTaskDecomposeService.updateBatchById(decomposeList); // 更新 办结红点点标识
        }
       // 获取分值
        List<Long> bizComposeIds = new ArrayList<>();
        records.forEach(bizTaskDTO -> {
            if (bizTaskDTO.getTaskDecomposeId()  != null){
                bizComposeIds.add(bizTaskDTO.getTaskDecomposeId());
            }

            // 获取任务分值
//            if (Objects.nonNull(bizTaskDTO.getTaskSupervisionId())) {
//                BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(bizTaskDTO.getTaskSupervisionId());
//                if (Objects.nonNull(bizTaskSupervision.getDegreeUrgency()) && Objects.nonNull(bizTaskSupervision.getDifficulty()) && Objects.nonNull(bizTaskSupervision.getInspectorTaskLevel())) {
//                    bizTaskDTO.setActualScore(bizTaskSupervisionService.getScoreById(bizTaskDTO.getTaskSupervisionId()));
//                }else {
//                    bizTaskDTO.setActualScore(0D);
//                }
//            }

            //判断是否为牵头单位人
            if(bizTaskDTO.getLeadPeopleId()!=null){
                if(bizTaskDTO.getDutyPeopleId().longValue() == bizTaskDTO.getLeadPeopleId().longValue()){
                    Long userId = entityDTO.getDutyPeopleId();
                    int num = bizTaskDecomposeService.selectTaskDecomposeCount(bizTaskDTO.getTaskSupervisionId(), userId);
                    if(num == 0){
                        bizTaskDTO.setIsFeedbackBtn(0);
                    }else{
                        bizTaskDTO.setIsFeedbackBtn(1);
                    }
                }else{
                    bizTaskDTO.setIsFeedbackBtn(0);
                }
            }else{
                bizTaskDTO.setIsFeedbackBtn(0);
            }


        });
        if (CollectionUtil.isNotEmpty(bizComposeIds)){
            LambdaQueryWrapper<BizTaskScore> lambFdaQueryWrapper = new LambdaQueryWrapper<>();
            lambFdaQueryWrapper.in(BizTaskScore::getBizTaskDecompose,bizComposeIds);
            List<BizTaskScore> taskScoreList = bizTaskScoreService.selectListNoAdd(lambFdaQueryWrapper);
            List<BizTaskDTO> finalRecords = records;
            taskScoreList.forEach(bizTaskScore -> {
                long decomeId = bizTaskScore.getBizTaskDecompose().longValue();
                finalRecords.forEach(bizTaskDTO -> {
                    if (bizTaskDTO.getTaskDecomposeId()  != null && bizTaskDTO.getTaskDecomposeId().longValue() ==decomeId ){
                        bizTaskDTO.setActualScore(bizTaskScore.getActualScore());
                    }
                });
            });
        }
        return bizTaskDTOPageResult;
    }

    @Override
    public PageResult<BizHomeTaskPackagingDTO> queryBizHomeTaskDecompose(BizHomeTaskDTO entityDTO , BasePageForm basePageForm) {
        IPage<BizHomeTaskDTO> page = this.queryDatabaseBizHomeTaskDecompose(entityDTO,basePageForm);
        List<BizHomeTaskDTO> bizHomeTaskDTO = page.getRecords();
        List<BizHomeTaskPackagingDTO> bizHomeTaskPackagingDTO = new ArrayList<>();
        for(BizHomeTaskDTO ben :bizHomeTaskDTO){
            BizHomeTaskPackagingDTO packaging = new BizHomeTaskPackagingDTO();
            packaging.setTaskSupervisionId(ben.getTaskSupervisionId());
            if(ben.getTaskDecomposeId()==0){
                packaging.setRowTitle("发布任务");
                packaging.setDetailPath("taskManagement/taskProgress?id="+ben.getTaskSupervisionId());
            }else{
                packaging.setRowTitle("承办任务");

                packaging.setDetailPath("taskManagement/taskDetail?formDataId="+ben.getTaskSupervisionId()+"&formId="+ben.getFormId()+"&subFormDataId="+ben.getTaskDecomposeId());
            }
            packaging.setRowKey("title");
            packaging.setData(ben);
            bizHomeTaskPackagingDTO.add(packaging);
        }
        IPage<BizHomeTaskPackagingDTO> newpage =new Page<>();
        newpage.setRecords(bizHomeTaskPackagingDTO);
        newpage.setTotal(page.getTotal());
        newpage.setSize(page.getSize());
        newpage.setCurrent(page.getCurrent());

        PageResult<BizHomeTaskPackagingDTO> bizTaskDTOPageResult = new PageResult<>(newpage.getRecords(), newpage.getTotal(), newpage.getCurrent());
        return bizTaskDTOPageResult;
    }


    private IPage<BizHomeTaskDTO> queryDatabaseBizHomeTaskDecompose(BizHomeTaskDTO entityDTO,BasePageForm basePageForm){
        if(databaseTypeUtils.getDatabaseType() == EnumDatabaseName.DMDREVER){//达梦数据库
            return bizTaskDecomposeMapper.selectDMSupAndDecomposeTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        } else if(databaseTypeUtils.getDatabaseType() == EnumDatabaseName.KINGBASE8){//人大金仓数据库
            return bizTaskDecomposeMapper.selectKingbaseSupAndDecomposeTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        } else{ //mysql数据库
            return bizTaskDecomposeMapper.selectSupAndDecomposeTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
        }
    }


    /**
     * 根据分解任务ID查询最新反馈信息
     * @param decomposeIdList
     * @return
     */
    @Override
    public Map<Long, BizTaskFeedbackDTO> queryLastedBizTaskFeedback(List<Long> decomposeIdList) {
        if (CollectionUtil.isEmpty(decomposeIdList)) {
            throw new BusinessException("分解任务ID不允许为空");
        }
        LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(BizTaskFeedback::getBizTaskDecompose, decomposeIdList);
        List<BizTaskFeedback> listData = bizTaskFeedbackMapper.selectListNoAdd(queryWrapper);

        List<BizTaskFeedbackDTO> BizTaskFeedbackDTOList = ListCopyUtil.copy(listData, BizTaskFeedbackDTO.class);
        Map<Long, BizTaskFeedbackDTO> bizTaskFeedbackDTOMap = new HashMap<>(16);

        //按分解任务分组
        Map<Long, List<BizTaskFeedbackDTO>> listMap = BizTaskFeedbackDTOList.stream().collect(Collectors.groupingBy(BizTaskFeedbackDTO::getBizTaskDecompose));
        for (Map.Entry<Long, List<BizTaskFeedbackDTO>> map : listMap.entrySet()) {
            Long bizTaskDecomposeId = map.getKey();
            BizTaskFeedbackDTO bizTaskFeedbackDTO = map.getValue().stream().sorted(Comparator.comparing(BizTaskFeedbackDTO::getCreateTime).reversed()).collect(Collectors.toList()).get(0);
            bizTaskFeedbackDTOMap.put(bizTaskDecomposeId,bizTaskFeedbackDTO);
        }

        if (CollectionUtil.isEmpty(BizTaskFeedbackDTOList)) {
            return new HashMap();
        }
        return bizTaskFeedbackDTOMap;
    }
}
