package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.entity.BizScoreUserConfig;
import com.ctsi.huaihua.entity.dto.BizScoreUserConfigDTO;
import com.ctsi.huaihua.mapper.BizScoreUserConfigMapper;
import com.ctsi.huaihua.service.IBizScoreUserConfigService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 免考核人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Slf4j
@Service
public class BizScoreUserConfigServiceImpl extends SysBaseServiceImpl<BizScoreUserConfigMapper, BizScoreUserConfig> implements IBizScoreUserConfigService {

    @Autowired
    private BizScoreUserConfigMapper bizScoreUserConfigMapper;

    @Autowired
    private CscpUserService cscpUserService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizScoreUserConfigDTO> queryListPage(BizScoreUserConfigDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getUserName()), BizScoreUserConfig::getUserName, entityDTO.getUserName());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getDepartmentName()), BizScoreUserConfig::getDepartmentName, entityDTO.getDepartmentName());
        IPage<BizScoreUserConfig> pageData = bizScoreUserConfigMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizScoreUserConfigDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizScoreUserConfigDTO.class));

        return new PageResult<BizScoreUserConfigDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizScoreUserConfigDTO> queryList(BizScoreUserConfigDTO entityDTO) {
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getUserName()), BizScoreUserConfig::getUserName, entityDTO.getUserName());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getDepartmentName()), BizScoreUserConfig::getDepartmentName, entityDTO.getDepartmentName());
        queryWrapper.like(Objects.nonNull(entityDTO.getHasChecked()), BizScoreUserConfig::getHasChecked, entityDTO.getHasChecked());
        List<BizScoreUserConfig> listData = bizScoreUserConfigMapper.selectList(queryWrapper);
        List<BizScoreUserConfigDTO> BizScoreUserConfigDTOList = ListCopyUtil.copy(listData, BizScoreUserConfigDTO.class);
        return BizScoreUserConfigDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizScoreUserConfigDTO findOne(Long id) {
        BizScoreUserConfig bizScoreUserConfig = bizScoreUserConfigMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizScoreUserConfig, BizScoreUserConfigDTO.class);
    }

    /**
     * 根据用户id获取免考核人员对象
     *
     * @param userIds the UserId of the entity
     * @return
     */
    @Override
    public List<BizScoreUserConfigDTO> findUserByUserId(List<Long> userIds) {
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizScoreUserConfig::getCompanyId, SecurityUtils.getCurrentCompanyId());
        // 当用户id数为1时，使用eq查询，提高查询速度
        if (userIds.size() == 1) {
            queryWrapper.eq(BizScoreUserConfig::getUserId, userIds.get(0));
        } else {
            queryWrapper.in(BizScoreUserConfig::getUserId, userIds);
        }
        List<BizScoreUserConfig> bizScoreUserConfigList = bizScoreUserConfigMapper.selectList(queryWrapper);

        return ListCopyUtil.copy(bizScoreUserConfigList, BizScoreUserConfigDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizScoreUserConfigDTO create(BizScoreUserConfigDTO entityDTO) {
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizScoreUserConfig::getUserId, entityDTO.getUserId());
        BizScoreUserConfig one = bizScoreUserConfigMapper.selectOneOnlyAddTenantId(queryWrapper);
        if (one != null) {
            throw new BusinessException("该用户已添加到免考核人员列表");
        }
        BizScoreUserConfig bizScoreUserConfig = BeanConvertUtils.copyProperties(entityDTO, BizScoreUserConfig.class);
        bizScoreUserConfig.setDepartmentName(cscpUserService.findByUserId(entityDTO.getUserId()).getDepartmentName());
        save(bizScoreUserConfig);
        return BeanConvertUtils.copyProperties(bizScoreUserConfig, BizScoreUserConfigDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizScoreUserConfigDTO entity) {
        BizScoreUserConfig bizScoreUserConfig = BeanConvertUtils.copyProperties(entity, BizScoreUserConfig.class);
        return bizScoreUserConfigMapper.updateById(bizScoreUserConfig);
    }

    /**
     * 批量删除删除
     *
     * @param ids the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(List<Long> ids) {
        return bizScoreUserConfigMapper.deleteBatchIds(ids);
    }


    /**
     * 验证是否存在
     *
     * @param BizScoreUserConfigId
     * @return
     */
    @Override
    public boolean existByBizScoreUserConfigId(Long BizScoreUserConfigId) {
        if (BizScoreUserConfigId != null) {
            LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizScoreUserConfig::getId, BizScoreUserConfigId);
            List<BizScoreUserConfig> result = bizScoreUserConfigMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizScoreUserConfigDTO> dataList) {
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper<>();
        List<BizScoreUserConfig> bizScoreUserConfigList = bizScoreUserConfigMapper.selectListOnlyAddTenantId(queryWrapper);
        List<Long> existUserIdList = bizScoreUserConfigList.stream().map(i -> i.getUserId()).collect(Collectors.toList());
        // 去除已添加的用户
        List<BizScoreUserConfigDTO> saveEntityList = dataList.stream()
                .filter(i -> !existUserIdList.contains(i.getUserId())).collect(Collectors.toList());
        List<BizScoreUserConfig> result = ListCopyUtil.copy(saveEntityList, BizScoreUserConfig.class);
        return saveBatch(result);
    }


}
