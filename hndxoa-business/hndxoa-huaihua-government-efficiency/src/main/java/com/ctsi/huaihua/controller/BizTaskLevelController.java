package com.ctsi.huaihua.controller;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.tree.CascadeNode;
import com.ctsi.hndx.tree.Node;
import com.ctsi.ssdc.model.PageResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import com.ctsi.huaihua.entity.BizTaskLevel;
import com.ctsi.huaihua.entity.dto.BizTaskLevelDTO;
import com.ctsi.huaihua.service.IBizTaskLevelService;
import com.ctsi.ssdc.security.SecurityUtils;
import com.github.pagehelper.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskLevel")
@Api(value = "任务级别表", tags = "任务级别表接口")
public class BizTaskLevelController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskLevel";

    @Autowired
    private IBizTaskLevelService bizTaskLevelService;



    /**
     *  新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskLevel.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLevel.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskLevelDTO> bizTaskLevelList) {
       Boolean  result = bizTaskLevelService.insertBatch(bizTaskLevelList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskLevel.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增数据")
  //  @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLevel.add')")
    public ResultVO<BizTaskLevelDTO> create(@RequestBody BizTaskLevelDTO bizTaskLevelDTO)  {
        BizTaskLevelDTO result = bizTaskLevelService.create(bizTaskLevelDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskLevel.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLevel.update')")
    public ResultVO update(@RequestBody BizTaskLevelDTO bizTaskLevelDTO) {
	    Assert.notNull(bizTaskLevelDTO.getId(), "general.IdNotNull");
        int count = bizTaskLevelService.update(bizTaskLevelDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskLevel.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskLevel.delete')")
    public ResultVO delete(@PathVariable Long id) {
        bizTaskLevelService.delete(id);
        return ResultVO.success();
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskLevelDTO bizTaskLevelDTO = bizTaskLevelService.findOne(id);
        return ResultVO.success(bizTaskLevelDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskLevelPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskLevelDTO>> queryBizTaskLevelPage(BizTaskLevelDTO bizTaskLevelDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskLevelService.queryListPage(bizTaskLevelDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskLevel")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskLevelDTO>> queryBizTaskLevel(BizTaskLevelDTO bizTaskLevelDTO) {
       List<BizTaskLevelDTO> list = bizTaskLevelService.queryList(bizTaskLevelDTO);
       return ResultVO.success(new ResResult<BizTaskLevelDTO>(list));
   }

    /**
     * 查询顶层数据
     */
    @GetMapping("/queryTopLevel")
    @ApiOperation(value = "查询顶层数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizTaskLevelDTO>> queryTopLevel() {
        List<BizTaskLevelDTO> list = bizTaskLevelService.getTopLevel();
        return ResultVO.success(new ResResult<BizTaskLevelDTO>(list));
    }

    /**
     * 查询所有数据后组装树
     * @param tenantId
     * @return
     */
    @GetMapping(value = "/queryLevelByTenantId")
    @ApiOperation(value = "根据租户id查询任务级别树", notes = "传入参数")
    public ResultVO<List<Tree<String>>> queryLevelByTenantId(
            @RequestParam(value = "tenantId", required = true, defaultValue = "0") Long tenantId) {
        if (tenantId == 0){
            tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        }
        List<Tree<String>> result = bizTaskLevelService.queryLevelByTenantId(tenantId);
        return ResultVO.success(result);
    }

    /**
     * 查询类型的树结构
     */
    @GetMapping("/queryTaskLevelTreeNode")
    @ApiOperation(value = "查询类型的树结构", notes = "查询类型的树结构")
        public ResultVO<List<Node<BizTaskLevelDTO>>> queryTaskLevelTreeNode(
            @RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId) {
        List<Node<BizTaskLevelDTO>> list = bizTaskLevelService.selectChildrenListNodeByParentId(parentId);
        return ResultVO.success(list);
    }

    /**
     * 查询任务级别的树结构
     */
    @GetMapping("/queryTaskLevelCascadeNode")
    @ApiOperation(value = "查询类型的树结构", notes = "查询级联的的树结构")
    @ApiImplicitParams(
            { @ApiImplicitParam(value ="父节点id，默认为0",name = "parentId"),
              @ApiImplicitParam(value ="数据来源单位id",name = "companyId"),
               @ApiImplicitParam(value ="数据来源单位id",name = "companyId")
            })

    public ResultVO<List<CascadeNode>> queryTaskLevelCascadeNode(
            @RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId,
            @RequestParam(value = "companyId", required = false) Long companyId,
            @RequestParam(value = "id", required = false) Long id) {
        List<CascadeNode> cascadeNodeList = new ArrayList<>();
        if (companyId != null){
            bizTaskLevelService.longThreadLocal.set(companyId);
        }
        if (id != null) {
            BizTaskLevel bizTaskLevel = bizTaskLevelService.getById(id);
            CascadeNode cascadeNode1 = new CascadeNode();
            cascadeNode1.setValue(String.valueOf(id));
            cascadeNode1.setLabel(bizTaskLevel.getTypeName());
            cascadeNodeList.add(cascadeNode1);   
            return  ResultVO.success(cascadeNodeList);
        }
        List<Node<BizTaskLevelDTO>> list = bizTaskLevelService.selectChildrenListNodeByParentId(parentId);

        list.forEach(tReceiveTypeDTONode -> {
            CascadeNode cascadeNode  = buildCascadeNode(tReceiveTypeDTONode);
            cascadeNodeList.add(cascadeNode);
        });
        bizTaskLevelService.longThreadLocal.remove();

        return ResultVO.success(cascadeNodeList);
    }



    private CascadeNode buildCascadeNode(Node node){
        CascadeNode cascadeNode = new CascadeNode();
        Long id = node.getId();
        String title = node.getTitle();
        cascadeNode.setValue(String.valueOf(id));
        cascadeNode.setLabel(title);
        List<Node> children = node.getChildren();
        List<CascadeNode> cascadeNodeList = new ArrayList<>();
        if (CollectionUtil.isEmpty(children)){
            cascadeNode.setChildren(new ArrayList<>());
            return cascadeNode;
        }
        children.forEach(node1 -> {
            cascadeNodeList.add(buildCascadeNode(node1));
        });
        cascadeNode.setChildren(cascadeNodeList);
        return cascadeNode;
    }

}
