package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizScoreRecordDTO;
import com.ctsi.huaihua.entity.dto.BizUserScoreDTO;
import com.ctsi.huaihua.service.IBizScoreRecordService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizScoreRecord")
@Api(value = "日常打分记录表", tags = "日常打分记录表接口")
public class BizScoreRecordController extends BaseController {

    private static final String ENTITY_NAME = "bizScoreRecord";

    @Autowired
    private IBizScoreRecordService bizScoreRecordService;



    /**
     *  新增日常打分记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizScoreRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增日常打分记录表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreRecord.add')")
    public ResultVO createBatch(@RequestBody List<BizScoreRecordDTO> bizScoreRecordList) {
       Boolean  result = bizScoreRecordService.insertBatch(bizScoreRecordList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizScoreRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增日常打分记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreRecord.add')")
    public ResultVO<BizScoreRecordDTO> create(@RequestBody BizScoreRecordDTO bizScoreRecordDTO)  {
        BizScoreRecordDTO result = bizScoreRecordService.create(bizScoreRecordDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizScoreRecord.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新日常打分记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreRecord.update')")
    public ResultVO update(@RequestBody BizScoreRecordDTO bizScoreRecordDTO) {
	    Assert.notNull(bizScoreRecordDTO.getId(), "general.IdNotNull");
        int count = bizScoreRecordService.update(bizScoreRecordDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除日常打分记录表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizScoreRecord.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreRecord.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizScoreRecordService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizScoreRecordDTO bizScoreRecordDTO = bizScoreRecordService.findOne(id);
        return ResultVO.success(bizScoreRecordDTO);
    }

    /**
    *  分页查询历史打分记录.
    */
    @GetMapping("/queryBizScoreRecordPage")
    @ApiOperation(value = "翻页查询历史打分记录", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizScoreRecordDTO>> queryBizScoreRecordPage(BizScoreRecordDTO bizScoreRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizScoreRecordService.queryListPage(bizScoreRecordDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizScoreRecord")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizScoreRecordDTO>> queryBizScoreRecord(BizScoreRecordDTO bizScoreRecordDTO) {
       List<BizScoreRecordDTO> list = bizScoreRecordService.queryList(bizScoreRecordDTO);
       return ResultVO.success(new ResResult<BizScoreRecordDTO>(list));
   }


    @PostMapping("/insertBizScoreRecord")
    @ApiOperation(value = "领导日常打分(权限code码为：cscp.bizScoreRecord.insertBizScoreRecord)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "领导日常打分")
    public ResultVO<BizScoreRecordDTO> insertBizScoreRecord(@RequestBody @Valid BizScoreRecordDTO bizScoreRecordDTO)  {
        LocalDate lastMonthDate = LocalDate.now().minusMonths(1);
        if (Objects.isNull(bizScoreRecordDTO.getScoreMonth())) {
            int value = lastMonthDate.getMonthValue();
            bizScoreRecordDTO.setScoreMonth(value);
        }
        if (Objects.isNull(bizScoreRecordDTO.getScoreYear())) {
            int value = lastMonthDate.getYear();
            bizScoreRecordDTO.setScoreYear(value);
        }
        BizScoreRecordDTO result = bizScoreRecordService.insertBizScoreRecord(bizScoreRecordDTO);
        return ResultVO.success(result);
    }


    @GetMapping("/queryCurrentBizScoreRecord")
    @ApiOperation(value = "领导查询当月打分记录", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "查询当月打分记录")
    public ResultVO<PageResult<BizScoreRecordDTO>> queryCurrentBizScoreRecord(BizScoreRecordDTO bizScoreRecordDTO, BasePageForm basePageForm)  {
        PageResult<BizScoreRecordDTO> bizScoreRecordDTOPageResult = bizScoreRecordService.queryCurrentBizScoreRecord(bizScoreRecordDTO, basePageForm);
        return ResultVO.success(bizScoreRecordDTOPageResult);
    }

    @GetMapping("/autoGenerateScoreRecord")
    @ApiOperation(value = "定时任务", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "查询当月打分记录")
    public ResultVO autoGenerateScoreRecord(BizScoreRecordDTO bizScoreRecordDTO)  {
        bizScoreRecordService.autoGenerateScoreRecord(bizScoreRecordDTO);
        return ResultVO.success(true);
    }

    @GetMapping("/averageAnnualScores")
    @ApiOperation(value = "",notes = "")
    public ResultVO<List<BizUserScoreDTO>> averageAnnualScores(BizUserScoreDTO bizUserScoreDTO){
       List<BizUserScoreDTO> bizScoreRecords = bizScoreRecordService.averageAnnualScores();
       return ResultVO.success(bizScoreRecords);
    }

}
