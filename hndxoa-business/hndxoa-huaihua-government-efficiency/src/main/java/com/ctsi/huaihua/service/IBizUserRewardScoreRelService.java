package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizRewardScoreRecordDTO;
import com.ctsi.huaihua.entity.dto.BizUserRewardScoreRelDTO;
import com.ctsi.huaihua.entity.BizUserRewardScoreRel;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 用户奖励分记录关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
public interface IBizUserRewardScoreRelService extends SysBaseServiceI<BizUserRewardScoreRel> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizUserRewardScoreRelDTO> queryListPage(BizUserRewardScoreRelDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizUserRewardScoreRelDTO> queryList(BizUserRewardScoreRelDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizUserRewardScoreRelDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizUserRewardScoreRelDTO create(BizUserRewardScoreRelDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizUserRewardScoreRelDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizUserRewardScoreRelId
     * @param code
     * @return
     */
    boolean existByBizUserRewardScoreRelId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizUserRewardScoreRelDTO> dataList);


    /**
     * 根据申请人的单位ID查询数据
     * @param companyId
     * @return
     */
    List<BizUserRewardScoreRelDTO> queryListByCompanyId(Long companyId);

    /**
     * 根据被奖励人的单位ID查询数据
     * @param companyId
     * @return
     */
    List<BizUserRewardScoreRelDTO> queryListByRewardCompanyId(Long companyId);

    /**
     * 根据被奖励人的用户ID查询数据
     * @param userId
     * @return
     */
    List<BizUserRewardScoreRelDTO> queryListByUserId(Long userId);

}
