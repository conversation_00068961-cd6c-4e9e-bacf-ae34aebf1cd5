package com.ctsi.huaihua.service.impl;

import com.alibaba.excel.EasyExcel;
import com.ctsi.hndx.common.MergeCellModel;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.handler.CustomMergeCellHandler;
import com.ctsi.hndx.utils.FileNameUtil;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.entity.dto.BizTaskFeedbackDTO;
import com.ctsi.huaihua.entity.dto.BizTaskPreviewToExcelDTO;
import com.ctsi.huaihua.service.BizTaskExportToExcelService;
import com.ctsi.huaihua.service.IBizTaskFeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname BizTaskExportToExcelServiceImpl
 * @Description
 * @Date 2022/9/27/0027 9:30
 */
@Slf4j
@Service
public class BizTaskExportToExcelServiceImpl implements BizTaskExportToExcelService {

    @Autowired
    private IBizTaskFeedbackService bizTaskFeedbackService;

    /**
     * 导出任务一览表
     * @param bizTaskDTOList
     * @param response
     * @return
     */
    @Override
    public Boolean exportTaskPreview(List<BizTaskDTO> bizTaskDTOList, HttpServletResponse response) {
        // 按主表分组
        LinkedHashMap<Long, List<BizTaskDTO>> linkedHashMap = bizTaskDTOList.stream().sorted(Comparator.comparing(BizTaskDTO::getCreateTime).reversed()).collect(Collectors.groupingBy(BizTaskDTO::getTaskSupervisionId,LinkedHashMap::new,Collectors.toList()));

        List<Long> taskDecomposeIdList = bizTaskDTOList.stream().map(i -> i.getTaskDecomposeId()).collect(Collectors.toList());
        //获取反馈结果
        Map<Long, BizTaskFeedbackDTO> listMap = bizTaskFeedbackService.queryLastedBizTaskFeedback(taskDecomposeIdList);

        List<BizTaskPreviewToExcelDTO> bizTaskPreviewToExcelDTOList = new ArrayList<>();

        // 生成表格数据
        int startRowNum = 2;
        int supNum = 1;
        // 生成合并单元格信息
        List<MergeCellModel> mergeCellList = new ArrayList<>();
        String sheetName="任务一览表导出模板";
        for (Map.Entry<Long, List<BizTaskDTO>> map : linkedHashMap.entrySet()) {
            List<BizTaskDTO> taskDTOList = map.getValue();
            List<BizTaskPreviewToExcelDTO> taskPreviewToExcelDTOList = this.assembledTaskPreviewDTO(supNum, taskDTOList, listMap);
            bizTaskPreviewToExcelDTOList.addAll(taskPreviewToExcelDTOList);
            int endRowNum = startRowNum + taskPreviewToExcelDTOList.size() - 1;
            mergeCellList.add(MergeCellModel.createMergeCellModel(sheetName, startRowNum,  endRowNum, 0, 0));
            mergeCellList.add(MergeCellModel.createMergeCellModel(sheetName, startRowNum,  endRowNum, 1, 1));
            mergeCellList.add(MergeCellModel.createMergeCellModel(sheetName, startRowNum,  endRowNum, 2, 2));
            mergeCellList.add(MergeCellModel.createMergeCellModel(sheetName, startRowNum,  endRowNum, 3, 3));
            mergeCellList.add(MergeCellModel.createMergeCellModel(sheetName, startRowNum,  endRowNum, 4, 4));
            mergeCellList.add(MergeCellModel.createMergeCellModel(sheetName, startRowNum,  endRowNum, 5, 5));
            startRowNum = startRowNum + taskPreviewToExcelDTOList.size();
            supNum ++;
        }

        // 写入Excel
        String uuid = UUID.randomUUID().toString();
        try {
            FileNameUtil.setDownloadResponseHeader(response,uuid + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            EasyExcel.write(response.getOutputStream(), BizTaskPreviewToExcelDTO.class)
                    .registerWriteHandler(new CustomMergeCellHandler(mergeCellList))
                    .sheet(sheetName).doWrite(bizTaskPreviewToExcelDTOList);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * 构建任务一览表导出模块DTO
     * @param taskNum
     * @param bizTaskDTOList
     * @return
     */
    private List<BizTaskPreviewToExcelDTO> assembledTaskPreviewDTO(Integer taskNum, List<BizTaskDTO> bizTaskDTOList, Map<Long, BizTaskFeedbackDTO> listMap) {
        List<BizTaskPreviewToExcelDTO> bizTaskPreviewToExcelDTOList = new ArrayList<>();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // bizTaskDTOList 是同一条主任务
        for (BizTaskDTO bizTaskDTO : bizTaskDTOList) {
            BizTaskPreviewToExcelDTO bizTaskPreviewToExcelDTO = new BizTaskPreviewToExcelDTO();
            bizTaskPreviewToExcelDTO.setTaskNum(taskNum);
            bizTaskPreviewToExcelDTO.setTitle(bizTaskDTO.getTitle());
            bizTaskPreviewToExcelDTO.setLeadPeopleName(bizTaskDTO.getLeadPeopleName());
            if (Objects.nonNull(bizTaskDTO.getInspectorTaskLevelDTO())) {
                bizTaskPreviewToExcelDTO.setInspectorTaskLevel(bizTaskDTO.getInspectorTaskLevelDTO().getTypeName());
            }
            if (Objects.nonNull(bizTaskDTO.getInspectorItemsDTO())) {
                bizTaskPreviewToExcelDTO.setInspectorItems(bizTaskDTO.getInspectorItemsDTO().getTypeName());
            }
            bizTaskPreviewToExcelDTO.setTaskDescription(bizTaskDTO.getTaskDescription());
            if (Objects.nonNull(bizTaskDTO.getSupDueTime())) {
                bizTaskPreviewToExcelDTO.setSupDueTime(bizTaskDTO.getSupDueTime().format(df));
            }
            bizTaskPreviewToExcelDTO.setContent(bizTaskDTO.getContent());
            bizTaskPreviewToExcelDTO.setDutyPeopleInfo(bizTaskDTO.getDutyPeopleCompanyName() + bizTaskDTO.getDutyPeopleName());
            if (Objects.nonNull(bizTaskDTO.getStartDate())){
                bizTaskPreviewToExcelDTO.setStartDate(bizTaskDTO.getStartDate().format(df));
            }
            if (Objects.nonNull(bizTaskDTO.getEndDate())){
                bizTaskPreviewToExcelDTO.setEndDate(bizTaskDTO.getEndDate().format(df));
            }
            if (SysConstant.BIZ_IS_FINISHED.equals(bizTaskDTO.getHasFinish())) {
                bizTaskPreviewToExcelDTO.setHasFinish("已办结");
            } else {
                bizTaskPreviewToExcelDTO.setHasFinish("未办结");
            }

            BizTaskFeedbackDTO bizTaskFeedbackDTO = listMap.get(bizTaskDTO.getTaskDecomposeId());
            if (Objects.nonNull(bizTaskFeedbackDTO)) {
                bizTaskPreviewToExcelDTO.setFeedbackSign(bizTaskFeedbackDTO.getAchievementDescription());
            }
            bizTaskPreviewToExcelDTOList.add(bizTaskPreviewToExcelDTO);
        }
        return bizTaskPreviewToExcelDTOList;
    }
}
