package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizTaskFeedbackTempDTO;
import com.ctsi.huaihua.service.IBizTaskFeedbackTempService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskFeedbackTemp")
@Api(value = "任务成果反馈暂存表", tags = "任务成果反馈暂存表接口")
public class BizTaskFeedbackTempController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskFeedbackTemp";

    @Autowired
    private IBizTaskFeedbackTempService bizTaskFeedbackTempService;



    /**
     *  新增任务成果反馈暂存表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskFeedbackTemp.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务成果反馈暂存表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskFeedbackTemp.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskFeedbackTempDTO> bizTaskFeedbackTempList) {
       Boolean  result = bizTaskFeedbackTempService.insertBatch(bizTaskFeedbackTempList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  暂存反馈数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "暂存反馈数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务成果反馈暂存表数据")
    public ResultVO<BizTaskFeedbackTempDTO> create(@RequestBody BizTaskFeedbackTempDTO bizTaskFeedbackTempDTO)  {
        BizTaskFeedbackTempDTO result = bizTaskFeedbackTempService.create(bizTaskFeedbackTempDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskFeedbackTemp.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新任务成果反馈暂存表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskFeedbackTemp.update')")
    public ResultVO update(@RequestBody BizTaskFeedbackTempDTO bizTaskFeedbackTempDTO) {
	    Assert.notNull(bizTaskFeedbackTempDTO.getId(), "general.IdNotNull");
        int count = bizTaskFeedbackTempService.update(bizTaskFeedbackTempDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除任务成果反馈暂存表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskFeedbackTemp.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskFeedbackTemp.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskFeedbackTempService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskFeedbackTempDTO bizTaskFeedbackTempDTO = bizTaskFeedbackTempService.findOne(id);
        return ResultVO.success(bizTaskFeedbackTempDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskFeedbackTempPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskFeedbackTempDTO>> queryBizTaskFeedbackTempPage(BizTaskFeedbackTempDTO bizTaskFeedbackTempDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskFeedbackTempService.queryListPage(bizTaskFeedbackTempDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskFeedbackTemp")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskFeedbackTempDTO>> queryBizTaskFeedbackTemp(BizTaskFeedbackTempDTO bizTaskFeedbackTempDTO) {
       List<BizTaskFeedbackTempDTO> list = bizTaskFeedbackTempService.queryList(bizTaskFeedbackTempDTO);
       return ResultVO.success(new ResResult<BizTaskFeedbackTempDTO>(list));
   }

    /**
     * 查询自己暂存的数据
     */
    @GetMapping("/getTempFeedback")
    @ApiOperation(value = "查询自己暂存的数据", notes = "无参")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO getTempFeedback(BizTaskFeedbackTempDTO dto) {
        Assert.notNull(dto.getBizTaskDecompose(), "general.IdNotNull");
        BizTaskFeedbackTempDTO bizTaskFeedbackTempDTO = bizTaskFeedbackTempService.getTempFeedback(dto);
        return ResultVO.success(bizTaskFeedbackTempDTO);
    }


}
