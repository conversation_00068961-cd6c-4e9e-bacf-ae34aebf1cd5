package com.ctsi.huaihua.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ctsi.hndx.annotations.LimitSubmit;
import com.ctsi.hndx.annotations.PermissionData;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.datapermission.DataFilterThreadLocal;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.*;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.huaihua.service.IBizTaskSupervisionService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.impl.CscpUserServiceImpl;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.SpringUtil;
import io.netty.handler.codec.compression.Bzip2Decoder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;


import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskSupervision")
@Api(value = "任务督查的主表", tags = "任务督查的主表接口")
public class BizTaskSupervisionController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskSupervision";

    @Autowired
    private IBizTaskSupervisionService bizTaskSupervisionService;

    @Autowired
    private IBizTaskDecomposeService bizTaskDecomposeService;


    /**
     * 新增任务督查的主表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskSupervision.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增任务督查的主表批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskSupervision.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskSupervisionDTO> bizTaskSupervisionList) {
        Boolean result = bizTaskSupervisionService.insertBatch(bizTaskSupervisionList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 新增数据.
     * 新增任务、接收任务
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskSupervision.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增任务督查的主表数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskSupervision.add')")
    public ResultVO<BizTaskSupervisionDTO> create(@RequestBody BizTaskSupervisionDTO bizTaskSupervisionDTO) {
        log.info("当前时间" + new Date());
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        bizTaskSupervisionDTO.setCreateName(currentCscpUserDetail.getRealName());
        bizTaskSupervisionDTO.setCreateTime(LocalDateTime.now());
        bizTaskSupervisionDTO.setMobile(currentCscpUserDetail.getMobile());
        bizTaskSupervisionDTO.setDepartmentName(currentCscpUserDetail.getDepartmentName());
        bizTaskSupervisionDTO.setCompanyName(currentCscpUserDetail.getCompanyName());
        BizTaskSupervisionDTO result = bizTaskSupervisionService.create(bizTaskSupervisionDTO);
        BizTaskSupervisionDTO result1 = new BizTaskSupervisionDTO();
        result1.setId(result.getId());
        log.info("当前时间11" + new Date());
        return ResultVO.success(result1);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskSupervision.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新任务督查的主表数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskSupervision.update')")
    public ResultVO update(@RequestBody BizTaskSupervisionDTO bizTaskSupervisionDTO) {
        Assert.notNull(bizTaskSupervisionDTO.getId(), "general.IdNotNull");
        int count = bizTaskSupervisionService.update(bizTaskSupervisionDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @GetMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除任务督查的主表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskSupervision.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskSupervision.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskSupervisionService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 撤回任务.
     */
    @PostMapping("/updateStatus/{id}")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "撤回任务督查的主表数据")
    @ApiOperation(value = "撤回任务督查的主表数据 ", notes = "传入参数")
    public ResultVO withdraw(@PathVariable Long id) {
        int count = bizTaskSupervisionService.withdraw(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskSupervisionDTO bizTaskSupervisionDTO = bizTaskSupervisionService.findOne(id);
        return ResultVO.success(bizTaskSupervisionDTO);
    }


    /**
     * 查询多条数据.不分页
     * 已办结数据
     * 成果收件箱数据
     */
    @GetMapping("/queryBizTaskSupervision")
    @ApiOperation(value = "不分页,查询多条数据(1 可以查询已办结数据,2 查成果收件箱数据)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    @PermissionData(pageComponent = "outcomeInbox")
    public ResultVO<ResResult<BizTaskSupervisionDTO>> queryBizTaskSupervision(BizTaskSupervisionDTO bizTaskSupervisionDTO) {
        List<BizTaskSupervisionDTO> list = bizTaskSupervisionService.queryList(bizTaskSupervisionDTO);
        return ResultVO.success(new ResResult<BizTaskSupervisionDTO>(list));
    }


    /**
     * 查询当前用户创建的督察主任务列表，不分页
     */
    @GetMapping("/querySupervisionList")
    @ApiOperation(value = "查询当前用户创建的督察主任务列表，不分页", notes = "传入参数")
    public ResultVO<ResResult<BizTaskSupervisionDTO>> querySupervisionList() {
        List<BizTaskSupervisionDTO> list = bizTaskSupervisionService.querySupervisionList();
        return ResultVO.success(new ResResult<BizTaskSupervisionDTO>(list));
    }


    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizTaskSupervisionPage")
    @ApiOperation(value = "通用主任务查询 ;分页查询", notes = "传入参数")
    @PermissionData(pageComponent = "task-input")
    public ResultVO<PageResult<BizTaskSupervisionDTO>> queryBizTaskSupervisionPage(BizTaskSupervisionDTO bizTaskSupervisionDTO, BasePageForm basePageForm) {
        PageResult<BizTaskSupervisionDTO> bizTaskSupervisionDTOPageResult = bizTaskSupervisionService.queryListPage(bizTaskSupervisionDTO, basePageForm);
        return ResultVO.success(bizTaskSupervisionDTOPageResult);
    }

    /**
     * 查询多条数据.分页
     */
    @GetMapping("/queryAllTasks")
    @ApiOperation(value = "查询全部任务", notes = "传入参数")
    @PermissionData(isWebComponent = true)
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskSupervisionDTO>> queryAllTasks(BizTaskSupervisionDTO bizTaskSupervisionDTO, BasePageForm basePageForm) {
        bizTaskSupervisionDTO.setCompanyId(SecurityUtils.getCurrentCompanyId());
        PageResult<BizTaskSupervisionDTO> data = bizTaskSupervisionService.queryPageAllTasks(bizTaskSupervisionDTO, basePageForm);
        return ResultVO.success(data);
    }


    /**
     * 查询多条数据.分页
     */
    @GetMapping("/queryOtherAllTasks")
    @ApiOperation(value = "查询全部任务可能查询到其它单位的", notes = "传入参数")
    @PermissionData(pageComponent = "queryOtherAllTasks")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskSupervisionDTO>> queryOtherAllTasks(BizTaskSupervisionDTO bizTaskSupervisionDTO, BasePageForm basePageForm) {
        PageResult<BizTaskSupervisionDTO> data = bizTaskSupervisionService.queryPageAllTasks(bizTaskSupervisionDTO, basePageForm);
        return ResultVO.success(data);
    }

    /**
     * 获取签收页面的详情数据.
     */
    @GetMapping("/getSignDetail/{id}")
    @ApiOperation(value = "获取签收页面的详情数据", notes = "传入参数")
    public ResultVO<BizSignTaskDTO> getSignDetail(@PathVariable Long id) {
        BizSignTaskDTO bizSignTaskDTO = bizTaskSupervisionService.getSignDetail(id);
        return ResultVO.success(bizSignTaskDTO);
    }


    /**
     * 根据分解表的业务id，获取主表的业务数据.
     */
    @GetMapping("/getSuperDataByDecomposeId")
    @ApiOperation(value = "根据分解表的业务id，获取主表的业务数据.", notes = "传入参数")
    public ResultVO<Map> getSuperDataByDecomposeId(@RequestParam Long id, @RequestParam Long formId) {
        BizTaskSupervisionDTO bizSignTaskDTO = bizTaskSupervisionService.getSuperDataByDecomposeId(id);
        ResultVO resultVO = RestTemplateRequestJWT.get(SpringUtil.getLocalUrlPort() + "/api/getFormDataByFormId/" + formId, ResultVO.class);
        if (ResultCode.SUCCESS.code().equals(resultVO.getResultCode())) {
            Map<String, Object> mapResult = (Map<String, Object>) resultVO.getResultData();
            Map<String, Object> formData = MapUtils.getMap(mapResult, "formData");
            Map<String, Object> mapM = MapUtils.getMap(formData, "M");
            formData.put("formDataId", bizSignTaskDTO.getId());
            bizSignTaskDTO.setTaskTransferDecomposeId(id);
            //  查询本单位最大的转办的任务编号
            String maxNumber = bizTaskSupervisionService.getSuperTransferMaxNumber(bizSignTaskDTO.getTaskNumber() + "ZB");
            bizSignTaskDTO.setTaskNumber(maxNumber);
            Long tranFormId = bizSignTaskDTO.getFormId();
            bizSignTaskDTO.setBizTaskSupervisionIdList(null);
            bizSignTaskDTO.setBizTaskDecomposeDTOList(null);
            bizSignTaskDTO.setId(SnowflakeIdUtil.getSnowFlakeLongId());
            bizSignTaskDTO.setHasFinish(0);
            bizSignTaskDTO.setFormId(null);
            bizSignTaskDTO.setCompanyName(null);
            bizSignTaskDTO.setCreateTime(null);
            bizSignTaskDTO.setDutyPeople(null);
            bizSignTaskDTO.setCompanyId(null);
            for (String key : mapM.keySet()) {
                if (!"modelId".equals(key)) {
                    Map<String, Object> map = BeanUtil.beanToMap(bizSignTaskDTO);
                    MapUtils.safeAddToMap(mapM, key, map);
                    Map<String, Object> data = (Map<String, Object>) mapM.get(key);
                    BizTaskDecompose bizTaskDecompose = bizTaskDecomposeService.getById(id);
                    data.put("ext1", bizTaskDecompose.getStartDate());
                    data.put("ext2", bizTaskDecompose.getContent());

                    String pos = bizTaskDecompose.getDutyPeoplePostName();
                    if (StringUtils.isNotEmpty(pos)) {
                        data.put("ext3", bizTaskDecompose.getDutyPeopleName() + "(" + pos + ")");
                    } else {
                        data.put("ext3", bizTaskDecompose.getDutyPeopleName());
                    }
                    data.put("tranFormId", tranFormId);
                    break;
                }
            }
            return ResultVO.success(mapResult);
        } else {
            throw new BusinessException(resultVO.getResultMsg());
        }
    }


    /**
     * // 内容:展示当前单位本年度所有已发布任务，可查看任务执行过程、任务成果，可进行反馈状态动态预警。
     * 发布任务总台账
     * 排序：按交办时间降序排序
     * 排序：按交办时间降序排序
     */
    @GetMapping("/listTaskPublishReport")
    @ApiOperation(value = "发布任务总台账,分页", notes = "")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDTO>> listTaskPublishReport(BizTaskDTO dto,
                                                                  BasePageForm basePageForm) {
        dto.setHasPublish(1); // 已发布的任务台账
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        dto.setSupCompanyId(currentCompanyId);
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.listReport(dto, basePageForm, TaskStatus.TASK_DECOMPOSE_DUE_TIME);

        return ResultVO.success(list);
    }

    @GetMapping("/listSupTaskPublishReport")
    @ApiOperation(value = "发布任务总台账,分页，查询主表数据记录数", notes = "")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDTO>> listSupTaskPublishReport(BizTaskDTO dto,
                                                                  BasePageForm basePageForm) {
        dto.setHasPublish(1); // 已发布的任务台账
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        dto.setSupCompanyId(currentCompanyId);
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.listSupReport(dto, basePageForm, TaskStatus.TASK_DECOMPOSE_DUE_TIME);

        return ResultVO.success(list);
    }


    /**
     * 一键催办
     * (正常请忽略)
     */
    @GetMapping("/messageReminders")
    @ApiOperation(value = "一键催办", notes = "传入参数:taskSupervisionId")
    @LimitSubmit(key = "complete:%s:#taskSupervisionId", limit = 5, needAllWait = true)
    public ResultVO messageReminders(BizTaskDTO dto) {
        bizTaskSupervisionService.messageReminders(dto);
        return ResultVO.success();
    }


    /**
     * 主任务包含 分解任务,分解反馈信息  分解任务根据责任人公司id分组
     * 2.发布任务情况-反馈详情，单位根据序号排先后；
     * 3.发布任务情况-反馈详情，单位下的人员排序按照主任务责任人的顺序排列
     */
    @GetMapping("/getSubAndDecAndFeedbackInfoGroupByCommpany/{id}")
    @ApiOperation(value = "主任务包含 分解任务,分解反馈信息  分解任务根据责任人公司id分组", notes = "督查任务id")
    public ResultVO<BizTaskSupervisionDTO> getSubAndDecAndFeedbackInfoGroupByCommpany(@PathVariable Long id) {
        BizTaskSupervisionDTO bizTaskSupervisionDTO = bizTaskSupervisionService.getSubAndDecAndFeedbackInfoGroupByCommpany(id);
        return ResultVO.success(bizTaskSupervisionDTO);
    }


//    -------------------------app 移动端任务查询---------------------Start

    /**
     * 督查任务数据包含分解任务数据信息
     */
    @GetMapping("/querySupAndDecList/{id}")
    @ApiOperation(value = "督查任务数据包含分解任务数据信息", notes = "督查任务id")
    public ResultVO<BizTaskSupervisionDTO> querySupAndDecList(@PathVariable Long id) {
        BizTaskSupervisionDTO bizTaskSupervisionDTO = bizTaskSupervisionService.querySupAndDecList(id);
        return ResultVO.success(bizTaskSupervisionDTO);
    }


    /**
     * 督查任务数据包含分解任务数据信息和反馈信息.
     * 含转办记录
     */
    @GetMapping("/getSubAndDecAndFeedbackInfo/{id}")
    @ApiOperation(value = "查询督查任务数据包含分解任务数据信息和反馈信息,", notes = "督查任务id")
    public ResultVO<BizTaskSupervisionDTO> getSubAndDecAndFeedbackInfo(@PathVariable Long id) {
        BizTaskSupervisionDTO bizTaskSupervisionDTO = bizTaskSupervisionService.getSubAndDecAndFeedbackInfo(id);
        return ResultVO.success(bizTaskSupervisionDTO);
    }

    /**
     * app临期;逾期查询
     */
    @GetMapping("/queryTaskTemporaryAndOverdue")
    @ApiOperation(value = "app临期;逾期查询", notes = "传入参数queryType: 2逾期 3 临期")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDTO>> queryTaskTemporaryAndOverdue(BizTaskDTO bizTaskDTO,
                                                                         BasePageForm basePageForm) {
        bizTaskDTO.setDutyPeopleId(SecurityUtils.getCurrentUserId());
        String queryType = bizTaskDTO.getQueryType();
        if (StrUtil.isNotEmpty(queryType)) {
            bizTaskDTO.setSortField("due_time");//2逾期
            bizTaskDTO.setSortType("asc");
        }


        PageResult<BizTaskDTO> list = bizTaskDecomposeService.queryList(bizTaskDTO, basePageForm);
        return ResultVO.success(list);
    }


    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizTaskSupervisionPageApp")
    @ApiOperation(value = "成果收件箱主任务查询 ;分页查询", notes = "传入参数")
    @PermissionData(pageComponent = "outcomeInbox")
    public ResultVO<PageResult<BizTaskSupervisionDTO>> queryBizTaskSupervisionPageApp(BizTaskSupervisionDTO bizTaskSupervisionDTO, BasePageForm basePageForm) {

        PageResult<BizTaskSupervisionDTO> bizTaskSupervisionDTOPageResult = bizTaskSupervisionService.queryNotTempTaskListPage(bizTaskSupervisionDTO, basePageForm);
        return ResultVO.success(bizTaskSupervisionDTOPageResult);
    }


//    -------------------------app 移动端任务查询---------------------end

    @GetMapping("/queryDecomposeTaskGroupDutyPeople")
    @ApiOperation(value = "分解任务办结数 排名前X的接收单位责任人姓名 ;分页查询", notes = "传入参数")
    public ResultVO<PageResult<BizDeTaskFinishCtDTO>> queryDecomposeTaskGroupDutyPeople(BizDeTaskFinishCtDTO bizDeTaskFinishCtDTO,
                                                                                        BasePageForm basePageForm) {

        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        bizDeTaskFinishCtDTO.setDutyPeopleCompanyId(currentCompanyId);
        PageResult<BizDeTaskFinishCtDTO> pageResult =
                bizTaskSupervisionService.queryDecomposeTaskGroupDutyPeople(bizDeTaskFinishCtDTO, basePageForm);


        return ResultVO.success(pageResult);
    }

    /**
     * 待审任务（待审，已发布，已驳回）
     *
     * @param bizDeTaskFinishCtDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryPendingTasks")
    @PermissionData(pageComponent = "pendingTasks")
    @ApiOperation(value = "待审任务（待审，已发布，已驳回）", notes = "传入参数")
    public ResultVO<PageResult<BizTaskSupervisionDTO>> queryPendingTasks(@Valid BizPendingTasksDTO bizDeTaskFinishCtDTO, BasePageForm basePageForm) {
        if (DataFilterThreadLocal.get() == null) {
            bizDeTaskFinishCtDTO.setLeaderId(SecurityUtils.getCurrentUserId());
        }
        PageResult<BizTaskSupervisionDTO> pageResult =
                bizTaskSupervisionService.queryPendingTasks(bizDeTaskFinishCtDTO, basePageForm);
        return ResultVO.success(pageResult);
    }


    /**
     * 待审任务的数量
     *
     * @param bizDeTaskFinishCtDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryPendingTasksCount")
    @PermissionData(pageComponent = "pendingTasks")
    @ApiOperation(value = "待审任务（待审，已发布，已驳回）", notes = "传入参数")
    public ResultVO<Integer> queryPendingTasksCount() {
        BizPendingTasksDTO bizDeTaskFinishCtDTO = new BizPendingTasksDTO();
        bizDeTaskFinishCtDTO.setTaskState(TaskStatus.TO_BE_APPROVED);
        BasePageForm basePageForm = new BasePageForm();
        if (DataFilterThreadLocal.get() == null) {
            bizDeTaskFinishCtDTO.setLeaderId(SecurityUtils.getCurrentUserId());
        }
        PageResult<BizTaskSupervisionDTO> pageResult =
                bizTaskSupervisionService.queryPendingTasks(bizDeTaskFinishCtDTO, basePageForm);
        return ResultVO.success(String.valueOf(pageResult.getRecordsTotal()));
    }

    /**
     * 审批任务通过
     */
    @PostMapping("/adoptApproval/{taskId}")
    @ApiOperation(value = "审批任务是否通过", notes = "传入参数")
    public ResultVO<Boolean> adoptApproval(@PathVariable("taskId") Long taskId) {
        Integer count = bizTaskSupervisionService.adoptApproval(taskId);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 驳回
     *
     * @param taskId
     * @return
     */
    @PostMapping("/reject/{taskId}/{rejectReason}")
    @ApiOperation(value = "驳回", notes = "传入参数")
    public ResultVO<Boolean> reject(@PathVariable("taskId") Long taskId,
                                    @PathVariable("rejectReason") String rejectReason) {
        Integer count = bizTaskSupervisionService.reject(taskId, rejectReason);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询角标
     *
     * @return
     */
    @GetMapping("/getAngleMark")
    @PermissionData(pageComponent = "pendingTasks")
    @ApiOperation(value = "查询角标")
    public ResultVO<Integer> getAngleMark() {
        return ResultVO.success(bizTaskSupervisionService.getAngleMark());
    }

    /**
     * 根据子任务id获取任务分数和是否是牵头单位
     *
     * @param decomposeId
     * @return
     */
    @GetMapping("/getScoreById/{id}")
    @ApiOperation(value = "根据主建id获取任务分数")
    public ResultVO<BizScoreByIdDTO> getScoreById(@PathVariable("id") Long decomposeId) {
        BizScoreByIdDTO score = bizTaskSupervisionService.getScoreAndLead(decomposeId);
        return ResultVO.success(score);
    }

    @GetMapping("/getEquationById/{id}")
    @ApiOperation(value = "根据主建id获取任务分数")
    public ResultVO<Double> getEquationById(@PathVariable Long id) {
        String result = bizTaskSupervisionService.getEquationById(id);
        return ResultVO.success(result);
    }

    /**
     * 全市年得分统计
     *
     * @return
     */
    @GetMapping("/municipalGovernment")
    @ApiOperation(value = "全市年得分统计")
    public ResultVO<List<BizTaskInformationDTO>> municipalGovernment() {
        List<BizTaskInformationDTO> bizTaskInformations = bizTaskSupervisionService.municipalGovernment();
        return ResultVO.success(bizTaskInformations);
    }

    /**
     * 单位得分统计
     */
    @GetMapping("/municipalUnits")
    @ApiOperation(value = "单位得分统计")
    public ResultVO<List<BizTaskInformationDTO>> municipalUnits() {
        List<BizTaskInformationDTO> bizTaskInformations = bizTaskSupervisionService.municipalUnits();
        return ResultVO.success(bizTaskInformations);
    }

    /**
     * 市直单位本年接收任务，本年已发任务，本年已办结任务
     */
    @GetMapping("/receiveSentCompletedTask")
    @ApiOperation(value = "市直单位本年接收任务，本年已发任务，本年已办结任务")
    public ResultVO<Map<String, Integer>> receiveSentCompletedTask() {
        Map<String, Integer> bizTaskInformations = bizTaskSupervisionService.receiveSentCompletedTask();
        return ResultVO.success(bizTaskInformations);
    }

    /**
     * 统计市政府本年交办任务，已办结任务，待办结任务
     */
    @GetMapping("/assignedCompletedToBeSettled")
    @ApiOperation(value = "统计本年交办任务，已办结任务，待办结任务")
    public ResultVO<Map<String, Integer>> assignedCompletedToBeSettledTask() {
        Map<String, Integer> bizTaskInformations = bizTaskSupervisionService.assignedCompletedToBeSettledTask();
        return ResultVO.success(bizTaskInformations);
    }


    /**
     * 展示全市单位月分数排名
     */
    @GetMapping("/firstHandTotalScore")
    @ApiOperation(value = "统计市直单位,单位得分统计")
    public ResultVO<List<Map<String, Object>>> firstHandTotalScore() {
        List<Map<String, Object>> map = bizTaskSupervisionService.firstHandTotalScore();
        return ResultVO.success(map);
    }

    /**
     * 全市单位总任务数量统计
     */
    @GetMapping("/companyAllNumberOfTasks")
    @ApiOperation(value = "全市单位总任务数量统计")
    public ResultVO<List<Map<String, Object>>> companyAllNumberOfTasks() {
        List<Map<String, Object>> list = bizTaskSupervisionService.companyAllNumberOfTasks();
        return ResultVO.success(list);
    }


    /**
     * 本单位月度任务数量统计
     */
    @GetMapping("/companyNumberOfTasks")
    @ApiOperation(value = "本单位月度任务数量统计")
    public ResultVO<List<Map<String, Object>>> companyNumberOfTasks() {
        List<Map<String, Object>> map = bizTaskSupervisionService.companyNumberOfTasks();
        return ResultVO.success(map);
    }

    /**
     * 统计各单位一把手分数
     */
    @GetMapping("/firstHandFraction")
    @ApiOperation(value = "统计各单位一把手分数")
    public ResultVO<List<BizTaskInformationDTO>> firstHandFraction() {
        List<BizTaskInformationDTO> map = bizTaskSupervisionService.firstHandFraction();
        return ResultVO.success(map);
    }

}
