package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskDecomposeDTO;
import com.ctsi.huaihua.entity.dto.BizTaskDelayDTO;
import com.ctsi.huaihua.entity.BizTaskDelay;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 任务延期表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IBizTaskDelayService extends SysBaseServiceI<BizTaskDelay> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskDelayDTO> queryListPage(BizTaskDelayDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskDelayDTO> queryList(BizTaskDelayDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskDelayDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskDelayDTO create(BizTaskDelayDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskDelayDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskDelayId
     * @param code
     * @return
     */
    boolean existByBizTaskDelayId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskDelayDTO> dataList);

    /**
     * 延期申请通过(无过滤)
     * @param bizTaskDelayDTO
     * @return
     */
    Boolean approvedBizTaskDelay(BizTaskDelayDTO bizTaskDelayDTO);

    /**
     * 分页查询延期申请列表
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskSupervisionDTO> queryPageDelayList(BizTaskSupervisionDTO entityDTO, BasePageForm page);

    /**
     * 查询延期详情
     * @param bizTaskDelayDTO
     * @return
     */
    BizTaskDecomposeDTO queryDelayDetail(BizTaskDelayDTO bizTaskDelayDTO);

    /**
     * 查询延期详情(会修改延期申请为已阅状态)
     * @param bizTaskDelayDTO
     * @return
     */
    BizTaskDecomposeDTO queryAndReadDelayDetail(BizTaskDelayDTO bizTaskDelayDTO);

    /**
     * 修改延期申请为已阅状态
     * @param bizTaskDelayDTO
     * @return
     */
    Boolean readBizTaskDelay(BizTaskDelayDTO bizTaskDelayDTO);


    /**
     * 查询延期申请列表(会修改延期申请为已阅状态)
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskSupervisionDTO>queryAndReadPageDelayList(BizTaskSupervisionDTO entityDTO, BasePageForm page);

    /**
     * 需要延期审核数量(无单位隔离)
     * @param bizTaskDelayDTO
     * @return
     */
    Integer queryDelayNeedReviewAmount(BizTaskDelayDTO bizTaskDelayDTO);

    /**
     * 提醒延期申请通过数量(无单位隔离)
     * @param bizTaskDelayDTO
     * @return
     */
    Integer queryDelayHasReviewedAmount(BizTaskDelayDTO bizTaskDelayDTO);


    /**
     * 获取自任务的延期记录
     * @param bizTaskDecompose
     * @return
     */
    List<BizTaskDelayDTO> queryBizTaskDelayByTaskDecompose(Long bizTaskDecompose);
}
