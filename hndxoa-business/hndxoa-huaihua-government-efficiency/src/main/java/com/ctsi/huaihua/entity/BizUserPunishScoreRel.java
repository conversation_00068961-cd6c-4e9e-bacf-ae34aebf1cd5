package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户惩罚分记录关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_user_punish_score_rel")
@ApiModel(value="BizUserPunishScoreRel对象", description="用户惩罚分记录关系表")
public class BizUserPunishScoreRel extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被奖励加分的用户ID
     */
    @ApiModelProperty(value = "被奖励加分的用户ID")
    private Long userId;

    /**
     * 被奖励加分的用户姓名
     */
    @ApiModelProperty(value = "被奖励加分的用户姓名")
    private String userName;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    private Integer hasRead;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String post;

    /**
     * 处罚分数表主键id
     */
    @ApiModelProperty(value = "处罚分数表主键id")
    private Long punishScoreRecordId;

    @ApiModelProperty(value = "被处罚公司名称")
    private String punishCompanyName;

    @ApiModelProperty(value = "被处罚公司id")
    private Long punishCompanyId;

    @ApiModelProperty(value = "被处罚部门名称")
    private String punishDepartmentName;

    @ApiModelProperty(value = "被处罚部门id")
    private Long punishDepartmentId;


}
