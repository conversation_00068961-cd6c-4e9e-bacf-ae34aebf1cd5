package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizScoreLeader;
import com.ctsi.huaihua.entity.dto.BizScoreLeaderDTO;
import com.ctsi.huaihua.mapper.BizScoreLeaderMapper;
import com.ctsi.huaihua.service.IBizScoreLeaderService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 日常打分领导管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Slf4j
@Service
public class BizScoreLeaderServiceImpl extends SysBaseServiceImpl<BizScoreLeaderMapper, BizScoreLeader> implements IBizScoreLeaderService {

    @Autowired
    private BizScoreLeaderMapper bizScoreLeaderMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizScoreLeaderDTO> queryListPage(BizScoreLeaderDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizScoreLeader> queryWrapper = new LambdaQueryWrapper();

        IPage<BizScoreLeader> pageData = bizScoreLeaderMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizScoreLeaderDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizScoreLeaderDTO.class));

        return new PageResult<BizScoreLeaderDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizScoreLeaderDTO> queryList(BizScoreLeaderDTO entityDTO) {
        LambdaQueryWrapper<BizScoreLeader> queryWrapper = new LambdaQueryWrapper();
            List<BizScoreLeader> listData = bizScoreLeaderMapper.selectList(queryWrapper);
            List<BizScoreLeaderDTO> BizScoreLeaderDTOList = ListCopyUtil.copy(listData, BizScoreLeaderDTO.class);
        return BizScoreLeaderDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizScoreLeaderDTO findOne(Long id) {
        BizScoreLeader  bizScoreLeader =  bizScoreLeaderMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizScoreLeader,BizScoreLeaderDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizScoreLeaderDTO create(BizScoreLeaderDTO entityDTO) {
       BizScoreLeader bizScoreLeader =  BeanConvertUtils.copyProperties(entityDTO,BizScoreLeader.class);
        save(bizScoreLeader);
        return  BeanConvertUtils.copyProperties(bizScoreLeader,BizScoreLeaderDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizScoreLeaderDTO entity) {
        BizScoreLeader bizScoreLeader = BeanConvertUtils.copyProperties(entity,BizScoreLeader.class);
        return bizScoreLeaderMapper.updateById(bizScoreLeader);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizScoreLeaderMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizScoreLeaderId
     * @return
     */
    @Override
    public boolean existByBizScoreLeaderId(Long BizScoreLeaderId) {
        if (BizScoreLeaderId != null) {
            LambdaQueryWrapper<BizScoreLeader> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizScoreLeader::getId, BizScoreLeaderId);
            List<BizScoreLeader> result = bizScoreLeaderMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizScoreLeaderDTO> dataList) {
        List<BizScoreLeader> result = ListCopyUtil.copy(dataList, BizScoreLeader.class);
        return saveBatch(result);
    }


}
