package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 日常打分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_score_record")
@ApiModel(value="BizScoreRecord对象", description="日常打分记录表")
public class BizScoreRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被打分的用户ID
     */
    @ApiModelProperty(value = "被打分的用户ID")
    private Long userId;

    /**
     * 被打分的用户姓名
     */
    @ApiModelProperty(value = "被打分的用户姓名")
    private String userName;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;

    @ApiModelProperty(value = "打分年份")
    private Integer scoreYear;

    /**
     * 打分月份
     */
    @ApiModelProperty(value = "打分月份")
    private Integer scoreMonth;

    /**
     * 打分理由
     */
    @ApiModelProperty(value = "打分理由")
    private String remarks;

    @ApiModelProperty(value = "是否修改")
    private Integer hasModify;


}
