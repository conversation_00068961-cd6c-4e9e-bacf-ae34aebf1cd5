package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskCoefficientDTO;
import com.ctsi.huaihua.entity.BizTaskCoefficient;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.huaihua.entity.dto.BizTaskEquationAndScoreDTO;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  任务难度系数服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
public interface IBizTaskCoefficientService extends SysBaseServiceI<BizTaskCoefficient> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskCoefficientDTO> queryListPage(BizTaskCoefficientDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskCoefficientDTO> queryList(BizTaskCoefficientDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskCoefficientDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskCoefficientDTO create(BizTaskCoefficientDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskCoefficientDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskCoefficientId
     * @param code
     * @return
     */
    boolean existByBizTaskCoefficientId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskCoefficientDTO> dataList);

    /**
     * 获取分数
     * @param list
     * @return
     */
    Double getScore(List<BizTaskCoefficientDTO> list);

    /**
     * 获取计算公式
     * @param list
     * @return
     */
    String getEquation(List<BizTaskCoefficientDTO> list);

    /**
     * 根据入参计算总分并拼接公式
     * @param list
     * @return
     */
    BizTaskEquationAndScoreDTO getEquationAndScore(List<BizTaskCoefficientDTO> list);


}
