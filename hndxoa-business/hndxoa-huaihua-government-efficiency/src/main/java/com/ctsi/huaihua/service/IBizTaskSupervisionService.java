package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.huaihua.entity.BizTaskSupervision;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.model.PageResult;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 任务督查的主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IBizTaskSupervisionService extends SysBaseServiceI<BizTaskSupervision> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskSupervisionDTO> queryListPage(BizTaskSupervisionDTO entityDTO, BasePageForm page);

    /**
     * 主任务查询
     * 不含未发布的任务
     */
    PageResult<BizTaskSupervisionDTO> queryNotTempTaskListPage(BizTaskSupervisionDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskSupervisionDTO> queryList(BizTaskSupervisionDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskSupervisionDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskSupervisionDTO create(BizTaskSupervisionDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskSupervisionDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 撤回任务
     *
     * @param id
     * @return
     */
    int withdraw(Long id);

    /**
     * 是否存在
     * <p>
     * existByBizTaskSupervisionId
     *
     * @param code
     * @return
     */
    boolean existByBizTaskSupervisionId(Long code);

    /**
     * 批量新增
     * <p>
     * create batch
     *
     * @param dataList
     * @return
     */
    Boolean insertBatch(List<BizTaskSupervisionDTO> dataList);

    /**
     * 查询全部任务
     *
     * @param entity
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskSupervisionDTO> queryPageAllTasks(BizTaskSupervisionDTO entity, BasePageForm basePageForm);


    /**
     * 获取签收页面的详情
     *
     * @param id
     * @return
     */
    BizSignTaskDTO getSignDetail(Long id);

    /**
     * 根据子表的id获取主表的数据,不包括撤回的
     *
     * @param id
     * @return
     */
    BizTaskSupervisionDTO getSuperDataByDecomposeId(Long id);


    /**
     * 获取本单位的最大任务转办的编号
     *
     * @param s
     * @return
     */
    String getSuperTransferMaxNumber(String s);

    PageResult<BizTaskSupervisionDTO> listTaskPublishReport(BizTaskSupervisionDTO dto, BasePageForm basePageForm);

    /**
     * 查询单条 任务数据包含 分解任务,分解反馈信息 .
     */
    BizTaskSupervisionDTO getSubAndDecAndFeedbackInfo(Long id);

    /**
     * 查询督查任务数据包含分解任务数据信息
     */
    BizTaskSupervisionDTO querySupAndDecList(Long id);

    /**
     * 查询单条 任务数据包含 分解任务,分解反馈信息 .
     */
    BizTaskSupervisionDTO getSubAndDecAndFeedbackInfoGroupByCommpany(Long id);

    /**
     * 一键催办
     *
     * @param dto
     */
    void messageReminders(BizTaskDTO dto);


    /**
     * 条件查询(无数据隔离)
     *
     * @param entity
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskSupervisionDTO> queryPageByCondition(BizTaskSupervisionDTO entity, BasePageForm basePageForm);

    /**
     * 首页 分解任务办结数统计 列表
     *
     * @param bizDeTaskFinishCtDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizDeTaskFinishCtDTO> queryDecomposeTaskGroupDutyPeople(BizDeTaskFinishCtDTO bizDeTaskFinishCtDTO, BasePageForm basePageForm);

    /**
     * 查询当前用户创建的督察主任务列表，不分页
     *
     * @return
     */
    List<BizTaskSupervisionDTO> querySupervisionList();


    /**
     * 待审任务（待审，已发布，已驳回）
     *
     * @param bizDeTaskFinishCtDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizTaskSupervisionDTO> queryPendingTasks(BizPendingTasksDTO bizDeTaskFinishCtDTO, BasePageForm basePageForm);

    /**
     * 审批任务通过
     *
     * @param taskId
     */
    Integer adoptApproval(Long taskId);

    /**
     * 驳回
     *
     * @param taskId
     * @param rejectReason
     * @return
     */
    Integer reject(Long taskId, String rejectReason);

    /**
     * 查询角标
     *
     * @return
     */
    Integer getAngleMark();

    /**
     * 根据主建id获取任务分数
     *
     * @param id
     * @return
     */
    Double getScoreById(Long id);

    /**
     * 获取分值和该数据是否是牵头人单位
     *
     * @param decomposeId
     * @return
     */
    BizScoreByIdDTO getScoreAndLead(Long decomposeId);

    /**
     * 根据主建id获取计算公式
     *
     * @param id
     * @return
     */
    String getEquationById(Long id);

    /**
     * 根据入参计算总分并拼接公式
     *
     * @param id
     * @return
     */
    BizTaskEquationAndScoreDTO getEquationAndScoreByid(Long id);


    /**
     * 查询当前用户ID是否是牵头人
     *
     * @param userId
     * @return
     */
    int isLeadPeople(Long userId);

    /**
     * 查询首页全市年得分统计(查询租户下的或者某个单位下的)
     *
     * @param cscpUsers
     * @return
     */
    List<BizTaskInformationDTO> scoreStatistics(List<CscpUserDTO> cscpUsers);

    /**
     * 统计市政府全市年得分统计
     *
     * @return
     */
    List<BizTaskInformationDTO> municipalGovernment();

    /**
     * 统计市直单位,单位得分统计
     *
     * @return
     */
    List<BizTaskInformationDTO> municipalUnits();

    /**
     * 统计本年交办任务
     *
     * @param assignedCondition
     * @return
     */
    List<BizTaskDTO> statisticsAssigned(BizTaskStatisticsDTO assignedCondition);


    /**
     * 展示全市单位（市直单位）月分数排名，取单位一把手每月分数
     *
     * @return
     */
    List<Map<String, Object>> firstHandTotalScore();

    /**
     * 查询对应用户的任务
     *
     * @param bizTaskStatisticsDTO
     * @return
     */
    List<BizTaskDTO> selectUserTasks(BizTaskStatisticsDTO bizTaskStatisticsDTO);

    /**
     * 查询对应任务的任务分,并且相加
     *
     * @param userIds
     * @param startTime
     * @param endTime
     * @return
     */
    Map<Long, Double> selectBizTaskScores(Set<Long> userIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询对应任务的减分分,并且相加
     *
     * @param userIds
     * @param startTime
     * @param endTime
     * @return
     */
    Map<Long, Double> selectBizTaskSupervisions(Set<Long> userIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询对应任务的加分分,并且相加
     *
     * @param userIds
     * @param startTime
     * @param endTime
     * @return
     */
    Map<Long, Double> selectBizTaskScoreAdds(Set<Long> userIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询对应用户的日常分,并且相加
     *
     * @param userIds
     * @return
     */
    Map<Long, Double> selectBizScoreRecords(Set<Long> userIds, Integer startMonth, Integer endMonth);

    /**
     * 获取一把手
     *
     * @return
     */
    Set<BizUserScoreDTO> getFirstHand();

    /**
     * 全市单位总任务数量统计
     *
     * @return
     */
    List<Map<String, Object>> companyAllNumberOfTasks();

    /**
     * 本单位月度任务数量统计
     *
     * @return
     */
    List<Map<String, Object>> companyNumberOfTasks();


    /**
     * 根据条件查询某个单位接收的任务
     *
     * @param bizTaskStatisticsDTO
     * @return
     */
    List<BizTaskDTO> receiveTask(BizTaskStatisticsDTO bizTaskStatisticsDTO);


    /**
     * 已接收任务
     *
     * @param sendCondition
     * @return
     */
    List<BizTaskDTO> received(BizTaskStatisticsDTO sendCondition);

    /**
     * 统计本年交办任务，已办结任务，待办结任务
     *
     * @return
     */
    Map<String, Integer> assignedCompletedToBeSettledTask();

    /**
     * 市直单位本年接收任务，本年已发任务，本年已办结任务
     *
     * @return
     */
    Map<String, Integer> receiveSentCompletedTask();

    /**
     * 统计各单位一把手分数
     *
     * @return
     */
    List<BizTaskInformationDTO> firstHandFraction();

    /**
     * 统计接收任务和办结任务数
     *
     * @param bizTaskStatisticsDTO
     * @return
     */
    List<BizTaskDTO> receiveConcludeTaskCount(BizTaskStatisticsDTO bizTaskStatisticsDTO);
}
