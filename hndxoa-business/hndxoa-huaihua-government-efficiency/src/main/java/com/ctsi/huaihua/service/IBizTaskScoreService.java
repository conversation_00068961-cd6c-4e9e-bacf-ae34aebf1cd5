package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskScoreDTO;
import com.ctsi.huaihua.entity.BizTaskScore;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 任务得分 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface IBizTaskScoreService extends SysBaseServiceI<BizTaskScore> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskScoreDTO> queryListPage(BizTaskScoreDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskScoreDTO> queryList(BizTaskScoreDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskScoreDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskScoreDTO create(BizTaskScoreDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskScoreDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskScoreId
     * @param code
     * @return
     */
    boolean existByBizTaskScoreId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskScoreDTO> dataList);


    /**
     * 根据子任务的id获取任务的分值
     * @param bizTaskDecompose
     * @return
     */
    BizTaskScoreDTO getTaskScoreByDecomposeId(Long bizTaskDecompose);
}
