package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.systenant.entity.dto.TenantUserListDTO;
import com.ctsi.huaihua.entity.dto.BizScoreScopeDTO;
import com.ctsi.huaihua.service.IBizScoreScopeService;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgAndUserDto;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizScoreScope")
@Api(value = "领导打分范围表", tags = "领导打分范围表接口")
public class BizScoreScopeController extends BaseController {

    private static final String ENTITY_NAME = "bizScoreScope";

    @Autowired
    private IBizScoreScopeService bizScoreScopeService;


    /**
     * 新增领导委托信息表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizScoreScope.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增领导委托信息表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreScope.add')")
    public ResultVO createBatch(@RequestBody List<BizScoreScopeDTO> bizScoreScopeList) {
        Boolean result = bizScoreScopeService.insertBatch(bizScoreScopeList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizScoreScope.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增领导委托信息表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreScope.add')")
    public ResultVO<BizScoreScopeDTO> create(@RequestBody BizScoreScopeDTO bizScoreScopeDTO) {
        BizScoreScopeDTO result = bizScoreScopeService.create(bizScoreScopeDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizScoreScope.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新领导委托信息表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreScope.update')")
    public ResultVO update(@RequestBody BizScoreScopeDTO bizScoreScopeDTO) {
        Assert.notNull(bizScoreScopeDTO.getId(), "general.IdNotNull");
        int count = bizScoreScopeService.update(bizScoreScopeDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除领导委托信息表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizScoreScope.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreScope.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizScoreScopeService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizScoreScopeDTO bizScoreScopeDTO = bizScoreScopeService.findOne(id);
        return ResultVO.success(bizScoreScopeDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizScoreScopePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizScoreScopeDTO>> queryBizScoreScopePage(BizScoreScopeDTO bizScoreScopeDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizScoreScopeService.queryListPage(bizScoreScopeDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryBizScoreScope")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizScoreScopeDTO>> queryBizScoreScope(BizScoreScopeDTO bizScoreScopeDTO) {
        List<BizScoreScopeDTO> list = bizScoreScopeService.queryList(bizScoreScopeDTO);
        return ResultVO.success(new ResResult<BizScoreScopeDTO>(list));
    }


    @PostMapping("/updateScoreScope")
    @ApiOperation(value = "编辑日常打分范围(权限code码为：cscp.bizScoreScope.insertScoreScope)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增日常打分范围")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreScope.insertScoreScope')")
    public ResultVO updateScoreScope(@RequestBody BizScoreScopeDTO bizScoreScopeDTO) {
        Boolean bool = bizScoreScopeService.updateScoreScope(bizScoreScopeDTO);
        return ResultVO.success(bool);
    }

    @PostMapping("/deleteScoreScope")
    @ApiOperation(value = "删除日常打分范围(权限code码为：cscp.bizScoreScope.deleteScoreScope)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "删除日常打分范围")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreScope.deleteScoreScope')")
    public ResultVO deleteScoreScope(@RequestBody BizScoreScopeDTO bizScoreScopeDTO) {
        Boolean bool = bizScoreScopeService.deleteScoreScope(bizScoreScopeDTO);
        return ResultVO.success(bool);
    }

    @GetMapping("/queryBizLeaderScoreScopePage")
    @ApiOperation(value = "分页查询领导日常打分列表", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizScoreScopeDTO>> queryBizLeaderScoreScopePage(BizScoreScopeDTO bizScoreScopeDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizScoreScopeService.queryBizLeaderScoreScopePage(bizScoreScopeDTO, basePageForm));
    }

    @GetMapping("/queryBizLeaderScoreScopeList")
    @ApiOperation(value = "查询该领导日常打分用户列表", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<List<CscpUserDTO>> queryBizLeaderScoreScopeList(BizScoreScopeDTO bizScoreScopeDTO) {
        if (Objects.isNull(bizScoreScopeDTO.getLeaderId())) {
            bizScoreScopeDTO.setLeaderId(SecurityUtils.getCurrentUserId());
        }
        return ResultVO.success(bizScoreScopeService.queryBizLeaderScoreScopeList(bizScoreScopeDTO));
    }


    @ApiOperation(value = "分页查询指定机构下的所有用户, 排除免考核用户")
    @GetMapping("/pageQueryUserNotInList/{id}")
    public ResultVO<PageResult<CscpUserDTO>> pageQueryUserNotInList(@PathVariable(value = "id", required = true) Long id,
                                                                    @RequestParam(required = false) String realName,
                                                                    BasePageForm basePageForm) {
        PageResult<CscpUserDTO> cscpUserDTOS = bizScoreScopeService.pageQueryUserNotInList(id, realName, basePageForm);
        return ResultVO.success(cscpUserDTOS);
    }

    @ApiOperation(value = "不分页查询本单位下的用户, 排除免考核用户")
    @GetMapping("/queryCompanyUserNotInList")
    public ResultVO<CscpOrgAndUserDto> queryCompanyUserNotInList(@RequestParam(value = "parentId", defaultValue = "0") Long parentId,
                                                                 String realName) {
        CscpOrgAndUserDto cscpUserDTOS = bizScoreScopeService.queryCompanyUserNotInList(parentId, realName);
        return ResultVO.success(cscpUserDTOS);
    }

    @ApiOperation(value = "搜索获取上下级租户下的用户, 排除免考核用户")
    @GetMapping("/queryTenantUserNotInList")
    public ResultVO<TenantUserListDTO> queryTenantUserNotInList(@RequestParam(required = false) String realName) {
        TenantUserListDTO tenantUserList = bizScoreScopeService.queryTenantUserNotInList(realName);
        return ResultVO.success(tenantUserList);
    }
}
