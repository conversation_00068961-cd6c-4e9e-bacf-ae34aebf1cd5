package com.ctsi.huaihua.controller;

import cn.hutool.core.util.StrUtil;
import com.ctsi.hndx.annotations.PermissionData;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.FormUrlData;
import com.ctsi.hndx.common.datapermission.DataFilterThreadLocal;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.huaihua.job.HuaihuaJobUtil;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskDecompose")
@Api(value = "任务分解表", tags = "任务分解表接口")
public class BizTaskDecomposeController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskDecompose";

    @Autowired
    private IBizTaskDecomposeService bizTaskDecomposeService;


    /**
     * 新增任务分解表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskDecompose.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增任务分解表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDecompose.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskDecomposeDTO> bizTaskDecomposeList) {
        Boolean result = bizTaskDecomposeService.insertBatch(bizTaskDecomposeList);
        return ResultVO.success();

    }

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskDecompose.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增任务分解表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDecompose.add')")
    public ResultVO<BizTaskDecomposeDTO> create(@RequestBody BizTaskDecomposeDTO bizTaskDecomposeDTO) {
        BizTaskDecomposeDTO result = bizTaskDecomposeService.create(bizTaskDecomposeDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     * 承办管理：接收保存、任务转办
     * 任务办结
     */
    @PostMapping("/update")
    @ApiOperation(value = "接收保存、任务转办)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新任务分解表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDecompose.update')")
    public ResultVO update(@RequestBody BizTaskDecomposeDTO bizTaskDecomposeDTO) {
        Assert.notNull(bizTaskDecomposeDTO.getId(), "general.IdNotNull");
        int count = bizTaskDecomposeService.update(bizTaskDecomposeDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * 签收任务
     */
    @GetMapping("/updateHasSign/{id}")
    @ApiOperation(value = "签收任务、任务转办)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "签收任务")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDecompose.update')")
    public ResultVO updateHasSign(@PathVariable Long id) {
        bizTaskDecomposeService.updateHasSign(id);
        return ResultVO.success("签收成功");
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除任务分解表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskDecompose.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDecompose.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskDecomposeService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 任务接收详情页 查询单条数据.
     * 反馈结果
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "任务接收详情页,分解任务包含反馈结果", notes = "请传入子任务id参数 task_decompose_id")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    // @ApiResponses(value = {
    //         @ApiResponse(code = 200, message = "成功", response = BizTaskDetailsDecomposeDTO.class)})
    public ResultVO<BizTaskDetailsDecomposeDTO> get(@PathVariable @ApiParam(value = "请传入子任务id参数 task_decompose_id") Long id) {
        BizTaskDetailsDecomposeDTO bizTaskDetailsDecomposeDTO = bizTaskDecomposeService.findOneDetail(id);
        return ResultVO.success(bizTaskDetailsDecomposeDTO);
    }

    /**
     * 根据主任务id获取所有子任务,并且标记哪个子任务是自己的
     *
     * @param id
     * @return
     */
    @GetMapping("/getTaskDecomposeAll/{id}")
    @ApiOperation(value = "根据主任务id获取所有子任务")
    public ResultVO<List<BizTaskDetailsDecomposeDTO>> getTaskDecomposeAll(@PathVariable("id") Long id) {
        List<BizTaskDetailsDecomposeDTO> bizTaskDetailsDecomposeDTO = bizTaskDecomposeService.getTaskDecomposeAll(id);
        return ResultVO.success(bizTaskDetailsDecomposeDTO);
    }

    /**
     * 查询多条数据
     */
    @GetMapping("/queryBizTaskDecomposePage")
    @ApiOperation(value = "分页查询 通用承办管理任务查看表", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDecomposeDTO>> queryBizTaskDecomposePage(BizTaskDecomposeDTO bizTaskDecomposeDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskDecomposeService.queryListPage(bizTaskDecomposeDTO, basePageForm));
    }

    /**
     * 查询未签收的数据
     */
    @GetMapping("/selectNoSignCommonTaskList")
    @ApiOperation(value = "查询多条数据.分页 通用承办管理任务查看表", notes = "传入参数")
    public ResultVO<PageResult<BizTaskDTO>> selectNoSignCommonTaskList(NoHasSignTaskDTO bizTaskDTO, BasePageForm basePageForm) {

        //  查询待签收的，签收自己的，如果是办公室可能还签收市政府下发给领导的，但是不能签收自己单位的其他人的
        if (SecurityUtils.hasPermissions(TaskStatus.TASK_SIGN_CODE)) {
            bizTaskDTO.setQueryType(2);
            bizTaskDTO.setCompanyId(SecurityUtils.getCurrentCompanyId());
        } else {
            bizTaskDTO.setQueryType(1);
        }
        bizTaskDTO.setDutyPeopleId(SecurityUtils.getCurrentUserId());
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.selectNoSignCommonTaskList(bizTaskDTO, basePageForm);
        return ResultVO.success(list);
    }


    /**
     * 查询多条数据.分页 通用承办管理任务查看表
     */
    @GetMapping("/queryBizTaskDecompose")
    @ApiOperation(value = "查询多条数据.分页 通用承办管理任务查看表", notes = "传入参数")
    public ResultVO<PageResult<BizTaskDTO>> queryBizTaskDecompose(BizTaskDTO bizTaskDTO, BasePageForm basePageForm) {
        bizTaskDTO.setSignPeopleId(SecurityUtils.getCurrentUserId());
        bizTaskDTO.setSortField("receive_time");
        bizTaskDTO.setSortType("desc");
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.queryList(bizTaskDTO, basePageForm);
        return ResultVO.success(list);
    }


    /**
     * 查询通报任务，分页
     */
    @GetMapping("/queryNotificationTask")
    @ApiOperation(value = "查询通报任务，分页", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDTO>> queryNotificationTask(BizTaskDTO bizTaskDTO, BasePageForm basePageForm) {
        bizTaskDTO.setDutyPeopleId(SecurityUtils.getCurrentUserId());
        bizTaskDTO.setSortField("receive_time");
        bizTaskDTO.setSortType("desc");
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.queryNotificationTask(bizTaskDTO, basePageForm);
        return ResultVO.success(list);
    }


    /**
     * 查询责任人和接收人
     */
    @GetMapping("/queryPlr/{id}")
    public ResultVO<List<BizPersonLiableAndReceiverDTO>> queryPersonLiableAndReceiver(@PathVariable(name = "id") Long subId) {
        List<BizPersonLiableAndReceiverDTO> list = bizTaskDecomposeService.queryPersonLiableAndReceiver(subId);
        return ResultVO.success(list);
    }


    /**
     * 获取签收的可以选择的转办的任务，只能选择一次
     */
    @GetMapping("/queryTaskTransferNumber")
    @ApiOperation(value = "获取签收的可以选择的转办的任务，只能选择一次,有传入的id就是任务的id", notes = "传入参数")
    public ResultVO<List<FormUrlData>> queryTaskTransferNumber(Long taskDecomposeId) {
        List<BizTaskDTO> list = bizTaskDecomposeService.queryTaskTransferNumber(SecurityUtils.getCurrentUserId(), taskDecomposeId);
        List<FormUrlData> formUrlDataList = new ArrayList<>();
        list.forEach(bizTaskDTO -> {
            FormUrlData formUrlData = new FormUrlData();
            formUrlData.setLabel(bizTaskDTO.getTaskNumber());
            formUrlData.setValue(String.valueOf(bizTaskDTO.getTaskDecomposeId()));
            formUrlDataList.add(formUrlData);
        });
        return ResultVO.success(formUrlDataList);
    }


    /**
     * 查询分解任务  根据主任务id
     * 右侧对应分解任务默认以截止时间升序排序；如有最新成果数据，则将最新分解任务置顶
     * 已办结任务查询 : 主任务已经办结
     */
    @GetMapping("/getListBySubId")
    @ApiOperation(value = "成果收件箱查询 分解任务 通用接口", notes = "请传入主任务id参数 taskSupervisionId, id可以为空,")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    @PermissionData(pageComponent = "outcomeInbox")
    public ResultVO<PageResult<BizTaskDTO>> getListBySubId(BizTaskDTO dto, BasePageForm basePageForm) {
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        long currentUserId = SecurityUtils.getCurrentUserId();
        boolean myFilter = DataFilterThreadLocal.get() == null;// 个人
        if (myFilter) { // 个人
            dto.setSupCreateById(currentUserId);
        } else { // 配置 管理员角色,按照单位过滤
            dto.setSupCompanyId(currentCompanyId);
        }
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.getTaskListBySubId(dto, basePageForm);
        return ResultVO.success(list);
    }

    /**
     * 查询分解任务
     */
    @GetMapping("/getAllTheSubtasks")
    @ApiOperation(value = "查询分解任务,分页", notes = "请传入主任务id参数")
    public ResultVO<PageResult<BizTaskDTO>> getAllTheSubtasks(BizTaskDTO dto, BasePageForm basePageForm) {
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        long currentUserId = SecurityUtils.getCurrentUserId();
        boolean myFilter = DataFilterThreadLocal.get() == null;// 个人
        if (myFilter) { // 个人
            dto.setSupCreateById(currentUserId);
        } else { // 配置 管理员角色,按照单位过滤
            dto.setSupCompanyId(currentCompanyId);
        }
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.getAllTheSubtasks(dto, basePageForm);
        return ResultVO.success(list);
    }


    /**
     * // 内容：统计当前单位所有已接收的任务，不包含基于接收任务之后的转办任务
     * 承办任务台账
     * 只显示本单位
     * 排序：按交办时间降序排序
     */
    @GetMapping("/listReport")
    @ApiOperation(value = "承办任务台账,分页", notes = "")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDTO>> listReport(BizTaskDTO dto, BasePageForm basePageForm) {
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        dto.setHasSign(1);  // 已接收的任务
            dto.setDutyPeopleCompanyId(currentCompanyId);  //只显示本单位
        // 不包含基于接收任务之后的转办任务
        dto.setTaskAddType(0);
        // 不显示已撤回状态的任务
        dto.setHasPublish(1);
        PageResult<BizTaskDTO> list = bizTaskDecomposeService.listReport(dto, basePageForm, TaskStatus.TASK_DECOMPOSE_DUE_TIME);
        return ResultVO.success(list);
    }


    /**
     * 更新存在数据.
     * 任务办结
     * 返回值 0 代表主表未办结 ; 1 代表主表已办结
     */
    @PostMapping("/updateHasFinish")
    @ApiOperation(value = "任务办结更新; 返回值 0 代表主表未办结 ; 1 代表主表已办结", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "任务办结更新")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDecompose.update')")
    public ResultVO updateHasFinish(@RequestBody BizTaskDecomposeScoreDTO bizTaskDecomposeScoreDTO) {
        Assert.notNull(bizTaskDecomposeScoreDTO.getId(), "general.IdNotNull");
        int count = bizTaskDecomposeService.updateHasFinish(bizTaskDecomposeScoreDTO);
        return ResultVO.success(count);

    }


    /**
     * 反馈结果
     */
    @GetMapping("/getDecIncludeFeedbackInfo/{id}")
    @ApiOperation(value = "查询分解任务信息包含反馈结果", notes = "请传入子任务id参数 task_decompose_id")
    public ResultVO<BizTaskDetailsDecomposeDTO> getDecIncludeFeedbackInfo(@PathVariable @ApiParam(value = "请传入子任务id参数task_decompose_id") Long id) {
        BizTaskDetailsDecomposeDTO bizTaskDetailsDecomposeDTO = bizTaskDecomposeService.getDecIncludeFeedbackInfo(id);
        return ResultVO.success(bizTaskDetailsDecomposeDTO);
    }

    // update

    /**
     * 更新最新反馈的成果标记.
     */
    @PostMapping("/updateFeedbackSign")
    @ApiOperation(value = "更新最新反馈的成果标记", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "传分解任务id,和更新状态码")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDecompose.update')")
    public ResultVO<BizTaskSupervisionDTO> updateFeedbackSign(@RequestBody BizTaskDecomposeDTO bizTaskDecomposeDTO) {
        Assert.notNull(bizTaskDecomposeDTO.getId(), "general.IdNotNull");
        int refreshFlag = bizTaskDecomposeService.updateFeedbackSign(bizTaskDecomposeDTO);
        BizTaskSupervisionDTO bizTaskSupervisionDTO = new BizTaskSupervisionDTO();
        bizTaskSupervisionDTO.setRefreshFlag(StrUtil.toString(refreshFlag));
        return ResultVO.success(bizTaskSupervisionDTO);

    }
// -----------------------------------------任务角标----------------------------------------------------start

    /**
     * 首页角标
     * 快捷菜单入口，其中以下具备角标
     */
    @GetMapping("/cornerMarkData/{type}")
    @ApiOperation(value = " 快捷菜单入口，角标接口;type=?;1==任务收件箱,2==成果收件箱,3==成果办结 ", notes = " ")
    @PermissionData(pageComponent = "outcomeInbox")
    public ResultVO<Integer> cornerMarkData(@PathVariable @ApiParam(value = "请传入类别参数 ") Integer type) {
        Integer counts = bizTaskDecomposeService.cornerMarkData(type);

        return ResultVO.success(counts);
    }

    @GetMapping("/cornerMarkTaskBox")
    @ApiOperation(value = " 任务收件箱角标接口 ", notes = " ")
    public ResultVO<Integer> cornerMarkTaskInbox() {
        Integer counts = bizTaskDecomposeService.cornerMarkTaskInbox();

        return ResultVO.success(counts);
    }

    @GetMapping("/cornerMarkResultsInbox")
    @ApiOperation(value = " 成果收件箱角标接口 ", notes = " ")
    @PermissionData(pageComponent = "outcomeInbox") //单位管理员的按照单位管理员登录
    public ResultVO<Integer> cornerMarkResultsInbox() {
        Integer counts = bizTaskDecomposeService.cornerMarkResultsInbox();

        return ResultVO.success(counts);
    }


    @GetMapping("/cornerMarkTemporaryTask")
    @ApiOperation(value = " 临期任务角标接口 ", notes = " ")
    public ResultVO<Integer> cornerMarkTemporaryTask() {
        Integer counts = bizTaskDecomposeService.cornerMarkTemporaryTask();
        return ResultVO.success(counts);
    }

    @GetMapping("/cornerMarkOverdueTask")
    @ApiOperation(value = " 逾期任务角标接口 ", notes = " ")
    public ResultVO<Integer> cornerMarkOverdueTask() {
        //queryTaskTemporaryAndOverdue
        Integer counts = bizTaskDecomposeService.cornerMarkOverdueTask();
        return ResultVO.success(counts);
    }

    @PostMapping("/queryScoreDetailList")
    @ApiOperation(value = "查询分解表对应的加减分申请列表", notes = "传入参数")
    public ResultVO<BizTaskScoreDetailDTO> queryScoreDetailList(@RequestBody BizTaskSupervisionAndScoreAddDTO bizTaskSupervisionAndScoreAddDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionAndScoreAddDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionAndScoreAddDTO.getPageSize());
        BizTaskScoreDetailDTO result = bizTaskDecomposeService.queryScoreDetailList(bizTaskSupervisionAndScoreAddDTO, basePageForm);
        return ResultVO.success(result);
    }

// -----------------------------------------任务角标----------------------------------------------------end


    //定时任务测试
    @GetMapping("/autoTaskRemind")
    public void autoTaskRemind() {
        LocalDate now = LocalDate.of(2022, 4, 29);
        HuaihuaJobUtil.autoTaskRemind(now);
    }


    /**
     * 报表,执行详情
     */
    @GetMapping("/queryImplementationDetail/{id}")
    @ApiOperation(value = "报表,执行详情", notes = "请传入分解任务id")
    public ResultVO<TaskImplementationDetailDTO> queryImplementationDetail(@PathVariable @ApiParam(value = "请传入子任务id参数task_decompose_id") Long id) {
        Assert.notNull(id, "general.IdNotNull");
        TaskImplementationDetailDTO bizTaskDetailsDecomposeDTO = bizTaskDecomposeService.queryImplementationDetail(id);
        return ResultVO.success(bizTaskDetailsDecomposeDTO);
    }

    /**
     * 根据分解任务id,查找该条分解任务转办 的所有任务的反馈记录
     */
    @GetMapping("/getDecomposeTaskTransferFeedback")
    @ApiOperation(value = "根据分解任务id,查找该条分解任务转办 的所有任务的反馈记录", notes = "id")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<List<BizTaskFeedbackDTO>> getDecomposeTaskTransferFeedback(BizTaskDecomposeDTO bizTaskDecomposeDTO) {
        Assert.notNull(bizTaskDecomposeDTO.getId(), "general.IdNotNull");

        List<BizTaskFeedbackDTO> list = bizTaskDecomposeService.getDecomposeTaskTransferFeedback(bizTaskDecomposeDTO);

        return ResultVO.success(list);
    }


}
