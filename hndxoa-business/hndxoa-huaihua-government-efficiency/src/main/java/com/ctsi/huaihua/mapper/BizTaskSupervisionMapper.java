package com.ctsi.huaihua.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.huaihua.entity.BizTaskSupervision;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.huaihua.entity.dto.BizDeTaskFinishCtDTO;
import com.ctsi.huaihua.entity.dto.BizReceiveConcludeTaskCountDTO;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 任务督查的主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface BizTaskSupervisionMapper extends MybatisBaseMapper<BizTaskSupervision> {

    /**
     *
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<BizDeTaskFinishCtDTO> queryDecomposeTaskGroupDutyPeople(IPage<BizDeTaskFinishCtDTO> page,
                                                                  @Param("taskFinishCtDTO") BizDeTaskFinishCtDTO taskFinishCtDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskSupervision> queryList(@Param("dto") BizTaskSupervisionDTO dto);

    /**
     * 统计本年交办任务
     *
     * @param companyIds
     * @param startTime
     * @param endTime
     * @param hasFinish
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskDTO> statisticalAssignment(@Param("companyIds") Set<Long> companyIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("hasFinish") Integer hasFinish);

    /**
     * 统计接收任务和办结任务数
     *
     * @param bizReceiveConcludeTaskCount
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<BizTaskDTO> queryReceiveConcludeTaskCount(@Param("conditions") BizReceiveConcludeTaskCountDTO bizReceiveConcludeTaskCount);
}
