package com.ctsi.huaihua.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.receive.entity.dto.AuthorizationGroupDTO;
import com.ctsi.hndx.receive.entity.dto.BizQueryHhClientDTO;
import com.ctsi.hndx.receive.entity.dto.GroupPopulationDTO;
import com.ctsi.hndx.receive.entity.dto.PopulationGroupDTO;
import com.ctsi.hndx.receive.service.IBizLeaderLiaisonService;
import com.ctsi.hndx.utils.FileNameUtil;
import com.ctsi.hndx.utils.ZipFileUtil;
import com.ctsi.huaihua.entity.BizTaskFeedback;
import com.ctsi.huaihua.entity.dto.BizTaskDecomposeDTO;
import com.ctsi.huaihua.entity.dto.BizTaskExportDTO;
import com.ctsi.huaihua.entity.dto.BizTaskImportDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import com.ctsi.huaihua.entity.dto.DutyPeopleDTO;
import com.ctsi.huaihua.service.BizTaskImportAndExportService;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.huaihua.service.IBizTaskFeedbackService;
import com.ctsi.huaihua.service.IBizTaskSupervisionService;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.service.impl.CscpEnclosureFileServiceImpl;
import com.ctsi.sysimport.domain.dto.TSysImportDTO;
import com.ctsi.sysimport.service.ISysImportService;
import com.ctsi.sysimport.util.SysImportTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Classname BizTaskImportAndExportServiceImpl
 * @Description
 * @Date 2022/3/15/0015 15:32
 */
@Slf4j
@Service
public class BizTaskImportAndExportServiceImpl implements BizTaskImportAndExportService {

    @Autowired
    private IBizLeaderLiaisonService iBizLeaderLiaisonService;

    @Autowired
    private ISysImportService iSysImportService;

    @Autowired
    private IBizTaskFeedbackService iBizTaskFeedbackService;

    @Autowired
    private CscpEnclosureFileServiceImpl cscpEnclosureFileService;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private IBizTaskSupervisionService iBizTaskSupervisionService;

    @Autowired
    private IBizTaskDecomposeService iBizTaskDecomposeService;

    @Override
    public List<BizTaskExportDTO> assemblyTaskImport(List<BizTaskImportDTO> dtoList) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 成功数据集合
        List<BizTaskExportDTO> bizTaskExportDTOList = new ArrayList<>();

        // 失败数据集合
        List<BizTaskExportDTO> failedList = new ArrayList<>();

        for (BizTaskImportDTO dto : dtoList) {
            dto.setSmsReminder(this.getSmsReminderCode(dto.getSmsReminder()));
            dto.setLeadingUnit(this.getLeadingUnitCode(dto.getLeadingUnit()));
            AuthorizationGroupDTO authorizationGroupDTO = iBizLeaderLiaisonService.queryHhClient(BizQueryHhClientDTO.builder().modelForm("task_supervision").leaderName(dto.getDutyPeopleName()).build());
            BizTaskExportDTO failedDTO = new BizTaskExportDTO();
            BeanUtils.copyProperties(dto, failedDTO);

            // 参数信息校验
            boolean bool = this.verifyTaskImportDTO(authorizationGroupDTO, failedDTO);
            if (!bool) {
                failedDTO.setSmsReminder(this.getSmsReminderValue(failedDTO.getSmsReminder()));
                failedDTO.setLeadingUnit(this.getLeadingUnitValue(failedDTO.getLeadingUnit()));
                failedList.add(failedDTO);
                continue;
            }
            dto.setStartDate(LocalDate.parse(dto.getStartDateStr(), fmt));
            dto.setEndDate(LocalDate.parse(dto.getEndDateStr(), fmt));

            // 组装回参数据
            BizTaskExportDTO bizTaskExportDTO = new BizTaskExportDTO();
            BeanUtils.copyProperties(dto, bizTaskExportDTO);
            GroupPopulationDTO populationDTO = authorizationGroupDTO.getPopulationGroupList().get(0).getGroupUsers().get(0);
            if (Objects.nonNull(populationDTO)) {
                List<DutyPeopleDTO> dutyPeopleDTOList = new ArrayList<>();
                DutyPeopleDTO dutyPeopleDTO = new DutyPeopleDTO();
                dutyPeopleDTO.setUserId(String.valueOf(populationDTO.getTemplateBusinessId()));
                dutyPeopleDTO.setUserName(populationDTO.getTemplateBusinessName());
                dutyPeopleDTO.setGroupName(populationDTO.getGroupName());
                dutyPeopleDTO.setCompanyId(populationDTO.getUnitId());
                dutyPeopleDTO.setCompanyName(populationDTO.getUnitName());
                dutyPeopleDTO.setDepartmentId(populationDTO.getBranchId());
                dutyPeopleDTO.setDepartmentName(populationDTO.getUnitName());
                dutyPeopleDTO.setMobile(populationDTO.getMobile());
                dutyPeopleDTO.setPost(populationDTO.getPost());

                // 委托人
                List<DutyPeopleDTO> groupPopulationDTO = new ArrayList<>();
                for (GroupPopulationDTO clientDTO :  populationDTO.getGroupPopulationDTO()) {
                    DutyPeopleDTO client  = new DutyPeopleDTO();
                    client.setUserId(String.valueOf(clientDTO.getTemplateBusinessId()));
                    client.setUserName(clientDTO.getTemplateBusinessName());
                    client.setGroupName(clientDTO.getGroupName());
                    client.setCompanyId(clientDTO.getUnitId());
                    client.setCompanyName(clientDTO.getUnitName());
                    client.setDepartmentId(clientDTO.getBranchId());
                    client.setDepartmentName(clientDTO.getUnitName());
                    client.setMobile(clientDTO.getMobile());
                    client.setPost(clientDTO.getPost());
                    groupPopulationDTO.add(client);
                }
                dutyPeopleDTO.setGroupPopulationDTO(groupPopulationDTO);

                dutyPeopleDTOList.add(dutyPeopleDTO);
                String dutyPeople = JSON.toJSONString(dutyPeopleDTOList);
                bizTaskExportDTO.setDutyPeople(dutyPeople);
            }
            bizTaskExportDTOList.add(bizTaskExportDTO);
        }

        TSysImportDTO sysUserImportDTO = new TSysImportDTO();
        sysUserImportDTO.setTotalNo(dtoList.size());
        sysUserImportDTO.setFailedNo(failedList.size());
        sysUserImportDTO.setSuccessNo(dtoList.size() - failedList.size());
        // 导入数据类型
        sysUserImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.TASK_IMPORT));

        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            return bizTaskExportDTOList;
        }

        // 保存导入记录，并上传Excel失败文件
        iSysImportService.saveAndUploadFile(sysUserImportDTO, failedList, BizTaskExportDTO.class, FileBasePathName.TASK_IMPORT);

        return bizTaskExportDTOList;
    }

    /**
     * 参数校验
     * @param authorizationGroupDTO
     * @param failedDTO
     * @return
     */
    private boolean verifyTaskImportDTO(AuthorizationGroupDTO authorizationGroupDTO, BizTaskExportDTO failedDTO) {
        if (StringUtils.isBlank(failedDTO.getDutyPeopleName())) {
            String failReason = "责任人姓名不允许为空";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        if (StringUtils.isBlank(failedDTO.getContent())) {
            String failReason = "任务内容不允许为空";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        if (StringUtils.isBlank(failedDTO.getStartDateStr())) {
            String failReason = "开始时间不允许为空";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        if (StringUtils.isBlank(failedDTO.getEndDateStr())) {
            String failReason = "截止时间不允许为空";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        try {
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            failedDTO.setStartDate(LocalDate.parse(failedDTO.getStartDateStr(), fmt));
            failedDTO.setEndDate(LocalDate.parse(failedDTO.getEndDateStr(), fmt));
        } catch (Exception e) {
            String failReason = "开始时间或截止时间格式错误";
            failedDTO.setFailedReason(failReason);
            return false;
        }
//        if (StringUtils.isBlank(failedDTO.getDegreeDifficulty())) {
//            String failReason = "难度系数不允许为空";
//            failedDTO.setFailedReason(failReason);
//            return false;
//        }
        if (StringUtils.isBlank(failedDTO.getSmsReminder())) {
            String failReason = "短信提醒不允许为空";
            failedDTO.setFailedReason(failReason);
            return false;
        }

        if (StringUtils.isBlank(failedDTO.getLeadingUnit())) {
            String failReason = "牵头单位不允许为空";
            failedDTO.setFailedReason(failReason);
            return false;
        }

        // 责任人信息校验
        boolean bool = Objects.isNull(authorizationGroupDTO) || CollectionUtils.isEmpty(authorizationGroupDTO.getPopulationGroupList());
        if (bool) {
            String failReason = "未找到该责任人信息";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        if (authorizationGroupDTO.getPopulationGroupList().size() > 1) {
            String failReason = "该责任人存在多条姓名相同的记录存在，需要手动操作";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        PopulationGroupDTO populationGroupDTO = authorizationGroupDTO.getPopulationGroupList().get(0);
        if (CollectionUtils.isEmpty(populationGroupDTO.getGroupUsers())) {
            String failReason = "未找到该责任人信息";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        if (populationGroupDTO.getGroupUsers().size() > 1) {
            String failReason = "该责任人存在多条姓名相同的记录存在，需要手动操作";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        if (failedDTO.getStartDate().isAfter(failedDTO.getEndDate())) {
            String failReason = "截止时间不允许设置在开始时间之前";
            failedDTO.setFailedReason(failReason);
            return false;
        }
        return true;
    }


    @Override
    public void downloadFeedBackZip(Long id, HttpServletResponse response) {
        if (Objects.isNull(id)) {
            throw new BusinessException("分解表主键ID不允许为空");
        }
        StringBuilder zipFileName = new StringBuilder();
        BizTaskDecomposeDTO bizTaskDecomposeDTO = iBizTaskDecomposeService.findOne(id);
        if (Objects.isNull(bizTaskDecomposeDTO)) {
            throw new BusinessException("未找到该记录的分解表任务信息");
        }
        BizTaskSupervisionDTO bizTaskSupervisionDTO = iBizTaskSupervisionService.findOne(bizTaskDecomposeDTO.getSubTblFk());
        if (Objects.isNull(bizTaskSupervisionDTO)) {
            throw new BusinessException("未找到该记录的主表任务信息");
        }
        zipFileName.append(bizTaskSupervisionDTO.getTitle()).append("(").append(bizTaskDecomposeDTO.getDutyPeoplePostName())
                .append(bizTaskDecomposeDTO.getDutyPeopleName()).append(")");


        // 通过分解任务ID查询反馈任务主键ID集合
        LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, id);
        List<Long> feedbackIdList = iBizTaskFeedbackService.selectListNoAdd(queryWrapper).stream().map(i -> {
                    return i.getId();
                }).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(feedbackIdList)) {
            throw new BusinessException("未找到该记录的反馈任务信息");
        }

        // 通过反馈主键ID查询附件表数据
        LambdaQueryWrapper<CscpEnclosureFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CscpEnclosureFile::getFormDataId, feedbackIdList);
        List<CscpEnclosureFile> cscpEnclosureFileList = cscpEnclosureFileService.selectListNoAdd(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(feedbackIdList)) {
            throw new BusinessException("未找到该记录的附件信息");
        }

        File file = new File(zipFileName.toString());
        if(!file.exists()){
            file.mkdirs();
        }
        // 下载附件
        for (CscpEnclosureFile cscpEnclosureFile : cscpEnclosureFileList) {
            fileStoreTemplateService.downloadFile(cscpEnclosureFile.getFileUrl(), zipFileName.toString() + "/" + cscpEnclosureFile.getFileName());
        }

        // 打包压缩附件
        try (OutputStream out = response.getOutputStream(); ZipOutputStream zos = new ZipOutputStream(out)) {
            String zipFullName = zipFileName.toString() + ".zip";
            FileNameUtil.setDownloadResponseHeader(response, zipFullName);
            // 对文件夹进行压缩，保留原文件夹路径
            ZipFileUtil.toZip(file, out, true);
            out.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 删除压缩前准备的中间文件
        if (file != null) {
            try {
                FileUtils.deleteDirectory(file);
                log.info("中间文件已删除");
            } catch (IOException e) {
                log.info("中间文件删除失败");
                e.printStackTrace();
            }
        }
    }

    private String getSmsReminderCode(String key) {
        Map<String, String> map = new HashMap<>(3);
        map.put("无", "1");
        map.put("每周", "2");
        map.put("每月", "3");
        return map.get(key);
    }

    private String getSmsReminderValue(String key) {
        Map<String, String> map = new HashMap<>(3);
        map.put("1", "无");
        map.put("2", "每周");
        map.put("3", "每月");
        return map.get(key);
    }

    private String getLeadingUnitCode(String key) {
        Map<String, String> map = new HashMap<>(3);
        map.put("无", "0");
        map.put("是", "1");
        map.put("否", "2");
        return map.get(key);
    }

    private String getLeadingUnitValue(String key) {
        Map<String, String> map = new HashMap<>(3);
        map.put("0", "无");
        map.put("1", "是");
        map.put("2", "否");
        return map.get(key);
    }

}
