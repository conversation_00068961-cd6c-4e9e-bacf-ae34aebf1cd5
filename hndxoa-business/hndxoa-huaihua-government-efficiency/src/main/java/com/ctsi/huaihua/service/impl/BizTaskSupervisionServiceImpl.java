package com.ctsi.huaihua.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.common.datapermission.DataFilterThreadLocal;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.receive.entity.GroupPopulation;
import com.ctsi.hndx.receive.entity.TemplateFormModel;
import com.ctsi.hndx.receive.entity.TemplateGroup;
import com.ctsi.hndx.receive.mapper.GroupPopulationMapper;
import com.ctsi.hndx.receive.mapper.TemplateFormModelMapper;
import com.ctsi.hndx.receive.mapper.TemplateGroupMapper;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SysTenantUtils;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.*;
import com.ctsi.huaihua.entity.dto.*;
import com.ctsi.huaihua.mapper.*;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.system.entity.dto.TSysDictRecordDTO;
import com.ctsi.system.service.ITSysDictRecordService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import com.ctsi.huaihua.service.IBizTaskCoefficientService;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.huaihua.service.IBizTaskDelayService;
import com.ctsi.huaihua.service.IBizTaskLevelService;
import com.ctsi.huaihua.service.IBizTaskScoreAddService;
import com.ctsi.huaihua.service.IBizTaskScoreSubService;
import com.ctsi.huaihua.service.IBizTaskSupervisionService;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.sms.smssend.SmsSendEnum;
import com.ctsi.sms.smssend.SmsSendUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务督查的主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Slf4j
@Service
public class BizTaskSupervisionServiceImpl extends SysBaseServiceImpl<BizTaskSupervisionMapper, BizTaskSupervision> implements IBizTaskSupervisionService {

    @Autowired
    private BizTaskSupervisionMapper bizTaskSupervisionMapper;

    @Autowired
    private BizRewardScoreRecordMapper bizRewardScoreRecordMapper;

    @Autowired
    private IBizTaskDecomposeService iBizTaskDecomposeService;

    @Autowired
    private BizTaskDecomposeMapper bizTaskDecomposeMapper;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;
    @Autowired
    private com.ctsi.huaihua.service.IBizTaskFeedbackService bizTaskFeedbackService;


    @Autowired
    private com.ctsi.ssdc.admin.service.CscpUserService cscpUserService;

    @Autowired
    private com.ctsi.ssdc.admin.service.CscpOrgService cscpOrgService;

    @Autowired
    private IBizTaskDelayService bizTaskDelayService;

    @Autowired
    private IBizTaskScoreAddService bizTaskScoreAddService;

    @Autowired
    private IBizTaskScoreSubService bizTaskScoreSubService;

    @Autowired
    private ITSysDictRecordService tSysDictRecordService;

    @Autowired
    private IBizTaskCoefficientService bizTaskCoefficientService;

    @Autowired
    private IBizTaskLevelService bizTaskLevelService;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private BizTaskScoreMapper bizTaskScoreMapper;

    @Autowired
    private BizScoreRecordMapper bizScoreRecordMapper;

    @Autowired
    private BizPunishScoreRecordMapper bizPunishScoreRecordMapper;

    @Autowired
    private BizTaskScoreSubMapper bizTaskScoreSubMapper;

    @Autowired
    private BizTaskScoreAddMapper bizTaskScoreAddMapper;

    @Value("${model.cFormModelTable:}")
    private String cFormModelTable;

    @Value("${model.companyId:}")
    private Long modelCompanyId;

    @Autowired
    private TemplateFormModelMapper templateFormModelMapper;

    @Autowired
    private TemplateGroupMapper templateGroupMapper;

    @Autowired
    private GroupPopulationMapper groupPopulationMapper;

    @Autowired
    private CscpUserOrgService userOrgService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskSupervisionDTO> queryListPage(BizTaskSupervisionDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        long currentUserId = SecurityUtils.getCurrentUserId();
        LambdaQueryWrapper<BizTaskSupervision> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTitle()), BizTaskSupervision::getTitle, entityDTO.getTitle());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getCreateName()), BizTaskSupervision::getCreateName, entityDTO.getCreateName());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getTaskType()), BizTaskSupervision::getTaskType, entityDTO.getTaskType());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getTaskTag()), BizTaskSupervision::getTaskTag, entityDTO.getTaskTag());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getTaskSource()), BizTaskSupervision::getTaskSource, entityDTO.getTaskSource());

        Integer hasPublish = entityDTO.getHasPublish();
        Integer hasFinish = entityDTO.getHasFinish();
        queryWrapper.eq(ObjectUtil.isNotEmpty(hasPublish), BizTaskSupervision::getHasPublish, hasPublish);
        queryWrapper.eq(ObjectUtil.isNotEmpty(hasFinish), BizTaskSupervision::getHasFinish, hasFinish);
        if (hasFinish != null && hasFinish.intValue() == 0) {
            // 任务录入查询 查办理中的任务 需要是发布状态
            queryWrapper.eq(BizTaskSupervision::getHasPublish, 1);
        }

        if (StringUtils.isNotEmpty(entityDTO.getStartTime())) {
            queryWrapper.ge(BizTaskSupervision::getCreateTime, entityDTO.getStartTime());
        }
        if (StringUtils.isNotEmpty(entityDTO.getEndTime())) {
            queryWrapper.le(BizTaskSupervision::getCreateTime, entityDTO.getEndTime());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getBizTaskSupervisionIdList())) {
            queryWrapper.in(BizTaskSupervision::getId, entityDTO.getBizTaskSupervisionIdList());
        }
        if (StringUtils.isNotBlank(entityDTO.getInspectorTaskLevel())) {
            queryWrapper.eq(BizTaskSupervision::getInspectorTaskLevel, entityDTO.getInspectorTaskLevel());
        }
        if (StringUtils.isNotBlank(entityDTO.getInspectorItems())) {
            queryWrapper.eq(BizTaskSupervision::getInspectorItems, entityDTO.getInspectorItems());
        }
        if (StringUtils.isNotBlank(entityDTO.getDegreeUrgency())) {
            queryWrapper.eq(BizTaskSupervision::getDegreeUrgency, entityDTO.getDegreeUrgency());
        }
        // 默认按照个人隔离
        queryWrapper.eq(DataFilterThreadLocal.get() == null, BizTaskSupervision::getCreateBy, SecurityUtils.getCurrentUserId());
        boolean myFilter = DataFilterThreadLocal.get() == null;// 个人
        if (myFilter) { // 个人
            queryWrapper.eq(BizTaskSupervision::getCreateBy, currentUserId);
        } else { // 配置 管理员角色,按照单位过滤
            queryWrapper.eq(BizTaskSupervision::getCompanyId, currentCompanyId);
        }
        queryWrapper.orderByDesc(BizTaskSupervision::getCreateTime);
        IPage<BizTaskSupervision> pageData = bizTaskSupervisionMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskSupervisionDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskSupervisionDTO.class));

        List<BizTaskSupervisionDTO> collect = data.getRecords().stream().map(i -> {
            // 审核人查询加分申请待审核
            LambdaQueryWrapper<BizTaskScoreAdd> scoreQueryWrapper = new LambdaQueryWrapper();
            scoreQueryWrapper.eq(BizTaskScoreAdd::getReviewerId, currentUserId);
            scoreQueryWrapper.eq(BizTaskScoreAdd::getBizTaskSupervisionId, i.getId());
            scoreQueryWrapper.eq(BizTaskScoreAdd::getHasReviewer, 0);
            Integer notReviewedCount = bizTaskScoreAddService.selectCountNoAdd(scoreQueryWrapper);
            if (notReviewedCount > 0) {
                i.setAuditNotReviewedFlag(1);
            } else {
                i.setAuditNotReviewedFlag(0);
            }

//            // 获取任务分值
//            if (Objects.nonNull(i.getId())) {
//                if (Objects.nonNull(i.getDegreeUrgency()) && Objects.nonNull(i.getDifficulty()) && Objects.nonNull(i.getInspectorTaskLevel())) {
//                    i.setActualScore(this.getScoreById(i.getId()));
//                } else {
//                    i.setActualScore(0D);
//                }
//            }

            // 审核人查询加分申请已审核
            LambdaQueryWrapper<BizTaskScoreAdd> scoreReviewedQueryWrapper = new LambdaQueryWrapper();
            scoreReviewedQueryWrapper.eq(BizTaskScoreAdd::getReviewerId, currentUserId);
            scoreReviewedQueryWrapper.eq(BizTaskScoreAdd::getBizTaskSupervisionId, i.getId());
            List<Integer> reviewedList = new ArrayList<>(Arrays.asList(new Integer[]{1, 2}));
            scoreReviewedQueryWrapper.in(BizTaskScoreAdd::getHasReviewer, reviewedList);
            Integer hasReviewedCount = bizTaskScoreAddService.selectCountNoAdd(scoreReviewedQueryWrapper);

            // 查询任务减分
            LambdaQueryWrapper<BizTaskScoreSub> soreSubQueryWrapper = new LambdaQueryWrapper();
            soreSubQueryWrapper.eq(BizTaskScoreSub::getBizTaskSupervisionId, i.getId());
            Integer soreSubCount = bizTaskScoreSubService.selectCountNoAdd(soreSubQueryWrapper);

            if (hasReviewedCount > 0 || soreSubCount > 0) {
                i.setAuditHasReviewedFlag(1);
            } else {
                i.setAuditHasReviewedFlag(0);
            }
            return i;
        }).collect(Collectors.toList());

        return new PageResult<>(collect, data.getTotal(), data.getCurrent());
    }

    /**
     * app成果收件箱主任务查询 不含暂存
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskSupervisionDTO> queryNotTempTaskListPage(BizTaskSupervisionDTO entityDTO,
                                                                      BasePageForm basePageForm) {
        // 成果收件箱 主任务查询: 过滤没有反馈的数据
        String searchKey = entityDTO.getSearchKey();
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        long currentUserId = SecurityUtils.getCurrentUserId();
        LambdaQueryWrapper<BizTaskSupervision> queryWrapper = new LambdaQueryWrapper();
        Integer hasFinish = entityDTO.getHasFinish();
        String title = entityDTO.getTitle();
        String taskSource = entityDTO.getTaskSource();
        String startTimeStr = entityDTO.getStartTime();
        String endTimeStr = entityDTO.getEndTime();
        Integer hasPublish = entityDTO.getHasPublish();
        Integer supHasFeedback = entityDTO.getSupHasFeedback();

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        queryWrapper.eq(ObjectUtil.isNotEmpty(hasFinish), BizTaskSupervision::getHasFinish, hasFinish)
                .eq(ObjectUtil.isNotEmpty(supHasFeedback), BizTaskSupervision::getSupHasFeedback, supHasFeedback)        // 没反馈不显示
                .like(StrUtil.isNotEmpty(title), BizTaskSupervision::getTitle, title)
                .like(StrUtil.isNotEmpty(searchKey), BizTaskSupervision::getTitle, searchKey) // 移动端按 标题搜索
                .eq(StrUtil.isNotEmpty(taskSource), BizTaskSupervision::getTaskSource, taskSource)
                .eq(ObjectUtil.isNotEmpty(hasPublish), BizTaskSupervision::getHasPublish, hasPublish);

        boolean myFilter = DataFilterThreadLocal.get() == null;// 个人
        if (myFilter) { // 个人
            int flag = this.isLeadPeople(currentUserId);
            if (flag == 0) { //是牵头单位
                queryWrapper.eq(BizTaskSupervision::getLeadPeopleId, currentUserId);
            } else if (flag == 2) {
                queryWrapper.and((wrapper) -> {
                    wrapper.eq(BizTaskSupervision::getLeadPeopleId, currentUserId)
                            .or().eq(BizTaskSupervision::getCreateBy, currentUserId);
                });
            } else {
                queryWrapper.eq(BizTaskSupervision::getCreateBy, currentUserId);
            }
        } else { // 配置 管理员角色,按照单位过滤
            queryWrapper.eq(BizTaskSupervision::getCompanyId, currentCompanyId);
        }

        if (StringUtils.isNotBlank(startTimeStr)) {
            LocalDateTime startTime = LocalDateTime.parse(startTimeStr, df);
            queryWrapper.gt(BizTaskSupervision::getCreateTime, startTime);
        }
        if (StringUtils.isNotBlank(endTimeStr)) {
            LocalDateTime endTime = LocalDateTime.parse(endTimeStr, df);
            queryWrapper.lt(BizTaskSupervision::getCreateTime, endTime);
        }


        // 左侧主任务列表以截止时间升序排序；  红点点置顶
        queryWrapper.orderByDesc(BizTaskSupervision::getNewSupFeedbackSign);
        queryWrapper.orderByAsc(BizTaskSupervision::getDueTime);
        IPage<BizTaskSupervision> pageData = bizTaskSupervisionMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskSupervisionDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskSupervisionDTO.class));

        return new PageResult<BizTaskSupervisionDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }


    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskSupervisionDTO> queryList(BizTaskSupervisionDTO entityDTO) {
        // 成果收件箱 主任务查询: 过滤没有反馈的数据
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        long currentUserId = SecurityUtils.getCurrentUserId();

        boolean myFilter = DataFilterThreadLocal.get() == null;// 个人
        if (myFilter) { // 个人
            entityDTO.setLeadPeopleId(currentUserId);
        } else { // 配置 管理员角色,按照单位过滤
            entityDTO.setCompanyId(currentCompanyId);
        }

        List<BizTaskSupervision> listData = bizTaskSupervisionMapper.queryList(entityDTO);

        List<BizTaskSupervisionDTO> BizTaskSupervisionDTOList = ListCopyUtil.copy(listData, BizTaskSupervisionDTO.class);
        return BizTaskSupervisionDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskSupervisionDTO findOne(Long id) {
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(id);
        BizTaskSupervisionDTO bizTaskSupervisionDTO = BeanConvertUtils.copyProperties(bizTaskSupervision, BizTaskSupervisionDTO.class);

        // 设置牵头人单位ID和单位名称
        if (Objects.nonNull(bizTaskSupervisionDTO.getLeadPeopleId())) {
            List<CscpUserOrgDTO> userOrgDTOList = userOrgService.queryUserOrgByUserId(bizTaskSupervisionDTO.getLeadPeopleId());
            if (CollectionUtil.isNotEmpty(userOrgDTOList)) {
                bizTaskSupervisionDTO.setLeadPeopleCompanyId(userOrgDTOList.get(0).getCompanyId());

                CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(userOrgDTOList.get(0).getCompanyId());
                if (Objects.nonNull(cscpOrgDTO)) {
                    bizTaskSupervisionDTO.setLeadPeopleCompanyName(cscpOrgDTO.getOrgName());
                }
            }
        }

        // 设置责任人单位ID
        if (StringUtils.isNotBlank(bizTaskSupervisionDTO.getDutyPeople())) {
            List<DutyPeopleDTO> dutyPeopleDTOList = JSON.parseArray(bizTaskSupervisionDTO.getDutyPeople(), DutyPeopleDTO.class);
            if (CollectionUtil.isNotEmpty(dutyPeopleDTOList)) {
                bizTaskSupervisionDTO.setDutyPeopleCompanyId(Long.parseLong(dutyPeopleDTOList.get(0).getCompanyId()));
            }
        }
        return bizTaskSupervisionDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskSupervisionDTO create(BizTaskSupervisionDTO entityDTO) {
        long a = System.currentTimeMillis();
        BizTaskSupervision bizTaskSupervision = BeanConvertUtils.copyProperties(entityDTO, BizTaskSupervision.class);

        if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(bizTaskSupervision.getBranchLeaderNameJson())) {
            JSONArray jsonArray = JSONArray.fromObject(entityDTO.getBranchLeaderNameJson());
            if (!jsonArray.isEmpty()) {
                JSONObject jsonObject = jsonArray.getJSONObject(0);
                DutyPeopleDTO dutyPeopleDTO = (DutyPeopleDTO) JSONObject.toBean(jsonObject, DutyPeopleDTO.class);
                bizTaskSupervision.setBranchLeaderId(dutyPeopleDTO.getUserId());
                bizTaskSupervision.setBranchLeaderName(dutyPeopleDTO.getUserName());
            }
        }

        //根据牵头人id查询对应的牵头人名称
        if (!Objects.isNull(bizTaskSupervision.getLeadPeopleId())) {
            CscpUser cscpUser = cscpUserRepository.selectOneNoAdd(
                    new LambdaQueryWrapper<CscpUser>().select(CscpUser::getId, CscpUser::getRealName).eq(CscpUser::getId, bizTaskSupervision.getLeadPeopleId()));
            if (Objects.nonNull(cscpUser)) {
                bizTaskSupervision.setLeadPeopleName(cscpUser.getRealName());
            }
        }

        Long taskTransferDecomposeId = bizTaskSupervision.getTaskTransferDecomposeId();
        if (taskTransferDecomposeId != null) {
            //  更新数据
            BizTaskDecompose bizTaskDecompose = iBizTaskDecomposeService.getById(taskTransferDecomposeId);
            if (bizTaskDecompose != null) {
                //一个任务最多转办5次
                if (bizTaskSupervision.getTaskLevel() == null) {
                    bizTaskSupervision.setTaskLevel(1);
                } else if (bizTaskSupervision.getTaskLevel() >= 5) {
                    throw new BusinessException("任务转办次数过多");
                } else {
                    bizTaskSupervision.setTaskLevel(bizTaskSupervision.getTaskLevel() + 1);
                }

                bizTaskDecompose.setHasTransfer(1);
                iBizTaskDecomposeService.updateById(bizTaskDecompose);
                bizTaskSupervision.setTaskAddType(1);
                bizTaskSupervision.setTaskTransferId(bizTaskDecompose.getSubTblFk());
                bizTaskSupervision.setCreateBy(null);
                bizTaskSupervision.setDepartmentName(null);
                Long superId = bizTaskDecompose.getSubTblFk();
                //  转办的表单使用最新的
                BizTaskSupervision bizTaskSupervision1 = bizTaskSupervisionMapper.selectById(superId);
                cscpEnclosureFileService.enclosureChangeEnclosure(superId, Long.valueOf(bizTaskSupervision.getId()));
                // bizTaskSupervision.setFormId(bizTaskSupervision1.getFormId());
            }
        }

        //如果牵头人不为空，和父级任务没有被审批，将状态设置为待审
        // if (!Objects.isNull(entityDTO.getLeadPeopleName()) && hasLeadPeolpleSign(entityDTO.getTaskTransferDecomposeId())) {
       /* if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(entityDTO.getLeadPeopleName())) {
            JSONArray jsonArray = JSONArray.fromObject(entityDTO.getLeadPeopleName());
            if (!jsonArray.isEmpty()) {
                JSONObject jsonObject = jsonArray.getJSONObject(0);
                DutyPeopleDTO dutyPeopleDTO = (DutyPeopleDTO) JSONObject.toBean(jsonObject, DutyPeopleDTO.class);
                bizTaskSupervision.setLeadPeopleId(Long.valueOf(dutyPeopleDTO.getUserId()));
                if (bizTaskSupervision.getHasPublish() != null &&
                        bizTaskSupervision.getHasPublish().intValue()
                                == TaskStatus.TO_BE_APPROVED.intValue()) {
                    bizTaskSupervision.setHasPublish(TaskStatus.RELEASE);
                }
            }
        }*/
        log.info("输出时间" + (System.currentTimeMillis() - a));

        // 分管领导报文解析


        log.info("输出时间" + (System.currentTimeMillis() - a));
        bizTaskSupervision.setHasFinish(0);
        save(bizTaskSupervision);
        log.info("第三次输出时间" + (System.currentTimeMillis() - a));
        return BeanConvertUtils.copyProperties(bizTaskSupervision, BizTaskSupervisionDTO.class);
    }


    /**
     * p判断此数据是否还需要领导待审，如果转办之前已经批示过了，不需要批示了，之前没有批示，但是此次选择了领导需要批示
     *
     * @param id
     * @return
     */
    public boolean hasLeadPeolpleSign(Long id) {
        BizTaskDecompose bizTaskDecompose = bizTaskDecomposeMapper.selectById(id);
        if (Objects.isNull(bizTaskDecompose) || bizTaskDecompose.getSubTblFk() == null) {
            return true;
        } else {
            BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(bizTaskDecompose.getSubTblFk());

            if (!Objects.isNull(bizTaskSupervision) && bizTaskSupervision.getApprovalId() != null && bizTaskSupervision.getLeadPeopleId() != null) {
                return false;
            } else {
                return hasLeadPeolpleSign(bizTaskSupervision.getTaskTransferId());
            }
        }

    }


    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskSupervisionDTO entity) {
        BizTaskSupervision bizTaskSupervision = BeanConvertUtils.copyProperties(entity, BizTaskSupervision.class);
        return bizTaskSupervisionMapper.updateById(bizTaskSupervision);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        //获取主表需要撤回的id
        List<Long> taskSupervisionIdList = new LinkedList<>();
        taskSupervisionIdList.add(id);
        withdrawTask(id, taskSupervisionIdList);
        //撤回

        bizTaskSupervisionMapper.deleteBatchIds(taskSupervisionIdList);


        //获取子表需要撤回的id
        bizTaskDecomposeMapper.delete(new LambdaQueryWrapper<BizTaskDecompose>().in(BizTaskDecompose::getSubTblFk, taskSupervisionIdList));

        // 删除主表关联的所有延期申请记录
        LambdaQueryWrapper<BizTaskDelay> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(BizTaskDelay::getBizTaskSupervisionId, taskSupervisionIdList);
        bizTaskDelayService.remove(lambdaQueryWrapper);

        return 1;
    }

    /**
     * 撤回任务
     *
     * @param id the id of the entity
     */
    @Override
    public int withdraw(Long id) {
        //获取主表需要撤回的id
        List<Long> taskSupervisionIdList = new LinkedList<>();
        taskSupervisionIdList.add(id);
        withdrawTask(id, taskSupervisionIdList);
        //撤回
        BizTaskSupervision bizTaskSupervision = new BizTaskSupervision();
        bizTaskSupervision.setHasPublish(2);
        bizTaskSupervisionMapper.update(bizTaskSupervision, new LambdaQueryWrapper<BizTaskSupervision>().in(BizTaskSupervision::getId, taskSupervisionIdList));


        //获取子表需要撤回的id
        BizTaskDecompose bizTaskDecompose = new BizTaskDecompose();
        bizTaskDecompose.setHasSign(2);
        bizTaskDecomposeMapper.update(bizTaskDecompose, new LambdaQueryWrapper<BizTaskDecompose>().in(BizTaskDecompose::getSubTblFk, taskSupervisionIdList));

        return 1;
    }


    /**
     * 递归获取主表需要撤回的id
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void withdrawTask(Long id, List<Long> taskSupervisionIdList) {
        //获取当前用户传过来的主表id
        List<BizTaskSupervision> bizTaskSupervisionList = bizTaskSupervisionMapper.selectListNoAdd(
                new LambdaQueryWrapper<BizTaskSupervision>().select(BizTaskSupervision::getId, BizTaskSupervision::getTaskTransferId).eq(BizTaskSupervision::getTaskTransferId, id));
        if (!Objects.isNull(bizTaskSupervisionList) && !bizTaskSupervisionList.isEmpty()) {
            taskSupervisionIdList.addAll(bizTaskSupervisionList.stream().map(i -> i.getId()).collect(Collectors.toList()));
            bizTaskSupervisionList.forEach(i -> {
                this.withdrawTask(i.getId(), taskSupervisionIdList);
            });
        }
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskSupervisionId
     * @return
     */
    @Override
    public boolean existByBizTaskSupervisionId(Long BizTaskSupervisionId) {
        if (BizTaskSupervisionId != null) {
            LambdaQueryWrapper<BizTaskSupervision> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskSupervision::getId, BizTaskSupervisionId);
            List<BizTaskSupervision> result = bizTaskSupervisionMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskSupervisionDTO> dataList) {
        List<BizTaskSupervision> result = ListCopyUtil.copy(dataList, BizTaskSupervision.class);
        return saveBatch(result);
    }


    /**
     * 查询全部任务
     *
     * @param dto
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskSupervisionDTO> queryPageAllTasks(BizTaskSupervisionDTO dto, BasePageForm basePageForm) {

        // 查询主表数据
        LambdaQueryWrapper<BizTaskSupervision> queryWrapper = new LambdaQueryWrapper();
        if (StringUtils.isNotBlank(dto.getTitle())) {
            queryWrapper.like(BizTaskSupervision::getTitle, dto.getTitle());
        }
        if (StringUtils.isNotBlank(dto.getCreateName())) {
            queryWrapper.like(BizTaskSupervision::getCreateName, dto.getCreateName());
        }
        if (StringUtils.isNotBlank(dto.getStartTime())) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startTime = LocalDateTime.parse(dto.getStartTime(), df);
            queryWrapper.gt(BizTaskSupervision::getCreateTime, startTime);
        }
        if (StringUtils.isNotBlank(dto.getEndTime())) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime endTime = LocalDateTime.parse(dto.getEndTime(), df);
            queryWrapper.lt(BizTaskSupervision::getCreateTime, endTime);
        }
        if (StringUtils.isNotBlank(dto.getTaskType())) {
            queryWrapper.eq(BizTaskSupervision::getTaskType, dto.getTaskType());
        }
        if (StringUtils.isNotBlank(dto.getTaskTag())) {
            queryWrapper.eq(BizTaskSupervision::getTaskTag, dto.getTaskTag());
        }
        if (StringUtils.isNotBlank(dto.getTaskSource())) {
            queryWrapper.eq(BizTaskSupervision::getTaskSource, dto.getTaskSource());
        }
        if (StringUtils.isNotBlank(dto.getDegreeUrgency())) {
            queryWrapper.eq(BizTaskSupervision::getDegreeUrgency, dto.getDegreeUrgency());
        }
        if (StringUtils.isNotBlank(dto.getInspectorTaskLevel())) {
            queryWrapper.eq(BizTaskSupervision::getInspectorTaskLevel, dto.getInspectorTaskLevel());
        }
        if (StringUtils.isNotBlank(dto.getInspectorItems())) {
            queryWrapper.eq(BizTaskSupervision::getInspectorItems, dto.getInspectorItems());
        }
        if (!Objects.isNull(dto.getDepartmentId())) {
            queryWrapper.eq(BizTaskSupervision::getDepartmentId, dto.getDepartmentId());
        }
        if (!Objects.isNull(dto.getCompanyId())) {
            queryWrapper.eq(BizTaskSupervision::getCompanyId, dto.getCompanyId());
        }

        // 左侧主任务列表以截止时间升序排序, 是否办结；
        queryWrapper.eq(DataFilterThreadLocal.get() == null, BizTaskSupervision::getCreateBy, SecurityUtils.getCurrentUserId());

        // 过滤待审核和驳回的数据
        queryWrapper.and(wq -> wq.ne(BizTaskSupervision::getHasPublish, 3).ne(BizTaskSupervision::getHasPublish, 4).ne(BizTaskSupervision::getHasPublish, 0));
        queryWrapper.orderByDesc(BizTaskSupervision::getCreateTime);
        IPage<BizTaskSupervision> pageData = bizTaskSupervisionMapper.selectPageOnlyAddTenantId(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        // DO转换成DTO
        IPage<BizTaskSupervisionDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskSupervisionDTO.class));

        // 获取分页数据
        List<BizTaskSupervisionDTO> bizTaskSupervisionDTOList = data.getRecords();

        // 组装数据
        for (BizTaskSupervisionDTO bizTaskSupervisionDTO : bizTaskSupervisionDTOList) {
            // 组装分解表数据
            List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = iBizTaskDecomposeService.queryListBySupervisionId(bizTaskSupervisionDTO.getId());
            bizTaskSupervisionDTO.setBizTaskDecomposeDTOList(bizTaskDecomposeDTOList);

            // 重要性
            if (StringUtils.isNotBlank(bizTaskSupervisionDTO.getInspectorTaskLevel())) {
                BizTaskLevelDTO inspectorTaskLevelDTO = bizTaskLevelService.findOne(Long.parseLong(bizTaskSupervisionDTO.getInspectorTaskLevel()));
                bizTaskSupervisionDTO.setInspectorTaskLevelDTO(inspectorTaskLevelDTO);
            }

            // 交办事项
            if (StringUtils.isNotBlank(bizTaskSupervisionDTO.getInspectorItems())) {
                BizTaskLevelDTO inspectorItemsDTO = bizTaskLevelService.findOne(Long.parseLong(bizTaskSupervisionDTO.getInspectorItems()));
                bizTaskSupervisionDTO.setInspectorItemsDTO(inspectorItemsDTO);
            }

            if (Objects.nonNull(bizTaskSupervisionDTO.getDegreeUrgency()) && Objects.nonNull(bizTaskSupervisionDTO.getDifficulty()) && Objects.nonNull(bizTaskSupervisionDTO.getInspectorTaskLevel())) {
                bizTaskSupervisionDTO.setActualScore(this.getScoreById(bizTaskSupervisionDTO.getId()));
            } else {
                bizTaskSupervisionDTO.setActualScore(0D);
            }

        }

        return new PageResult<>(bizTaskSupervisionDTOList, data.getTotal(), data.getCurrent());
    }


    @Override
    public BizSignTaskDTO getSignDetail(Long id) {
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(id);
        BizSignTaskDTO bizSignTaskDTO = BeanConvertUtils.copyProperties(bizTaskSupervision, BizSignTaskDTO.class);
        //  获取分发的数量
        LambdaQueryWrapper<BizTaskDecompose> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BizTaskDecompose::getSubTblFk, bizTaskSupervision.getId());
        bizSignTaskDTO.setUndertakeUnitNumber(iBizTaskDecomposeService.selectCountNoAdd(lambdaQueryWrapper));
        lambdaQueryWrapper.isNotNull(BizTaskDecompose::getSignPeopleId);
        bizSignTaskDTO.setSignUnitNumber(iBizTaskDecomposeService.selectCountNoAdd(lambdaQueryWrapper));
        return bizSignTaskDTO;
    }

    @Override
    public BizTaskSupervisionDTO getSuperDataByDecomposeId(Long id) {
        BizTaskDecompose bizTaskDecompose = iBizTaskDecomposeService.getById(id);
        BizTaskSupervisionDTO one = findOne(bizTaskDecompose.getSubTblFk());
        return one;
    }

    @Override
    public PageResult<BizTaskSupervisionDTO> listTaskPublishReport(BizTaskSupervisionDTO dto, BasePageForm basePageForm) {
        LambdaQueryWrapper<BizTaskSupervision> queryWrapper = new LambdaQueryWrapper();

        String title = dto.getTitle();
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        String taskSource = dto.getTaskSource();
        Integer hasFinish = dto.getHasFinish();
        queryWrapper.like(StrUtil.isNotEmpty(title), BizTaskSupervision::getTitle, title).eq(StrUtil.isNotEmpty(taskSource), BizTaskSupervision::getTaskSource, taskSource)
                .eq(ObjectUtil.isNotEmpty(hasFinish), BizTaskSupervision::getHasFinish, hasFinish).gt(StrUtil.isNotEmpty(startTime), BizTaskSupervision::getDueTime, startTime)
                .lt(StrUtil.isNotEmpty(endTime), BizTaskSupervision::getDueTime, endTime);

        // 查询条件
        IPage<BizTaskSupervision> pageData = bizTaskSupervisionMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        IPage<BizTaskSupervisionDTO> bizTaskSupervisionDTOList = pageData.convert(i -> BeanConvertUtils.copyProperties(i, BizTaskSupervisionDTO.class));
        for (BizTaskSupervisionDTO bizTaskSupervisionDTO : bizTaskSupervisionDTOList.getRecords()) {
            // 获得 分解任务
            List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = iBizTaskDecomposeService.queryListBySupervisionId(bizTaskSupervisionDTO.getId());
            bizTaskDecomposeDTOList.forEach(bizTaskDTO -> {   //  标识 颜色
                Integer hasFeedback = bizTaskDTO.getHasFeedback();
                if (ObjectUtil.isNotEmpty(hasFeedback) && hasFeedback.intValue() == 1) { // 有反馈
                    bizTaskDTO.setHasFeedback(1);
                    bizTaskDTO.setWarningColorCode("3");
                } else {
                    bizTaskDTO.setHasFeedback(0);
                    LocalDate dueTime = bizTaskDTO.getEndDate();
                    LocalDateTime now = LocalDateTimeUtil.now();
                    LocalDateTime dueTimeStart = dueTime.atStartOfDay();
                    Duration duration = Duration.between(now, dueTimeStart);
                    long days = duration.toDays(); // 相差的天数
                    if (days >= 0 && days <= 2) {
                        bizTaskDTO.setWarningColorCode("2");
                    }
                    if (days < 0) {
                        bizTaskDTO.setWarningColorCode("1");
                    }
                }
            });
            bizTaskSupervisionDTO.setBizTaskDecomposeDTOList(bizTaskDecomposeDTOList);
        }
        return new PageResult<BizTaskSupervisionDTO>(bizTaskSupervisionDTOList.getRecords(),
                bizTaskSupervisionDTOList.getTotal(), bizTaskSupervisionDTOList.getCurrent());
    }

    @Override
    public BizTaskSupervisionDTO getSubAndDecAndFeedbackInfo(Long id) {
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(id);
        BizTaskSupervisionDTO bizTaskSupervisionDTO = BeanConvertUtils.copyProperties(bizTaskSupervision, BizTaskSupervisionDTO.class);
        // 获得 分解任务
        List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = iBizTaskDecomposeService.queryListBySupervisionId(bizTaskSupervisionDTO.getId());
        // 增加反馈结果集合
        for (BizTaskDecomposeDTO bizTaskDecomposeDTO : bizTaskDecomposeDTOList) {
            LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, bizTaskDecomposeDTO.getId()).orderByDesc(BizTaskFeedback::getCreateTime);
            List<BizTaskFeedback> bizTaskFeedbacks = bizTaskFeedbackService.selectListNoAdd(queryWrapper);
            List<BizTaskFeedbackDTO> collect = bizTaskFeedbacks.stream().map(bizTaskFeedback -> BeanConvertUtils.copyProperties(bizTaskFeedback,
                    BizTaskFeedbackDTO.class)).collect(Collectors.toList());
            bizTaskDecomposeDTO.setBizTaskFeedbackDTOList(collect);
        }
        // 设置转办任务
        setTransferTask(id, bizTaskDecomposeDTOList);

        bizTaskSupervisionDTO.setBizTaskDecomposeDTOList(bizTaskDecomposeDTOList);
        return bizTaskSupervisionDTO;
    }

    /**
     * 设置 转办任务  循环转办(无限转办)
     *
     * @param id                      主任务id
     * @param bizTaskDecomposeDTOList 该主任务的分解任务
     */
    private void setTransferTask(Long id, List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList) {
        // 1 查找 主任务 的转办主任务
        List<BizTaskSupervisionDTO> transferTaskListDTO = getSupTaskTransfer(id);
        if (!transferTaskListDTO.isEmpty()) {
            // 2 设置转办任务
            transferTaskListDTO.forEach(taskSupervisionDTO -> {
                Long taskTransferDecomposeId = taskSupervisionDTO.getTaskTransferDecomposeId();
                bizTaskDecomposeDTOList.forEach(item -> {
                    if (taskTransferDecomposeId.longValue() == item.getId()) {
                        item.setTaskSupervisionTransfer(taskSupervisionDTO);
                    }
                });
            });
            // 3 循环转办
            for (BizTaskSupervisionDTO bizTaskSupervisionDTO : transferTaskListDTO) {
                // 获得 分解任务
                List<BizTaskDecomposeDTO> transferDecompse = bizTaskSupervisionDTO.getBizTaskDecomposeDTOList();
                bizTaskSupervisionDTO.setBizTaskDecomposeDTOList(transferDecompse);
                setTransferTask(bizTaskSupervisionDTO.getId(), transferDecompse);
            }
        }
    }

    //

    /**
     * 获得转办任务
     *
     * @param id 主任务id
     * @return 转办后的主任务集合 含分解任务 含反馈
     */
    private List<BizTaskSupervisionDTO> getSupTaskTransfer(Long id) {
        LambdaQueryWrapper<BizTaskSupervision> qwrapper = new LambdaQueryWrapper();
        // 转办的主任务
        qwrapper.eq(BizTaskSupervision::getTaskTransferId, id);
        List<BizTaskSupervision> transferTaskList = this.selectListNoAdd(qwrapper);
        List<BizTaskSupervisionDTO> transferTaskListDTO = ListCopyUtil.copy(transferTaskList,
                BizTaskSupervisionDTO.class);
        if (!transferTaskListDTO.isEmpty()) {
            // 设置分解任务
            for (BizTaskSupervisionDTO supervision : transferTaskListDTO) {
                List<BizTaskDecomposeDTO> deList =
                        iBizTaskDecomposeService.queryListBySupervisionId(supervision.getId());
                // 增加反馈结果集合
                for (BizTaskDecomposeDTO bizTaskDecomposeDTO : deList) {
                    LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
                    queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, bizTaskDecomposeDTO.getId()).orderByDesc(BizTaskFeedback::getCreateTime);
                    List<BizTaskFeedback> bizTaskFeedbacks = bizTaskFeedbackService.selectListNoAdd(queryWrapper);
                    List<BizTaskFeedbackDTO> collect = ListCopyUtil.copy(bizTaskFeedbacks, BizTaskFeedbackDTO.class);
                    bizTaskDecomposeDTO.setBizTaskFeedbackDTOList(collect);

                }
                supervision.setBizTaskDecomposeDTOList(deList);
            }
        }
        return transferTaskListDTO;
    }


    @Override
    public BizTaskSupervisionDTO querySupAndDecList(Long id) {
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(id);
        BizTaskSupervisionDTO bizTaskSupervisionDTO = BeanConvertUtils.copyProperties(bizTaskSupervision, BizTaskSupervisionDTO.class);
        // 获得 分解任务
        List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = iBizTaskDecomposeService.queryListBySupervisionId(bizTaskSupervisionDTO.getId());
        bizTaskSupervisionDTO.setBizTaskDecomposeDTOList(bizTaskDecomposeDTOList);
        return bizTaskSupervisionDTO;
    }

    @Override
    public BizTaskSupervisionDTO getSubAndDecAndFeedbackInfoGroupByCommpany(Long id) {
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(id);
        BizTaskSupervisionDTO bizTaskSupervisionDTO = BeanConvertUtils.copyProperties(bizTaskSupervision, BizTaskSupervisionDTO.class);
        // 获得分解任务 <发布任务情况-反馈详情，单位下的人员排序按照主任务责任人的顺序排列>
        LambdaQueryWrapper<BizTaskDecompose> wrapper = new LambdaQueryWrapper();
        wrapper.eq(BizTaskDecompose::getSubTblFk, bizTaskSupervisionDTO.getId()).orderByAsc(BizTaskDecompose::getSubTblNum);
        List<BizTaskDecompose> listData = bizTaskDecomposeMapper.selectListNoAdd(wrapper);
        List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = ListCopyUtil.copy(listData, BizTaskDecomposeDTO.class);
        Map<Long, List<BizTaskDecomposeDTO>> bizTaskDecomposeDTOListMap = new HashMap<>();

        // 单位排序  <发布任务情况-反馈详情，单位根据序号排先后>
        Set<Long> dutyCommpanyIds = new HashSet<>();
        bizTaskDecomposeDTOList.forEach(item -> dutyCommpanyIds.add(item.getDutyPeopleCompanyId()));
        LambdaQueryWrapper<CscpOrg> cscpOrgwrapper = new LambdaQueryWrapper();
        LambdaQueryWrapper<CscpOrg> in = cscpOrgwrapper.in(CscpOrg::getId, dutyCommpanyIds).orderByAsc(CscpOrg::getOrderBy);
        List<CscpOrg> cscpOrgs = cscpOrgService.selectListNoAdd(in);

        for (BizTaskDecomposeDTO bizTaskDecomposeDTO : bizTaskDecomposeDTOList) {// 增加反馈结果集合
            LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, bizTaskDecomposeDTO.getId()).orderByDesc(BizTaskFeedback::getCreateTime);
            List<BizTaskFeedback> bizTaskFeedbacks = bizTaskFeedbackService.selectListNoAdd(queryWrapper);
            List<BizTaskFeedbackDTO> collect = bizTaskFeedbacks.stream().map(bizTaskFeedback -> BeanConvertUtils.copyProperties(bizTaskFeedback,
                    BizTaskFeedbackDTO.class)).collect(Collectors.toList());
            bizTaskDecomposeDTO.setBizTaskFeedbackDTOList(collect);
        }

        for (CscpOrg org : cscpOrgs) { // 单位排序
            List<BizTaskDecomposeDTO> list = new ArrayList<>();
            Long orgId = org.getId();
            for (BizTaskDecomposeDTO bizTaskDecomposeDTO : bizTaskDecomposeDTOList) {
                if (bizTaskDecomposeDTO.getDutyPeopleCompanyId().longValue() == orgId.longValue()) { // 分组
                    list.add(bizTaskDecomposeDTO);
                }
            }
            bizTaskDecomposeDTOListMap.put(orgId, list);
        }
        bizTaskSupervisionDTO.setBizTaskDecomposeDTOListMap(bizTaskDecomposeDTOListMap);
        return bizTaskSupervisionDTO;
    }

    /**
     * 对阻塞节点催办，不含已办结的。
     * （1）未接收未反馈，催接收；
     * （2）已接收未反馈，催反馈。
     * 如果责任人有联络员的就只发给联络员
     *
     * @param dto
     */
    @Override
    public void messageReminders(BizTaskDTO dto) {
        Long taskSupervisionId = dto.getTaskSupervisionId();
        if (taskSupervisionId == null) {
            throw new BusinessException(" 请输入正确参数:taskSupervisionId ");
        }
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(taskSupervisionId);
        List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList = iBizTaskDecomposeService.queryListBySupervisionId(bizTaskSupervision.getId());

        Set<String> signMobileSet = new HashSet();
        Set<String> feedbackMobileSet = new HashSet();
        for (BizTaskDecomposeDTO bizTaskDecomposeDTO : bizTaskDecomposeDTOList) {
            Integer hasFinish = bizTaskDecomposeDTO.getHasFinish();
            Integer hasFeedback = bizTaskDecomposeDTO.getHasFeedback();
            Integer hasSign = bizTaskDecomposeDTO.getHasSign();
            Long contactPeopleId = bizTaskDecomposeDTO.getContactPeopleId();
            if (contactPeopleId == null) {
                contactPeopleId = bizTaskDecomposeDTO.getDutyPeopleId();
            }
            String mobilePhone = bizTaskDecomposeDTO.getContactPeoplePhone();
            if (hasSign.intValue() == 0 && hasFinish.intValue() == 0) { // 未签收 未办结
                // 催签收
                signMobileSet.add(mobilePhone);
            }
            if (hasSign.intValue() == 1 && hasFinish.intValue() == 0 && hasFeedback.intValue() == 0) { // 未办结 已签收 没反馈
                // 催反馈
                feedbackMobileSet.add(mobilePhone);
            }
        }
        // 检查当天是否发送短信 TODO
        SmsSendUtil.batchSendSms(signMobileSet, bizTaskSupervision.getTitle(), SmsSendEnum.HH_TASK_SMS_SIGN);
        SmsSendUtil.batchSendSms(feedbackMobileSet, bizTaskSupervision.getTitle(), SmsSendEnum.HH_TASK_SMS);
    }

    @Override
    public String getSuperTransferMaxNumber(String s) {
        LambdaQueryWrapper<BizTaskSupervision> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.likeRight(BizTaskSupervision::getTaskNumber, s)
                .orderByAsc(BizTaskSupervision::getTaskNumber).last(SysConstant.LIMIT_ONE);
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectOne(lambdaQueryWrapper);
        if (bizTaskSupervision != null) {
            String maxNumber = bizTaskSupervision.getTaskNumber();
            String replace = maxNumber.replace(s, "");
            int va = Integer.valueOf(replace);
            return s + (va + 1);
        }
        return s + DateUtil.year(new Date()) + "00001";
    }

    @Override
    public PageResult<BizTaskSupervisionDTO> queryPageByCondition(BizTaskSupervisionDTO entityDTO, BasePageForm basePageForm) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LambdaQueryWrapper<BizTaskSupervision> queryWrapper = new LambdaQueryWrapper();
        if (CollectionUtils.isNotEmpty(entityDTO.getBizTaskSupervisionIdList())) {
            queryWrapper.in(BizTaskSupervision::getId, entityDTO.getBizTaskSupervisionIdList());
        }
        if (StringUtils.isNotBlank(entityDTO.getTitle())) {
            queryWrapper.like(BizTaskSupervision::getTitle, entityDTO.getTitle());
        }
        if (StringUtils.isNotEmpty(entityDTO.getStartTime())) {
            LocalDateTime startTime = LocalDateTime.parse(entityDTO.getStartTime(), df);
            queryWrapper.ge(BizTaskSupervision::getCreateTime, startTime);
        }
        if (StringUtils.isNotEmpty(entityDTO.getEndTime())) {
            LocalDateTime endTime = LocalDateTime.parse(entityDTO.getEndTime(), df);
            queryWrapper.le(BizTaskSupervision::getCreateTime, endTime);
        }

        queryWrapper.orderByDesc(BizTaskSupervision::getCreateTime);
        IPage<BizTaskSupervision> pageData = bizTaskSupervisionMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        IPage<BizTaskSupervisionDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskSupervisionDTO.class));
        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    @Override
    public PageResult<BizDeTaskFinishCtDTO> queryDecomposeTaskGroupDutyPeople(BizDeTaskFinishCtDTO bizDeTaskFinishCtDTO, BasePageForm basePageForm) {
        IPage<BizDeTaskFinishCtDTO> page = bizTaskSupervisionMapper.queryDecomposeTaskGroupDutyPeople(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizDeTaskFinishCtDTO);
        PageResult<BizDeTaskFinishCtDTO> pageResults = new PageResult<>(page.getRecords(), page.getTotal(),
                page.getTotal());
        return pageResults;
    }

    /**
     * 查询当前用户创建的督察主任务列表，不分页
     *
     * @return
     */
    @Override
    public List<BizTaskSupervisionDTO> querySupervisionList() {
        List<BizTaskSupervision> listData = bizTaskSupervisionMapper.selectList(
                new LambdaQueryWrapper<BizTaskSupervision>()
                        .eq(BizTaskSupervision::getCreateBy, SecurityUtils.getCurrentUserId())
                        .orderByDesc(BizTaskSupervision::getCreateTime)
                        .eq(BizTaskSupervision::getHasPublish, TaskStatus.TASK_RELEASE));
        List<Long> taskSupervisionIdList = listData.stream().map(i -> i.getId()).collect(Collectors.toList());
        //主任务下没有签收的子任务，就不展示这个主任务
        if (!taskSupervisionIdList.isEmpty()) {
            List<BizTaskDecompose> bizTaskDecomposes = bizTaskDecomposeMapper.selectListNoAdd(
                    new LambdaQueryWrapper<BizTaskDecompose>().select(BizTaskDecompose::getSubTblFk)
                            .in(BizTaskDecompose::getSubTblFk, taskSupervisionIdList).eq(BizTaskDecompose::getHasSign, TaskStatus.HAS_SIGN));
            Set<Long> subTblFkIdSet = bizTaskDecomposes.stream().map(i -> i.getSubTblFk()).collect(Collectors.toSet());
            List<BizTaskSupervision> removeList = new LinkedList();
            listData.forEach(i -> {
                if (!subTblFkIdSet.contains(i.getId())) {
                    removeList.add(i);
                }
            });
            listData.removeAll(removeList);
        }

        return ListCopyUtil.copy(listData, BizTaskSupervisionDTO.class);
    }

    /**
     * 待审任务（待审，已发布，已驳回）
     *
     * @param bizDeTaskFinishCtDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskSupervisionDTO> queryPendingTasks(BizPendingTasksDTO bizDeTaskFinishCtDTO, BasePageForm basePageForm) {
        LambdaQueryWrapper<BizTaskSupervision> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.eq(BizTaskSupervision::getHasPublish, bizDeTaskFinishCtDTO.getTaskState())
                .like(StringUtils.isNotEmpty(bizDeTaskFinishCtDTO.getTaskName()), BizTaskSupervision::getTitle, bizDeTaskFinishCtDTO.getTaskName())
                .ge(StringUtils.isNotEmpty(bizDeTaskFinishCtDTO.getStartTime()), BizTaskSupervision::getCreateTime, bizDeTaskFinishCtDTO.getStartTime() + " 00:00:00")
                .le(StringUtils.isNotEmpty(bizDeTaskFinishCtDTO.getEndTime()), BizTaskSupervision::getCreateTime, bizDeTaskFinishCtDTO.getEndTime() + " 23:59:59")
                .eq(StringUtils.isNotEmpty(bizDeTaskFinishCtDTO.getDegreeUrgency()), BizTaskSupervision::getDegreeUrgency, bizDeTaskFinishCtDTO.getDegreeUrgency())
                .eq(!Objects.isNull(bizDeTaskFinishCtDTO.getLeaderId()), BizTaskSupervision::getLeadPeopleId, bizDeTaskFinishCtDTO.getLeaderId())
                .isNotNull(bizDeTaskFinishCtDTO.getTaskState().equals(TaskStatus.RELEASE), BizTaskSupervision::getApprovalId)
//                .isNull(!Objects.isNull(bizDeTaskFinishCtDTO.getLeaderId()), BizTaskSupervision::getLeadPeopleId)
                .orderByDesc(BizTaskSupervision::getCreateTime);

        IPage<BizTaskSupervision> page = bizTaskSupervisionMapper.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), lambdaQueryWrapper);
        IPage<BizTaskSupervisionDTO> data = page.convert(entity -> BeanConvertUtils.copyProperties(entity, BizTaskSupervisionDTO.class));
        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 审批任务通过
     *
     * @param taskId
     */
    @Override
    public Integer adoptApproval(Long taskId) {
        if (Objects.isNull(taskId)) {
            throw new BusinessException("任务id不能为空!");
        }
        BizTaskSupervision bizTaskSupervision = new BizTaskSupervision();
        bizTaskSupervision.setHasPublish(TaskStatus.RELEASE);
        bizTaskSupervision.setApprovalTime(LocalDateTime.now());
        bizTaskSupervision.setApprovalId(SecurityUtils.getCurrentUserId());
        bizTaskSupervision.setApprovalName(SecurityUtils.getCurrentCscpUserDetail().getRealName());

        Set<String> phoneList = new HashSet<>();
        LambdaQueryWrapper<BizTaskDecompose> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BizTaskDecompose::getSubTblFk, taskId);
        List<BizTaskDecompose> bizTaskSupervisionList = bizTaskDecomposeMapper.selectListNoAdd(lambdaQueryWrapper);
        for (BizTaskDecompose bizTaskDecompose : bizTaskSupervisionList) {
            if (StringUtils.isNotEmpty(bizTaskDecompose.getContactPeoplePhone())) {
                phoneList.add(bizTaskDecompose.getContactPeoplePhone());
            } else {
                phoneList.add(bizTaskDecompose.getDutyPeoplePhone());
            }
        }
        // 短信发送
        BizTaskSupervision bizTaskSupervision1 = this.getById(taskId);
        if (bizTaskSupervision1 != null) {
            if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(bizTaskSupervision1.getTitle()) && CollectionUtil.isNotEmpty(phoneList)) {
                SmsSendUtil.batchSendSms(phoneList, bizTaskSupervision1.getTitle(), SmsSendEnum.HH_TASK_SMS_SIGN);
            }
        }
        //审批任务通过
        return bizTaskSupervisionMapper.update(bizTaskSupervision,
                new LambdaQueryWrapper<BizTaskSupervision>()
                        .eq(BizTaskSupervision::getId, taskId).eq(BizTaskSupervision::getHasPublish, TaskStatus.TO_BE_APPROVED));
    }

    /**
     * 驳回
     *
     * @param taskId
     * @param rejectReason
     * @return
     */
    @Override
    public Integer reject(Long taskId, String rejectReason) {

        BizTaskSupervision bizTaskSupervision = new BizTaskSupervision();
        bizTaskSupervision.setHasPublish(TaskStatus.REJECT);
        bizTaskSupervision.setRejectReason(rejectReason);
        bizTaskSupervision.setDifficultyTime(LocalDateTime.now());
        bizTaskSupervision.setDifficultyId(SecurityUtils.getCurrentUserId());
        bizTaskSupervision.setDifficultyName(SecurityUtils.getCurrentCscpUserDetail().getRealName());

        //驳回
        return bizTaskSupervisionMapper.update(bizTaskSupervision,
                new LambdaQueryWrapper<BizTaskSupervision>().eq(BizTaskSupervision::getId, taskId).eq(BizTaskSupervision::getHasPublish, TaskStatus.TO_BE_APPROVED));
    }

    /**
     * 查询角标
     *
     * @return
     */
    @Override
    public Integer getAngleMark() {
        BizPendingTasksDTO bizPendingTasksDTO = new BizPendingTasksDTO();
        if (DataFilterThreadLocal.get() == null) {
            bizPendingTasksDTO.setLeaderId(SecurityUtils.getCurrentUserId());
        }
        LambdaQueryWrapper<BizTaskSupervision> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .select(BizTaskSupervision::getId)
                .eq(BizTaskSupervision::getHasPublish, TaskStatus.TO_BE_APPROVED)
                .eq(!Objects.isNull(bizPendingTasksDTO.getLeaderId()), BizTaskSupervision::getLeadPeopleId, bizPendingTasksDTO.getLeaderId())
                .isNull(Objects.isNull(bizPendingTasksDTO.getLeaderId()), BizTaskSupervision::getLeadPeopleId);

        return bizTaskSupervisionMapper.selectCount(lambdaQueryWrapper);
    }

    /**
     * 根据任务主表id获取分数及计算公式
     *
     * @param id
     * @return
     */
    @Override
    public BizTaskEquationAndScoreDTO getEquationAndScoreByid(Long id) {
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(id);
        List<BizTaskCoefficientDTO> list = new ArrayList<>();
        BizTaskCoefficientDTO importDTO = new BizTaskCoefficientDTO();
        //添加重要性系数
        importDTO.setCoefficientId(Long.valueOf(bizTaskSupervision.getInspectorTaskLevel()));
        list.add(importDTO);
        //添加紧急性系数
        List<TSysDictRecordDTO> Degree = tSysDictRecordService.getDictRecordListByDictCode("taskDegreeUrgency", SecurityUtils.getCurrentCompanyId());
        BizTaskCoefficientDTO degreeDTO = new BizTaskCoefficientDTO();
        degreeDTO.setCoefficientId(Degree.get(0).getDictId());
        degreeDTO.setDictCode(bizTaskSupervision.getDegreeUrgency());
        list.add(degreeDTO);
        //添加难易性系数
        List<TSysDictRecordDTO> difficulty = tSysDictRecordService.getDictRecordListByDictCode("task_difficulty", SecurityUtils.getCurrentCompanyId());
        BizTaskCoefficientDTO difficultyDTO = new BizTaskCoefficientDTO();
        difficultyDTO.setCoefficientId(difficulty.get(0).getDictId());
        difficultyDTO.setDictCode(bizTaskSupervision.getDifficulty());
        list.add(difficultyDTO);

        return bizTaskCoefficientService.getEquationAndScore(list);
    }


    /**
     * 根据任务主表id获取分数
     *
     * @param decomposeId
     * @return
     */
    @Override
    public BizScoreByIdDTO getScoreAndLead(Long decomposeId) {
        if (Objects.isNull(decomposeId)) {
            throw new BusinessException("子任务id不能为空");
        }

        BizTaskDecompose bizTaskDecompose = bizTaskDecomposeMapper.selectById(decomposeId);
        if (Objects.isNull(bizTaskDecompose) && Objects.isNull(bizTaskDecompose.getSubTblFk())) {
            throw new BusinessException("子任务数据错误,没有对应的主任务id!");
        }
        //根据id查询对应的主任务
        BizTaskSupervision bizTaskSupervision = bizTaskSupervisionMapper.selectById(bizTaskDecompose.getSubTblFk());

        //获取分值,和判断是否是牵头人单位
        BizScoreByIdDTO bizScoreByIdDTO = new BizScoreByIdDTO();
        bizScoreByIdDTO.setScore(bizTaskDecompose.getTaskScore());
        boolean b = !Objects.isNull(bizTaskSupervision) && !Objects.isNull(bizTaskSupervision.getLeadPeopleId()) && bizTaskSupervision.getLeadPeopleId().equals(bizTaskDecompose.getDutyPeopleId());
        if (b) {
            bizScoreByIdDTO.setWhetherLead(1);
        } else {
            bizScoreByIdDTO.setWhetherLead(0);
        }

        return bizScoreByIdDTO;
    }

    @Override
    public Double getScoreById(Long id) {
        return this.getEquationAndScoreByid(id).getScore();
    }


    /**
     * 根据任务主表id获取计算公式
     *
     * @param id
     * @return
     */
    @Override
    public String getEquationById(Long id) {
        return this.getEquationAndScoreByid(id).getEquation();
    }

    @Override
    public int isLeadPeople(Long userId) {
        LambdaQueryWrapper<BizTaskSupervision> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskSupervision::getLeadPeopleId, userId);
        queryWrapper.eq(BizTaskSupervision::getHasFinish, 0);
        List<BizTaskSupervision> bizTaskDecomposeList = bizTaskSupervisionMapper.selectListNoAdd(queryWrapper);
        if (bizTaskDecomposeList.size() > 0) {
            LambdaQueryWrapper<BizTaskSupervision> queryWrapper2 = new LambdaQueryWrapper();
            queryWrapper2.eq(BizTaskSupervision::getCreateBy, userId);
            queryWrapper2.eq(BizTaskSupervision::getHasFinish, 0);
            int count = bizTaskSupervisionMapper.selectCount(queryWrapper);
            if (count > 0) {
                return 2;
            } else {
                return 0;
            }
        } else {
            return 1;
        }

    }

    /**
     * 查询首页全市年得分统计 （总分是：任务分+日常分+加减分，平均分=（任务分+日常分）/月份+加减分）
     *
     * @param cscpUsers
     * @return
     */
    @Override
    public List<BizTaskInformationDTO> scoreStatistics(List<CscpUserDTO> cscpUsers) {
        if (Objects.isNull(cscpUsers) || cscpUsers.isEmpty()) {
            return new ArrayList<>();
        }

        List<BizTaskInformationDTO> copy = ListCopyUtil.copy(cscpUsers, BizTaskInformationDTO.class);
        Set<Long> userIds = copy.stream().filter(i -> !Objects.isNull(i.getId())).map(i -> i.getId()).collect(Collectors.toSet());
        if (userIds.isEmpty()) {
            return new ArrayList<>();
        }

        //获取今年第一天的月份
        LocalDateTime firstDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        //获取上个月最后一天的月份
        LocalDateTime lastDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth())), LocalTime.MAX);
        // 获取今年第一天及最后一天
        LocalDateTime firstDayOfYear1 = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        LocalDateTime lastDayOfYear1 = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);


        //通过用户查询到对应的任务
        BizTaskStatisticsDTO build = BizTaskStatisticsDTO.builder()
                .userIds(Optional.ofNullable(cscpUsers.stream().map(i -> i.getId()).collect(Collectors.toSet())).orElse(new HashSet<>()))
                .taskStartTime(firstDayOfYear)
                .taskEndTime(lastDayOfYear)
                .hasPublish(TaskStatus.RELEASE)
                .decHasFinish(TaskStatus.DECOMSE_FINISH)
                .returnFields(Arrays.asList("dec_task.duty_people_id"))
                .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_id"))
                .supHasFinish(TaskStatus.SUP_FINISH).build();
        List<BizTaskDTO> bizTaskDTOList = this.selectUserTasks(build);
        Map<Long, List<BizTaskDTO>> bizTaskDTOMaps = bizTaskDTOList.stream().filter(i -> !Objects.isNull(i.getDutyPeopleId())).collect(Collectors.groupingBy(BizTaskDTO::getDutyPeopleId));


        //查询任务对应的分数，并且统计好每个任务的总分。（任务分，加减分，日常分）
        //k:用户id,v分数和
        Map<Long, Double> bizTaskScoresMap = Optional.ofNullable(this.selectBizTaskScores(userIds, firstDayOfYear, lastDayOfYear)).orElse(new HashMap<>());
        Map<Long, Double> bizTaskScoreAddsMap = Optional.ofNullable(this.selectBizTaskScoreAdds(userIds, firstDayOfYear1, lastDayOfYear1)).orElse(new HashMap<>());
        Map<Long, Double> bizTaskSupervisionsMap = Optional.ofNullable(this.selectBizTaskSupervisions(userIds, firstDayOfYear1, lastDayOfYear1)).orElse(new HashMap<>());
        int month = LocalDateTime.now().getMonth().getValue() - 1 == 0 ? 1 : LocalDateTime.now().getMonth().getValue() - 1;
        Map<Long, Double> bizScoreRecordsMap = Optional.ofNullable(this.selectBizScoreRecords(userIds, 1, month)).orElse(new HashMap<>());


        //总分和平均分统计
        for (int i = 0; i < copy.size(); i++) {
            BizTaskInformationDTO bizTaskInformationDTO = copy.get(i);
            //任务分
            double taskScores = !Objects.isNull(bizTaskScoresMap.get(bizTaskInformationDTO.getId())) ? bizTaskScoresMap.get(bizTaskInformationDTO.getId()) : 0;
            //加分
            double taskScoreAdd = !Objects.isNull(bizTaskScoreAddsMap.get(bizTaskInformationDTO.getId())) ? bizTaskScoreAddsMap.get(bizTaskInformationDTO.getId()) : 0;
            //减分
            double taskSupervision = !Objects.isNull(bizTaskSupervisionsMap.get(bizTaskInformationDTO.getId())) ? bizTaskSupervisionsMap.get(bizTaskInformationDTO.getId()) : 0;
            //日常分
            double scoreRecord = !Objects.isNull(bizScoreRecordsMap.get(bizTaskInformationDTO.getId())) ? bizScoreRecordsMap.get(bizTaskInformationDTO.getId()) : 0;

            //任务数
            List<BizTaskDTO> bizTasks = bizTaskDTOMaps.get(bizTaskInformationDTO.getId());
            if (!Objects.isNull(bizTasks)) {
                bizTaskInformationDTO.setTaskCount(bizTasks.size());
            }

            //奖惩分取-10到10之间
            double rewardsAndPunishments = taskScoreAdd - taskSupervision;
            if (rewardsAndPunishments <= -10) {
                rewardsAndPunishments = -10;
            } else if (rewardsAndPunishments >= 10) {
                rewardsAndPunishments = 10;
            }

            bizTaskInformationDTO.setTotalScore((double) Math.round((taskScores + scoreRecord + rewardsAndPunishments) * 100) / 100.0);

            //如果分数为零，不求平均值直接赋值为零
            if (taskScores + scoreRecord <= 0) {
                bizTaskInformationDTO.setAverage(0.0);
            } else {
                //如果是一月，将月份设置为一月，防止为-1
                int y = LocalDateTime.now().getMonth().getValue();
                if (y == 0) {
                    y = 1;
                } else {
                    y = y - 9;
                }
                bizTaskInformationDTO.setAverage((double) Math.round((((taskScores + scoreRecord) / y) + taskScoreAdd - taskSupervision) * 100) / 100.0);
            }
        }

        return copy.stream().sorted(Comparator.comparing(BizTaskInformationDTO::getTotalScore).reversed()).collect(Collectors.toList());
    }

    /**
     * 月分数排名，取单位一把手每月分数
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> firstHandTotalScore() {
        List<Map<String, Object>> list = new LinkedList<>();
        //获取一把手
        Set<BizUserScoreDTO> bizUserScoreDTOS = Optional.ofNullable(this.getFirstHand()).orElse(new LinkedHashSet<>());
        Set<Long> userIds = bizUserScoreDTOS.stream().filter(i -> !Objects.isNull(i.getTemplateBusinessId())).map(i -> i.getTemplateBusinessId()).collect(Collectors.toSet());

        //获取上个月第一天和最后一天
        LocalDateTime firstDay = LocalDateTime.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime lastDay = LocalDateTime.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());

        //查询任务对应的分数，并且统计好每个任务的总分。（任务分，日常分）
        Map<Long, Double> bizTaskScoresMap = Optional.ofNullable(this.selectBizTaskScores(userIds, firstDay, lastDay)).orElse(new HashMap<>());
        int endMonth = LocalDateTime.now().getMonth().getValue() - 1 == 0 ? 1 : LocalDateTime.now().getMonth().getValue() - 1;
        Map<Long, Double> bizScoreRecordsMap = Optional.ofNullable(this.selectBizScoreRecords(userIds, endMonth, endMonth)).orElse(new HashMap<>());


        //获取接收任务数和办结任务数
        BizTaskStatisticsDTO signInCondition = BizTaskStatisticsDTO.builder()
                .userIds(userIds)
                .taskStartTime(firstDay)
                .taskEndTime(lastDay)
                .hasSign(TaskStatus.HAS_SIGN)
                .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_id"))
                .returnFields(Arrays.asList("dec_task.duty_people_company_id", "dec_task.duty_people_id")).build();
        List<BizTaskDTO> receiveTasks = Optional.ofNullable(this.receiveConcludeTaskCount(signInCondition)).orElse(new LinkedList<>());
        Map<Long, List<BizTaskDTO>> receiveTaskMap = receiveTasks.stream().filter(i -> !Objects.isNull(i.getDutyPeopleId())).collect(Collectors.groupingBy(BizTaskDTO::getDutyPeopleId));

        BizTaskStatisticsDTO concludeCondition = BizTaskStatisticsDTO.builder()
                .userIds(userIds)
                .taskStartTime(firstDay)
                .taskEndTime(lastDay)
                .supHasFinish(TaskStatus.SUP_FINISH)
                .decHasFinish(TaskStatus.DECOMSE_FINISH)
                .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_id"))
                .returnFields(Arrays.asList("dec_task.duty_people_company_id", "dec_task.duty_people_id")).build();
        List<BizTaskDTO> concludeTasks = Optional.ofNullable(this.receiveConcludeTaskCount(concludeCondition)).orElse(new LinkedList<>());
        Map<Long, List<BizTaskDTO>> concludeTaskMap = concludeTasks.stream().filter(i -> !Objects.isNull(i.getDutyPeopleId())).collect(Collectors.groupingBy(BizTaskDTO::getDutyPeopleId));


        //统计每个一把手的总分
        bizUserScoreDTOS.forEach(i -> {
            Map<String, Object> map = new HashMap<>();
            //任务分
            Double aDouble = !Objects.isNull(bizTaskScoresMap.get(i.getTemplateBusinessId())) ? bizTaskScoresMap.get(i.getTemplateBusinessId()) : 0.0;
            //日常分
            Double scoreRecord = !Objects.isNull(bizScoreRecordsMap.get(i.getTemplateBusinessId())) ? bizScoreRecordsMap.get(i.getTemplateBusinessId()) : 0.0;

            map.put("companyName", i.getUnitName());
            map.put("fraction", (double) Math.round((scoreRecord + aDouble) * 100) / 100.0);
            map.put("receiveTaskCount", Optional.ofNullable(receiveTaskMap.get(i.getTemplateBusinessId())).orElse(new LinkedList<>()).size());
            map.put("concludeTaskCount", Optional.ofNullable(concludeTaskMap.get(i.getTemplateBusinessId())).orElse(new LinkedList<>()).size());
            list.add(map);
        });

        return list.stream().sorted(Comparator.comparing((Function<Map<String, Object>, Double>) HashMap -> (Double) HashMap.get("fraction")).reversed()).collect(Collectors.toList());
    }


    /**
     * 全市单位总任务数量统计
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> companyAllNumberOfTasks() {
        List<Map<String, Object>> list = new LinkedList<>();

        //获取今年第一天的月份
        LocalDateTime firstDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        //获取上个月最后一天的月份
        LocalDateTime lastDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth())), LocalTime.MAX);


        //获取到一把手，根据一把手对应的单位进行统计
        Set<BizUserScoreDTO> firstHand = Optional.ofNullable(this.getFirstHand()).orElse(new HashSet<>());
        Set<Long> companyIds = firstHand.stream().map(i -> Long.valueOf(i.getUnitId())).collect(Collectors.toSet());
        if (companyIds.isEmpty()) {
            return list;
        }

        //已发任务
        BizTaskStatisticsDTO sendCondition = BizTaskStatisticsDTO.builder()
                .taskCompanyIds(companyIds)
                .hasFinishStartTime(firstDayOfYear)
                .hasFinishEndTime(lastDayOfYear)
                .supHasFinish(TaskStatus.SUP_FINISH)
                .returnFields(Arrays.asList("sup_task.company_id", "sup_task.has_finish AS supHasFinish", "sup_task.CREATE_TIME"))
                .groupingFields(Arrays.asList("sup_task.id")).build();
        List<BizTaskDTO> bizTasks = Optional.ofNullable(this.statisticsAssigned(sendCondition)).orElse(new LinkedList<>());

        //接收任务
        BizTaskStatisticsDTO receiveTaskCondition = BizTaskStatisticsDTO.builder()
                .userCompanyId(companyIds)
                .filterCompanyId(companyIds)
                .hasFinishStartTime(firstDayOfYear)
                .hasFinishEndTime(lastDayOfYear)
                .decHasFinish(TaskStatus.DECOMSE_FINISH)
                .supHasFinish(TaskStatus.SUP_FINISH)
                .hasSign(TaskStatus.HAS_SIGN)
                .returnFields(Arrays.asList("dec_task.has_finish_time", "dec_task.duty_people_company_id", " sup_task.company_id", " dec_task.duty_people_id"))
                .build();
        List<BizTaskDTO> bizTasks1 = Optional.ofNullable(this.receiveTask(receiveTaskCondition)).orElse(new LinkedList<>());
        bizTasks.addAll(bizTasks1);

        //获取接收任务数和办结任务数
        BizTaskStatisticsDTO signInCondition = BizTaskStatisticsDTO.builder()
                .userCompanyId(companyIds)
                .taskStartTime(firstDayOfYear)
                .taskEndTime(firstDayOfYear)
                .hasSign(TaskStatus.HAS_SIGN)
//                .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_company_id"))
                .returnFields(Arrays.asList("dec_task.duty_people_company_id", "dec_task.duty_people_id")).build();
        List<BizTaskDTO> receiveTasks = Optional.ofNullable(this.receiveConcludeTaskCount(signInCondition)).orElse(new LinkedList<>());
        Map<Long, List<BizTaskDTO>> receiveTaskMap = receiveTasks.stream().filter(i -> !Objects.isNull(i.getDutyPeopleCompanyId())).collect(Collectors.groupingBy(BizTaskDTO::getDutyPeopleCompanyId));

        BizTaskStatisticsDTO concludeCondition = BizTaskStatisticsDTO.builder()
                .userCompanyId(companyIds)
                .taskStartTime(firstDayOfYear)
                .taskEndTime(firstDayOfYear)
                .supHasFinish(TaskStatus.SUP_FINISH)
                .decHasFinish(TaskStatus.DECOMSE_FINISH)
//                .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_company_id"))
                .returnFields(Arrays.asList("dec_task.duty_people_company_id", "dec_task.duty_people_id")).build();
        List<BizTaskDTO> concludeTasks = Optional.ofNullable(this.receiveConcludeTaskCount(concludeCondition)).orElse(new LinkedList<>());
        Map<Long, List<BizTaskDTO>> concludeTaskMap = concludeTasks.stream().filter(i -> !Objects.isNull(i.getDutyPeopleCompanyId())).collect(Collectors.groupingBy(BizTaskDTO::getDutyPeopleCompanyId));


        Map<Long, List<BizTaskDTO>> bizTaskMap = bizTasks.stream().filter(i -> !Objects.isNull(i.getDutyPeopleCompanyId())).collect(Collectors.groupingBy(BizTaskDTO::getDutyPeopleCompanyId));

        firstHand.forEach(i -> {
            MapBuilder<String, Object> objectObjectMapBuilder = MapBuilder.create(new HashMap<String, Object>());

            //办结条数
            int concludeTaskCount = Optional.ofNullable(concludeTaskMap.get(Long.valueOf(i.getUnitId()))).orElse(new LinkedList<>()).size();
            //接收条数
            int unsettledTaskCount = Optional.ofNullable(receiveTaskMap.get(Long.valueOf(i.getUnitId()))).orElse(new LinkedList<>()).size();

            objectObjectMapBuilder.put("companyName", i.getUnitName());
            objectObjectMapBuilder.put("taskCount", Optional.ofNullable(bizTaskMap.get(Long.valueOf(i.getUnitId()))).orElse(new LinkedList<>()).size());
            objectObjectMapBuilder.put("concludeTaskCount", unsettledTaskCount);
            objectObjectMapBuilder.put("unsettledTaskCount", unsettledTaskCount - concludeTaskCount);

            list.add(objectObjectMapBuilder.build());
        });

        return list;
    }

    /**
     * 本单位月度任务数量统计
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> companyNumberOfTasks() {
        //存放月份
        List<String> l = new LinkedList<>();
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        Set<Long> companyIds = Arrays.asList(currentCompanyId).stream().collect(Collectors.toSet());

        int monthValue = LocalDateTime.now().getMonthValue();
        int year = LocalDateTime.now().getYear();
        for (int i = 1; i <= monthValue - 1; i++) {
            l.add(year + "-" + String.format("%02d", i));
        }

        //获取今年第一天的月份
        LocalDateTime firstDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        //获取上个月最后一天的月份
        LocalDateTime lastDayOfMonth = LocalDateTime.of(LocalDate.from(LocalDateTime.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth())), LocalTime.MAX);

        //单位已发任务数+接受任务数
        BizTaskStatisticsDTO receiveTaskCondition = BizTaskStatisticsDTO.builder()
                .userCompanyId(companyIds)
                .filterCompanyId(companyIds)
                .hasFinishStartTime(firstDayOfYear)
                .hasFinishEndTime(lastDayOfMonth)
                .decHasFinish(TaskStatus.DECOMSE_FINISH)
                .supHasFinish(TaskStatus.SUP_FINISH)
                .hasSign(TaskStatus.HAS_SIGN)
                .returnFields(Arrays.asList("dec_task.has_finish_time", "dec_task.duty_people_company_id", " sup_task.company_id", " dec_task.duty_people_id"))
                .groupingFields(Arrays.asList("sup_task.id")).build();
        List<BizTaskDTO> bizTasks = Optional.ofNullable(this.receiveTask(receiveTaskCondition)).orElse(new LinkedList<>());

        BizTaskStatisticsDTO sendCondition = BizTaskStatisticsDTO.builder()
                .taskCompanyIds(companyIds)
                .hasFinishStartTime(firstDayOfYear)
                .hasFinishEndTime(lastDayOfMonth)
                .supHasFinish(TaskStatus.SUP_FINISH)
                .returnFields(Arrays.asList("sup_task.company_id", "sup_task.CREATE_TIME"))
                .build();
        List<BizTaskDTO> bizTasks1 = Optional.ofNullable(this.received(sendCondition)).orElse(new LinkedList<>());
        bizTasks.addAll(bizTasks1);

        //转换指定时间格式
        DateTimeFormatter fmt1 = DateTimeFormatter.ofPattern("yyyy-MM");
        Map<String, List<BizTaskDTO>> bizTaskMap = bizTasks.stream().filter(i -> !Objects.isNull(i.getHasFinishTime())).map(i -> {
            String format = fmt1.format(i.getHasFinishTime());
            i.setHasFinishTimeStrig(format);
            return i;
        }).collect(Collectors.groupingBy(BizTaskDTO::getHasFinishTimeStrig));

        List<Map<String, Object>> list = new LinkedList<>();
        l.forEach(i -> {
            MapBuilder<String, Object> objectObjectMapBuilder = MapBuilder.create(new HashMap<String, Object>());

            List<BizTaskDTO> bizTaskDTOS = bizTaskMap.get(i);
            if (!Objects.isNull(bizTaskDTOS)) {
                objectObjectMapBuilder.put("month", i);
                objectObjectMapBuilder.put("taskCount", bizTaskDTOS.size());
            } else {
                objectObjectMapBuilder.put("month", i);
                objectObjectMapBuilder.put("taskCount", 0);
            }
            list.add(objectObjectMapBuilder.build());
        });

        return list;
    }

    /**
     * 统计本年交办任务，已办结任务，待办结任务
     *
     * @return
     */
    @Override
    public Map<String, Integer> assignedCompletedToBeSettledTask() {
        Set<Long> companyIds = Arrays.asList(SecurityUtils.getCurrentCompanyId()).stream().collect(Collectors.toSet());
        companyIds.removeAll(Collections.singleton(null));

        // 获取今年第一天及最后一天
        LocalDateTime firstDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        LocalDateTime lastDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);

        //交办任务
        BizTaskStatisticsDTO assignedCondition = BizTaskStatisticsDTO.builder()
                .hasPublish(TaskStatus.RELEASE)
                .subTaskCompanyIds(companyIds)
                .taskStartTime(firstDayOfYear)
                .taskEndTime(lastDayOfYear)
                .returnFields(Arrays.asList("sup_task.has_finish_time", "sup_task.has_finish AS supHasFinish", "sup_task.company_id AS sup_company_id", "sup_task.lead_people_id", "sup_task.id AS taskSupervisionId"))
                .groupingFields(Arrays.asList("sup_task.id")).build();
        List<BizTaskDTO> assignedConditionTasks = Optional.ofNullable(this.statisticsAssigned(assignedCondition)).orElse(new LinkedList<>());
        //已办结任务
        List<BizTaskDTO> completedTasks = Optional.ofNullable(assignedConditionTasks.stream().filter(i -> !Objects.isNull(i.getSupHasFinish()) && i.getSupHasFinish().equals(TaskStatus.SUP_FINISH)).collect(Collectors.toList())).orElse(new LinkedList<>());
        //待办结任务
        List<BizTaskDTO> toBeSettledTasks = Optional.ofNullable(assignedConditionTasks.stream().filter(i -> !Objects.isNull(i.getSupHasFinish()) && i.getSupHasFinish().equals(TaskStatus.SUP_NO_FINISH)).collect(Collectors.toList())).orElse(new LinkedList<>());

        Map<String, Integer> build = MapBuilder.create(new HashMap<String, Integer>(3)).put("assigned", assignedConditionTasks.size()).put("completed", completedTasks.size()).put("toBeSettled", toBeSettledTasks.size()).build();
        return build;
    }

    /**
     * 市直单位本年接收任务，本年已发任务，本年已办结任务
     *
     * @return
     */
    @Override
    public Map<String, Integer> receiveSentCompletedTask() {
        Set<Long> companyIds = Arrays.asList(SecurityUtils.getCurrentCompanyId()).stream().collect(Collectors.toSet());
        companyIds.removeAll(Collections.singleton(null));

        // 获取今年第一天及最后一天
        LocalDateTime firstDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        LocalDateTime lastDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);

        //接收任务
        BizTaskStatisticsDTO receiveTaskCondition1 = BizTaskStatisticsDTO.builder()
                .userCompanyId(companyIds)
                .filterCompanyId(companyIds)
                .taskStartTime(firstDayOfYear)
                .taskEndTime(lastDayOfYear)
                .hasSign(TaskStatus.HAS_SIGN)
                .returnFields(Arrays.asList("dec_task.has_finish_time", "dec_task.duty_people_company_id", " sup_task.company_id", " dec_task.duty_people_id"))
                .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_id", "dec_task.duty_people_company_id")).build();
        Integer count1 = Optional.ofNullable(this.receiveConcludeTaskCount(receiveTaskCondition1)).orElse(new LinkedList<>()).size();

        //已发任务
        BizTaskStatisticsDTO sendCondition1 = BizTaskStatisticsDTO.builder()
                .taskCompanyIds(companyIds)
                .hasPublish(TaskStatus.RELEASE)
                .taskStartTime(firstDayOfYear)
                .taskEndTime(lastDayOfYear)
                .returnFields(Arrays.asList("sup_task.company_id", "sup_task.has_finish AS supHasFinish", "sup_task.CREATE_TIME"))
                .groupingFields(Arrays.asList("sup_task.id")).build();
        List<BizTaskDTO> assignedConditionTasks = Optional.ofNullable(this.statisticsAssigned(sendCondition1)).orElse(new LinkedList<>());

        //已办结任务
//        BizTaskStatisticsDTO receiveTaskCondition2 = BizTaskStatisticsDTO.builder()
//                .userCompanyId(companyIds)
//                .filterCompanyId(companyIds)
//                .taskStartTime(firstDayOfYear)
//                .taskEndTime(lastDayOfYear)
//                .decHasFinish(TaskStatus.DECOMSE_FINISH)
//                .supHasFinish(TaskStatus.SUP_FINISH)
//                .hasSign(TaskStatus.HAS_SIGN)
//                .returnFields(Arrays.asList("dec_task.has_finish_time", "dec_task.duty_people_company_id", " sup_task.company_id", " dec_task.duty_people_id"))
//                .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_id", "dec_task.duty_people_company_id")).build();
//        Integer count4 = Optional.ofNullable(this.receiveConcludeTaskCount(receiveTaskCondition2)).orElse(new LinkedList<>()).size();
        List<BizTaskDTO> completedTasks = Optional.ofNullable(assignedConditionTasks.stream().filter(i -> !Objects.isNull(i.getSupHasFinish()) && i.getSupHasFinish().equals(TaskStatus.SUP_FINISH)).collect(Collectors.toList())).orElse(new LinkedList<>());


        Map<String, Integer> build = MapBuilder.create(new HashMap<String, Integer>(3)).put("receive", count1).put("sent", assignedConditionTasks.size()).put("completed", completedTasks.size()).build();
        return build;
    }

    /**
     * 统计各单位一把手分数
     *
     * @return
     */
    @Override
    public List<BizTaskInformationDTO> firstHandFraction() {
        //获取到一把手，根据一把手对应的单位进行统计
        Set<BizUserScoreDTO> firstHand = this.getFirstHand();
        if (Objects.isNull(firstHand) && firstHand.isEmpty()) {
            return new LinkedList<>();
        }

        List<CscpUserDTO> users = new LinkedList<>();
        firstHand.forEach(i -> {
            CscpUserDTO user = new CscpUserDTO();
            user.setId(i.getTemplateBusinessId());
            user.setRealName(i.getTemplateBusinessName());
            user.setCompanyId(Objects.isNull(i.getUnitId()) ? null : Long.valueOf(i.getUnitId()));
            user.setCompanyName(i.getUnitName());
            users.add(user);
        });

        List<BizTaskInformationDTO> bizTaskInformationDTOS = this.scoreStatistics(users);

        return bizTaskInformationDTOS;
    }

    /**
     * 统计接收任务和办结任务数
     *
     * @param bizTaskStatisticsDTO
     * @return
     */
    @Override
    public List<BizTaskDTO> receiveConcludeTaskCount(BizTaskStatisticsDTO bizTaskStatisticsDTO) {
        List<BizTaskDTO> bizTasks = bizTaskDecomposeMapper.selectUserTasks(bizTaskStatisticsDTO);
        return bizTasks;
    }


    /**
     * 统计市政府全市年得分统计
     *
     * @return
     */
    @Override
    public List<BizTaskInformationDTO> municipalGovernment() {
        List<CscpUserDTO> cscpUsers = cscpUserService.selectTenantUsers(SecurityUtils.getCurrentUser().get().getTenantId());
        //过滤不考核的
        List<CscpUserDTO> cscpUsersReal = new ArrayList<>();
        long a = new Long("1563720173746466818");
        cscpUsers.forEach(user -> {
            if (user.getCompanyId() != null && user.getCompanyId().longValue() != a) {
                cscpUsersReal.add(user);
            }
        });
        return this.scoreStatistics(cscpUsersReal);
    }

    /**
     * 统计市直单位,单位得分统计
     *
     * @return
     */
    @Override
    public List<BizTaskInformationDTO> municipalUnits() {
        List<CscpUserDTO> cscpUsers = cscpOrgService.selectCompayAllUserByCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        return this.scoreStatistics(cscpUsers);
    }

    /**
     * 根据条件查询某个单位接收的任务
     *
     * @param bizTaskStatisticsDTO
     * @return
     */
    @Override
    public List<BizTaskDTO> receiveTask(BizTaskStatisticsDTO bizTaskStatisticsDTO) {
        return bizTaskDecomposeMapper.selectUserTasks(bizTaskStatisticsDTO);
    }

    /**
     * 已发任务
     *
     * @param sendCondition
     * @return
     */
    @Override
    public List<BizTaskDTO> received(BizTaskStatisticsDTO sendCondition) {
        List<BizTaskDTO> bizTasks = bizTaskDecomposeMapper.selectUserTasks(sendCondition);


        return bizTasks.stream().map(i -> {
            return BizTaskDTO.builder().dutyPeopleCompanyId(i.getCompanyId()).hasFinishTime(i.getCreateTime()).companyId(i.getCompanyId()).build();
        }).collect(Collectors.toList());
    }

    /**
     * 统计本年交办任务
     *
     * @param assignedCondition
     * @return
     */
    @Override
    public List<BizTaskDTO> statisticsAssigned(BizTaskStatisticsDTO assignedCondition) {
        List<BizTaskDTO> bizTaskDTOS = bizTaskDecomposeMapper.selectUserTasks(assignedCondition);

        //获取没有牵头人和有牵头人的数据（有牵头人（牵头单位）时，统计时计算子任务数，没有牵头人（牵头单位）时，统计时计算主任务数；）
        List<BizTaskDTO> isEmptyTask = bizTaskDTOS.stream().filter(i -> Objects.isNull(i.getLeadPeopleId())).collect(Collectors.toList());

        List<BizTaskDTO> notEmptyTask = Optional.ofNullable(bizTaskDTOS.stream().filter(i -> !Objects.isNull(i.getLeadPeopleId())).collect(Collectors.toList())).orElse(new LinkedList<>());
        if (!notEmptyTask.isEmpty()) {
            Set<Long> subTaskId = notEmptyTask.stream().filter(i -> !Objects.isNull(i.getTaskSupervisionId())).map(i -> i.getTaskSupervisionId()).collect(Collectors.toSet());
            BizTaskStatisticsDTO build = BizTaskStatisticsDTO.builder()
                    .subTaskIds(subTaskId)
                    .returnFields(Arrays.asList("sup_task.has_finish_time", "dec_task.has_finish AS supHasFinish", "sup_task.company_id AS sup_company_id", "sup_task.lead_people_id", "sup_task.id AS taskSupervisionId"))
                    .taskStartTime(assignedCondition.getTaskStartTime())
                    .groupingFields(Arrays.asList("dec_task.sub_tbl_fk", "dec_task.duty_people_id", "dec_task.duty_people_company_id"))
                    .taskEndTime(assignedCondition.getTaskEndTime()).build();

            List<BizTaskDTO> bizTaskDTOS1 = bizTaskDecomposeMapper.selectUserTasks(build);
            isEmptyTask.addAll(bizTaskDTOS1);
        }

        return isEmptyTask;
    }


    /**
     * 查询对应用户的任务
     *
     * @param bizTaskStatisticsDTO
     * @return
     */
    @Override
    public List<BizTaskDTO> selectUserTasks(BizTaskStatisticsDTO bizTaskStatisticsDTO) {
        return bizTaskDecomposeMapper.selectUserTasks(bizTaskStatisticsDTO);
    }

    /**
     * 查询对应任务的任务分,并且相加
     * k:用户id,v分数和
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Map<Long, Double> selectBizTaskScores(Set<Long> userIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (Objects.isNull(userIds) || userIds.isEmpty()) {
            return new HashMap<Long, Double>();
        }

        List<BizTaskDTO> bizTaskDTO = bizTaskDecomposeMapper.selectBizTaskScores(userIds, startTime, endTime);

        //将任务分和加减法的结构组装成map
        return bizTaskDTO.stream().map(i -> {
            if (Objects.isNull(i.getDecomposeTaskSource())) {
                i.setDecomposeTaskSource("0");
            }
            return i;
        }).collect(Collectors.groupingBy(BizTaskDTO::getDutyPeopleId, Collectors.summingDouble(BizTaskDTO -> Double.valueOf(BizTaskDTO.getDecomposeTaskSource()))));
    }

    /**
     * 查询对应任务的减分分,并且相加
     *
     * @param userIds
     * @return
     */
    @Override
    public Map<Long, Double> selectBizTaskSupervisions(Set<Long> userIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (Objects.isNull(userIds) || userIds.isEmpty()) {
            return new HashMap<Long, Double>();
        }

        List<BizPunishScoreRecordDTO> bizTaskSupervisions = bizPunishScoreRecordMapper.selectBizTaskSupervisions(userIds, startTime, endTime);

        return bizTaskSupervisions.stream().map(i -> {
            if (Objects.isNull(i.getScore())) {
                i.setScore(0);
            }
            return i;
        }).collect(Collectors.groupingBy(BizPunishScoreRecordDTO::getUserId, Collectors.summingDouble(BizPunishScoreRecordDTO::getScore)));
    }

    /**
     * 查询对应任务的加分分,并且相加
     *
     * @param userIds
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Map<Long, Double> selectBizTaskScoreAdds(Set<Long> userIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (Objects.isNull(userIds) || userIds.isEmpty()) {
            return new HashMap<Long, Double>();
        }
        List<BizPunishScoreRecordDTO> bizTaskScoreAdds = bizRewardScoreRecordMapper.selectBizTaskScoreAdds(userIds, startTime, endTime);
        return bizTaskScoreAdds.stream().map(i -> {
            if (Objects.isNull(i.getScore())) {
                i.setScore(0);
            }
            return i;
        }).collect(Collectors.groupingBy(BizPunishScoreRecordDTO::getUserId, Collectors.summingDouble(BizPunishScoreRecordDTO::getScore)));
    }

    /**
     * 查询对应用户的日常分,并且相加
     *
     * @param userIds
     * @return
     */
    @Override
    public Map<Long, Double> selectBizScoreRecords(Set<Long> userIds, Integer startMonth, Integer endMonth) {
        if (Objects.isNull(userIds) || userIds.isEmpty()) {
            return new HashMap<Long, Double>();
        }

        List<BizScoreRecord> bizScoreRecords = bizScoreRecordMapper.selectListNoAdd(
                new LambdaQueryWrapper<BizScoreRecord>()
                        .select(BizScoreRecord::getId, BizScoreRecord::getUserId, BizScoreRecord::getScore)
                        .in(BizScoreRecord::getUserId, userIds)
                        .eq(BizScoreRecord::getScoreYear, LocalDateTime.now().getYear())
                        .ge(BizScoreRecord::getScoreMonth, startMonth).le(BizScoreRecord::getScoreMonth, endMonth));

        return bizScoreRecords.stream().map(i -> {
            if (Objects.isNull(i.getScore())) {
                i.setScore(0);
            }
            return i;
        }).collect(Collectors.groupingBy(BizScoreRecord::getUserId, Collectors.summingDouble(BizScoreRecord::getScore)));
    }

    /**
     * 获取一把手
     *
     * @return
     */
    @Override
    public Set<BizUserScoreDTO> getFirstHand() {
        //查找模型表
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        Long topFloorTenantId = SysTenantUtils.getTopFloorTenamtId(tenantId);
        LambdaQueryWrapper<TemplateFormModel> templateFormModelLambdaQueryWrapper = Wrappers.lambdaQuery();
        templateFormModelLambdaQueryWrapper.eq(TemplateFormModel::getTenantId, topFloorTenantId);
        templateFormModelLambdaQueryWrapper.eq(TemplateFormModel::getCompanyId, modelCompanyId);
        templateFormModelLambdaQueryWrapper.eq(TemplateFormModel::getModelTable, cFormModelTable);
        List<TemplateFormModel> templateFormModels = templateFormModelMapper.selectListNoAdd(templateFormModelLambdaQueryWrapper);
        List<Long> templateIds = templateFormModels.stream().map(i -> i.getTemplateId()).collect(Collectors.toList());
        if (templateIds.isEmpty()) {
            return new HashSet<>();
        }
        //查找模型分组表
        LambdaQueryWrapper<TemplateGroup> templateGroupLambdaQueryWrapper = Wrappers.lambdaQuery();
        templateGroupLambdaQueryWrapper.in(TemplateGroup::getTemplateId, templateIds);
        List<TemplateGroup> templateGroups = templateGroupMapper.selectListNoAdd(templateGroupLambdaQueryWrapper);
        List<Long> groupIds = templateGroups.stream().map(i -> i.getGroupId()).collect(Collectors.toList());
        if (groupIds.isEmpty()) {
            return new HashSet<>();
        }
        //查找分组表
        LambdaQueryWrapper<GroupPopulation> groupPopulationLambdaQueryWrapper = Wrappers.lambdaQuery();
        groupPopulationLambdaQueryWrapper.select(GroupPopulation::getTemplateBusinessId, GroupPopulation::getTemplateBusinessName, GroupPopulation::getUnitName, GroupPopulation::getUnitId);
        groupPopulationLambdaQueryWrapper.in(GroupPopulation::getGroupId, groupIds);
        List<GroupPopulation> groupUser = groupPopulationMapper.selectListNoAdd(groupPopulationLambdaQueryWrapper);
        if (groupUser.isEmpty()) {
            return new HashSet<>();
        }
        List<BizUserScoreDTO> bizUserScoreDTOList = ListCopyUtil.copy(groupUser, BizUserScoreDTO.class);
        Set<BizUserScoreDTO> bizUserScoreDTOS = new HashSet<>(bizUserScoreDTOList);
        return bizUserScoreDTOS;
    }

}
