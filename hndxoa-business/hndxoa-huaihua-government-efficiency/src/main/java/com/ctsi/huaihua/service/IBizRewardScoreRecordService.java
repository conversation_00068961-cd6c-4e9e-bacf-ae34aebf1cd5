package com.ctsi.huaihua.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.huaihua.entity.BizRewardScoreRecord;
import com.ctsi.huaihua.entity.dto.BizRewardScoreRecordDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 奖分记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
public interface IBizRewardScoreRecordService extends SysBaseServiceI<BizRewardScoreRecord> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizRewardScoreRecordDTO> queryListPage(BizRewardScoreRecordDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizRewardScoreRecordDTO> queryList(BizRewardScoreRecordDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizRewardScoreRecordDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizRewardScoreRecordDTO create(BizRewardScoreRecordDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizRewardScoreRecordDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizRewardScoreRecordId
     * @param code
     * @return
     */
    boolean existByBizRewardScoreRecordId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizRewardScoreRecordDTO> dataList);

    /**
     * 市直单位办公室专职人员分页查询加分记录(查询本单位)
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordMunicipal(BizRewardScoreRecordDTO entityDTO, BasePageForm page);


    /**
     * 市政府办督查室分页查询加分记录(查询本租户)
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordSupervision(BizRewardScoreRecordDTO entityDTO, BasePageForm page);

    /**
     * 市直单位个人分页查询加分记录(查询个人已通过的记录或已)
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordIndividual(BizRewardScoreRecordDTO entityDTO, BasePageForm page);


    /**
     * 加分审核与驳回
     * @param entityDTO
     * @return
     */
    Boolean approvedBizRewardScoreRecord(BizRewardScoreRecordDTO entityDTO);

    /**
     * 查询待审核奖分记录数量(查本租户)
     * @return
     */
    Integer queryReviewedRewardScoreNum();

    /**
     * 个人查询单条数据并修改为已阅状态
     * @param id
     * @return
     */
    BizRewardScoreRecordDTO getAndUpdateStatus(Long id);

    /**
     * 市直单位办公室专职人员分页查询加分记录(安卓)
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordMunicipalByAndroid(BizRewardScoreRecordDTO entityDTO, BasePageForm page);

}
