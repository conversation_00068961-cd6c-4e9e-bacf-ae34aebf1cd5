package com.ctsi.huaihua.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.entity.BizScoreDeadlineConfig;
import com.ctsi.huaihua.entity.dto.BizScoreDeadlineConfigDTO;
import com.ctsi.huaihua.mapper.BizScoreDeadlineConfigMapper;
import com.ctsi.huaihua.service.IBizScoreDeadlineConfigService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 日常工作打分截止时间设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Slf4j
@Service
public class BizScoreDeadlineConfigServiceImpl extends SysBaseServiceImpl<BizScoreDeadlineConfigMapper, BizScoreDeadlineConfig> implements IBizScoreDeadlineConfigService {

    @Autowired
    private BizScoreDeadlineConfigMapper bizScoreDeadlineConfigMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizScoreDeadlineConfigDTO> queryListPage(BizScoreDeadlineConfigDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizScoreDeadlineConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizScoreDeadlineConfig::getTenantId, SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        IPage<BizScoreDeadlineConfig> pageData = bizScoreDeadlineConfigMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizScoreDeadlineConfigDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizScoreDeadlineConfigDTO.class));

        return new PageResult<BizScoreDeadlineConfigDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizScoreDeadlineConfigDTO> queryList(BizScoreDeadlineConfigDTO entityDTO) {
        LambdaQueryWrapper<BizScoreDeadlineConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizScoreDeadlineConfig::getTenantId, SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        List<BizScoreDeadlineConfig> listData = bizScoreDeadlineConfigMapper.selectListNoAdd(queryWrapper);
        List<BizScoreDeadlineConfigDTO> BizScoreDeadlineConfigDTOList = ListCopyUtil.copy(listData, BizScoreDeadlineConfigDTO.class);
        return BizScoreDeadlineConfigDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizScoreDeadlineConfigDTO findOne(Long id) {
        BizScoreDeadlineConfig bizScoreDeadlineConfig = bizScoreDeadlineConfigMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizScoreDeadlineConfig, BizScoreDeadlineConfigDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizScoreDeadlineConfigDTO create(BizScoreDeadlineConfigDTO entityDTO) {
        LambdaQueryWrapper<BizScoreDeadlineConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizScoreDeadlineConfig::getTenantId, SecurityUtils.getCurrentCscpUserDetail().getTenantId());
        BizScoreDeadlineConfig oneEntity = bizScoreDeadlineConfigMapper.selectOneOnlyAddTenantId(queryWrapper);
        if (Objects.nonNull(oneEntity)) {
            throw new BusinessException("已添加日常工作打分截止时间");
        }

        BizScoreDeadlineConfig bizScoreDeadlineConfig = BeanConvertUtils.copyProperties(entityDTO, BizScoreDeadlineConfig.class);
        bizScoreDeadlineConfig.setUpdateName(SecurityUtils.getCurrentRealName());
        bizScoreDeadlineConfig.setUpdateTime(LocalDateTime.now());
        save(bizScoreDeadlineConfig);
        return BeanConvertUtils.copyProperties(bizScoreDeadlineConfig, BizScoreDeadlineConfigDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizScoreDeadlineConfigDTO entity) {
        BizScoreDeadlineConfig bizScoreDeadlineConfig = BeanConvertUtils.copyProperties(entity, BizScoreDeadlineConfig.class);
        bizScoreDeadlineConfig.setUpdateName(SecurityUtils.getCurrentRealName());
        bizScoreDeadlineConfig.setUpdateTime(LocalDateTime.now());
        return bizScoreDeadlineConfigMapper.updateById(bizScoreDeadlineConfig);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizScoreDeadlineConfigMapper.deleteById(id);
    }

    /**
     * 是否允许打分
     *
     * @param scoreMonth 打分月份
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableMark(Integer scoreMonth) {
        LocalDate now = LocalDate.now();
        // 上月月份
        Integer lastMonth = now.minusMonths(1).getMonthValue();
        if (!scoreMonth.equals(lastMonth)) {
            return false;
        }
        List<BizScoreDeadlineConfigDTO> bizScoreDeadlineConfigDTOList = queryList(null);
        if (CollectionUtil.isEmpty(bizScoreDeadlineConfigDTOList)) {
            //未设置日常工作打分截止时间
            return false;
        }
        // 打分截止日
        Integer deadLineDay = bizScoreDeadlineConfigDTOList.get(0).getDay();
        // 本月最后一日
        Integer monthLastDay = DateUtil.endOfMonth(DateTime.now()).dayOfMonth();
        // 本月最后一天在打分截止日前或相等，则截止日为本月最后一天
        // 如截止日为31日，但2月份只有28天，则截止日为28日。
        if (monthLastDay.compareTo(deadLineDay) < 1) {
            deadLineDay = monthLastDay;
        }

        // 当前打分日
        Integer markDate = now.getDayOfMonth();
        // 当前打分日markDate在系统设置的截止日deadLineDay之前，compareTo比较值等于-1。
        // 当前打分日markDate与系统设置的截止日deadLineDay相同，compareTo比较值等于0。
        return markDate.compareTo(deadLineDay) < 1;
    }


    /**
     * 验证是否存在
     *
     * @param BizScoreDeadlineConfigId
     * @return
     */
    @Override
    public boolean existByBizScoreDeadlineConfigId(Long BizScoreDeadlineConfigId) {
        if (BizScoreDeadlineConfigId != null) {
            LambdaQueryWrapper<BizScoreDeadlineConfig> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizScoreDeadlineConfig::getId, BizScoreDeadlineConfigId);
            List<BizScoreDeadlineConfig> result = bizScoreDeadlineConfigMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizScoreDeadlineConfigDTO> dataList) {
        List<BizScoreDeadlineConfig> result = ListCopyUtil.copy(dataList, BizScoreDeadlineConfig.class);
        return saveBatch(result);
    }


}
