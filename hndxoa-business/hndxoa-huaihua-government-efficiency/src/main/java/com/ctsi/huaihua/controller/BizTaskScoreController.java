package com.ctsi.huaihua.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.huaihua.entity.BizTaskScore;
import com.ctsi.huaihua.entity.dto.BizTaskScoreDTO;
import com.ctsi.huaihua.service.IBizTaskScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskScore")
@Api(value = "任务得分", tags = "任务得分接口")
public class BizTaskScoreController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskScore";

    @Autowired
    private IBizTaskScoreService bizTaskScoreService;



    /**
     *  新增任务得分批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskScore.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务得分批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScore.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskScoreDTO> bizTaskScoreList) {
       Boolean  result = bizTaskScoreService.insertBatch(bizTaskScoreList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskScore.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务得分数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScore.add')")
    public ResultVO<BizTaskScoreDTO> create(@RequestBody BizTaskScoreDTO bizTaskScoreDTO)  {
        BizTaskScoreDTO result = bizTaskScoreService.create(bizTaskScoreDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskScore.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新任务得分数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScore.update')")
    public ResultVO update(@RequestBody BizTaskScoreDTO bizTaskScoreDTO) {
	    Assert.notNull(bizTaskScoreDTO.getId(), "general.IdNotNull");
        int count = bizTaskScoreService.update(bizTaskScoreDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除任务得分数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskScore.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskScore.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskScoreService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/getTaskScore/{bizTaskDecompose}")
    @ApiOperation(value = "根据子任务的id获取任务的分值情况", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<BizTaskScoreDTO> getTaskScore(@PathVariable Long bizTaskDecompose) {
        BizTaskScoreDTO bizTaskScoreDTO = bizTaskScoreService.getTaskScoreByDecomposeId(bizTaskDecompose);
        return ResultVO.success(bizTaskScoreDTO);
    }


    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskScoreDTO bizTaskScoreDTO = bizTaskScoreService.findOne(id);
        return ResultVO.success(bizTaskScoreDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskScorePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskScoreDTO>> queryBizTaskScorePage(BizTaskScoreDTO bizTaskScoreDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskScoreService.queryListPage(bizTaskScoreDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskScore")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskScoreDTO>> queryBizTaskScore(BizTaskScoreDTO bizTaskScoreDTO) {
       List<BizTaskScoreDTO> list = bizTaskScoreService.queryList(bizTaskScoreDTO);
       return ResultVO.success(new ResResult<BizTaskScoreDTO>(list));
   }

}
