package com.ctsi.huaihua.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.huaihua.entity.BizTaskDelay;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 任务延期表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface BizTaskDelayMapper extends MybatisBaseMapper<BizTaskDelay> {

    /**
     * 查询根据延期申请的信息查询主表信息(无单位限制)
     * 查询延期列表专用(慎用)
     * @param iPage
     * @param bizTaskSupervisionDTO
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<BizTaskSupervisionDTO> queryPageTaskSupervisionAndDelayInfo(IPage iPage, @Param("dto") BizTaskSupervisionDTO bizTaskSupervisionDTO);

}
