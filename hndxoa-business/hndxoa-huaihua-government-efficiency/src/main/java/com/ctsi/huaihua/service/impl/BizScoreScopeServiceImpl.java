package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.systenant.entity.dto.TenantUserListDTO;
import com.ctsi.hndx.systenant.service.ITSysTenantService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.entity.BizScoreScope;
import com.ctsi.huaihua.entity.BizScoreUserConfig;
import com.ctsi.huaihua.entity.dto.BizBaseScoreLeaderScopeDTO;
import com.ctsi.huaihua.entity.dto.BizScoreScopeDTO;
import com.ctsi.huaihua.mapper.BizScoreScopeMapper;
import com.ctsi.huaihua.service.IBizScoreLeaderService;
import com.ctsi.huaihua.service.IBizScoreScopeService;
import com.ctsi.huaihua.service.IBizScoreUserConfigService;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgAndUserDto;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 领导委托信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Slf4j
@Service
public class BizScoreScopeServiceImpl extends SysBaseServiceImpl<BizScoreScopeMapper, BizScoreScope> implements IBizScoreScopeService {

    @Autowired
    private IBizScoreLeaderService bizScoreLeaderService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private IBizScoreUserConfigService bizScoreUserConfigService;

    @Autowired
    private BizScoreScopeMapper bizScoreScopeMapper;

    @Autowired
    private ITSysTenantService itSysTenantService;


    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizScoreScopeDTO> queryListPage(BizScoreScopeDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizScoreScope> queryWrapper = new LambdaQueryWrapper();

        IPage<BizScoreScope> pageData = bizScoreScopeMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizScoreScopeDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizScoreScopeDTO.class));

        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizScoreScopeDTO> queryList(BizScoreScopeDTO entityDTO) {
        LambdaQueryWrapper<BizScoreScope> queryWrapper = new LambdaQueryWrapper();
        List<BizScoreScope> listData = bizScoreScopeMapper.selectList(queryWrapper);
        List<BizScoreScopeDTO> BizScoreScopeDTOList = ListCopyUtil.copy(listData, BizScoreScopeDTO.class);
        return BizScoreScopeDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizScoreScopeDTO findOne(Long id) {
        BizScoreScope bizScoreScope = bizScoreScopeMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizScoreScope, BizScoreScopeDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizScoreScopeDTO create(BizScoreScopeDTO entityDTO) {
        if (Objects.isNull(entityDTO.getLeaderId())) {
            throw new BusinessException("领导ID不允许为空");
        }
        // 判断该领导是否存在
        LambdaQueryWrapper<BizScoreScope> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizScoreScope::getLeaderId, entityDTO.getLeaderId());
        Integer count = bizScoreScopeMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new BusinessException("已存在该领导的打分范围");
        }
        return this.insertScoreScope(entityDTO);
    }

    /**
     * 修改
     *
     * @param entityDTO the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizScoreScopeDTO entityDTO) {
        entityDTO.setLastUpdateBy(SecurityUtils.getCurrentUserId());
        entityDTO.setLastUpdateName(SecurityUtils.getCurrentRealName());
        entityDTO.setLastUpdateTime(LocalDateTime.now());
        BizScoreScope bizScoreScope = BeanConvertUtils.copyProperties(entityDTO, BizScoreScope.class);
        return bizScoreScopeMapper.updateById(bizScoreScope);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizScoreScopeMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizScoreScopeId
     * @return
     */
    @Override
    public boolean existByBizScoreScopeId(Long BizScoreScopeId) {
        if (BizScoreScopeId != null) {
            LambdaQueryWrapper<BizScoreScope> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizScoreScope::getId, BizScoreScopeId);
            List<BizScoreScope> result = bizScoreScopeMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizScoreScopeDTO> dataList) {
        List<BizScoreScope> result = ListCopyUtil.copy(dataList, BizScoreScope.class);
        return saveBatch(result);
    }


    /**
     * 编辑日常打分范围
     *
     * @param bizScoreScopeDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateScoreScope(BizScoreScopeDTO bizScoreScopeDTO) {
        if (Objects.isNull(bizScoreScopeDTO)) {
            throw new BusinessException("日常打分范围入参不允许为空");
        }
        if (Objects.isNull(bizScoreScopeDTO.getLeaderId())) {
            throw new BusinessException("日常打分范围领导ID不允许为空");
        }

        // 先删除，再新增
        this.deleteScoreScope(bizScoreScopeDTO);
        this.insertScoreScope(bizScoreScopeDTO);
        return true;
    }

    /**
     * 删除日常打分范围
     *
     * @param bizScoreScopeDTO
     * @return
     */
    @Override
    public Boolean deleteScoreScope(BizScoreScopeDTO bizScoreScopeDTO) {
        if (Objects.isNull(bizScoreScopeDTO)) {
            throw new BusinessException("接口入参不允许为空");
        }
        if (Objects.isNull(bizScoreScopeDTO.getLeaderId())) {
            throw new BusinessException("删除日常打分范围领导的用户ID不允许为空");
        }
        LambdaQueryWrapper<BizScoreScope> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizScoreScope::getLeaderId, bizScoreScopeDTO.getLeaderId());
        bizScoreScopeMapper.delete(queryWrapper);
        return true;
    }

    /**
     * 分页查询领导日常打分列表
     *
     * @param bizScoreScopeDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizScoreScopeDTO> queryBizLeaderScoreScopePage(BizScoreScopeDTO bizScoreScopeDTO, BasePageForm page) {
        LambdaQueryWrapper<BizScoreScope> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(bizScoreScopeDTO.getLeaderName())) {
            queryWrapper.like(BizScoreScope::getLeaderName, bizScoreScopeDTO.getLeaderName());
        }
        IPage<BizScoreScope> pageData = bizScoreScopeMapper.selectPage(PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);
        if (CollectionUtils.isEmpty(pageData.getRecords())) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }

        // 组装数据
        IPage<BizScoreScopeDTO> bizScoreScopeDTOIPage = pageData.convert(entity ->
                BeanConvertUtils.copyProperties(entity, BizScoreScopeDTO.class));
        bizScoreScopeDTOIPage.getRecords().stream().forEach(i -> {
            List<Long> markIdList = Arrays.asList(i.getMarkUserIds().split(",")).stream().map(idStr -> {
                return Long.parseLong(idStr);
            }).collect(Collectors.toList());
            List<CscpUserDTO> userDTOList = cscpUserService.listQueryUserDTO(markIdList, true);
            List<BizBaseScoreLeaderScopeDTO> bizBaseScoreLeaderScopeDTOS = userDTOList.stream().map(j -> {
                BizBaseScoreLeaderScopeDTO dto = new BizBaseScoreLeaderScopeDTO();
                dto.setUserId(j.getId());
                dto.setUserName(j.getRealName());
                return dto;
            }).collect(Collectors.toList());
            i.setBizBaseScoreLeaderScopeDTOList(bizBaseScoreLeaderScopeDTOS);
        });

        return new PageResult<>(bizScoreScopeDTOIPage.getRecords(), pageData.getTotal(), pageData.getCurrent());
    }

    /**
     * 查询该领导日常打分用户列表
     *
     * @param bizScoreScopeDTO
     * @return
     */
    @Override
    public List<CscpUserDTO> queryBizLeaderScoreScopeList(BizScoreScopeDTO bizScoreScopeDTO) {

        LambdaQueryWrapper<BizScoreScope> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BizScoreScope::getLeaderId, bizScoreScopeDTO.getLeaderId());
        List<BizScoreScope> bizScoreScopes = bizScoreScopeMapper.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isEmpty(bizScoreScopes)) {
            return new ArrayList<>();
        }
        if (bizScoreScopes.size() > 1) {
            throw new BusinessException("该用户存在多条打分范围记录");
        }

        String markUserIdStr = bizScoreScopes.get(0).getMarkUserIds();
        if (StringUtils.isBlank(markUserIdStr)) {
            return new ArrayList<>();
        }
        List<Long> markIdList = Arrays.asList(markUserIdStr.split(",")).stream().map(idStr -> {
            return Long.parseLong(idStr);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(markIdList)) {
            return new ArrayList<>();
        }
        return cscpUserService.listQueryUserDTO(markIdList, true);
    }

    /**
     * 插入打分范围记录
     *
     * @param entityDTO
     * @return
     */
    private BizScoreScopeDTO insertScoreScope(BizScoreScopeDTO entityDTO) {
        entityDTO.setLastUpdateBy(SecurityUtils.getCurrentUserId());
        entityDTO.setLastUpdateName(SecurityUtils.getCurrentRealName());
        entityDTO.setLastUpdateTime(LocalDateTime.now());
        BizScoreScope bizScoreScope = BeanConvertUtils.copyProperties(entityDTO, BizScoreScope.class);
        save(bizScoreScope);
        return BeanConvertUtils.copyProperties(bizScoreScope, BizScoreScopeDTO.class);
    }


    /**
     * 分页查询指定机构下的所有用户, 排除免考核用户
     *
     * @param id
     * @param realName
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<CscpUserDTO> pageQueryUserNotInList(Long id, String realName, BasePageForm basePageForm) {
        // 找出不需要考核的用户ID
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizScoreUserConfig::getHasChecked, 0);
        List<Long> idList = bizScoreUserConfigService.selectListNoAdd(queryWrapper).stream().map(i -> {
            return i.getUserId();
        }).collect(Collectors.toList());

        // 排除领导选人范围已经选过的人
        List<Long> leaderScopeUserIdList = this.queryAllLeaderScopeList();
        if (CollectionUtils.isNotEmpty(leaderScopeUserIdList)) {
            idList.addAll(leaderScopeUserIdList);
        }

        // 查询用户信息
        return cscpOrgService.pageQueryUserList(id, realName, idList, basePageForm);
    }

    /**
     * 不分页查询本单位下的用户
     *
     * @param parentId
     * @param realName
     * @return
     */
    @Override
    public CscpOrgAndUserDto queryCompanyUserNotInList(Long parentId, String realName) {
        // 找出不需要考核的用户ID
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizScoreUserConfig::getHasChecked, 0);
        List<Long> idList = bizScoreUserConfigService.selectListNoAdd(queryWrapper).stream().map(i -> {
            return i.getUserId();
        }).collect(Collectors.toList());

        // 排除领导选人范围已经选过的人
        List<Long> leaderScopeUserIdList = this.queryAllLeaderScopeList();
        if (CollectionUtils.isNotEmpty(leaderScopeUserIdList)) {
            idList.addAll(leaderScopeUserIdList);
        }

        CscpOrgAndUserDto cscpOrgAndUserDto = cscpOrgService.selectOrgAndQueryRealNameList(parentId, realName,null);

        if (!Objects.isNull(cscpOrgAndUserDto.getCscpUserDTOList())) {
            List<CscpUserDTO> users = cscpOrgAndUserDto.getCscpUserDTOList().stream().filter(i -> !idList.contains(i.getId())).collect(Collectors.toList());
            cscpOrgAndUserDto.setCscpUserDTOList(users);
        }

        return cscpOrgAndUserDto;
    }

    /**
     * 搜索获取上下级租户下的用户
     *
     * @param realName
     * @return
     */
    @Override
    public TenantUserListDTO queryTenantUserNotInList(String realName) {
        // 找出不需要考核的用户ID
        LambdaQueryWrapper<BizScoreUserConfig> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizScoreUserConfig::getHasChecked, 0);
        List<Long> idList = bizScoreUserConfigService.selectListNoAdd(queryWrapper).stream().map(i -> {
            return i.getUserId();
        }).collect(Collectors.toList());

        // 排除领导选人范围已经选过的人
        List<Long> leaderScopeUserIdList = this.queryAllLeaderScopeList();
        if (CollectionUtils.isNotEmpty(leaderScopeUserIdList)) {
            idList.addAll(leaderScopeUserIdList);
        }

        TenantUserListDTO tenantUserList = itSysTenantService.getTenantUserList(realName);

        if (!Objects.isNull(tenantUserList.getCscpUsers())) {
            List<CscpUserDTO> users = tenantUserList.getCscpUsers().stream().filter(i -> !idList.contains(i.getId())).collect(Collectors.toList());
            tenantUserList.setCscpUsers(users);
        }

        return tenantUserList;
    }

    /**
     * 查询所有领导选人的选人范围
     * @return
     */
    @Override
    public List<Long> queryAllLeaderScopeList() {
        LambdaQueryWrapper<BizScoreScope> queryWrapper = Wrappers.lambdaQuery();
        List<BizScoreScope> bizScoreScopeList = bizScoreScopeMapper.selectListNoAdd(queryWrapper);
        if (org.springframework.util.CollectionUtils.isEmpty(bizScoreScopeList)) {
            return new ArrayList<>();
        }
        List<Long> idList = new ArrayList<>();
        for (BizScoreScope bizScoreScope : bizScoreScopeList) {
            if (StringUtils.isBlank(bizScoreScope.getMarkUserIds())) {
                continue;
            }
            Arrays.asList(bizScoreScope.getMarkUserIds().split(",")).forEach(idStr -> {
                Long id = Long.parseLong(idStr);
                if (!idList.contains(id)) {
                    idList.add(id);
                }
            });
        }
        return idList;
    }

}
