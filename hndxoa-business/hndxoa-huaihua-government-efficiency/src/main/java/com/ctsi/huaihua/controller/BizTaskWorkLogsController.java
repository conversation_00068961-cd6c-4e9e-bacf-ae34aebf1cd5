package com.ctsi.huaihua.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsAnnexDTO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsDTO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsPageDTO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsSourceDTO;
import com.ctsi.huaihua.service.IBizTaskWorkLogsService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.temporal.ChronoUnit;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskWorkLogs")
@Api(value = "任务工作日志", tags = "任务工作日志接口")
public class BizTaskWorkLogsController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskWorkLogs";

    @Autowired
    private IBizTaskWorkLogsService bizTaskWorkLogsService;


    /**
     * 新增工作日志批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskWorkLogs.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增工作日志批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogs.add')")
    public ResultVO createBatch(@Validated @RequestBody List<BizTaskWorkLogsDTO> bizTaskWorkLogsList) {
        Boolean result = bizTaskWorkLogsService.insertBatch(bizTaskWorkLogsList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskWorkLogs.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增工作日志数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogs.add')")
    public ResultVO<BizTaskWorkLogsAnnexDTO> create(@Validated @RequestBody BizTaskWorkLogsDTO bizTaskWorkLogsDTO) {
        if (bizTaskWorkLogsDTO.getStartTime().isAfter(bizTaskWorkLogsDTO.getEndTime())) {
            throw new BusinessException("日期范围设置错误");
        }
        // 判断设置的时长是否超出日期范围内的时长
        long useHour = ChronoUnit.DAYS.between(bizTaskWorkLogsDTO.getStartTime(), bizTaskWorkLogsDTO.getEndTime());
        if ((float) (useHour + 1) * 24 < bizTaskWorkLogsDTO.getCostTime()) {
            throw new BusinessException("时长超出日期范围");
        }
        BizTaskWorkLogsAnnexDTO result = bizTaskWorkLogsService.create(bizTaskWorkLogsDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskWorkLogs.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新工作日志数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogs.update')")
    public ResultVO update(@Validated @RequestBody BizTaskWorkLogsDTO bizTaskWorkLogsDTO) {
        Assert.notNull(bizTaskWorkLogsDTO.getId(), "general.IdNotNull");
        int count = bizTaskWorkLogsService.update(bizTaskWorkLogsDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除工作日志数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskWorkLogs.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogs.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskWorkLogsService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除附件专用.
     */
    @DeleteMapping("/deleteAnnex/{annexId}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除附件专用")
    @ApiOperation(value = "删除附件专用(权限code码为：cscp.bizTaskWorkLogs.deleteAnnex)", notes = "传入参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "annexId", value = "表单")
    })
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogs.delete')")
    public ResultVO deleteAnnex(@PathVariable Long annexId) {
        int count = bizTaskWorkLogsService.deleteAnnex(annexId);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskWorkLogsAnnexDTO bizTaskWorkLogsAnnexDTO = bizTaskWorkLogsService.findOne(id);
        return ResultVO.success(bizTaskWorkLogsAnnexDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizTaskWorkLogsPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskWorkLogsDTO>> queryBizTaskWorkLogsPage(BizTaskWorkLogsPageDTO bizTaskWorkLogsDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskWorkLogsService.queryListPage(bizTaskWorkLogsDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryBizTaskWorkLogs")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizTaskWorkLogsDTO>> queryBizTaskWorkLogs(BizTaskWorkLogsPageDTO bizTaskWorkLogsDTO) {
        List<BizTaskWorkLogsDTO> list = bizTaskWorkLogsService.queryList(bizTaskWorkLogsDTO);
        return ResultVO.success(new ResResult<BizTaskWorkLogsDTO>(list));
    }

    /**
     * 查询承办中已接收任务数据.不分页
     */
    @GetMapping("/getBizTasksReceivedTodoList")
    @ApiOperation(value = "查询所有待办和已接收任务", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<List<BizTaskWorkLogsSourceDTO>> getBizTasksReceivedTodoList(Long id, Long companyId) {
        List<BizTaskWorkLogsSourceDTO> list = bizTaskWorkLogsService.getBizTasksReceivedTodoList(id,companyId);
        return ResultVO.success(list);
    }

}
