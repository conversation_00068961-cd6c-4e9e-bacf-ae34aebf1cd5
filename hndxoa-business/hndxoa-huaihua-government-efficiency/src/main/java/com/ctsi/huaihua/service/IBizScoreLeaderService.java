package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizScoreLeaderDTO;
import com.ctsi.huaihua.entity.BizScoreLeader;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 日常打分领导管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface IBizScoreLeaderService extends SysBaseServiceI<BizScoreLeader> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizScoreLeaderDTO> queryListPage(BizScoreLeaderDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizScoreLeaderDTO> queryList(BizScoreLeaderDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizScoreLeaderDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizScoreLeaderDTO create(BizScoreLeaderDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizScoreLeaderDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizScoreLeaderId
     * @param code
     * @return
     */
    boolean existByBizScoreLeaderId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizScoreLeaderDTO> dataList);


}
