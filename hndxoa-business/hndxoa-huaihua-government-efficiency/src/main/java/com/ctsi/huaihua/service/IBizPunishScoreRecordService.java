package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizPunishScoreRecordDTO;
import com.ctsi.huaihua.entity.BizPunishScoreRecord;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 奖分记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
public interface IBizPunishScoreRecordService extends SysBaseServiceI<BizPunishScoreRecord> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizPunishScoreRecordDTO> queryListPage(BizPunishScoreRecordDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizPunishScoreRecordDTO> queryList(BizPunishScoreRecordDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizPunishScoreRecordDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizPunishScoreRecordDTO create(BizPunishScoreRecordDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizPunishScoreRecordDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizPunishScoreRecordId
     * @param code
     * @return
     */
    boolean existByBizPunishScoreRecordId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizPunishScoreRecordDTO> dataList);

    /**
     * 市政府办分页展示
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizPunishScoreRecordDTO> queryBizMunicipalUnitPage(BizPunishScoreRecordDTO entityDTO, BasePageForm page);

    /**
     * 查询未审核惩分申请数量
     * @return
     */
    Integer queryPunishScoreRecordNeedReviewAmount();

    /**
     * 审核通过与驳回
     * @param bizPunishScoreRecord
     * @return
     */
    Boolean approvedBizPunishScoreRecord(BizPunishScoreRecordDTO bizPunishScoreRecord);

    /**
     * 个人列表查询
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizPunishScoreRecordDTO> queryPunishScoreRecordPage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm);

    /**
     * 督查室列表查询
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizPunishScoreRecordDTO> queryBizSuperviseOfficePage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm);

    /**
     * 市直单位列表查询
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizPunishScoreRecordDTO> queryBizStraightUnitPage(BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm);

    /**
     * 查看减分详情
     * @param id
     * @param type  0:非个人查看详情  1:个人查看详情
     * @return
     */
    BizPunishScoreRecordDTO getPunishScoreRecordDetail(Long id, Integer type);

    /**
     * 更新小红点
     * @param id
     * @param type
     * @return
     */
    Boolean updateRed(Long id, Integer type);
}
