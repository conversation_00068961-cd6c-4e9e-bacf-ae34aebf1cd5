package com.ctsi.huaihua.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.BizTaskDelay;
import com.ctsi.huaihua.entity.dto.BizTaskDecomposeDTO;
import com.ctsi.huaihua.entity.dto.BizTaskDelayDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import com.ctsi.huaihua.mapper.BizTaskDecomposeMapper;
import com.ctsi.huaihua.mapper.BizTaskDelayMapper;
import com.ctsi.huaihua.service.IBizTaskDecomposeService;
import com.ctsi.huaihua.service.IBizTaskDelayService;
import com.ctsi.huaihua.service.IBizTaskSupervisionService;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务延期表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Slf4j
@Service
public class BizTaskDelayServiceImpl extends SysBaseServiceImpl<BizTaskDelayMapper, BizTaskDelay> implements IBizTaskDelayService {

    @Autowired
    private BizTaskDelayMapper bizTaskDelayMapper;

    @Autowired
    private IBizTaskDecomposeService iBizTaskDecomposeService;

    @Autowired
    private BizTaskDecomposeMapper bizTaskDecomposeMapper;

    @Autowired
    private IBizTaskSupervisionService iBizTaskSupervisionService;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskDelayDTO> queryListPage(BizTaskDelayDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskDelay> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskDelay> pageData = bizTaskDelayMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskDelayDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizTaskDelayDTO.class));

        return new PageResult<BizTaskDelayDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskDelayDTO> queryList(BizTaskDelayDTO entityDTO) {
        LambdaQueryWrapper<BizTaskDelay> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(!Objects.isNull(entityDTO.getBizTaskDecompose()),BizTaskDelay::getBizTaskDecompose,entityDTO.getBizTaskDecompose());
        queryWrapper.orderByDesc(BizTaskDelay::getCreateTime);
        List<BizTaskDelay> listData = bizTaskDelayMapper.selectList(queryWrapper);
        List<BizTaskDelayDTO> BizTaskDelayDTOList = ListCopyUtil.copy(listData, BizTaskDelayDTO.class);
        return BizTaskDelayDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskDelayDTO findOne(Long id) {
        BizTaskDelay bizTaskDelay =  bizTaskDelayMapper.selectById(id);
        BizTaskDelayDTO bizTaskDelayDTO = BeanConvertUtils.copyProperties(bizTaskDelay, BizTaskDelayDTO.class);
        return bizTaskDelayDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskDelayDTO create(BizTaskDelayDTO entityDTO) {
        // 查询分解表数据
        BizTaskDecompose bizTaskDecomposeDTO = iBizTaskDecomposeService.getById(entityDTO.getBizTaskDecompose());
        if (Objects.isNull(bizTaskDecomposeDTO)) {
            throw new BusinessException("未找到任务分解表数据");
        }

        // 新增延期申请必须要在接收之后办结之前
        if (!SysConstant.BIZ_IS_SIGN.equals(bizTaskDecomposeDTO.getHasSign())
                || SysConstant.BIZ_IS_FINISHED.equals(bizTaskDecomposeDTO.getHasFinish())) {
            throw new BusinessException("延期申请必须要在任务接收之后且在办结之前申请");
        }

        // 查询该分解表之前有没有延期申请
        LambdaQueryWrapper<BizTaskDelay> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskDelay::getBizTaskDecompose, entityDTO.getBizTaskDecompose());
        List<BizTaskDelay> listData = bizTaskDelayMapper.selectList(queryWrapper);
        // 如果该分解表之前有延期申请，需要判断之前的申请有没有被审核
        if (CollectionUtils.isNotEmpty(listData)) {
            for (BizTaskDelay bizTaskDelay : listData) {
                if (SysConstant.BIZ_NOT_FINISHED.equals(bizTaskDelay.getHasReviewer())) {
                    throw new BusinessException("该任务的分解表已存在未被审核的延期申请");
                }
            }
        }

        // 查询该主任务信息
        if (Objects.isNull(bizTaskDecomposeDTO.getSubTblFk())) {
            throw new BusinessException("未找到任务的主任务信息数据");
        }
        BizTaskSupervisionDTO bizTaskSupervisionDTO = iBizTaskSupervisionService.findOne(bizTaskDecomposeDTO.getSubTblFk());
        if (Objects.isNull(bizTaskSupervisionDTO)) {
            throw new BusinessException("未找到任务的主任务信息数据");
        }

        // 如果没有牵头单位，则交给分解任务的创建人审核
//        if (Objects.isNull(bizTaskSupervisionDTO.getLeadPeopleId())) {
//            throw new BusinessException("未找到该任务主任务的牵头单位信息");
//        }

        // 判断该分解任务的责任人是不是牵头单位
        boolean isLeaderUnit = Objects.equals(bizTaskSupervisionDTO.getLeadPeopleId(), bizTaskDecomposeDTO.getDutyPeopleId());

        // 如果是牵头单位，则延期申请交给创建人审核，否则给分解任务的牵头单位审核
        if (Objects.isNull(bizTaskSupervisionDTO.getLeadPeopleId()) || isLeaderUnit) {
            entityDTO.setReviewerId(bizTaskDecomposeDTO.getCreateBy().toString());
            entityDTO.setReviewerDepartmentId(bizTaskDecomposeDTO.getDepartmentId());
            entityDTO.setReviewerCompanyId(bizTaskDecomposeDTO.getCompanyId());
        } else {
            String concludePeopleId = bizTaskSupervisionDTO.getLeadPeopleId().toString();
            //获取牵头单位人单位ID
            LambdaQueryWrapper<CscpUserOrg> queryWrapper2 = new LambdaQueryWrapper();
            queryWrapper2.eq(CscpUserOrg::getUserId, bizTaskSupervisionDTO.getLeadPeopleId());
            CscpUserOrg cscpUserOrg = cscpUserOrgRepository.selectOneNoAdd(queryWrapper2);
            //获取牵头单位专职人员ID
            List<CscpUserDTO> cscpUserDTOS = cscpUserRepository.selectCodeUser(cscpUserOrg.getCompanyId(), TaskStatus.TASK_SIGN_CODE);
            if (CollectionUtil.isNotEmpty(cscpUserDTOS)) {
                concludePeopleId = concludePeopleId +","+ cscpUserDTOS.get(0).getId().toString();
            }

            entityDTO.setReviewerId(concludePeopleId);
            List<CscpUserOrgDTO> userOrgDTOList = cscpUserOrgService.queryUserOrgByUserId(bizTaskSupervisionDTO.getLeadPeopleId());
            if (CollectionUtils.isNotEmpty(userOrgDTOList)) {
                entityDTO.setReviewerDepartmentId(userOrgDTOList.get(0).getOrgId());
                entityDTO.setReviewerCompanyId(userOrgDTOList.get(0).getCompanyId());
            }
        }

        // 新增延期申请记录
        entityDTO.setDelayBeforeEndDate(bizTaskDecomposeDTO.getEndDate());
        entityDTO.setDelayBeforeContent(bizTaskDecomposeDTO.getContent());
        entityDTO.setHasReviewer(0);
        entityDTO.setHasRead(0);
        BizTaskDelay bizTaskDelay = BeanConvertUtils.copyProperties(entityDTO, BizTaskDelay.class);
        save(bizTaskDelay);

        // 修改分解表延期申请状态
        LambdaUpdateWrapper<BizTaskDecompose> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BizTaskDecompose::getId, entityDTO.getBizTaskDecompose())
                .set(BizTaskDecompose::getTaskDelayStatus, 1);
        bizTaskDecomposeMapper.updateTenantId(null, updateWrapper);
        return BeanConvertUtils.copyProperties(bizTaskDelay, BizTaskDelayDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskDelayDTO entity) {
        BizTaskDelay bizTaskDelay = BeanConvertUtils.copyProperties(entity,BizTaskDelay.class);
        return bizTaskDelayMapper.updateById(bizTaskDelay);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 查询该条任务的延期申请
        BizTaskDelayDTO bizTaskDelayDTO = this.findOne(id);
        if (Objects.isNull(bizTaskDelayDTO)) {
            throw new BusinessException("未到找该条延期申请记录");
        }

        // 修改反馈表中的延期申请状态
        LambdaUpdateWrapper<BizTaskDecompose> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(BizTaskDecompose::getId, bizTaskDelayDTO.getBizTaskDecompose()).set(BizTaskDecompose::getTaskDelayStatus, 0);
        bizTaskDecomposeMapper.updateTenantId(null, lambdaUpdateWrapper);

        return bizTaskDelayMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskDelayId
     * @return
     */
    @Override
    public boolean existByBizTaskDelayId(Long BizTaskDelayId) {
        if (BizTaskDelayId != null) {
            LambdaQueryWrapper<BizTaskDelay> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskDelay::getId, BizTaskDelayId);
            List<BizTaskDelay> result = bizTaskDelayMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskDelayDTO> dataList) {
        List<BizTaskDelay> result = ListCopyUtil.copy(dataList, BizTaskDelay.class);
        return saveBatch(result);
    }


    /**
     * 延期申请通过
     * @param bizTaskDelayDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean approvedBizTaskDelay(BizTaskDelayDTO bizTaskDelayDTO) {

        // 查询延期申请
        BizTaskDelayDTO taskDelayDTO = this.findOne(bizTaskDelayDTO.getId());
        if (Objects.isNull(taskDelayDTO)) {
            throw new BusinessException("未找到该条任务的延期申请");
        }
        if (1 == taskDelayDTO.getHasReviewer() || 2 == taskDelayDTO.getHasReviewer()) {
            throw new BusinessException("该条延期申请记录已审核");
        }

        // 查询分解表数据
        BizTaskDecomposeDTO bizTaskDecomposeDTO = iBizTaskDecomposeService.findOne(
                taskDelayDTO.getBizTaskDecompose());
        if (Objects.isNull(bizTaskDecomposeDTO)) {
            throw new BusinessException("未找到任务分解表数据");
        }

        // 保存延期申请之前的内容和截止时间
        taskDelayDTO.setHasReviewer(1);
        taskDelayDTO.setAuditId(SecurityUtils.getCurrentUserId());
        taskDelayDTO.setAuditName(SecurityUtils.getCurrentRealName());
        taskDelayDTO.setAuditTime(LocalDateTime.now());
        taskDelayDTO.setDelayBeforeContent(bizTaskDecomposeDTO.getContent());
        taskDelayDTO.setDelayBeforeEndDate(bizTaskDecomposeDTO.getEndDate());

        // 修改分解表截止时间
        bizTaskDecomposeDTO.setEndDate(taskDelayDTO.getDelayEndDate());
        bizTaskDecomposeDTO.setTaskDelayStatus(2);
        if (Objects.nonNull(bizTaskDelayDTO.getManualDelayEndDate())) {
            taskDelayDTO.setHasReviewer(2);
            taskDelayDTO.setManualDelayEndDate(bizTaskDelayDTO.getManualDelayEndDate());
            bizTaskDecomposeDTO.setEndDate(bizTaskDelayDTO.getManualDelayEndDate());
            bizTaskDecomposeDTO.setTaskDelayStatus(3);
        }

        this.update(taskDelayDTO);
        iBizTaskDecomposeService.update(bizTaskDecomposeDTO);

        // 修改主任务截止时间
        BizTaskSupervisionDTO bizTaskSupervisionDTO = iBizTaskSupervisionService.findOne(taskDelayDTO.getBizTaskSupervisionId());
        LocalDate supDueTime = bizTaskSupervisionDTO.getDueTime();
        LocalDate decomposeEndTime = bizTaskDecomposeDTO.getEndDate();

        // 如果修改的子任务时间在主任务时间之后，则需要延期主任务时间
        if (decomposeEndTime.isAfter(supDueTime)) {
            bizTaskSupervisionDTO.setDueTime(decomposeEndTime);
            iBizTaskSupervisionService.update(bizTaskSupervisionDTO);
        }

        return true;
    }

    /**
     * 分页查询延期申请列表
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizTaskSupervisionDTO> queryPageDelayList(BizTaskSupervisionDTO entityDTO, BasePageForm page) {
        // 查询该用户需要审核的任务延期数据
        LambdaQueryWrapper<BizTaskDelay> queryWrapper = new LambdaQueryWrapper();
        if (Objects.nonNull(entityDTO.getDelayReviewerId())) {
//            queryWrapper.eq(BizTaskDelay::getReviewerId, entityDTO.getDelayReviewerId());
            queryWrapper.like(BizTaskDelay::getReviewerId, entityDTO.getDelayReviewerId());
        }
       if (Objects.nonNull(entityDTO.getDelayCreateId())) {
            queryWrapper.eq(BizTaskDelay::getCreateBy, entityDTO.getDelayCreateId());
        }
        if (Objects.nonNull(entityDTO.getHasReviewer())) {
            queryWrapper.eq(BizTaskDelay::getHasReviewer, entityDTO.getHasReviewer());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getHasReviewerList())) {
            queryWrapper.in(BizTaskDelay::getHasReviewer, entityDTO.getHasReviewerList());
        }
        List<BizTaskDelay> listData = bizTaskDelayMapper.selectListNoAdd(queryWrapper);
        List<BizTaskDelayDTO> bizTaskDelayDTOList = ListCopyUtil.copy(listData, BizTaskDelayDTO.class);
        if (CollectionUtils.isEmpty(bizTaskDelayDTOList)) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }

        // 获取延期申请主键ID集合
        List<Long> taskDelayIdList = bizTaskDelayDTOList.stream().map(i -> i.getId()).collect(Collectors.toList());
        entityDTO.setTaskDelayIdList(taskDelayIdList);

        // 主表和延期表联合查询
        IPage<BizTaskSupervisionDTO> bizTaskSupervisionDTOIPage = bizTaskDelayMapper.queryPageTaskSupervisionAndDelayInfo(
                PageHelperUtil.getMPlusPageByBasePage(page), entityDTO);

        // 组转数据
        List<BizTaskSupervisionDTO> collect = bizTaskSupervisionDTOIPage.getRecords().stream().map(i -> {
            // 分解表数据
            BizTaskDecomposeDTO bizTaskDecomposeDTO = iBizTaskDecomposeService.findOne(i.getBizTaskDecomposeId());
            if (Objects.nonNull(bizTaskDecomposeDTO)) {
                i.setDutyPeopleName(bizTaskDecomposeDTO.getDutyPeopleName());
                i.setDutyPeopleCompanyName(bizTaskDecomposeDTO.getDutyPeopleCompanyName());
                i.setEndDate(bizTaskDecomposeDTO.getEndDate());
                i.setBizTaskDecomposeId(bizTaskDecomposeDTO.getId());
            }
            return i;
        }).collect(Collectors.toList());

        PageResult<BizTaskSupervisionDTO> bizTaskSupervisionDTOPageResult = new PageResult<>(collect,
                bizTaskSupervisionDTOIPage.getTotal(), bizTaskSupervisionDTOIPage.getCurrent());
        return bizTaskSupervisionDTOPageResult;
    }

    /**
     * 查询延期详情
     * @param bizTaskDelayDTO
     * @return
     */
    @Override
    public BizTaskDecomposeDTO queryDelayDetail(BizTaskDelayDTO bizTaskDelayDTO) {
        // 分解表数据
        BizTaskDecomposeDTO bizTaskDecomposeDTO = new BizTaskDecomposeDTO();
        // 延期申请任务
        BizTaskDelayDTO taskDelayDTO = this.findOne(bizTaskDelayDTO.getId());
        if (Objects.nonNull(taskDelayDTO.getBizTaskDecompose())) {
            // 分解表数据
            bizTaskDecomposeDTO = iBizTaskDecomposeService.findOne(taskDelayDTO.getBizTaskDecompose());
            bizTaskDecomposeDTO.setBizTaskDelayDTO(taskDelayDTO);
        }
        return bizTaskDecomposeDTO;
    }

    /**
     * 查询延期详情(会修改延期申请为已阅状态)
     * @param bizTaskDelayDTO
     * @return
     */
    @Override
    public BizTaskDecomposeDTO queryAndReadDelayDetail(BizTaskDelayDTO bizTaskDelayDTO) {
        BizTaskDecomposeDTO bizTaskDecomposeDTO = this.queryDelayDetail(bizTaskDelayDTO);

        // 修改为延期申请为已阅状态
        LambdaUpdateWrapper<BizTaskDelay> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(BizTaskDelay::getId, bizTaskDelayDTO.getId()).set(BizTaskDelay::getHasRead, 1);
        bizTaskDelayMapper.update(null, lambdaUpdateWrapper);

        return bizTaskDecomposeDTO;
    }

    /**
     * 修改延期申请为已阅状态
     * @param bizTaskDelayDTO
     * @return
     */
    @Override
    public Boolean readBizTaskDelay(BizTaskDelayDTO bizTaskDelayDTO) {
        if (CollectionUtils.isEmpty(bizTaskDelayDTO.getIdList())) {
            return true;
        }
        LambdaUpdateWrapper<BizTaskDelay> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.in(BizTaskDelay::getId, bizTaskDelayDTO.getIdList()).set(BizTaskDelay::getHasRead, 1);
        bizTaskDelayMapper.update(null, lambdaUpdateWrapper);
        return true;
    }

    /**
     * 查询延期申请列表(会修改延期申请为已阅状态)
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizTaskSupervisionDTO> queryAndReadPageDelayList(BizTaskSupervisionDTO entityDTO, BasePageForm page) {
        PageResult<BizTaskSupervisionDTO> bizTaskSupervisionDTOPageResult = this.queryPageDelayList(entityDTO, page);

        // 修改延期申请为已阅状态
        BizTaskDelayDTO bizTaskDelayDTO = new BizTaskDelayDTO();
        List<Long> delayIdList = bizTaskSupervisionDTOPageResult.getData().stream().map(i ->
                i.getTaskDelayId()).distinct().collect(Collectors.toList());
        bizTaskDelayDTO.setIdList(delayIdList);
        this.readBizTaskDelay(bizTaskDelayDTO);
        return bizTaskSupervisionDTOPageResult;
    }

    /**
     * 需要延期审核数量(无单位隔离)
     * @param bizTaskDelayDTO
     * @return
     */
    @Override
    public Integer queryDelayNeedReviewAmount(BizTaskDelayDTO bizTaskDelayDTO) {
        LambdaQueryWrapper<BizTaskDelay> queryWrapper = new LambdaQueryWrapper();
        if (Objects.nonNull(bizTaskDelayDTO.getReviewerId())) {
//            queryWrapper.eq(BizTaskDelay::getReviewerId, bizTaskDelayDTO.getReviewerId());
            queryWrapper.like(BizTaskDelay::getReviewerId, bizTaskDelayDTO.getReviewerId());
        }
        if (Objects.nonNull(bizTaskDelayDTO.getCreateBy())) {
            queryWrapper.eq(BizTaskDelay::getCreateBy, bizTaskDelayDTO.getCreateBy());
        }
        if (Objects.nonNull(bizTaskDelayDTO.getHasReviewer())) {
            queryWrapper.eq(BizTaskDelay::getHasReviewer, bizTaskDelayDTO.getHasReviewer());
        }
        if (CollectionUtils.isNotEmpty(bizTaskDelayDTO.getHasReviewerList())) {
            queryWrapper.in(BizTaskDelay::getHasReviewer, bizTaskDelayDTO.getHasReviewerList());
        }
        if (Objects.nonNull(bizTaskDelayDTO.getHasRead())) {
            queryWrapper.eq(BizTaskDelay::getHasRead, bizTaskDelayDTO.getHasRead());
        }
        return bizTaskDelayMapper.selectCountNoAdd(queryWrapper);
    }


    /**
     * 提醒延期申请通过数量(无单位隔离)
     * @param bizTaskDelayDTO
     * @return
     */
    @Override
    public Integer queryDelayHasReviewedAmount(BizTaskDelayDTO bizTaskDelayDTO) {
        Integer count = this.queryDelayNeedReviewAmount(bizTaskDelayDTO);
        return count;
    }

    @Override
    public List<BizTaskDelayDTO> queryBizTaskDelayByTaskDecompose(Long bizTaskDecompose) {

        LambdaQueryWrapper<BizTaskDelay> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskDelay::getBizTaskDecompose,bizTaskDecompose);
        queryWrapper.ne(BizTaskDelay::getHasReviewer,0);
        queryWrapper.orderByDesc(BizTaskDelay::getCreateTime);
        List<BizTaskDelay> listData = bizTaskDelayMapper.selectListNoAdd(queryWrapper);
        List<BizTaskDelayDTO> BizTaskDelayDTOList = ListCopyUtil.copy(listData, BizTaskDelayDTO.class);
        return BizTaskDelayDTOList;
    }

}
