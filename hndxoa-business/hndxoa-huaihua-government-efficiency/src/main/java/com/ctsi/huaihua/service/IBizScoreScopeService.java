package com.ctsi.huaihua.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.systenant.entity.dto.TenantUserListDTO;
import com.ctsi.huaihua.entity.BizScoreScope;
import com.ctsi.huaihua.entity.dto.BizScoreScopeDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgAndUserDto;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 领导委托信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface IBizScoreScopeService extends SysBaseServiceI<BizScoreScope> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizScoreScopeDTO> queryListPage(BizScoreScopeDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizScoreScopeDTO> queryList(BizScoreScopeDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizScoreScopeDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizScoreScopeDTO create(BizScoreScopeDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizScoreScopeDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 是否存在
     * <p>
     * existByBizScoreScopeId
     *
     * @param code
     * @return
     */
    boolean existByBizScoreScopeId(Long code);

    /**
     * 批量新增
     * <p>
     * create batch
     *
     * @param dataList
     * @return
     */
    Boolean insertBatch(List<BizScoreScopeDTO> dataList);


    /**
     * 编辑日常打分范围
     *
     * @param bizScoreScopeDTO
     * @return
     */
    Boolean updateScoreScope(BizScoreScopeDTO bizScoreScopeDTO);

    /**
     * 删除日常打分范围
     *
     * @param bizScoreScopeDTO
     * @return
     */
    Boolean deleteScoreScope(BizScoreScopeDTO bizScoreScopeDTO);

    /**
     * 分页查询领导日常打分列表
     *
     * @param bizScoreScopeDTO
     * @param page
     * @return
     */
    PageResult<BizScoreScopeDTO> queryBizLeaderScoreScopePage(BizScoreScopeDTO bizScoreScopeDTO, BasePageForm page);


    /**
     * 查询该领导日常打分用户列表
     *
     * @param bizScoreScopeDTO
     * @return
     */
    List<CscpUserDTO> queryBizLeaderScoreScopeList(BizScoreScopeDTO bizScoreScopeDTO);

    /**
     * 分页查询指定机构下的所有用户, 排除免考核用户
     *
     * @param id
     * @param realName
     * @param basePageForm
     * @return
     */
    PageResult<CscpUserDTO> pageQueryUserNotInList(Long id, String realName, BasePageForm basePageForm);

    /**
     * 不分页查询本单位下的用户
     *
     * @param parentId
     * @param realName
     * @return
     */
    CscpOrgAndUserDto queryCompanyUserNotInList(Long parentId, String realName);

    /**
     * 搜索获取上下级租户下的用户
     *
     * @param realName
     * @return
     */
    TenantUserListDTO queryTenantUserNotInList(String realName);

    /**
     * 查询所有领导选人的选人范围
     * @return
     */
    List<Long> queryAllLeaderScopeList();

}
