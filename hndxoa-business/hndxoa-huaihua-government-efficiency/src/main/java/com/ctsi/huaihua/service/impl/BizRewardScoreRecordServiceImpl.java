package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.entity.BizRewardScoreRecord;
import com.ctsi.huaihua.entity.BizUserRewardScoreRel;
import com.ctsi.huaihua.entity.dto.BizRewardScoreRecordDTO;
import com.ctsi.huaihua.entity.dto.BizUserRewardScoreRelDTO;
import com.ctsi.huaihua.mapper.BizRewardScoreRecordMapper;
import com.ctsi.huaihua.service.IBizRewardScoreRecordService;
import com.ctsi.huaihua.service.IBizUserRewardScoreRelService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 奖分记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Slf4j
@Service
public class BizRewardScoreRecordServiceImpl extends SysBaseServiceImpl<BizRewardScoreRecordMapper, BizRewardScoreRecord> implements IBizRewardScoreRecordService {

    /**
     * 市直单位
     */
    private static final Integer BIZ_MUNICIPAL_UNIT = 1;

    /**
     * 督查室
     */
    private static final Integer BIZ_SUPERVISE_OFFICE = 2;

    @Autowired
    private BizRewardScoreRecordMapper bizRewardScoreRecordMapper;

    @Autowired
    private IBizUserRewardScoreRelService iBizUserRewardScoreRelService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizRewardScoreRecordDTO> queryListPage(BizRewardScoreRecordDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();

        IPage<BizRewardScoreRecord> pageData = bizRewardScoreRecordMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizRewardScoreRecordDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizRewardScoreRecordDTO.class));

        return new PageResult<BizRewardScoreRecordDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizRewardScoreRecordDTO> queryList(BizRewardScoreRecordDTO entityDTO) {
        LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();
        List<BizRewardScoreRecord> listData = bizRewardScoreRecordMapper.selectList(queryWrapper);
        List<BizRewardScoreRecordDTO> BizRewardScoreRecordDTOList = ListCopyUtil.copy(listData, BizRewardScoreRecordDTO.class);
        return BizRewardScoreRecordDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizRewardScoreRecordDTO findOne(Long id) {
        BizRewardScoreRecord  bizRewardScoreRecord =  bizRewardScoreRecordMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizRewardScoreRecord,BizRewardScoreRecordDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizRewardScoreRecordDTO create(BizRewardScoreRecordDTO entityDTO) {
        List<BizUserRewardScoreRelDTO> userRewardScoreRelList = entityDTO.getUserRewardScoreRelList();
        if (Objects.isNull(userRewardScoreRelList)) {
            throw new BusinessException("被奖励加分的用户信息不允许为空");
        }

        // 用户列表只能是同一单位
        Set<Long> companyIdSet = new HashSet<>(16);
        userRewardScoreRelList.forEach(i -> {
            if (Objects.isNull(i.getRewardCompanyId())) {
                throw new BusinessException("被奖励的用户单位ID不允许为空");
            }
            companyIdSet.add(i.getRewardCompanyId());
        });
        if (companyIdSet.size() > 1) {
            throw new BusinessException("被奖励的用户必须为同一单位");
        }

        if (Objects.isNull(entityDTO.getScoreYear())) {
            entityDTO.setScoreYear(LocalDateTime.now().getYear());
        }

        // 如果是市直单位，则需要审核；如果是市政府办，则不需要审核
        if (BIZ_MUNICIPAL_UNIT.equals(entityDTO.getRewardResource())) {
            entityDTO.setHasReviewer(0);
        }
        if (BIZ_SUPERVISE_OFFICE.equals(entityDTO.getRewardResource())) {
            entityDTO.setHasReviewer(1);
            entityDTO.setReviewerId(SecurityUtils.getCurrentUserId());
            entityDTO.setReviewerName(SecurityUtils.getCurrentRealName());
            entityDTO.setReviewerDepartmentId(SecurityUtils.getCurrentCscpUserDetail().getDepartmentId());
            entityDTO.setReviewerCompanyId(SecurityUtils.getCurrentCompanyId());
            entityDTO.setReviewerTime(LocalDateTime.now());
        }
        entityDTO.setCreateCompanyName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());
        entityDTO.setReviewerHasRead(0);
        BizRewardScoreRecord bizRewardScoreRecord = BeanConvertUtils.copyProperties(entityDTO, BizRewardScoreRecord.class);
        save(bizRewardScoreRecord);

        // 保存用户奖励分关系数据
        for (BizUserRewardScoreRelDTO bizUserRewardScoreRelDTO : userRewardScoreRelList) {
            bizUserRewardScoreRelDTO.setRewardScoreRecordId(bizRewardScoreRecord.getId());
            bizUserRewardScoreRelDTO.setHasRead(0);
        }
        iBizUserRewardScoreRelService.insertBatch(userRewardScoreRelList);
        return BeanConvertUtils.copyProperties(bizRewardScoreRecord, BizRewardScoreRecordDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizRewardScoreRecordDTO entity) {
        BizRewardScoreRecord bizRewardScoreRecord = BeanConvertUtils.copyProperties(entity,BizRewardScoreRecord.class);
        return bizRewardScoreRecordMapper.updateById(bizRewardScoreRecord);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizRewardScoreRecordMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizRewardScoreRecordId
     * @return
     */
    @Override
    public boolean existByBizRewardScoreRecordId(Long BizRewardScoreRecordId) {
        if (BizRewardScoreRecordId != null) {
            LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizRewardScoreRecord::getId, BizRewardScoreRecordId);
            List<BizRewardScoreRecord> result = bizRewardScoreRecordMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizRewardScoreRecordDTO> dataList) {
        List<BizRewardScoreRecord> result = ListCopyUtil.copy(dataList, BizRewardScoreRecord.class);
        return saveBatch(result);
    }


    /**
     * 市直单位办公室专职人员分页查询加分记录(查询本单位)
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordMunicipal(BizRewardScoreRecordDTO entityDTO, BasePageForm page) {
        if (Objects.isNull(SecurityUtils.getCurrentCompanyId())) {
            throw new BusinessException("未找到该用户的单位信息");
        }
        List<BizUserRewardScoreRelDTO> bizUserRewardScoreRelDTOS = iBizUserRewardScoreRelService.queryListByCompanyId(SecurityUtils.getCurrentCompanyId());
        if (CollectionUtils.isEmpty(bizUserRewardScoreRelDTOS)) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }

        Map<Long, List<BizUserRewardScoreRelDTO>> userRewardRelMap = bizUserRewardScoreRelDTOS
                .stream().collect(Collectors.groupingBy(BizUserRewardScoreRelDTO::getRewardScoreRecordId));
        List<Long> rewardIdList = bizUserRewardScoreRelDTOS.stream()
                .map(i -> i.getRewardScoreRecordId()).collect(Collectors.toList());

        LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();
        if (CollectionUtils.isNotEmpty(entityDTO.getScoreReasonList())) {
            queryWrapper.in(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReasonList());
        }
        if (StringUtils.isNotBlank(entityDTO.getScoreReason())) {
            queryWrapper.eq(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReason());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(BizRewardScoreRecord::getCreateTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(BizRewardScoreRecord::getCreateTime, entityDTO.getEndTime());
        }
        if (Objects.nonNull(entityDTO.getHasReviewer())) {
            queryWrapper.eq(BizRewardScoreRecord::getHasReviewer, entityDTO.getHasReviewer());
        }
        queryWrapper.in(BizRewardScoreRecord::getId, rewardIdList);
        queryWrapper.orderByAsc(BizRewardScoreRecord::getHasReviewer);
        queryWrapper.orderByDesc(BizRewardScoreRecord::getCreateTime);
        IPage<BizRewardScoreRecord> pageData = bizRewardScoreRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);
        IPage<BizRewardScoreRecordDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizRewardScoreRecordDTO.class));

        data.getRecords().forEach(i -> {
            i.setUserRewardScoreRelList(userRewardRelMap.get(i.getId()));
        });
        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }


    /**
     * 市政府办督查室分页查询加分记录(查询本租户)
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordSupervision(BizRewardScoreRecordDTO entityDTO, BasePageForm page) {
        // 查询关联用户
        LambdaQueryWrapper<BizUserRewardScoreRel> lambdaQueryWrapper = new LambdaQueryWrapper();
        if (Objects.nonNull(entityDTO.getRewardCompanyName())) {
            lambdaQueryWrapper.like(BizUserRewardScoreRel::getRewardCompanyName, entityDTO.getRewardCompanyName());
        }
        if (StringUtils.isNotBlank(entityDTO.getUserName())) {
            lambdaQueryWrapper.like(BizUserRewardScoreRel::getUserName, entityDTO.getUserName());
        }
        List<BizUserRewardScoreRel> bizUserRewardScoreRelList = iBizUserRewardScoreRelService.selectListOnlyAddTenantId(lambdaQueryWrapper);
        List<BizUserRewardScoreRelDTO> bizUserRewardScoreRelDTOList = ListCopyUtil.copy(bizUserRewardScoreRelList, BizUserRewardScoreRelDTO.class);
        if (CollectionUtils.isEmpty(bizUserRewardScoreRelDTOList)) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }

        List<Long> rewardIdList = bizUserRewardScoreRelDTOList.stream().map(i -> i.getRewardScoreRecordId()).collect(Collectors.toList());

        // 查询所有关联奖励记录的用户信息
        LambdaQueryWrapper<BizUserRewardScoreRel> userRewardWrapper = new LambdaQueryWrapper();
        userRewardWrapper.in(BizUserRewardScoreRel::getRewardScoreRecordId, rewardIdList);
        List<BizUserRewardScoreRel> userRewardScoreRelList = iBizUserRewardScoreRelService.selectListNoAdd(userRewardWrapper);
        List<BizUserRewardScoreRelDTO> userRewardScoreRelDTOList = ListCopyUtil.copy(userRewardScoreRelList, BizUserRewardScoreRelDTO.class);
        Map<Long, List<BizUserRewardScoreRelDTO>> userRewardRelMap = userRewardScoreRelDTOList
                .stream().collect(Collectors.groupingBy(BizUserRewardScoreRelDTO::getRewardScoreRecordId));

        LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();
        if (StringUtils.isNotBlank(entityDTO.getScoreReason())) {
            queryWrapper.eq(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReason());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getScoreReasonList())) {
            queryWrapper.in(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReasonList());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(BizRewardScoreRecord::getCreateTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(BizRewardScoreRecord::getCreateTime, entityDTO.getEndTime());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getHasReviewerList())) {
            queryWrapper.in(BizRewardScoreRecord::getHasReviewer, entityDTO.getHasReviewerList());
        }
        queryWrapper.in(BizRewardScoreRecord::getId, rewardIdList);
        queryWrapper.orderByDesc(BizRewardScoreRecord::getCreateTime);
        IPage<BizRewardScoreRecord> pageData = bizRewardScoreRecordMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);
        //返回
        IPage<BizRewardScoreRecordDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizRewardScoreRecordDTO.class));

        // 补充用户信息
        data.getRecords().forEach(i -> {
            i.setUserRewardScoreRelList(userRewardRelMap.get(i.getId()));
        });
        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 市直单位个人分页查询加分记录(查询个人已通过的记录)
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordIndividual(BizRewardScoreRecordDTO entityDTO, BasePageForm page) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (Objects.isNull(currentUserId)) {
            throw new BusinessException("未找到当前用户ID");
        }
        List<BizUserRewardScoreRelDTO> bizUserRewardScoreRelDTOS = iBizUserRewardScoreRelService.queryListByUserId(currentUserId);
        if (CollectionUtils.isEmpty(bizUserRewardScoreRelDTOS)) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }

        Map<Long, List<BizUserRewardScoreRelDTO>> userRewardRelMap = bizUserRewardScoreRelDTOS
                .stream().collect(Collectors.groupingBy(BizUserRewardScoreRelDTO::getRewardScoreRecordId));
        List<Long> rewardIdList = bizUserRewardScoreRelDTOS.stream().map(BizUserRewardScoreRelDTO::getRewardScoreRecordId).collect(Collectors.toList());
        LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();
        if (StringUtils.isNotBlank(entityDTO.getScoreReason())) {
            queryWrapper.eq(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReason());
        }
        if (CollectionUtils.isNotEmpty(entityDTO.getScoreReasonList())) {
            queryWrapper.in(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReasonList());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(BizRewardScoreRecord::getCreateTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(BizRewardScoreRecord::getCreateTime, entityDTO.getEndTime());
        }
        queryWrapper.eq(BizRewardScoreRecord::getHasReviewer, 1);
        queryWrapper.in(BizRewardScoreRecord::getId, rewardIdList);
        queryWrapper.orderByDesc(BizRewardScoreRecord::getReviewerTime);
        IPage<BizRewardScoreRecord> pageData = bizRewardScoreRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);
        //返回
        IPage<BizRewardScoreRecordDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizRewardScoreRecordDTO.class));
        data.getRecords().forEach(i -> {
            if (CollectionUtils.isNotEmpty(userRewardRelMap.get(i.getId()))
                    && Objects.nonNull(userRewardRelMap.get(i.getId()).get(0))) {
                i.setRewardHasRead(userRewardRelMap.get(i.getId()).get(0).getHasRead());
            }
            i.setUserRewardScoreRelList(userRewardRelMap.get(i.getId()));
        });
        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }


    /**
     * 加分审核与驳回
     * @param entityDTO
     * @return
     */
    @Override
    public Boolean approvedBizRewardScoreRecord(BizRewardScoreRecordDTO entityDTO) {
        if (Objects.isNull(SecurityUtils.getCurrentCscpUserDetail())) {
            throw new BusinessException("未找到该用户的用户信息");
        }

        // 补充审核信息
        entityDTO.setReviewerId(SecurityUtils.getCurrentUserId());
        entityDTO.setReviewerName(SecurityUtils.getCurrentRealName());
        entityDTO.setReviewerDepartmentId(SecurityUtils.getCurrentCscpUserDetail().getDepartmentId());
        entityDTO.setReviewerCompanyId(SecurityUtils.getCurrentCompanyId());
        entityDTO.setReviewerTime(LocalDateTime.now());
        this.update(entityDTO);
        return true;
    }

    /**
     * 查询待审核奖分记录数量(查本租户)
     * @return
     */
    @Override
    public Integer queryReviewedRewardScoreNum() {
        LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizRewardScoreRecord::getHasReviewer, 0);
        Integer count = bizRewardScoreRecordMapper.selectCountOnlyAddTenantId(queryWrapper);
        return count;
    }


    /**
     * 个人查询单条数据并修改为已阅状态
     * @param id
     * @return
     */
    @Override
    public BizRewardScoreRecordDTO getAndUpdateStatus(Long id) {
        if (Objects.isNull(id)) {
            throw new BusinessException("未找到该条奖励分的ID");
        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (Objects.isNull(currentUserId)) {
            throw new BusinessException("未找到该用户的用户信息");
        }

        // 查询用户信息
        LambdaQueryWrapper<BizUserRewardScoreRel> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(BizUserRewardScoreRel::getUserId, currentUserId);
        lambdaQueryWrapper.eq(BizUserRewardScoreRel::getRewardScoreRecordId, id);
        List<BizUserRewardScoreRel> bizUserRewardScoreRelList = iBizUserRewardScoreRelService.selectListNoAdd(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(bizUserRewardScoreRelList)) {
            throw new BusinessException("未找到该条奖励分记录");
        }

        // 查询奖分记录
        BizRewardScoreRecordDTO recordDTO = this.findOne(id);
        BizUserRewardScoreRel bizUserRewardScoreRel = bizUserRewardScoreRelList.get(0);
        recordDTO.setUserId(bizUserRewardScoreRel.getUserId());
        recordDTO.setUserName(bizUserRewardScoreRel.getUserName());
        recordDTO.setRewardDepartmentName(bizUserRewardScoreRel.getRewardDepartmentName());
        recordDTO.setRewardDepartmentName(bizUserRewardScoreRel.getRewardCompanyName());
        recordDTO.setPost(bizUserRewardScoreRel.getPost());

        // 修改为已读状态
        bizUserRewardScoreRel.setHasRead(1);
        iBizUserRewardScoreRelService.updateById(bizUserRewardScoreRel);
        return recordDTO;
    }

    /**
     * 市直单位办公室专职人员分页查询加分记录(查询本单位所有被加分的记录)
     * @param entityDTO
     * @param page
     * @return
     */
    @Override
    public PageResult<BizRewardScoreRecordDTO> pageQueryBizRewardScoreRecordMunicipalByAndroid(BizRewardScoreRecordDTO entityDTO, BasePageForm page) {
        if (Objects.isNull(SecurityUtils.getCurrentCompanyId())) {
            throw new BusinessException("未找到该用户的单位信息");
        }
        List<BizUserRewardScoreRelDTO> bizUserRewardScoreRelDTOS = iBizUserRewardScoreRelService.queryListByRewardCompanyId(SecurityUtils.getCurrentCompanyId());
        if (CollectionUtils.isEmpty(bizUserRewardScoreRelDTOS)) {
            return new PageResult<>(new ArrayList<>(), 0, 0);
        }

        Map<Long, List<BizUserRewardScoreRelDTO>> userRewardRelMap = bizUserRewardScoreRelDTOS
                .stream().collect(Collectors.groupingBy(BizUserRewardScoreRelDTO::getRewardScoreRecordId));
        List<Long> rewardIdList = bizUserRewardScoreRelDTOS.stream()
                .map(i -> i.getRewardScoreRecordId()).collect(Collectors.toList());

        LambdaQueryWrapper<BizRewardScoreRecord> queryWrapper = new LambdaQueryWrapper();
        if (CollectionUtils.isNotEmpty(entityDTO.getScoreReasonList())) {
            queryWrapper.in(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReasonList());
        }
        if (StringUtils.isNotBlank(entityDTO.getScoreReason())) {
            queryWrapper.eq(BizRewardScoreRecord::getScoreReason, entityDTO.getScoreReason());
        }
        if (Objects.nonNull(entityDTO.getStartTime())) {
            queryWrapper.ge(BizRewardScoreRecord::getCreateTime, entityDTO.getStartTime());
        }
        if (Objects.nonNull(entityDTO.getEndTime())) {
            queryWrapper.le(BizRewardScoreRecord::getCreateTime, entityDTO.getEndTime());
        }
        if (Objects.nonNull(entityDTO.getHasReviewer())) {
            queryWrapper.eq(BizRewardScoreRecord::getHasReviewer, entityDTO.getHasReviewer());
        }
        queryWrapper.in(BizRewardScoreRecord::getId, rewardIdList);
        //queryWrapper.orderByAsc(BizRewardScoreRecord::getHasReviewer);
        queryWrapper.orderByDesc(BizRewardScoreRecord::getCreateTime);
        IPage<BizRewardScoreRecord> pageData = bizRewardScoreRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(page), queryWrapper);
        IPage<BizRewardScoreRecordDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizRewardScoreRecordDTO.class));

        data.getRecords().forEach(i -> {
            i.setUserRewardScoreRelList(userRewardRelMap.get(i.getId()));
        });
        return new PageResult<>(data.getRecords(), data.getTotal(), data.getCurrent());
    }
}
