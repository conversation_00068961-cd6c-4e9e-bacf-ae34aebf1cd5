package com.ctsi.huaihua.controller;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsUnstatisticDTO;
import com.ctsi.huaihua.service.IBizTaskWorkLogsUnstatisticService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskWorkLogsUnstatistic")
@Api(value = "任务工作日志免统计的用户列表", tags = "任务工作日志免统计的用户列表接口")
public class BizTaskWorkLogsUnstatisticController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskWorkLogsUnstatistic";

    @Autowired
    private IBizTaskWorkLogsUnstatisticService bizTaskWorkLogsUnstatisticService;



    /**
     *  新增免统计工作日志的用户列表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskWorkLogsUnstatistic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增免统计工作日志的用户列表批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogsUnstatistic.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskWorkLogsUnstatisticDTO> bizTaskWorkLogsUnstatisticList) {
       Boolean  result = bizTaskWorkLogsUnstatisticService.insertBatch(bizTaskWorkLogsUnstatisticList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskWorkLogsUnstatistic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增免统计工作日志的用户列表数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogsUnstatistic.add')")
    public ResultVO<BizTaskWorkLogsUnstatisticDTO> create(@RequestBody BizTaskWorkLogsUnstatisticDTO bizTaskWorkLogsUnstatisticDTO)  {
        BizTaskWorkLogsUnstatisticDTO result = bizTaskWorkLogsUnstatisticService.create(bizTaskWorkLogsUnstatisticDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskWorkLogsUnstatistic.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新免统计工作日志的用户列表数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogsUnstatistic.update')")
    public ResultVO update(@RequestBody BizTaskWorkLogsUnstatisticDTO bizTaskWorkLogsUnstatisticDTO) {
	    Assert.notNull(bizTaskWorkLogsUnstatisticDTO.getId(), "general.IdNotNull");
        int count = bizTaskWorkLogsUnstatisticService.update(bizTaskWorkLogsUnstatisticDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除免统计工作日志的用户列表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizTaskWorkLogsUnstatistic.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskWorkLogsUnstatistic.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskWorkLogsUnstatisticService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskWorkLogsUnstatisticDTO bizTaskWorkLogsUnstatisticDTO = bizTaskWorkLogsUnstatisticService.findOne(id);
        return ResultVO.success(bizTaskWorkLogsUnstatisticDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskWorkLogsUnstatisticPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskWorkLogsUnstatisticDTO>> queryBizTaskWorkLogsUnstatisticPage(BizTaskWorkLogsUnstatisticDTO bizTaskWorkLogsUnstatisticDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskWorkLogsUnstatisticService.queryListPage(bizTaskWorkLogsUnstatisticDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskWorkLogsUnstatistic")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskWorkLogsUnstatisticDTO>> queryBizTaskWorkLogsUnstatistic(BizTaskWorkLogsUnstatisticDTO bizTaskWorkLogsUnstatisticDTO) {
       List<BizTaskWorkLogsUnstatisticDTO> list = bizTaskWorkLogsUnstatisticService.queryList(bizTaskWorkLogsUnstatisticDTO);
       return ResultVO.success(new ResResult<BizTaskWorkLogsUnstatisticDTO>(list));
   }

}
