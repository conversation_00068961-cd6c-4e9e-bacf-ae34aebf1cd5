package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务领导对应的联络员表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_leader_liaison")
@ApiModel(value="BizTaskLeaderLiaison对象", description="任务领导对应的联络员表")
public class BizTaskLeaderLiaison extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 联络员id
     */
    @ApiModelProperty(value = "联络员id")
    private Long liaisonManId;

    /**
     * 联络员名称
     */
    @ApiModelProperty(value = "联络员名称")
    private String liaisonManName;

    /**
     * 联络人单位id
     */
    @ApiModelProperty(value = "联络人单位id")
    private Long liaisonManUnitId;

    /**
     * 联络人单位名称
     */
    @ApiModelProperty(value = "联络人单位名称")
    private String liaisonManUnitName;

    /**
     * 联络人部门id
     */
    @ApiModelProperty(value = "联络人部门id")
    private Long liaisonManBranchId;

    /**
     * 联络人部门名称
     */
    @ApiModelProperty(value = "联络人部门名称")
    private String liaisonManBranchName;

    /**
     * 联络员手机号码
     */
    @ApiModelProperty(value = "联络员手机号码")
    private String liaisonManTelephone;

    /**
     * 领导id
     */
    @ApiModelProperty(value = "领导id")
    private Long leaderId;

    /**
     * 领导名称
     */
    @ApiModelProperty(value = "领导名称")
    private String leaderName;

    /**
     * 领导单位id
     */
    @ApiModelProperty(value = "领导单位id")
    private Long leaderUnitId;

    /**
     * 领导单位名称
     */
    @ApiModelProperty(value = "领导单位名称")
    private String leaderUnitName;

    /**
     * 领导部门id
     */
    @ApiModelProperty(value = "领导部门id")
    private Long leaderBranchId;

    /**
     * 领导部门名称
     */
    @ApiModelProperty(value = "领导部门名称")
    private String leaderBranchName;

    /**
     * 领导手机号码
     */
    @ApiModelProperty(value = "领导手机号码")
    private String leaderTelephone;


}
