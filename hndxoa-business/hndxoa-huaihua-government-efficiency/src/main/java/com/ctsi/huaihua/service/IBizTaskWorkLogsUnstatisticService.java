package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskWorkLogsUnstatisticDTO;
import com.ctsi.huaihua.entity.BizTaskWorkLogsUnstatistic;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 任务工作日志免统计的领导列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface IBizTaskWorkLogsUnstatisticService extends SysBaseServiceI<BizTaskWorkLogsUnstatistic> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskWorkLogsUnstatisticDTO> queryListPage(BizTaskWorkLogsUnstatisticDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskWorkLogsUnstatisticDTO> queryList(BizTaskWorkLogsUnstatisticDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskWorkLogsUnstatisticDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskWorkLogsUnstatisticDTO create(BizTaskWorkLogsUnstatisticDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskWorkLogsUnstatisticDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskWorkLogsUnstatisticId
     * @param code
     * @return
     */
    boolean existByBizTaskWorkLogsUnstatisticId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskWorkLogsUnstatisticDTO> dataList);


}
