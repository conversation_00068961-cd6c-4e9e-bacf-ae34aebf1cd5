package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 任务督查的主表
 * 主任务
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_supervision")
@ApiModel(value = "BizTaskSupervision对象", description = "任务督查的主表")
public class BizTaskSupervision extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    /**
     * 任务类型，对应数据字典taskType
     */
    @ApiModelProperty(value = "任务类型，对应数据字典taskType")
    private String taskType;

    /**
     * 任务紧急程度，对应数据字典taskDegreeUrgency
     */
    @ApiModelProperty(value = "任务紧急程度，对应数据字典taskDegreeUrgency")
    private String degreeUrgency;

    /**
     * 任务来源，对应数据字典taskSource
     */
    @ApiModelProperty(value = "任务来源，对应数据字典taskSource")
    private String taskSource;

    /**
     * 任务标签，对应数据字典taskTag
     */
    @ApiModelProperty(value = "任务标签，对应数据字典taskTag")
    private String taskTag;

    /**
     * 考核方式，对应数据字典ExamineMethod
     */
    @ApiModelProperty(value = "考核方式，对应数据字典ExamineMethod")
    private String examineMethod;

    /**
     * 截止时间，yyyy-mm-dd
     */
    @ApiModelProperty(value = "截止时间，yyyy-mm-dd")
    private LocalDate dueTime;

    /**
     * 任务概叙
     */
    @ApiModelProperty(value = "任务概叙")
    private String taskDescription;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;

    /**
     * 0 未发布暂存，1：发布  2：撤回
     */
    @ApiModelProperty(value = "0 未发布暂存，1：发布  2：撤回")
    private Integer hasPublish;

    /**
     * 0：未办结 1 办结
     */
    @ApiModelProperty(value = "0：未办结 1 办结")
    private Integer hasFinish;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private LocalDateTime hasFinishTime;

    /**
     * 拟稿人部门名称
     */
    @ApiModelProperty(value = "拟稿人部门名称")
    @TableField(fill = FieldFill.INSERT)
    private String departmentName;

    /**
     * 拟稿人手机号码
     */
    @ApiModelProperty(value = "拟稿人手机号码")
    @TableField(fill = FieldFill.INSERT)
    private String mobile;

    /**
     * 拟稿人单位名称
     */
    @ApiModelProperty(value = "拟稿人单位名称")
    @TableField(fill = FieldFill.INSERT)
    private String companyName;

    /**
     * 任务编号，中间：隔开，：之前为父编号，转办的以zb开头
     */
    @ApiModelProperty(value = "任务编号，中间：隔开，：之前为父编号，转办的以zb开头")
    private String taskNumber;

    /**
     * 任务层级，转办了多少级
     */
    @ApiModelProperty(value = "任务层级，转办了多少级")
    private Integer taskLevel;

    /**
     * 任务新增的方式0表示新增 1表示转办
     */
    @ApiModelProperty(value = "任务新增的方式0表示新增 1表示转办")
    private Integer taskAddType;

    /**
     * 任务来自转办，转办的id
     * 转办前 原主任务id
     */
    @ApiModelProperty(value = "任务来自转办，转办的id")
    private Long taskTransferId;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String dutyPeople;

    /**
     * 任务转办过来后的分解的任务id
     * 转办前 原分解任务id
     */
    @ApiModelProperty(value = "任务转办过来后的分解的任务id")
    private Long taskTransferDecomposeId;


    @ApiModelProperty(value = "表单项的id")
    private Long formId;


    /**
     * 对于最新反馈的成果标记 0：没有 , 1 有
     * 对于最新反馈的成果，页面列表以小红点标识最新数据，用户点击成果审核后恢复默认样式。
     */
    @ApiModelProperty(value = " 对于最新反馈的成果，页面列表以小红点标识最新数据,用户点击成果审核后恢复默认样式  0：没有标识, 1 最新反馈成果,有小红点标识")
    private Integer newSupFeedbackSign;

    /**
     * 是否有反馈记录 0：没有 , 1 有
     */
    @ApiModelProperty(value = " 是否有反馈记录 0：没有, 1 有")
    private Integer supHasFeedback;


    @ApiModelProperty(value = "督查任务等级对应biz_task_level")
    private String inspectorTaskLevel;


    @ApiModelProperty(value = "督查任务具体的事项对应biz_task_levell")
    private String inspectorItems;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "难易性，对应数据数据字段task_difficulty")
    private String difficulty;

    @ApiModelProperty(value = "牵头人名称")
    private String leadPeopleName;

    @ApiModelProperty(value = "牵头人id")
    private Long leadPeopleId;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime approvalTime;

    @ApiModelProperty(value = "驳回时间")
    private LocalDateTime difficultyTime;

    @ApiModelProperty(value = "审核人名称")
    private String approvalName;

    @ApiModelProperty(value = "审核人id")
    private Long approvalId;

    @ApiModelProperty(value = "驳回名称")
    private String difficultyName;

    @ApiModelProperty(value = "驳回id")
    private Long difficultyId;

    @ApiModelProperty(value = "任务分值")
    private String taskScore;

    @ApiModelProperty(value = "分管领导ID")
    private String branchLeaderId;

    @ApiModelProperty(value = "分管领导姓名")
    private String branchLeaderName;


    @ApiModelProperty(value = "分管领导姓名的表单json字符串")
    private String branchLeaderNameJson;
}
