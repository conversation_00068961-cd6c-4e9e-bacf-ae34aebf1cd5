package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizScoreRecordDTO;
import com.ctsi.huaihua.entity.BizScoreRecord;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.huaihua.entity.dto.BizUserScoreDTO;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 日常打分记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
public interface IBizScoreRecordService extends SysBaseServiceI<BizScoreRecord> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizScoreRecordDTO> queryListPage(BizScoreRecordDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizScoreRecordDTO> queryList(BizScoreRecordDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizScoreRecordDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizScoreRecordDTO create(BizScoreRecordDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizScoreRecordDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizScoreRecordId
     * @param code
     * @return
     */
    boolean existByBizScoreRecordId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizScoreRecordDTO> dataList);


    /**
     * 领导日常打分
     * @param bizScoreRecordDTO
     * @return
     */
    BizScoreRecordDTO insertBizScoreRecord(BizScoreRecordDTO bizScoreRecordDTO);


    /**
     * 查询当月打分记录
     * @param bizScoreRecordDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizScoreRecordDTO> queryCurrentBizScoreRecord(BizScoreRecordDTO bizScoreRecordDTO, BasePageForm basePageForm);

    /**
     * 定时任务: 自动生成默认打分记录
     * @param bizScoreRecordDTO
     * @return
     */
    Boolean autoGenerateScoreRecord(BizScoreRecordDTO bizScoreRecordDTO);

    List<BizUserScoreDTO> averageAnnualScores();

}
