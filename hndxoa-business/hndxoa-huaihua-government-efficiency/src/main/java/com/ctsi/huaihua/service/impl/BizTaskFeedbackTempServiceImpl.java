package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizTaskFeedbackTemp;
import com.ctsi.huaihua.entity.dto.BizTaskFeedbackTempDTO;
import com.ctsi.huaihua.mapper.BizTaskFeedbackTempMapper;
import com.ctsi.huaihua.service.IBizTaskFeedbackTempService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 任务成果反馈暂存表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Slf4j
@Service
public class BizTaskFeedbackTempServiceImpl extends SysBaseServiceImpl<BizTaskFeedbackTempMapper, BizTaskFeedbackTemp> implements IBizTaskFeedbackTempService {

    @Autowired
    private BizTaskFeedbackTempMapper bizTaskFeedbackTempMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizTaskFeedbackTempDTO> queryListPage(BizTaskFeedbackTempDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizTaskFeedbackTemp> queryWrapper = new LambdaQueryWrapper();

        IPage<BizTaskFeedbackTemp> pageData = bizTaskFeedbackTempMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizTaskFeedbackTempDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizTaskFeedbackTempDTO.class));

        return new PageResult<BizTaskFeedbackTempDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizTaskFeedbackTempDTO> queryList(BizTaskFeedbackTempDTO entityDTO) {
        LambdaQueryWrapper<BizTaskFeedbackTemp> queryWrapper = new LambdaQueryWrapper();
            List<BizTaskFeedbackTemp> listData = bizTaskFeedbackTempMapper.selectList(queryWrapper);
            List<BizTaskFeedbackTempDTO> BizTaskFeedbackTempDTOList = ListCopyUtil.copy(listData, BizTaskFeedbackTempDTO.class);
        return BizTaskFeedbackTempDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizTaskFeedbackTempDTO findOne(Long id) {
        BizTaskFeedbackTemp  bizTaskFeedbackTemp =  bizTaskFeedbackTempMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizTaskFeedbackTemp,BizTaskFeedbackTempDTO.class);
    }


    /**
     * 新增
     * 暂存 每个人可以暂存
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizTaskFeedbackTempDTO create(BizTaskFeedbackTempDTO entityDTO) {
       BizTaskFeedbackTemp bizTaskFeedbackTemp =  BeanConvertUtils.copyProperties(entityDTO,BizTaskFeedbackTemp.class);
        //save(bizTaskFeedbackTemp);
        saveOrUpdate(bizTaskFeedbackTemp);
        return  BeanConvertUtils.copyProperties(bizTaskFeedbackTemp,BizTaskFeedbackTempDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizTaskFeedbackTempDTO entity) {
        BizTaskFeedbackTemp bizTaskFeedbackTemp = BeanConvertUtils.copyProperties(entity,BizTaskFeedbackTemp.class);
        return bizTaskFeedbackTempMapper.updateById(bizTaskFeedbackTemp);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizTaskFeedbackTempMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizTaskFeedbackTempId
     * @return
     */
    @Override
    public boolean existByBizTaskFeedbackTempId(Long BizTaskFeedbackTempId) {
        if (BizTaskFeedbackTempId != null) {
            LambdaQueryWrapper<BizTaskFeedbackTemp> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizTaskFeedbackTemp::getId, BizTaskFeedbackTempId);
            List<BizTaskFeedbackTemp> result = bizTaskFeedbackTempMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizTaskFeedbackTempDTO> dataList) {
        List<BizTaskFeedbackTemp> result = ListCopyUtil.copy(dataList, BizTaskFeedbackTemp.class);
        return saveBatch(result);
    }

    @Override
    public BizTaskFeedbackTempDTO getTempFeedback(BizTaskFeedbackTempDTO dto) {
        BizTaskFeedbackTempDTO bizTaskFeedbackTemp = null;
        long currentUserId = SecurityUtils.getCurrentUserId();
        LambdaQueryWrapper<BizTaskFeedbackTemp> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskFeedbackTemp::getCreateBy, currentUserId).orderByDesc(BizTaskFeedbackTemp::getCreateTime)
                .eq(BizTaskFeedbackTemp::getBizTaskDecompose,dto.getBizTaskDecompose());

        List<BizTaskFeedbackTemp> result = bizTaskFeedbackTempMapper.selectList(queryWrapper);
        if(!result.isEmpty()){
             bizTaskFeedbackTemp = BeanConvertUtils.copyProperties(result.get(0) , BizTaskFeedbackTempDTO.class);
        }
        return bizTaskFeedbackTemp;
    }


}
