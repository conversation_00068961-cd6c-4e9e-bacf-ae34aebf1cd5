package com.ctsi.huaihua.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.huaihua.entity.BizPunishScoreRecord;
import com.ctsi.huaihua.entity.dto.BizPunishScoreRecordDTO;
import com.ctsi.huaihua.service.IBizPunishScoreRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizPunishScoreRecord")
@Api(value = "惩分记录表", tags = "惩分记录表接口")
public class BizPunishScoreRecordController extends BaseController {

    private static final String ENTITY_NAME = "bizPunishScoreRecord";

    @Autowired
    private IBizPunishScoreRecordService bizPunishScoreRecordService;



    /**
     *  新增奖分记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizPunishScoreRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增奖分记录表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizPunishScoreRecord.add')")
    public ResultVO createBatch(@RequestBody List<BizPunishScoreRecordDTO> bizPunishScoreRecordList) {
       Boolean  result = bizPunishScoreRecordService.insertBatch(bizPunishScoreRecordList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizPunishScoreRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增奖分记录表数据")
  //  @PreAuthorize("@permissionService.hasPermi('cscp.bizPunishScoreRecord.add')")
    public ResultVO<BizPunishScoreRecordDTO> create(@RequestBody BizPunishScoreRecordDTO bizPunishScoreRecordDTO)  {
        BizPunishScoreRecordDTO result = bizPunishScoreRecordService.create(bizPunishScoreRecordDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizPunishScoreRecord.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新奖分记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizPunishScoreRecord.update')")
    public ResultVO update(@RequestBody BizPunishScoreRecordDTO bizPunishScoreRecordDTO) {
	    Assert.notNull(bizPunishScoreRecordDTO.getId(), "general.IdNotNull");
        int count = bizPunishScoreRecordService.update(bizPunishScoreRecordDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除奖分记录表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizPunishScoreRecord.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizPunishScoreRecord.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizPunishScoreRecordService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizPunishScoreRecordDTO bizPunishScoreRecordDTO = bizPunishScoreRecordService.findOne(id);
        return ResultVO.success(bizPunishScoreRecordDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizPunishScoreRecordPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizPunishScoreRecordDTO>> queryBizPunishScoreRecordPage(BizPunishScoreRecordDTO bizPunishScoreRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizPunishScoreRecordService.queryListPage(bizPunishScoreRecordDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizPunishScoreRecord")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizPunishScoreRecordDTO>> queryBizPunishScoreRecord(BizPunishScoreRecordDTO bizPunishScoreRecordDTO) {
       List<BizPunishScoreRecordDTO> list = bizPunishScoreRecordService.queryList(bizPunishScoreRecordDTO);
       return ResultVO.success(new ResResult<BizPunishScoreRecordDTO>(list));
   }

    /**
     *  市政府办分页展示
     */
    @PostMapping("/queryBizMunicipalUnitPage")
    @ApiOperation(value = "市政府办分页展示", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizPunishScoreRecordDTO>> queryBizMunicipalUnitPage(@RequestBody BizPunishScoreRecordDTO bizPunishScoreRecordDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizPunishScoreRecordDTO.getCurrentPage());
        basePageForm.setPageSize(bizPunishScoreRecordDTO.getPageSize());
        return ResultVO.success(bizPunishScoreRecordService.queryBizMunicipalUnitPage(bizPunishScoreRecordDTO, basePageForm));
    }

    /**
     * 查询未审核惩分申请数量
     * @return
     */
    @GetMapping("/queryPunishScoreRecordNeedReviewAmount")
    @ApiOperation(value = "查询未审核惩分申请数量", notes = "传入参数")
    public ResultVO<Integer> queryPunishScoreRecordNeedReviewAmount(){
        Integer count = bizPunishScoreRecordService.queryPunishScoreRecordNeedReviewAmount();
        return ResultVO.success(count);
    }

    /**
     * 审核通过与驳回
     * @param bizPunishScoreRecord
     * @return
     */
    @PostMapping("/approvedBizPunishScoreRecord")
    @ApiOperation(value = "审核通过与驳回", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "处罚分审核")
    public ResultVO<Boolean> approvedBizPunishScoreRecord(@RequestBody BizPunishScoreRecordDTO bizPunishScoreRecord){
        Boolean result = bizPunishScoreRecordService.approvedBizPunishScoreRecord(bizPunishScoreRecord);
        return ResultVO.success(result);
    }

    /**
     * 非个人查看减分详情
     * @param id
     * @return
     */
    @GetMapping("/getPunishScoreRecordDetail/{id}")
    @ApiOperation(value = "非个人查看减分详情",notes = "传入参数")
    public ResultVO<BizPunishScoreRecordDTO> getPunishScoreRecordDetail(@PathVariable Long id){
        BizPunishScoreRecordDTO result = bizPunishScoreRecordService.getPunishScoreRecordDetail(id,0);
        return ResultVO.success(result);
    }

    /**
     * 个人查看减分详情
     * @param id
     * @return
     */
    @GetMapping("/getPunishScoreRecordDetailByUser/{id}")
    @ApiOperation(value = "个人查看减分详情",notes = "传入参数")
    public ResultVO<BizPunishScoreRecordDTO> getPunishScoreRecordDetailByUser(@PathVariable Long id){
        BizPunishScoreRecordDTO result = bizPunishScoreRecordService.getPunishScoreRecordDetail(id,1);
        return ResultVO.success(result);
    }

    /**
     * 个人分页展示
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @PostMapping("/queryPunishScoreRecordPage")
    @ApiOperation(value = "个人分页展示",notes = "传入参数")
    public ResultVO<PageResult<BizPunishScoreRecordDTO>> queryPunishScoreRecordPage(@RequestBody BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm){
        basePageForm.setCurrentPage(entityDTO.getCurrentPage());
        basePageForm.setPageSize(entityDTO.getPageSize());
        return ResultVO.success(bizPunishScoreRecordService.queryPunishScoreRecordPage(entityDTO, basePageForm));
    }

    /**
     * 督查室列表查询
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @PostMapping("/queryBizSuperviseOfficePage")
    @ApiOperation(value = "督查室列表查询",notes = "传入参数")
    public ResultVO<PageResult<BizPunishScoreRecordDTO>> queryBizSuperviseOfficePage(@RequestBody BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm){
        basePageForm.setCurrentPage(entityDTO.getCurrentPage());
        basePageForm.setPageSize(entityDTO.getPageSize());
        return ResultVO.success(bizPunishScoreRecordService.queryBizSuperviseOfficePage(entityDTO, basePageForm));
    }

    /**
     * 市直单位列表查询
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @PostMapping("/queryBizStraightUnitPage")
    @ApiOperation(value = "市直单位列表查询",notes = "传入参数")
    public ResultVO<PageResult<BizPunishScoreRecordDTO>> queryBizStraightUnitPage(@RequestBody BizPunishScoreRecordDTO entityDTO, BasePageForm basePageForm){
        basePageForm.setCurrentPage(entityDTO.getCurrentPage());
        basePageForm.setPageSize(entityDTO.getPageSize());
        return ResultVO.success(bizPunishScoreRecordService.queryBizStraightUnitPage(entityDTO, basePageForm));
    }

    /**
     * 更新小红点
     * @param id     个人查看为惩分用户副表的主键id，其他为主表的主键id
     * @param type   小红点更新类型 0:市政府办  1:市直单位 2:个人
     * @return
     */
    @GetMapping("/updateRed")
    @ApiOperation(value = "更新小红点",notes = "传入参数")
    public ResultVO<Boolean> updateRed(Long id,Integer type){
        Boolean result = bizPunishScoreRecordService.updateRed(id,type);
        return ResultVO.success(result);
    }
}
