package com.ctsi.huaihua.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.huaihua.entity.BizScoreLeader;
import com.ctsi.huaihua.entity.dto.BizScoreLeaderDTO;
import com.ctsi.huaihua.service.IBizScoreLeaderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizScoreLeader")
@Api(value = "日常打分领导管理表", tags = "日常打分领导管理表接口")
public class BizScoreLeaderController extends BaseController {

    private static final String ENTITY_NAME = "bizScoreLeader";

    @Autowired
    private IBizScoreLeaderService bizScoreLeaderService;



    /**
     *  新增日常打分领导管理表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizScoreLeader.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增日常打分领导管理表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreLeader.add')")
    public ResultVO createBatch(@RequestBody List<BizScoreLeaderDTO> bizScoreLeaderList) {
       Boolean  result = bizScoreLeaderService.insertBatch(bizScoreLeaderList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizScoreLeader.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增日常打分领导管理表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreLeader.add')")
    public ResultVO<BizScoreLeaderDTO> create(@RequestBody BizScoreLeaderDTO bizScoreLeaderDTO)  {
        BizScoreLeaderDTO result = bizScoreLeaderService.create(bizScoreLeaderDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizScoreLeader.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新日常打分领导管理表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreLeader.update')")
    public ResultVO update(@RequestBody BizScoreLeaderDTO bizScoreLeaderDTO) {
	    Assert.notNull(bizScoreLeaderDTO.getId(), "general.IdNotNull");
        int count = bizScoreLeaderService.update(bizScoreLeaderDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除日常打分领导管理表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizScoreLeader.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizScoreLeader.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizScoreLeaderService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizScoreLeaderDTO bizScoreLeaderDTO = bizScoreLeaderService.findOne(id);
        return ResultVO.success(bizScoreLeaderDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizScoreLeaderPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizScoreLeaderDTO>> queryBizScoreLeaderPage(BizScoreLeaderDTO bizScoreLeaderDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizScoreLeaderService.queryListPage(bizScoreLeaderDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizScoreLeader")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizScoreLeaderDTO>> queryBizScoreLeader(BizScoreLeaderDTO bizScoreLeaderDTO) {
       List<BizScoreLeaderDTO> list = bizScoreLeaderService.queryList(bizScoreLeaderDTO);
       return ResultVO.success(new ResResult<BizScoreLeaderDTO>(list));
   }

}
