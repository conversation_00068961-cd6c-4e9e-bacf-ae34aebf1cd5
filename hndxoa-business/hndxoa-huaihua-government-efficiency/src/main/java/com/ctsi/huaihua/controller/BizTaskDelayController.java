package com.ctsi.huaihua.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.huaihua.entity.dto.BizTaskDecomposeDTO;
import com.ctsi.huaihua.entity.dto.BizTaskDelayDTO;
import com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO;
import com.ctsi.huaihua.service.IBizTaskDelayService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizTaskDelay")
@Api(value = "任务延期表", tags = "任务延期表接口")
public class BizTaskDelayController extends BaseController {

    private static final String ENTITY_NAME = "bizTaskDelay";

    @Autowired
    private IBizTaskDelayService bizTaskDelayService;



    /**
     *  新增任务延期表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizTaskDelay.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务延期表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDelay.add')")
    public ResultVO createBatch(@RequestBody List<BizTaskDelayDTO> bizTaskDelayList) {
       Boolean  result = bizTaskDelayService.insertBatch(bizTaskDelayList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizTaskDelay.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务延期表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDelay.add')")
    public ResultVO<BizTaskDelayDTO> create(@RequestBody BizTaskDelayDTO bizTaskDelayDTO)  {
        BizTaskDelayDTO result = bizTaskDelayService.create(bizTaskDelayDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizTaskDelay.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新任务延期表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizTaskDelay.update')")
    public ResultVO update(@RequestBody BizTaskDelayDTO bizTaskDelayDTO) {
	    Assert.notNull(bizTaskDelayDTO.getId(), "general.IdNotNull");
        int count = bizTaskDelayService.update(bizTaskDelayDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除任务延期表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.feedbackManagment.delayedPendingRecall)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.feedbackManagment.delayedPendingRecall')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizTaskDelayService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizTaskDelayDTO bizTaskDelayDTO = bizTaskDelayService.findOne(id);
        return ResultVO.success(bizTaskDelayDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizTaskDelayPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskDelayDTO>> queryBizTaskDelayPage(BizTaskDelayDTO bizTaskDelayDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizTaskDelayService.queryListPage(bizTaskDelayDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizTaskDelay")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizTaskDelayDTO>> queryBizTaskDelay(BizTaskDelayDTO bizTaskDelayDTO) {
       List<BizTaskDelayDTO> list = bizTaskDelayService.queryList(bizTaskDelayDTO);
       return ResultVO.success(new ResResult<BizTaskDelayDTO>(list));
   }


    /**
     * 根据子任务id查询数据
     */
    @GetMapping("/queryBizTaskDelayByTaskDecompose")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizTaskDelayDTO>> queryBizTaskDelayByTaskDecompose(@RequestParam Long bizTaskDecompose ) {
        List<BizTaskDelayDTO> list = bizTaskDelayService.queryBizTaskDelayByTaskDecompose(bizTaskDecompose);
        return ResultVO.success(new ResResult<BizTaskDelayDTO>(list));
    }

    @PostMapping("/approvedBizTaskDelayBy")
    @ApiOperation(value = "延期申请审核通过", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "延期申请审核通过")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Boolean> approvedBizTaskDelay(@RequestBody BizTaskDelayDTO bizTaskDelayDTO) {
        Boolean bool = bizTaskDelayService.approvedBizTaskDelay(bizTaskDelayDTO);
        return ResultVO.success(bool);
    }


    /**
     * 查询多条数据.分页
     */
    @PostMapping("/queryPageDelayList")
    @ApiOperation(value = "查询延期申请列表", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskSupervisionDTO>> queryPageDelayList(@RequestBody BizTaskSupervisionDTO bizTaskSupervisionDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionDTO.getPageSize());
        PageResult<BizTaskSupervisionDTO> data = bizTaskDelayService.queryPageDelayList(bizTaskSupervisionDTO, basePageForm);
        return ResultVO.success(data);
    }

    @GetMapping("/queryDelayDetail")
    @ApiOperation(value = "查询延期详情", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<BizTaskDecomposeDTO> queryDelayDetail(BizTaskDelayDTO bizTaskDelayDTO) {
        BizTaskDecomposeDTO bizTaskDecomposeDTO = bizTaskDelayService.queryDelayDetail(bizTaskDelayDTO);
        return ResultVO.success(bizTaskDecomposeDTO);
    }

    @GetMapping("/queryAndReadDelayDetail")
    @ApiOperation(value = "查询延期详情(会修改延期申请为已阅状态)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<BizTaskDecomposeDTO> queryAndReadDelayDetail(BizTaskDelayDTO bizTaskDelayDTO) {
        BizTaskDecomposeDTO bizTaskDecomposeDTO = bizTaskDelayService.queryAndReadDelayDetail(bizTaskDelayDTO);
        return ResultVO.success(bizTaskDecomposeDTO);
    }


    /**
     * 查询延期申请列表(会修改延期申请为已阅状态)
     */
    @PostMapping("/queryAndReadPageDelayList")
    @ApiOperation(value = "查询延期申请列表(会修改延期申请为已阅状态)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizTaskSupervisionDTO>> queryAndReadPageDelayList(@RequestBody BizTaskSupervisionDTO bizTaskSupervisionDTO, BasePageForm basePageForm) {
        basePageForm.setCurrentPage(bizTaskSupervisionDTO.getCurrentPage());
        basePageForm.setPageSize(bizTaskSupervisionDTO.getPageSize());
        PageResult<BizTaskSupervisionDTO> data = bizTaskDelayService.queryAndReadPageDelayList(bizTaskSupervisionDTO, basePageForm);
        return ResultVO.success(data);
    }

    @GetMapping("/queryDelayNeedReviewAmount")
    @ApiOperation(value = "需要延期审核数量", notes = "传入参数")
    public ResultVO<Integer> queryDelayNeedReviewAmount() {
        BizTaskDelayDTO bizTaskDelayDTO = new BizTaskDelayDTO();
        bizTaskDelayDTO.setReviewerId(String.valueOf(SecurityUtils.getCurrentUserId()));
        bizTaskDelayDTO.setHasReviewer(0);
        Integer count = bizTaskDelayService.queryDelayNeedReviewAmount(bizTaskDelayDTO);
        return ResultVO.success(count);
    }

    @GetMapping("/queryDelayHasReviewedAmount")
    @ApiOperation(value = "提醒延期申请通过数量", notes = "传入参数")
    public ResultVO<Integer> queryDelayHasReviewedAmount() {
        BizTaskDelayDTO bizTaskDelayDTO = new BizTaskDelayDTO();
        bizTaskDelayDTO.setCreateBy(SecurityUtils.getCurrentUserId());
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        bizTaskDelayDTO.setHasReviewerList(list);
        bizTaskDelayDTO.setHasRead(0);
        Integer count = bizTaskDelayService.queryDelayHasReviewedAmount(bizTaskDelayDTO);
        return ResultVO.success(count);
    }

}
