package com.ctsi.huaihua.service;

import com.ctsi.huaihua.entity.dto.BizTaskFeedbackTempDTO;
import com.ctsi.huaihua.entity.BizTaskFeedbackTemp;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 任务成果反馈暂存表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public interface IBizTaskFeedbackTempService extends SysBaseServiceI<BizTaskFeedbackTemp> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizTaskFeedbackTempDTO> queryListPage(BizTaskFeedbackTempDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizTaskFeedbackTempDTO> queryList(BizTaskFeedbackTempDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizTaskFeedbackTempDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizTaskFeedbackTempDTO create(BizTaskFeedbackTempDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizTaskFeedbackTempDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizTaskFeedbackTempId
     * @param code
     * @return
     */
    boolean existByBizTaskFeedbackTempId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizTaskFeedbackTempDTO> dataList);


    /**
     * 获得自己暂存的数据
     * @return
     * @param dto
     */
    BizTaskFeedbackTempDTO getTempFeedback(BizTaskFeedbackTempDTO dto);
}
