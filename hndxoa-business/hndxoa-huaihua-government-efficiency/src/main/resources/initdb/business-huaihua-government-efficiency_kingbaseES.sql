CREATE TABLE "public"."biz_task_decompose" (
                                               "id" int8 NOT NULL,
                                               CREATE_BY int8 NULL,
                                               CREATE_TIME timestamp(6) NULL,
                                               UPDATE_BY int8 NULL,
                                               "department_id" int8 NULL,
                                               UPDATE_TIME timestamp(6) NULL,
                                               "company_id" int8 NULL,
                                               "tenant_id" int8 NULL,
                                               "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"deleted" int4 NULL,
	"duty_people_id" int8 NULL,
	"duty_people_name" varchar(30 char) NULL,
	"duty_people_department_name" varchar(30 char) NULL,
	"duty_people_company_name" varchar(50 char) NULL,
	"duty_people_post_name" varchar(50 char) NULL,
	"duty_people_department_id" int8 NULL,
	"duty_people_company_id" int8 NULL,
	"contact_people_id" int8 NULL,
	"contact_people_name" varchar(30 char) NULL,
	"contact_people_department_name" varchar(30 char) NULL,
	"contact_people_company_name" varchar(50 char) NULL,
	"contact_people_post_name" varchar(50 char) NULL,
	"contact_people_department_id" int8 NULL,
	"contact_people_company_id" int8 NULL,
	"content" varchar(500 char) NULL,
	"start_date" date NULL,
	"end_date" date NULL,
	"receive_time" timestamp(6) NULL,
	"degree_difficulty" varchar(5 char) NULL,
	"task_description" varchar(500 char) NULL,
	"sms_reminder" varchar(5 char) NULL DEFAULT '0'::varchar,
	"has_finish" int4 NULL DEFAULT 0,
	"has_sign" int4 NULL DEFAULT 0,
	"has_transfer" int4 NULL DEFAULT 0,
	"sub_domain_id" varchar(255 char) NULL,
	"sub_tbl_fk" int8 NULL,
	"sub_tbl_num" int4 NULL,
	"duty_people" varchar(800 char) NULL,
	"sign_people_id" int8 NULL,
	"sign_people_name" varchar(255 char) NULL,
	"group_name" varchar(100 char) NULL,
	"has_finish_time" timestamp(6) NULL,
	"has_feedback" int4 NULL DEFAULT 0,
	"new_feedback_sign" int4 NULL DEFAULT 0,
	"new_finish_sign" int4 NULL DEFAULT 0,
	"new_feedback_time" timestamp(6) NULL,
	"task_delay_status" int4 NULL DEFAULT 0,
	"sms_send_time" timestamp(6) NULL,
	"duty_people_phone" varchar(11 char) NULL,
	"contact_people_phone" varchar(11 char) NULL,
	"sign_people_phone" varchar(11 char) NULL,
	"sign_people_company_name" varchar(50 char) NULL,
	"host_company" varchar(2 char) NULL DEFAULT '0'::varchar,
	"preview" int4 NULL DEFAULT 0,
	CONSTRAINT "biz_task_decompose_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_decompose"."preview" IS '是否预览（0：未预览 1：已预览），用于减分';
COMMENT ON COLUMN "public"."biz_task_decompose"."host_company" IS '是否主办单位，1表示主办单位，0表示协办单位';
COMMENT ON COLUMN "public"."biz_task_decompose"."sign_people_company_name" IS '签收人的单位';
COMMENT ON COLUMN "public"."biz_task_decompose"."sign_people_phone" IS '操作员手机号';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_phone" IS '联络员的电话';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_phone" IS '责任人电话';
COMMENT ON COLUMN "public"."biz_task_decompose"."sms_send_time" IS '(已发)短信提醒日期';
COMMENT ON COLUMN "public"."biz_task_decompose"."task_delay_status" IS '延期申请状态 0 无申请, 1 有申请(暂未通过), 2 申请通过, 3通过并手动调整延期时间 ';
COMMENT ON COLUMN "public"."biz_task_decompose"."new_feedback_time" IS '最新反馈时间';
COMMENT ON COLUMN "public"."biz_task_decompose"."new_finish_sign" IS '是否有最新办结的任务标记:0：没有 , 1 有';
COMMENT ON COLUMN "public"."biz_task_decompose"."new_feedback_sign" IS '是否有最新反馈的成果标记 0：没有 , 1 有';
COMMENT ON COLUMN "public"."biz_task_decompose"."has_feedback" IS '是否有反馈记录 0：没有 , 1 有';
COMMENT ON COLUMN "public"."biz_task_decompose"."has_finish_time" IS '办结时间';
COMMENT ON COLUMN "public"."biz_task_decompose"."group_name" IS '群名称';
COMMENT ON COLUMN "public"."biz_task_decompose"."sign_people_name" IS '签收人的姓名';
COMMENT ON COLUMN "public"."biz_task_decompose"."sign_people_id" IS '签收人的id';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people" IS '责任人详细信息，表单展示用';
COMMENT ON COLUMN "public"."biz_task_decompose"."sub_tbl_num" IS '序号';
COMMENT ON COLUMN "public"."biz_task_decompose"."sub_tbl_fk" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "public"."biz_task_decompose"."sub_domain_id" IS '子表的域id';
COMMENT ON COLUMN "public"."biz_task_decompose"."has_transfer" IS '0：未转办 1 已转办';
COMMENT ON COLUMN "public"."biz_task_decompose"."has_sign" IS '0：未签收 1 已签收 2 撤回';
COMMENT ON COLUMN "public"."biz_task_decompose"."has_finish" IS '0：未办结 1 办结';
COMMENT ON COLUMN "public"."biz_task_decompose"."sms_reminder" IS '短信提醒方式，对应数据字典sms_reminder';
COMMENT ON COLUMN "public"."biz_task_decompose"."task_description" IS '任务概叙';
COMMENT ON COLUMN "public"."biz_task_decompose"."degree_difficulty" IS '难度系数';
COMMENT ON COLUMN "public"."biz_task_decompose"."receive_time" IS '任务接收时间';
COMMENT ON COLUMN "public"."biz_task_decompose"."end_date" IS '任务结束时间';
COMMENT ON COLUMN "public"."biz_task_decompose"."start_date" IS '任务开始时间';
COMMENT ON COLUMN "public"."biz_task_decompose"."content" IS '任务内容';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_company_id" IS '联络员的单位id';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_department_id" IS '联络员的部门id';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_post_name" IS '联络员的职务';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_company_name" IS '联络员的单位名称';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_department_name" IS '联络员的部门名称';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_name" IS '联络员的姓名';
COMMENT ON COLUMN "public"."biz_task_decompose"."contact_people_id" IS '联络员的id';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_company_id" IS '责任人的单位id';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_department_id" IS '责任人的部门id';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_post_name" IS '责任人的职务';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_company_name" IS '责任人的单位名称';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_department_name" IS '责任人的部门名称';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_name" IS '责任人的姓名';
COMMENT ON COLUMN "public"."biz_task_decompose"."duty_people_id" IS '责任人的id';
COMMENT ON COLUMN "public"."biz_task_decompose"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "public"."biz_task_decompose"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_decompose"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_decompose"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_decompose"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."biz_task_decompose"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_decompose"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."biz_task_decompose"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_decompose"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_decompose"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_decompose"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_decompose" IS '任务分解表';


CREATE TABLE "public"."biz_task_delay" (
                                           "id" int8 NOT NULL,
                                           CREATE_BY int8 NULL,
                                           CREATE_TIME timestamp(6) NULL,
                                           UPDATE_BY int8 NULL,
                                           "department_id" int8 NULL,
                                           UPDATE_TIME timestamp(6) NULL,
                                           "company_id" int8 NULL,
                                           "tenant_id" int8 NULL,
                                           "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"deleted" int4 NULL,
	"biz_task_supervision_id" int8 NULL,
	"delay_content" varchar(500 char) NULL,
	"delay_reason" varchar(500 char) NULL,
	"delay_end_date" date NULL,
	"delay_before_content" varchar(500 char) NULL,
	"delay_before_end_date" date NULL,
	"reviewer_id" int8 NULL,
	"reviewer_department_id" int8 NULL,
	"reviewer_company_id" int8 NULL,
	"has_reviewer" int4 NULL DEFAULT 0,
	"biz_task_decompose" int8 NULL,
	"audit_id" int8 NULL,
	"audit_name" varchar(255 char) NULL,
	"audit_time" timestamp(6) NULL,
	"has_read" int4 NULL,
	"manual_delay_end_date" date NULL,
	CONSTRAINT "biz_task_delay_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_delay"."manual_delay_end_date" IS '手动调整的延期截至时间';
COMMENT ON COLUMN "public"."biz_task_delay"."has_read" IS '0：未阅 1 已阅';
COMMENT ON COLUMN "public"."biz_task_delay"."audit_time" IS '审核时间';
COMMENT ON COLUMN "public"."biz_task_delay"."audit_name" IS '审核人的姓名';
COMMENT ON COLUMN "public"."biz_task_delay"."audit_id" IS '审核人的id';
COMMENT ON COLUMN "public"."biz_task_delay"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "public"."biz_task_delay"."has_reviewer" IS '0：未审核 1 审核通过 2 通过并调整延期时间';
COMMENT ON COLUMN "public"."biz_task_delay"."reviewer_company_id" IS '接收审核人的单位id';
COMMENT ON COLUMN "public"."biz_task_delay"."reviewer_department_id" IS '接收审核人的部门id';
COMMENT ON COLUMN "public"."biz_task_delay"."reviewer_id" IS '接收审核人的的id';
COMMENT ON COLUMN "public"."biz_task_delay"."delay_before_end_date" IS '申请延期之前的截至时间';
COMMENT ON COLUMN "public"."biz_task_delay"."delay_before_content" IS '申请延期之前的内容';
COMMENT ON COLUMN "public"."biz_task_delay"."delay_end_date" IS '申请延期的截至时间';
COMMENT ON COLUMN "public"."biz_task_delay"."delay_reason" IS '申请延期的理由';
COMMENT ON COLUMN "public"."biz_task_delay"."delay_content" IS '申请延期的内容';
COMMENT ON COLUMN "public"."biz_task_delay"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "public"."biz_task_delay"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "public"."biz_task_delay"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_delay"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_delay"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_delay"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."biz_task_delay"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_delay"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."biz_task_delay"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_delay"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_delay"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_delay"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_delay" IS '任务延期表';
CREATE INDEX biz_task_delay_indexTaskDelayDecompose ON biz_task_delay USING btree (biz_task_decompose);
CREATE INDEX biz_task_delay_indexTaskDelaySupervisionId ON biz_task_delay USING btree (biz_task_supervision_id);


CREATE TABLE "public"."biz_task_feedback" (
                                              "id" int8 NOT NULL,
                                              CREATE_BY int8 NULL,
                                              CREATE_TIME timestamp(6) NULL,
                                              UPDATE_BY int8 NULL,
                                              "department_id" int8 NULL,
                                              UPDATE_TIME timestamp(6) NULL,
                                              "company_id" int8 NULL,
                                              "tenant_id" int8 NULL,
                                              "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"deleted" int4 NULL,
	"biz_task_supervision_id" int8 NULL,
	"achievement_description" varchar(500 char) NULL,
	"completion" varchar(5 char) NULL,
	"reviewer_id" int8 NULL,
	"reviewer_department_id" int8 NULL,
	"reviewer_company_id" int8 NULL,
	"has_reviewer" int4 NULL,
	"biz_task_decompose" int8 NULL,
	"annex" varchar(1000 char) NULL,
	"department_name" varchar(32 char) NULL,
	"mobile" varchar(11 char) NULL,
	"company_name" varchar(32 char) NULL,
	"reviewer_time" timestamp(6) NULL,
	CONSTRAINT "biz_task_feedback_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_feedback"."reviewer_time" IS '审核时间';
COMMENT ON COLUMN "public"."biz_task_feedback"."company_name" IS '拟稿人单位名称';
COMMENT ON COLUMN "public"."biz_task_feedback"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "public"."biz_task_feedback"."department_name" IS '拟稿人单位';
COMMENT ON COLUMN "public"."biz_task_feedback"."annex" IS '附件';
COMMENT ON COLUMN "public"."biz_task_feedback"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "public"."biz_task_feedback"."has_reviewer" IS '0：未审核 1 审核通过';
COMMENT ON COLUMN "public"."biz_task_feedback"."reviewer_company_id" IS '审核人的单位id';
COMMENT ON COLUMN "public"."biz_task_feedback"."reviewer_department_id" IS '审核人的部门id';
COMMENT ON COLUMN "public"."biz_task_feedback"."reviewer_id" IS '审核人的id';
COMMENT ON COLUMN "public"."biz_task_feedback"."completion" IS '完成情况的百分比';
COMMENT ON COLUMN "public"."biz_task_feedback"."achievement_description" IS '任务成果描述';
COMMENT ON COLUMN "public"."biz_task_feedback"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "public"."biz_task_feedback"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "public"."biz_task_feedback"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_feedback"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_feedback"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_feedback"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."biz_task_feedback"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_feedback"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."biz_task_feedback"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_feedback"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_feedback"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_feedback"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_feedback" IS '任务成果反馈表';
CREATE INDEX biz_task_feedback_indexTaskFeedDecompose ON biz_task_feedback USING btree (biz_task_decompose);
CREATE INDEX biz_task_feedback_indexTaskFeedbackSupervisionId ON biz_task_feedback USING btree (biz_task_supervision_id);


CREATE TABLE "public"."biz_task_feedback_temp" (
                                                   "id" int8 NOT NULL,
                                                   CREATE_BY int8 NULL,
                                                   CREATE_TIME timestamp(6) NULL,
                                                   UPDATE_BY int8 NULL,
                                                   "department_id" int8 NULL,
                                                   UPDATE_TIME timestamp(6) NULL,
                                                   "company_id" int8 NULL,
                                                   "tenant_id" int8 NULL,
                                                   "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"deleted" int4 NULL,
	"biz_task_supervision_id" int8 NULL,
	"achievement_description" varchar(500 char) NULL,
	"completion" varchar(5 char) NULL,
	"reviewer_id" int8 NULL,
	"reviewer_department_id" int8 NULL,
	"reviewer_company_id" int8 NULL,
	"has_reviewer" int4 NULL,
	"biz_task_decompose" int8 NULL,
	"annex" varchar(1000 char) NULL,
	"department_name" varchar(32 char) NULL,
	"mobile" varchar(11 char) NULL,
	"company_name" varchar(32 char) NULL,
	"reviewer_time" timestamp(6) NULL,
	CONSTRAINT "biz_task_feedback_temp_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."reviewer_time" IS '审核时间';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."company_name" IS '拟稿人单位名称';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."department_name" IS '拟稿人单位';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."annex" IS '附件';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."has_reviewer" IS '0：未审核 1 审核通过';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."reviewer_company_id" IS '审核人的单位id';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."reviewer_department_id" IS '审核人的部门id';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."reviewer_id" IS '审核人的id';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."completion" IS '完成情况的百分比';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."achievement_description" IS '任务成果描述';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_feedback_temp"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_feedback_temp" IS '任务成果反馈暂存表';
CREATE INDEX biz_task_feedback_temp_indexTaskFeedDecompose ON biz_task_feedback_temp USING btree (biz_task_decompose);
CREATE INDEX biz_task_feedback_temp_indexTaskFeedbackSupervisionId ON biz_task_feedback_temp USING btree (biz_task_supervision_id);


CREATE TABLE "public"."biz_task_leader_liaison" (
                                                    "id" int8 NOT NULL,
                                                    "liaison_man_id" int8 NULL,
                                                    "liaison_man_name" varchar(20 char) NULL,
	"liaison_man_unit_id" int8 NULL,
	"liaison_man_unit_name" varchar(255 char) NULL,
	"liaison_man_branch_id" int8 NULL,
	"liaison_man_branch_name" varchar(255 char) NULL,
	"liaison_man_telephone" varchar(20 char) NULL,
	"leader_id" int8 NULL,
	"leader_name" varchar(20 char) NULL,
	"leader_unit_id" int8 NULL,
	"leader_unit_name" varchar(255 char) NULL,
	"leader_branch_id" int8 NULL,
	"leader_branch_name" varchar(255 char) NULL,
	"leader_telephone" varchar(20 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "biz_task_leader_liaison_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."leader_telephone" IS '领导手机号码';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."leader_branch_name" IS '领导部门名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."leader_branch_id" IS '领导部门id';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."leader_unit_name" IS '领导单位名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."leader_unit_id" IS '领导单位id';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."leader_name" IS '领导名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."leader_id" IS '领导id';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."liaison_man_telephone" IS '联络员手机号码';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."liaison_man_branch_name" IS '联络人部门名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."liaison_man_branch_id" IS '联络人部门id';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."liaison_man_unit_name" IS '联络人单位名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."liaison_man_unit_id" IS '联络人单位id';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."liaison_man_name" IS '联络员名称';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."liaison_man_id" IS '联络员id';
COMMENT ON COLUMN "public"."biz_task_leader_liaison"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_leader_liaison" IS '任务领导对应的联络员表';


CREATE TABLE "public"."biz_task_level" (
                                           "id" int8 NOT NULL,
                                           "type_name" varchar(255 char) NULL,
	"parent_id" int8 NULL DEFAULT 0,
	"order_by" int4 NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp NULL,
	"deleted" int4 NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	CONSTRAINT "biz_task_level_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_level"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_level"."company_id" IS '公司id';
COMMENT ON COLUMN "public"."biz_task_level"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."biz_task_level"."deleted" IS '逻辑删除 0：未删除 1：已删除';
COMMENT ON COLUMN "public"."biz_task_level"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_task_level"."update_name" IS '修改人姓名';
COMMENT ON COLUMN "public"."biz_task_level"."update_by" IS '修改人id';
COMMENT ON COLUMN "public"."biz_task_level"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_level"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_level"."create_by" IS '创建人id';
COMMENT ON COLUMN "public"."biz_task_level"."order_by" IS '排序号';
COMMENT ON COLUMN "public"."biz_task_level"."parent_id" IS '上级类型id，顶层为0';
COMMENT ON COLUMN "public"."biz_task_level"."type_name" IS '类型名称';
COMMENT ON COLUMN "public"."biz_task_level"."id" IS '主键';


CREATE TABLE "public"."biz_task_score_add" (
                                               "id" int8 NOT NULL,
                                               CREATE_BY int8 NULL,
                                               CREATE_TIME timestamp NULL,
                                               UPDATE_BY int8 NULL,
                                               "department_id" int8 NULL,
                                               UPDATE_TIME timestamp NULL,
                                               "company_id" int8 NULL,
                                               "tenant_id" int8 NULL,
                                               "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"deleted" int4 NULL,
	"biz_task_supervision_id" int8 NULL,
	"score_add_content" varchar(512 char) NULL,
	"score_add_reason" varchar(512 char) NULL,
	"score" float8 NULL,
	"reviewer_id" int8 NULL,
	"reviewer_department_id" int8 NULL,
	"reviewer_company_id" int8 NULL,
	"has_reviewer" int4 NULL DEFAULT 0,
	"refuse_reason" varchar(512 char) NULL,
	"biz_task_decompose" int8 NULL,
	"audit_id" int8 NULL,
	"audit_name" varchar(255 char) NULL,
	"audit_time" timestamp NULL,
	"has_read" int4 NULL,
	"audit_has_read" int4 NULL,
	CONSTRAINT "biz_task_score_add_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_score_add"."audit_has_read" IS '审核人是否未读0: 未查看  1:已查看';
COMMENT ON COLUMN "public"."biz_task_score_add"."has_read" IS '申请人是否未读0: 未查看  1:已查看';
COMMENT ON COLUMN "public"."biz_task_score_add"."audit_time" IS '审核时间';
COMMENT ON COLUMN "public"."biz_task_score_add"."audit_name" IS '审核人的姓名';
COMMENT ON COLUMN "public"."biz_task_score_add"."audit_id" IS '审核人的id';
COMMENT ON COLUMN "public"."biz_task_score_add"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "public"."biz_task_score_add"."refuse_reason" IS '驳回理由';
COMMENT ON COLUMN "public"."biz_task_score_add"."has_reviewer" IS '0：未审核 1 审核通过 2 驳回';
COMMENT ON COLUMN "public"."biz_task_score_add"."reviewer_company_id" IS '接收审核人的单位id';
COMMENT ON COLUMN "public"."biz_task_score_add"."reviewer_department_id" IS '接收审核人的部门id';
COMMENT ON COLUMN "public"."biz_task_score_add"."reviewer_id" IS '接收审核人的的id';
COMMENT ON COLUMN "public"."biz_task_score_add"."score" IS '申请加分的分数';
COMMENT ON COLUMN "public"."biz_task_score_add"."score_add_reason" IS '申请加分的理由';
COMMENT ON COLUMN "public"."biz_task_score_add"."score_add_content" IS '申请加分的内容';
COMMENT ON COLUMN "public"."biz_task_score_add"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "public"."biz_task_score_add"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "public"."biz_task_score_add"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_score_add"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_score_add"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_score_add"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."biz_task_score_add"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_score_add"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."biz_task_score_add"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_score_add"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_score_add"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_score_add"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_score_add" IS '任务加分表';
CREATE INDEX biz_task_score_add_indexTaskDelayDecompose ON biz_task_score_add USING btree (biz_task_decompose);
CREATE INDEX biz_task_score_add_indexTaskDelaySupervisionId ON biz_task_score_add USING btree (biz_task_supervision_id);


CREATE TABLE "public"."biz_task_score_sub" (
                                               "id" int8 NOT NULL,
                                               CREATE_BY int8 NULL,
                                               CREATE_TIME timestamp NULL,
                                               UPDATE_BY int8 NULL,
                                               "department_id" int8 NULL,
                                               UPDATE_TIME timestamp NULL,
                                               "company_id" int8 NULL,
                                               "tenant_id" int8 NULL,
                                               "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"deleted" int4 NULL,
	"biz_task_supervision_id" int8 NULL,
	"sore_sub_content" varchar(512 char) NULL,
	"sore_sub_reason" varchar(512 char) NULL,
	"sores" float8 NULL,
	"refuse_reason" varchar(512 char) NULL,
	"biz_task_decompose" int8 NULL,
	CONSTRAINT "biz_task_score_sub_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_score_sub"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "public"."biz_task_score_sub"."refuse_reason" IS '减回理由';
COMMENT ON COLUMN "public"."biz_task_score_sub"."sores" IS '申请减分的分数';
COMMENT ON COLUMN "public"."biz_task_score_sub"."sore_sub_reason" IS '申请减分的理由';
COMMENT ON COLUMN "public"."biz_task_score_sub"."sore_sub_content" IS '申请减分的内容';
COMMENT ON COLUMN "public"."biz_task_score_sub"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "public"."biz_task_score_sub"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "public"."biz_task_score_sub"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_score_sub"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_score_sub"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_score_sub"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."biz_task_score_sub"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_score_sub"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."biz_task_score_sub"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_score_sub"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_score_sub"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_score_sub"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_score_sub" IS '任务减分表';
CREATE INDEX biz_task_score_sub_indexTaskDelayDecompose ON biz_task_score_sub USING btree (biz_task_decompose);
CREATE INDEX biz_task_score_sub_indexTaskDelaySupervisionId ON biz_task_score_sub USING btree (biz_task_supervision_id);


CREATE TABLE "public"."biz_task_supervision" (
                                                 "id" int8 NOT NULL,
                                                 CREATE_BY int8 NULL,
                                                 CREATE_TIME timestamp(6) NULL,
                                                 UPDATE_BY int8 NULL,
                                                 "department_id" int8 NULL,
                                                 UPDATE_TIME timestamp(6) NULL,
                                                 "company_id" int8 NULL,
                                                 "tenant_id" int8 NULL,
                                                 "create_name" varchar(32 char) NULL,
	"update_name" varchar(32 char) NULL,
	"deleted" int4 NULL,
	"title" varchar(255 char) NULL,
	"task_type" varchar(2 char) NULL,
	"degree_urgency" varchar(2 char) NULL,
	"task_source" varchar(2 char) NULL,
	"task_tag" varchar(10 char) NULL,
	"examine_method" varchar(10 char) NULL,
	"due_time" date NULL,
	"task_description" varchar(500 char) NULL,
	"annex" varchar(1 char) NULL,
	"has_publish" int4 NULL DEFAULT 0,
	"has_finish" int4 NULL DEFAULT 0,
	"department_name" varchar(50 char) NULL,
	"mobile" varchar(11 char) NULL,
	"company_name" varchar(255 char) NULL,
	"task_number" varchar(255 char) NULL,
	"task_level" int4 NULL,
	"task_add_type" int4 NULL DEFAULT 0,
	"task_transfer_id" int8 NULL,
	"duty_people" text NULL,
	"task_transfer_decompose_id" int8 NULL,
	"form_id" int8 NULL,
	"has_finish_time" timestamp(6) NULL,
	"ext1" varchar(255 char) NULL,
	"ext2" varchar(255 char) NULL,
	"ext3" varchar(255 char) NULL,
	"new_sup_feedback_sign" int4 NULL DEFAULT 0,
	"sup_has_feedback" int4 NULL DEFAULT 0,
	"inspector_task_level" varchar(50 char) NULL,
	"inspector_items" varchar(255 char) NULL,
	CONSTRAINT "biz_task_supervision_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_supervision"."inspector_items" IS '督查任务具体的事项对应biz_task_level';
COMMENT ON COLUMN "public"."biz_task_supervision"."inspector_task_level" IS '督查任务等级对应biz_task_level';
COMMENT ON COLUMN "public"."biz_task_supervision"."sup_has_feedback" IS '是否有反馈记录 0：没有, 1 有';
COMMENT ON COLUMN "public"."biz_task_supervision"."new_sup_feedback_sign" IS '对于最新反馈的成果标记 0：没有 , 1 有';
COMMENT ON COLUMN "public"."biz_task_supervision"."ext3" IS '任务转办显示责任人不能填不要实体';
COMMENT ON COLUMN "public"."biz_task_supervision"."ext2" IS '任务转办内容备注不要实体';
COMMENT ON COLUMN "public"."biz_task_supervision"."ext1" IS '任务转办开始时间备注，不要实体';
COMMENT ON COLUMN "public"."biz_task_supervision"."has_finish_time" IS '办结时间';
COMMENT ON COLUMN "public"."biz_task_supervision"."form_id" IS '表单的id';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_transfer_decompose_id" IS '任务转办过来后的分解的任务id';
COMMENT ON COLUMN "public"."biz_task_supervision"."duty_people" IS '责任人';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_transfer_id" IS '任务来自转办，转办的id';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_add_type" IS '任务新增的方式0表示新增 1表示转办';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_level" IS '任务层级，转办了多少级';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_number" IS '任务编号，中间：隔开，：之前为父编号，转办的以zb开头';
COMMENT ON COLUMN "public"."biz_task_supervision"."company_name" IS '拟稿人单位名称';
COMMENT ON COLUMN "public"."biz_task_supervision"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "public"."biz_task_supervision"."department_name" IS '拟稿人部门名称';
COMMENT ON COLUMN "public"."biz_task_supervision"."has_finish" IS '0：未办结 1 办结';
COMMENT ON COLUMN "public"."biz_task_supervision"."has_publish" IS '0 未发布暂存，1：发布  2：撤回';
COMMENT ON COLUMN "public"."biz_task_supervision"."annex" IS '附件';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_description" IS '任务概叙';
COMMENT ON COLUMN "public"."biz_task_supervision"."due_time" IS '截止时间，yyyy-mm-dd';
COMMENT ON COLUMN "public"."biz_task_supervision"."examine_method" IS '考核方式，对应数据字典ExamineMethod';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_tag" IS '任务标签，对应数据字典taskTag';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_source" IS '任务来源，对应数据字典taskSource';
COMMENT ON COLUMN "public"."biz_task_supervision"."degree_urgency" IS '任务紧急程度，对应数据字典taskDegreeUrgency';
COMMENT ON COLUMN "public"."biz_task_supervision"."task_type" IS '任务类型，对应数据字典taskType';
COMMENT ON COLUMN "public"."biz_task_supervision"."title" IS '任务名称，任务标题';
COMMENT ON COLUMN "public"."biz_task_supervision"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "public"."biz_task_supervision"."update_name" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_supervision"."create_name" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_supervision"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."biz_task_supervision"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "public"."biz_task_supervision"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "public"."biz_task_supervision"."department_id" IS '创建人部门';
COMMENT ON COLUMN "public"."biz_task_supervision"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "public"."biz_task_supervision"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."biz_task_supervision"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "public"."biz_task_supervision"."id" IS '主键';
COMMENT ON TABLE "public"."biz_task_supervision" IS '任务督查的主表';


CREATE TABLE "public"."biz_task_work_logs" (
                                               "id" int8 NOT NULL,
                                               "is_todo_task" int4 NULL DEFAULT 0,
                                               "todo_task_id" int8 NULL,
                                               "form_id" int8 NULL,
                                               "post" varchar(32 char) NULL,
	"work_source" varchar(200 char) NULL,
	"work_type" varchar(200 char) NULL,
	"content" varchar(500 char) NULL,
	"start_time" date NULL,
	"end_time" date NULL,
	"cost_time" float4 NULL,
	"annex" int4 NULL DEFAULT 0,
	"bpm_status" int4 NULL,
	"process_instance_id" int8 NULL,
	"create_time" timestamp NULL,
	"create_name" varchar(32 char) NULL,
	"create_by" int8 NULL,
	"company_id" int8 NULL,
	"department_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"department_name" varchar(32 char) NULL,
	"tenant_id" int8 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp NULL,
	"deleted" int4 NULL DEFAULT 0,
	CONSTRAINT "biz_task_work_logs_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_work_logs"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_task_work_logs"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_task_work_logs"."update_name" IS '修改人姓名';
COMMENT ON COLUMN "public"."biz_task_work_logs"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."biz_task_work_logs"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_task_work_logs"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."biz_task_work_logs"."company_name" IS '单位名称';
COMMENT ON COLUMN "public"."biz_task_work_logs"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_task_work_logs"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_task_work_logs"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_task_work_logs"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_task_work_logs"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_task_work_logs"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_task_work_logs"."bpm_status" IS '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停';
COMMENT ON COLUMN "public"."biz_task_work_logs"."annex" IS '附件：1表示有附件，0表示无附件';
COMMENT ON COLUMN "public"."biz_task_work_logs"."cost_time" IS '时长';
COMMENT ON COLUMN "public"."biz_task_work_logs"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."biz_task_work_logs"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."biz_task_work_logs"."content" IS '工作内容';
COMMENT ON COLUMN "public"."biz_task_work_logs"."work_type" IS '工作类型';
COMMENT ON COLUMN "public"."biz_task_work_logs"."work_source" IS '工作来源';
COMMENT ON COLUMN "public"."biz_task_work_logs"."post" IS '职务';
COMMENT ON COLUMN "public"."biz_task_work_logs"."form_id" IS '表单id';
COMMENT ON COLUMN "public"."biz_task_work_logs"."todo_task_id" IS '待办任务的id';
COMMENT ON COLUMN "public"."biz_task_work_logs"."is_todo_task" IS '是否为待办任务，1：表示待办任务，0：表示自建工作任务';
COMMENT ON COLUMN "public"."biz_task_work_logs"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_task_work_logs" IS '工作日志';


CREATE TABLE "public"."biz_task_work_logs_unstatistic" (
                                                           "id" int8 NOT NULL,
                                                           "user_id" int8 NULL,
                                                           "user_name" varchar(32 char) NULL,
	"create_time" timestamp NULL,
	"create_name" varchar(32 char) NULL,
	"create_by" int8 NULL,
	"company_id" int8 NULL,
	"department_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"department_name" varchar(32 char) NULL,
	"tenant_id" int8 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp NULL,
	"deleted" int4 NULL DEFAULT 0,
	CONSTRAINT "biz_task_work_logs_unstatistic_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."update_name" IS '修改人姓名';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."company_name" IS '单位名称';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."user_name" IS '用户名称';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."biz_task_work_logs_unstatistic"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_task_work_logs_unstatistic" IS '工作日志免统计的用户列表';
