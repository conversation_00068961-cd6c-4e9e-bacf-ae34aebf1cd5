CREATE TABLE `biz_task_decompose` (
                                      `id` bigint NOT NULL COMMENT '主键',
                                      `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                      `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                      `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                      `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                      `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                      `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                      `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                      `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                      `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                      `deleted` int DEFAULT NULL COMMENT '逻辑删除字段 0没删除 1表示删除',
                                      `duty_people_id` bigint DEFAULT NULL COMMENT '责任人的id',
                                      `duty_people_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '责任人的姓名',
                                      `duty_people_department_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '责任人的部门名称',
                                      `duty_people_company_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '责任人的单位名称',
                                      `duty_people_post_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '责任人的职务',
                                      `duty_people_department_id` bigint DEFAULT NULL COMMENT '责任人的部门id',
                                      `duty_people_company_id` bigint DEFAULT NULL COMMENT '责任人的单位id',
                                      `contact_people_id` bigint DEFAULT NULL COMMENT '联络员的id',
                                      `contact_people_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络员的姓名',
                                      `contact_people_department_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络员的部门名称',
                                      `contact_people_company_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络员的单位名称',
                                      `contact_people_post_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络员的职务',
                                      `contact_people_department_id` bigint DEFAULT NULL COMMENT '联络员的部门id',
                                      `contact_people_company_id` bigint DEFAULT NULL COMMENT '联络员的单位id',
                                      `content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务内容',
                                      `start_date` date DEFAULT NULL COMMENT '任务开始时间',
                                      `end_date` date DEFAULT NULL COMMENT '任务结束时间',
                                      `receive_time` datetime DEFAULT NULL COMMENT '任务接收时间',
                                      `degree_difficulty` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '难度系数',
                                      `task_description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务概叙',
                                      `sms_reminder` varchar(5) CHARACTER SET utf32 COLLATE utf32_general_ci DEFAULT '0' COMMENT '短信提醒方式，对应数据字典sms_reminder',
                                      `has_finish` int DEFAULT '0' COMMENT '0：未办结 1 办结',
                                      `has_sign` int DEFAULT '0' COMMENT '0：未签收 1 已签收 2 撤回',
                                      `has_transfer` int DEFAULT '0' COMMENT '0：未转办 1 已转办',
                                      `sub_domain_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '子表的域id',
                                      `sub_tbl_fk` bigint DEFAULT NULL COMMENT '任务表biz_task_supervision的主键',
                                      `sub_tbl_num` int DEFAULT NULL COMMENT '序号',
                                      `duty_people` varchar(800) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '责任人详细信息，表单展示用',
                                      `sign_people_id` bigint DEFAULT NULL COMMENT '签收人的id',
                                      `sign_people_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '签收人的姓名',
                                      `group_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '群名称',
                                      `has_finish_time` datetime DEFAULT NULL COMMENT '办结时间',
                                      `has_feedback` int DEFAULT '0' COMMENT '是否有反馈记录 0：没有 , 1 有',
                                      `new_feedback_sign` int DEFAULT '0' COMMENT '是否有最新反馈的成果标记 0：没有 , 1 有',
                                      `new_finish_sign` int DEFAULT '0' COMMENT '是否有最新办结的任务标记:0：没有 , 1 有',
                                      `new_feedback_time` datetime DEFAULT NULL COMMENT '最新反馈时间',
                                      `task_delay_status` int DEFAULT '0' COMMENT '延期申请状态 0 无申请, 1 有申请(暂未通过), 2 申请通过, 3通过并手动调整延期时间 ',
                                      `sms_send_time` datetime DEFAULT NULL COMMENT '(已发)短信提醒日期',
                                      `duty_people_phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '责任人电话',
                                      `contact_people_phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络员的电话',
                                      `sign_people_phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作员手机号',
                                      `sign_people_company_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '签收人的单位',
                                      `host_company` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '是否主办单位，1表示主办单位，0表示协办单位',
                                      `preview` int DEFAULT '0' COMMENT '是否预览(0:未预览 1:预览)',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务分解表';

CREATE TABLE `biz_task_delay` (
                                  `id` bigint NOT NULL COMMENT '主键',
                                  `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                  `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                  `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                  `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                  `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                  `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                  `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                  `deleted` int DEFAULT NULL COMMENT '逻辑删除字段 0没删除 1表示删除',
                                  `biz_task_supervision_id` bigint DEFAULT NULL COMMENT '任务表biz_task_supervision的主键',
                                  `delay_content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请延期的内容',
                                  `delay_reason` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请延期的理由',
                                  `delay_end_date` date DEFAULT NULL COMMENT '申请延期的截至时间',
                                  `delay_before_content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请延期之前的内容',
                                  `delay_before_end_date` date DEFAULT NULL COMMENT '申请延期之前的截至时间',
                                  `reviewer_id` bigint DEFAULT NULL COMMENT '接收审核人的的id',
                                  `reviewer_department_id` bigint DEFAULT NULL COMMENT '接收审核人的部门id',
                                  `reviewer_company_id` bigint DEFAULT NULL COMMENT '接收审核人的单位id',
                                  `has_reviewer` int DEFAULT '0' COMMENT '0：未审核 1 审核通过 2 通过并调整延期时间',
                                  `biz_task_decompose` bigint DEFAULT NULL COMMENT '分解表的主键id',
                                  `audit_id` bigint DEFAULT NULL COMMENT '审核人的id',
                                  `audit_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核人的姓名',
                                  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
                                  `has_read` int DEFAULT NULL COMMENT '0：未阅 1 已阅',
                                  `manual_delay_end_date` date DEFAULT NULL COMMENT '手动调整的延期截至时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `indexTaskDelaySupervisionId` (`biz_task_supervision_id`) USING BTREE,
                                  KEY `indexTaskDelayDecompose` (`biz_task_decompose`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务延期表';

CREATE TABLE `biz_task_feedback` (
                                     `id` bigint NOT NULL COMMENT '主键',
                                     `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                     `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                     `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                     `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                     `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                     `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                     `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                     `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                     `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                     `deleted` int DEFAULT NULL COMMENT '逻辑删除字段 0没删除 1表示删除',
                                     `biz_task_supervision_id` bigint DEFAULT NULL COMMENT '任务表biz_task_supervision的主键',
                                     `achievement_description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务成果描述',
                                     `completion` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '完成情况的百分比',
                                     `reviewer_id` bigint DEFAULT NULL COMMENT '审核人的id',
                                     `reviewer_department_id` bigint DEFAULT NULL COMMENT '审核人的部门id',
                                     `reviewer_company_id` bigint DEFAULT NULL COMMENT '审核人的单位id',
                                     `has_reviewer` int DEFAULT NULL COMMENT '0：未审核 1 审核通过',
                                     `biz_task_decompose` bigint DEFAULT NULL COMMENT '分解表的主键id',
                                     `annex` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
                                     `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人单位',
                                     `mobile` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人手机号码',
                                     `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人单位名称',
                                     `reviewer_time` datetime DEFAULT NULL COMMENT '审核时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `indexTaskFeedbackSupervisionId` (`biz_task_supervision_id`) USING BTREE,
                                     KEY `indexTaskFeedDecompose` (`biz_task_decompose`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务成果反馈表';

CREATE TABLE `biz_task_feedback_temp` (
                                          `id` bigint NOT NULL COMMENT '主键',
                                          `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                          `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                          `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                          `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                          `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                          `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                          `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                          `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                          `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                          `deleted` int DEFAULT NULL COMMENT '逻辑删除字段 0没删除 1表示删除',
                                          `biz_task_supervision_id` bigint DEFAULT NULL COMMENT '任务表biz_task_supervision的主键',
                                          `achievement_description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务成果描述',
                                          `completion` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '完成情况的百分比',
                                          `reviewer_id` bigint DEFAULT NULL COMMENT '审核人的id',
                                          `reviewer_department_id` bigint DEFAULT NULL COMMENT '审核人的部门id',
                                          `reviewer_company_id` bigint DEFAULT NULL COMMENT '审核人的单位id',
                                          `has_reviewer` int DEFAULT NULL COMMENT '0：未审核 1 审核通过',
                                          `biz_task_decompose` bigint DEFAULT NULL COMMENT '分解表的主键id',
                                          `annex` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
                                          `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人单位',
                                          `mobile` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人手机号码',
                                          `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人单位名称',
                                          `reviewer_time` datetime DEFAULT NULL COMMENT '审核时间',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          KEY `indexTaskFeedbackSupervisionId` (`biz_task_supervision_id`) USING BTREE,
                                          KEY `indexTaskFeedDecompose` (`biz_task_decompose`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务成果反馈暂存表';

CREATE TABLE `biz_task_leader_liaison` (
                                           `id` bigint NOT NULL COMMENT '主键',
                                           `liaison_man_id` bigint DEFAULT NULL COMMENT '联络员id',
                                           `liaison_man_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络员名称',
                                           `liaison_man_unit_id` bigint DEFAULT NULL COMMENT '联络人单位id',
                                           `liaison_man_unit_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络人单位名称',
                                           `liaison_man_branch_id` bigint DEFAULT NULL COMMENT '联络人部门id',
                                           `liaison_man_branch_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络人部门名称',
                                           `liaison_man_telephone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联络员手机号码',
                                           `leader_id` bigint DEFAULT NULL COMMENT '领导id',
                                           `leader_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导名称',
                                           `leader_unit_id` bigint DEFAULT NULL COMMENT '领导单位id',
                                           `leader_unit_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导单位名称',
                                           `leader_branch_id` bigint DEFAULT NULL COMMENT '领导部门id',
                                           `leader_branch_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导部门名称',
                                           `leader_telephone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '领导手机号码',
                                           `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                           `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                           `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                           `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                           `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                           `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务领导对应的联络员表';

CREATE TABLE `biz_task_level` (
                                  `id` bigint NOT NULL COMMENT '主键',
                                  `type_name` varchar(255) DEFAULT NULL COMMENT '类型名称',
                                  `parent_id` bigint DEFAULT '0' COMMENT '上级类型id，顶层为0',
                                  `order_by` int DEFAULT NULL COMMENT '排序号',
                                  `create_by` bigint DEFAULT NULL COMMENT '创建人id',
                                  `create_name` varchar(32) DEFAULT NULL COMMENT '创建人',
                                  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                  `update_by` bigint DEFAULT NULL COMMENT '修改人id',
                                  `update_name` varchar(32) DEFAULT NULL COMMENT '修改人姓名',
                                  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
                                  `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：已删除',
                                  `department_id` bigint DEFAULT NULL COMMENT '部门id',
                                  `company_id` bigint DEFAULT NULL COMMENT '公司id',
                                  `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `biz_task_score_add` (
                                      `id` bigint NOT NULL COMMENT '主键',
                                      `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                      `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                      `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                      `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                      `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                      `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                      `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                      `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                      `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                      `deleted` int DEFAULT NULL COMMENT '逻辑删除字段 0没删除 1表示删除',
                                      `biz_task_supervision_id` bigint DEFAULT NULL COMMENT '任务表biz_task_supervision的主键',
                                      `score_add_content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请加分的内容',
                                      `score_add_reason` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请加分的理由',
                                      `score` double DEFAULT NULL COMMENT '申请加分的分数',
                                      `reviewer_id` bigint DEFAULT NULL COMMENT '接收审核人的的id',
                                      `reviewer_department_id` bigint DEFAULT NULL COMMENT '接收审核人的部门id',
                                      `reviewer_company_id` bigint DEFAULT NULL COMMENT '接收审核人的单位id',
                                      `has_reviewer` int DEFAULT '0' COMMENT '0：未审核 1 审核通过 2 驳回',
                                      `refuse_reason` varchar(512) DEFAULT NULL COMMENT '驳回理由',
                                      `biz_task_decompose` bigint DEFAULT NULL COMMENT '分解表的主键id',
                                      `audit_id` bigint DEFAULT NULL COMMENT '审核人的id',
                                      `audit_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核人的姓名',
                                      `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
                                      `has_read` int DEFAULT NULL COMMENT '申请人是否未读0: 未查看  1:已查看',
                                      `audit_has_read` int DEFAULT NULL COMMENT '审核人是否未读0: 未查看  1:已查看',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      KEY `indexTaskDelaySupervisionId` (`biz_task_supervision_id`) USING BTREE,
                                      KEY `indexTaskDelayDecompose` (`biz_task_decompose`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务加分表';

CREATE TABLE `biz_task_score_sub` (
                                      `id` bigint NOT NULL COMMENT '主键',
                                      `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                      `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                      `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                      `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                      `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                      `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                      `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                      `create_name` varchar(32) DEFAULT NULL COMMENT '创建人',
                                      `update_name` varchar(32) DEFAULT NULL COMMENT '更新人',
                                      `deleted` int DEFAULT NULL COMMENT '逻辑删除字段 0没删除 1表示删除',
                                      `biz_task_supervision_id` bigint DEFAULT NULL COMMENT '任务表biz_task_supervision的主键',
                                      `sore_sub_content` varchar(512) DEFAULT NULL COMMENT '申请减分的内容',
                                      `sore_sub_reason` varchar(512) DEFAULT NULL COMMENT '申请减分的理由',
                                      `sores` double DEFAULT NULL COMMENT '申请减分的分数',
                                      `refuse_reason` varchar(512) DEFAULT NULL COMMENT '减回理由',
                                      `biz_task_decompose` bigint DEFAULT NULL COMMENT '分解表的主键id',
                                      PRIMARY KEY (`id`),
                                      KEY `INDEX246314124506400` (`biz_task_supervision_id`),
                                      KEY `INDEX246314146142500` (`biz_task_decompose`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务减分表';

CREATE TABLE `biz_task_supervision` (
                                        `id` bigint NOT NULL COMMENT '主键',
                                        `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人',
                                        `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                        `UPDATE_BY` bigint DEFAULT NULL COMMENT '更新人',
                                        `department_id` bigint DEFAULT NULL COMMENT '创建人部门',
                                        `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                        `company_id` bigint DEFAULT NULL COMMENT '创建人单位id',
                                        `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                        `create_name` varchar(32) DEFAULT NULL COMMENT '创建人',
                                        `update_name` varchar(32) DEFAULT NULL COMMENT '更新人',
                                        `deleted` int DEFAULT NULL COMMENT '逻辑删除字段 0没删除 1表示删除',
                                        `title` varchar(255) DEFAULT NULL COMMENT '任务名称，任务标题',
                                        `task_type` varchar(2) DEFAULT NULL COMMENT '任务类型，对应数据字典taskType',
                                        `degree_urgency` varchar(2) DEFAULT NULL COMMENT '任务紧急程度，对应数据字典taskDegreeUrgency',
                                        `task_source` varchar(2) DEFAULT NULL COMMENT '任务来源，对应数据字典taskSource',
                                        `task_tag` varchar(10) DEFAULT NULL COMMENT '任务标签，对应数据字典taskTag',
                                        `examine_method` varchar(10) DEFAULT NULL COMMENT '考核方式，对应数据字典ExamineMethod',
                                        `due_time` date DEFAULT NULL COMMENT '截止时间，yyyy-mm-dd',
                                        `task_description` varchar(500) DEFAULT NULL COMMENT '任务概叙',
                                        `annex` varchar(1) DEFAULT NULL COMMENT '附件',
                                        `has_publish` int DEFAULT '0' COMMENT '0 未发布暂存，1：发布  2：撤回',
                                        `has_finish` int DEFAULT '0' COMMENT '0：未办结 1 办结',
                                        `department_name` varchar(50) DEFAULT NULL COMMENT '拟稿人部门名称',
                                        `mobile` varchar(11) DEFAULT NULL COMMENT '拟稿人手机号码',
                                        `company_name` varchar(255) DEFAULT NULL COMMENT '拟稿人单位名称',
                                        `task_number` varchar(255) DEFAULT NULL COMMENT '任务编号，中间：隔开，：之前为父编号，转办的以zb开头',
                                        `task_level` int DEFAULT NULL COMMENT '任务层级，转办了多少级',
                                        `task_add_type` int DEFAULT '0' COMMENT '任务新增的方式0表示新增 1表示转办',
                                        `task_transfer_id` bigint DEFAULT NULL COMMENT '任务来自转办，转办的id',
                                        `duty_people` longtext COMMENT '责任人',
                                        `task_transfer_decompose_id` bigint DEFAULT NULL COMMENT '任务转办过来后的分解的任务id',
                                        `form_id` bigint DEFAULT NULL COMMENT '表单的id',
                                        `has_finish_time` timestamp NULL DEFAULT NULL COMMENT '办结时间',
                                        `ext1` varchar(255) DEFAULT NULL COMMENT '任务转办开始时间备注，不要实体',
                                        `ext2` varchar(255) DEFAULT NULL COMMENT '任务转办内容备注不要实体',
                                        `ext3` varchar(255) DEFAULT NULL COMMENT '任务转办显示责任人不能填不要实体',
                                        `new_sup_feedback_sign` int DEFAULT '0' COMMENT '对于最新反馈的成果标记 0：没有 , 1 有',
                                        `sup_has_feedback` int DEFAULT '0' COMMENT '是否有反馈记录 0：没有, 1 有',
                                        `inspector_task_level` varchar(50) DEFAULT NULL COMMENT '督查任务等级对应biz_task_level',
                                        `inspector_items` varchar(255) DEFAULT NULL COMMENT '督查任务具体的事项对应biz_task_level',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务督查的主表';

CREATE TABLE `biz_task_work_logs` (
                                      `id` bigint NOT NULL COMMENT '主键ID',
                                      `is_todo_task` int DEFAULT '0' COMMENT '是否为待办任务，1：表示待办任务，0：表示自建工作任务',
                                      `todo_task_id` bigint DEFAULT NULL COMMENT '待办任务的id',
                                      `form_id` bigint DEFAULT NULL COMMENT '表单id',
                                      `post` varchar(32) DEFAULT NULL COMMENT '职务',
                                      `work_source` varchar(200) DEFAULT NULL COMMENT '工作来源',
                                      `work_type` varchar(200) DEFAULT NULL COMMENT '工作类型',
                                      `content` varchar(500) DEFAULT NULL COMMENT '工作内容',
                                      `start_time` date DEFAULT NULL COMMENT '开始时间',
                                      `end_time` date DEFAULT NULL COMMENT '结束时间',
                                      `cost_time` float DEFAULT NULL COMMENT '时长',
                                      `annex` int DEFAULT NULL COMMENT '附件：1表示有附件，0表示无附件',
                                      `bpm_status` int DEFAULT NULL COMMENT '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停',
                                      `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                      `create_time` timestamp NULL DEFAULT NULL COMMENT '创建日期',
                                      `create_name` varchar(32) DEFAULT NULL COMMENT '创建人名称',
                                      `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                      `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                      `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                      `company_name` varchar(32) DEFAULT NULL COMMENT '单位名称',
                                      `department_name` varchar(32) DEFAULT NULL COMMENT '部门名称',
                                      `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                      `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                                      `update_name` varchar(32) DEFAULT NULL COMMENT '修改人姓名',
                                      `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
                                      `deleted` int DEFAULT '0' COMMENT '逻辑删除 0：未删除 1：删除',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工作日志';

CREATE TABLE `biz_task_work_logs_unstatistic` (
                                                  `id` bigint NOT NULL COMMENT '主键ID',
                                                  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
                                                  `user_name` varchar(32) DEFAULT NULL COMMENT '用户名称',
                                                  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建日期',
                                                  `create_name` varchar(32) DEFAULT NULL COMMENT '创建人名称',
                                                  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                                  `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                                  `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                                  `company_name` varchar(32) DEFAULT NULL COMMENT '单位名称',
                                                  `department_name` varchar(32) DEFAULT NULL COMMENT '部门名称',
                                                  `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                                  `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                                                  `update_name` varchar(32) DEFAULT NULL COMMENT '修改人姓名',
                                                  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
                                                  `deleted` int DEFAULT '0' COMMENT '逻辑删除 0：未删除 1：删除',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工作日志免统计的用户列表';
