CREATE TABLE "myapp"."biz_task_decompose"
 (
     "id" BIGINT NOT NULL,
     "CREATE_BY" BIGINT,
     "CREATE_TIME" TIMESTAMP(0),
     "UPDATE_BY" BIGINT,
     "department_id" BIGINT,
     "UPDATE_TIME" TIMESTAMP(0),
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "create_name" VARCHAR(32),
     "update_name" VARCHAR(32),
     "deleted" INT,
     "duty_people_id" BIGINT,
     "duty_people_name" VARCHAR(30),
     "duty_people_department_name" VARCHAR(30),
     "duty_people_company_name" VARCHAR(50),
     "duty_people_post_name" VARCHAR(50),
     "duty_peo, ple_department_id" BIGINT,
     "duty_people_company_id" BIGINT,
     "contact_people_id" BIGINT,
     "contact_people_name" VARCHAR(30),
     "contact_people_department_name" VARCHAR(30),
     "contact_people_company_name" VARCHAR(50),
     "contact_people_post_name" VARCHAR(50),
     "contact_people_department_id" BIGINT,
     "contact_people_company_id" BIGINT,
     "content" VARCHAR(500),
     "start_date" DATE,
     "end_date" DATE,
     "receive_time" TIMESTAMP(0),
     "degree_difficulty" VARCHAR(5),
     "task_description" VARCHAR(500),
     "sms, _reminder" VARCHAR(5) DEFAULT '0',
     "has_finish" INT DEFAULT 0,
     "has_sign" INT DEFAULT 0,
     "has_transfer" INT DEFAULT 0,
     "sub_domain_id" VARCHAR(255),
     "sub_tbl_fk" BIGINT,
     "sub_tbl_num" INT,
     "duty_people" VARCHAR(800),
     "sign_people_id" BIGINT,
     "sign_people_name" VARCHAR(255),
     "group_name" VARCHAR(100),
     "has_finish_time" TIMESTAMP(0),
     "has_feedback" INT DEFAULT 0,
     "new_feedback_sign" INT DEFAULT 0,
     "new_finish_sign" INT DEFAULT 0,
     "new_feedback_time" TIMESTAMP(0),
     "task_delay_status, " INT DEFAULT 0,
     "sms_send_time" TIMESTAMP(0),
     "duty_people_phone" VARCHAR(11),
     "contact_people_phone" VARCHAR(11),
     "sign_people_phone" VARCHAR(11),
     "sign_people_company_name" VARCHAR(50),
     "host_company" VARCHAR(50) DEFAULT 0,
     NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_task_decompose" IS '任务分解表';COMMENT ON COLUMN "myapp"."biz_task_decompose"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_id" IS '责任人的id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_name" IS '责任人的姓名';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_department_name" IS '责任人的部门名称';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_company_name" IS '责任人的单位名称';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_post_name" IS '责任人的职务';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_department_id" IS '责任人的部门id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_company_id" IS '责任人的单位id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_id" IS '联络员的id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_name" IS '联络员的姓名';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_department_name" IS '联络员的部门名称';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_company_name" IS '联络员的单位名称';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_post_name" IS '联络员的职务';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_department_id" IS '联络员的部门id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_company_id" IS '联络员的单位id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."content" IS '任务内容';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."start_date" IS '任务开始时间';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."end_date" IS '任务结束时间';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."receive_time" IS '任务接收时间';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."degree_difficulty" IS '难度系数';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."task_description" IS '任务概叙';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sms_reminder" IS '短信提醒方式，对应数据字典sms_reminder';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."has_finish" IS '0：未办结 1 办结';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."has_sign" IS '0：未签收 1 已签收 2 撤回';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."has_transfer" IS '0：未转办 1 已转办';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sub_domain_id" IS '子表的域id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sub_tbl_fk" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sub_tbl_num" IS '序号';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people" IS '责任人详细信息，表单展示用';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sign_people_id" IS '签收人的id';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sign_people_name" IS '签收人的姓名';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."group_name" IS '群名称';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."has_finish_time" IS '办结时间';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."has_feedback" IS '是否有反馈记录 0：没有 , 1 有';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."new_feedback_sign" IS '是否有最新反馈的成果标记 0：没有 , 1 有';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."new_finish_sign" IS '是否有最新办结的任务标记:0：没有 , 1 有';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."new_feedback_time" IS '最新反馈时间';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."task_delay_status" IS '延期申请状态 0 无申请, 1 有申请(暂未通过), 2 申请通过, 3通过并手动调整延期时间 ';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sms_send_time" IS '(已发)短信提醒日期';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."duty_people_phone" IS '责任人电话';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."contact_people_phone" IS '联络员的电话';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sign_people_phone" IS '操作员手机号';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."sign_people_company_name" IS '签收人的单位';
COMMENT ON COLUMN "myapp"."biz_task_decompose"."host_company" IS '是否主办单位，1表示主办单位，0表示协办单位';




CREATE TABLE "myapp"."biz_task_delay"
 (
     "id" BIGINT NOT NULL,
     "CREATE_BY" BIGINT,
     "CREATE_TIME" TIMESTAMP(0),
     "UPDATE_BY" BIGINT,
     "department_id" BIGINT,
     "UPDATE_TIME" TIMESTAMP(0),
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "create_name" VARCHAR(32),
     "update_name" VARCHAR(32),
     "deleted" INT,
     "biz_task_supervision_id" BIGINT,
     "delay_content" VARCHAR(500),
     "delay_reason" VARCHAR(500),
     "delay_end_date" DATE,
     "delay_before_content" VARCHAR(500),
     "delay_before_end_date" DATE,
     "revie, wer_id" BIGINT,
     "reviewer_department_id" BIGINT,
     "reviewer_company_id" BIGINT,
     "has_reviewer" INT DEFAULT 0,
     "biz_task_decompose" BIGINT,
     "audit_id" BIGINT,
     "audit_name" VARCHAR(255),
     "audit_time" TIMESTAMP(0),
     "has_read" INT,
     "manual_delay_end_date" DATE,
     NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_task_delay" IS '任务延期表';COMMENT ON COLUMN "myapp"."biz_task_delay"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_delay"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_delay"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_delay"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_delay"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."biz_task_delay"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_task_delay"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."biz_task_delay"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."biz_task_delay"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_delay"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_delay"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "myapp"."biz_task_delay"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "myapp"."biz_task_delay"."delay_content" IS '申请延期的内容';
COMMENT ON COLUMN "myapp"."biz_task_delay"."delay_reason" IS '申请延期的理由';
COMMENT ON COLUMN "myapp"."biz_task_delay"."delay_end_date" IS '申请延期的截至时间';
COMMENT ON COLUMN "myapp"."biz_task_delay"."delay_before_content" IS '申请延期之前的内容';
COMMENT ON COLUMN "myapp"."biz_task_delay"."delay_before_end_date" IS '申请延期之前的截至时间';
COMMENT ON COLUMN "myapp"."biz_task_delay"."reviewer_id" IS '接收审核人的的id';
COMMENT ON COLUMN "myapp"."biz_task_delay"."reviewer_department_id" IS '接收审核人的部门id';
COMMENT ON COLUMN "myapp"."biz_task_delay"."reviewer_company_id" IS '接收审核人的单位id';
COMMENT ON COLUMN "myapp"."biz_task_delay"."has_reviewer" IS '0：未审核 1 审核通过 2 通过并调整延期时间';
COMMENT ON COLUMN "myapp"."biz_task_delay"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "myapp"."biz_task_delay"."audit_id" IS '审核人的id';
COMMENT ON COLUMN "myapp"."biz_task_delay"."audit_name" IS '审核人的姓名';
COMMENT ON COLUMN "myapp"."biz_task_delay"."audit_time" IS '审核时间';
COMMENT ON COLUMN "myapp"."biz_task_delay"."has_read" IS '0：未阅 1 已阅';
COMMENT ON COLUMN "myapp"."biz_task_delay"."manual_delay_end_date" IS '手动调整的延期截至时间';


CREATE  INDEX "INDEX33558304" ON "myapp"."biz_task_delay"("id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "INDEX177283396464000" ON "myapp"."biz_task_delay"("biz_task_supervision_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "INDEX177283411754900" ON "myapp"."biz_task_delay"("biz_task_decompose" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."biz_task_feedback"
 (
     "id" BIGINT NOT NULL,
     "CREATE_BY" BIGINT,
     "CREATE_TIME" TIMESTAMP(0),
     "UPDATE_BY" BIGINT,
     "department_id" BIGINT,
     "UPDATE_TIME" TIMESTAMP(0),
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "create_name" VARCHAR(32),
     "update_name" VARCHAR(32),
     "deleted" INT,
     "biz_task_supervision_id" BIGINT,
     "achievement_description" VARCHAR(500),
     "completion" VARCHAR(5),
     "reviewer_id" BIGINT,
     "reviewer_department_id" BIGINT,
     "reviewer_company_id" BIGINT,
     "h, as_reviewer" INT,
     "biz_task_decompose" BIGINT,
     "annex" VARCHAR(1000),
     "department_name" VARCHAR(32),
     "mobile" VARCHAR(11),
     "company_name" VARCHAR(32),
     "reviewer_time" TIMESTAMP(0),
     NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_task_feedback" IS '任务成果反馈表';COMMENT ON COLUMN "myapp"."biz_task_feedback"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."achievement_description" IS '任务成果描述';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."completion" IS '完成情况的百分比';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."reviewer_id" IS '审核人的id';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."reviewer_department_id" IS '审核人的部门id';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."reviewer_company_id" IS '审核人的单位id';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."has_reviewer" IS '0：未审核 1 审核通过';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."annex" IS '附件';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."department_name" IS '拟稿人单位';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."company_name" IS '拟稿人单位名称';
COMMENT ON COLUMN "myapp"."biz_task_feedback"."reviewer_time" IS '审核时间';


CREATE  INDEX "INDEX33558303" ON "myapp"."biz_task_feedback"("id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "INDEX177283215066500" ON "myapp"."biz_task_feedback"("biz_task_decompose" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "INDEX177283230706600" ON "myapp"."biz_task_feedback"("biz_task_supervision_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."biz_task_feedback_temp"
 (
     "id" BIGINT NOT NULL,
     "CREATE_BY" BIGINT,
     "CREATE_TIME" TIMESTAMP(0),
     "UPDATE_BY" BIGINT,
     "department_id" BIGINT,
     "UPDATE_TIME" TIMESTAMP(0),
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "create_name" VARCHAR(32),
     "update_name" VARCHAR(32),
     "deleted" INT,
     "biz_task_supervision_id" BIGINT,
     "achievement_description" VARCHAR(500),
     "completion" VARCHAR(5),
     "reviewer_id" BIGINT,
     "reviewer_department_id" BIGINT,
     "reviewer_company_id" BIGINT, ,
     "has_reviewer" INT,
     "biz_task_decompose" BIGINT,
     "annex" VARCHAR(1000),
     "department_name" VARCHAR(32),
     "mobile" VARCHAR(11),
     "company_name" VARCHAR(32),
     "reviewer_time" TIMESTAMP(0),
     NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_task_feedback_temp" IS '任务成果反馈暂存表';COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."achievement_description" IS '任务成果描述';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."completion" IS '完成情况的百分比';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."reviewer_id" IS '审核人的id';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."reviewer_department_id" IS '审核人的部门id';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."reviewer_company_id" IS '审核人的单位id';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."has_reviewer" IS '0：未审核 1 审核通过';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."biz_task_decompose" IS '分解表的主键id';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."annex" IS '附件';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."department_name" IS '拟稿人单位';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."company_name" IS '拟稿人单位名称';
COMMENT ON COLUMN "myapp"."biz_task_feedback_temp"."reviewer_time" IS '审核时间';


CREATE  INDEX "INDEX33558302" ON "myapp"."biz_task_feedback_temp"("id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "indexTaskFeedDecompose" ON "myapp"."biz_task_feedback_temp"("biz_task_decompose" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "indexTaskFeedbackSupervisionId" ON "myapp"."biz_task_feedback_temp"("biz_task_supervision_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."biz_task_leader_liaison"
 (
     "id" BIGINT NOT NULL,
     "liaison_man_id" BIGINT,
     "liaison_man_name" VARCHAR(20),
     "liaison_man_unit_id" BIGINT,
     "liaison_man_unit_name" VARCHAR(255),
     "liaison_man_branch_id" BIGINT,
     "liaison_man_branch_name" VARCHAR(255),
     "liaison_man_telephone" VARCHAR(20),
     "leader_id" BIGINT,
     "leader_name" VARCHAR(20),
     "leader_unit_id" BIGINT,
     "leader_unit_name" VARCHAR(255),
     "leader_branch_id" BIGINT,
     "leader_branch_name" VARCHAR(255),
     "leader_tele, phone" VARCHAR(20),
     "create_by" BIGINT,
     "create_name" VARCHAR(32),
     "create_time" TIMESTAMP(0),
     "update_by" BIGINT,
     "update_name" VARCHAR(32),
     "update_time" TIMESTAMP(0),
     "department_id" BIGINT,
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "deleted" INT,
     NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_task_leader_liaison" IS '任务领导对应的联络员表';COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."liaison_man_id" IS '联络员id';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."liaison_man_name" IS '联络员名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."liaison_man_unit_id" IS '联络人单位id';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."liaison_man_unit_name" IS '联络人单位名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."liaison_man_branch_id" IS '联络人部门id';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."liaison_man_branch_name" IS '联络人部门名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."liaison_man_telephone" IS '联络员手机号码';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."leader_id" IS '领导id';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."leader_name" IS '领导名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."leader_unit_id" IS '领导单位id';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."leader_unit_name" IS '领导单位名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."leader_branch_id" IS '领导部门id';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."leader_branch_name" IS '领导部门名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."leader_telephone" IS '领导手机号码';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_task_leader_liaison"."deleted" IS '逻辑删除';




CREATE TABLE "myapp"."biz_task_level"
 (
     "id" BIGINT NOT NULL,
     "type_name" VARCHAR(255),
     "parent_id" BIGINT DEFAULT 0,
     "order_by" INT,
     "create_by" BIGINT,
     "create_name" VARCHAR(32),
     "create_time" TIMESTAMP(0),
     "update_by" BIGINT,
     "update_name" VARCHAR(32),
     "update_time" TIMESTAMP(0),
     "deleted" INT,
     "department_id" BIGINT,
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "myapp"."biz_task_level"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_level"."type_name" IS '类型名称';
COMMENT ON COLUMN "myapp"."biz_task_level"."parent_id" IS '上级类型id，顶层为0';
COMMENT ON COLUMN "myapp"."biz_task_level"."order_by" IS '排序号';
COMMENT ON COLUMN "myapp"."biz_task_level"."create_by" IS '创建人id';
COMMENT ON COLUMN "myapp"."biz_task_level"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_level"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_level"."update_by" IS '修改人id';
COMMENT ON COLUMN "myapp"."biz_task_level"."update_name" IS '修改人姓名';
COMMENT ON COLUMN "myapp"."biz_task_level"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."biz_task_level"."deleted" IS '逻辑删除 0：未删除 1：已删除';
COMMENT ON COLUMN "myapp"."biz_task_level"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."biz_task_level"."company_id" IS '公司id';
COMMENT ON COLUMN "myapp"."biz_task_level"."tenant_id" IS '租户id';




CREATE TABLE "myapp"."biz_task_score_add"
 (
     "id" BIGINT NOT NULL,
     "CREATE_BY" BIGINT,
     "CREATE_TIME" TIMESTAMP(0),
     "UPDATE_BY" BIGINT,
     "department_id" BIGINT,
     "UPDATE_TIME" TIMESTAMP(0),
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "create_name" VARCHAR(32),
     "update_name" VARCHAR(32),
     "deleted" INT,
     "biz_task_supervision_id" BIGINT,
     "score_add_content" VARCHAR(512),
     "score_add_reason" VARCHAR(512),
     "score" DOUBLE,
     "reviewer_id" BIGINT,
     "reviewer_department_id" BIGINT,
     "reviewer_com, pany_id" BIGINT,
     "has_reviewer" INT DEFAULT 0,
     "refuse_reason" VARCHAR(512),
     "biz_task_decompose" BIGINT,
     "audit_id" BIGINT,
     "audit_name" VARCHAR(255),
     "audit_time" TIMESTAMP(0),
     "has_read" INT) STORAGE(ON "MAIN", CLUSTERBTR) ;



CREATE  INDEX "INDEX247257069625800" ON "myapp"."biz_task_score_add"("biz_task_supervision_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "INDEX247257088689100" ON "myapp"."biz_task_score_add"("biz_task_decompose" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."biz_task_score_sub"
 (
     "id" BIGINT NOT NULL,
     "CREATE_BY" BIGINT,
     "CREATE_TIME" TIMESTAMP(0),
     "UPDATE_BY" BIGINT,
     "department_id" BIGINT,
     "UPDATE_TIME" TIMESTAMP(0),
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "create_name" VARCHAR(32),
     "update_name" VARCHAR(32),
     "deleted" INT,
     "biz_task_supervision_id" BIGINT,
     "sore_sub_content" VARCHAR(512),
     "sore_sub_reason" VARCHAR(512),
     "sores" DOUBLE,
     "refuse_reason" VARCHAR(512),
     "biz_task_decompose" BIGINT,
     NOT CLUSTER,  PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_task_score_sub" IS '任务减分表';COMMENT ON COLUMN "myapp"."biz_task_score_sub"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."biz_task_supervision_id" IS '任务表biz_task_supervision的主键';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."sore_sub_content" IS '申请减分的内容';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."sore_sub_reason" IS '申请减分的理由';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."sores" IS '申请减分的分数';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."refuse_reason" IS '减回理由';
COMMENT ON COLUMN "myapp"."biz_task_score_sub"."biz_task_decompose" IS '分解表的主键id';


CREATE  INDEX "INDEX33558298" ON "myapp"."biz_task_score_sub"("id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "indexTaskDelayDecompose" ON "myapp"."biz_task_score_sub"("biz_task_decompose" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE  INDEX "indexTaskDelaySupervisionId" ON "myapp"."biz_task_score_sub"("biz_task_supervision_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


CREATE TABLE "myapp"."biz_task_supervision"
 (
     "id" BIGINT NOT NULL,
     "CREATE_BY" BIGINT,
     "CREATE_TIME" TIMESTAMP(0),
     "UPDATE_BY" BIGINT,
     "department_id" BIGINT,
     "UPDATE_TIME" TIMESTAMP(0),
     "company_id" BIGINT,
     "tenant_id" BIGINT,
     "create_name" VARCHAR(32),
     "update_name" VARCHAR(32),
     "deleted" INT,
     "title" VARCHAR(255),
     "task_type" VARCHAR(2),
     "degree_urgency" VARCHAR(2),
     "task_source" VARCHAR(2),
     "task_tag" VARCHAR(10),
     "examine_method" VARCHAR(10),
     "due_time" DATE,
     "task_desc, ription" VARCHAR(500),
     "annex" VARCHAR(1),
     "has_publish" INT DEFAULT 0,
     "has_finish" INT DEFAULT 0,
     "department_name" VARCHAR(50),
     "mobile" VARCHAR(11),
     "company_name" VARCHAR(255),
     "task_number" VARCHAR(255),
     "task_level" INT,
     "task_add_type" INT DEFAULT 0,
     "task_transfer_id" BIGINT,
     "duty_people" TEXT,
     "task_transfer_decompose_id" BIGINT,
     "form_id" BIGINT,
     "has_finish_time" TIMESTAMP(0),
     "ext1" VARCHAR(255),
     "ext2" VARCHAR(255),
     "ext3" VARCHAR(255),
     "new_sup_feedback_sign" IN, T DEFAULT 0,
     "sup_has_feedback" INT DEFAULT 0,
     "inspector_task_level" VARCHAR(50),
     "inspector_items" VARCHAR(255),
     NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_task_supervision" IS '任务督查的主表';COMMENT ON COLUMN "myapp"."biz_task_supervision"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."department_id" IS '创建人部门';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."company_id" IS '创建人单位id';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."create_name" IS '创建人';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."update_name" IS '更新人';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."deleted" IS '逻辑删除字段 0没删除 1表示删除';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."title" IS '任务名称，任务标题';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_type" IS '任务类型，对应数据字典taskType';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."degree_urgency" IS '任务紧急程度，对应数据字典taskDegreeUrgency';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_source" IS '任务来源，对应数据字典taskSource';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_tag" IS '任务标签，对应数据字典taskTag';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."examine_method" IS '考核方式，对应数据字典ExamineMethod';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."due_time" IS '截止时间，yyyy-mm-dd';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_description" IS '任务概叙';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."annex" IS '附件';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."has_publish" IS '0 未发布暂存，1：发布  2：撤回';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."has_finish" IS '0：未办结 1 办结';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."department_name" IS '拟稿人部门名称';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."company_name" IS '拟稿人单位名称';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_number" IS '任务编号，中间：隔开，：之前为父编号，转办的以zb开头';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_level" IS '任务层级，转办了多少级';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_add_type" IS '任务新增的方式0表示新增 1表示转办';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_transfer_id" IS '任务来自转办，转办的id';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."duty_people" IS '责任人';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."task_transfer_decompose_id" IS '任务转办过来后的分解的任务id';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."form_id" IS '表单的id';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."has_finish_time" IS '办结时间';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."ext1" IS '任务转办开始时间备注，不要实体';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."ext2" IS '任务转办内容备注不要实体';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."ext3" IS '任务转办显示责任人不能填不要实体';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."new_sup_feedback_sign" IS '对于最新反馈的成果标记 0：没有 , 1 有';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."sup_has_feedback" IS '是否有反馈记录 0：没有, 1 有';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."inspector_task_level" IS '督查任务等级对应biz_task_level';
COMMENT ON COLUMN "myapp"."biz_task_supervision"."inspector_items" IS '督查任务具体的事项对应biz_task_level';




CREATE TABLE "myapp"."biz_task_work_logs"
 (
     "id" BIGINT NOT NULL,
     "is_todo_task" INT DEFAULT 0,
     "post" VARCHAR(32),
     "work_source" VARCHAR(200),
     "work_type" VARCHAR(200),
     "content" VARCHAR(500),
     "start_time" DATE,
     "end_time" DATE,
     "cost_time" REAL,
     "annex" VARCHAR(500),
     "bpm_status" INT,
     "process_instance_id" BIGINT,
     "create_time" TIMESTAMP(0),
     "create_name" VARCHAR(32),
     "create_by" BIGINT,
     "company_id" BIGINT,
     "department_id" BIGINT,
     "company_name" VARCHAR(32),
     "department_na, me" VARCHAR(32),
     "tenant_id" BIGINT,
     "update_by" BIGINT,
     "update_name" VARCHAR(32),
     "update_time" TIMESTAMP(0),
     "deleted" INT DEFAULT 0) STORAGE(ON "MAIN", CLUSTERBTR) ;




CREATE TABLE "myapp"."biz_task_work_logs_unstatistic"
 (
     "id" BIGINT NOT NULL,
     "user_id" BIGINT,
     "user_name" VARCHAR(32),
     "create_time" TIMESTAMP(0),
     "create_name" VARCHAR(32),
     "create_by" BIGINT,
     "company_id" BIGINT,
     "department_id" BIGINT,
     "company_name" VARCHAR(32),
     "department_name" VARCHAR(32),
     "tenant_id" BIGINT,
     "update_by" BIGINT,
     "update_name" VARCHAR(32),
     "update_time" TIMESTAMP(0),
     "deleted" INT DEFAULT 0) STORAGE(ON "MAIN", CLUSTERBTR) ;





