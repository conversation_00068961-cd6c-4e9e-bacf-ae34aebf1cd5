<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.huaihua.mapper.BizTaskDecomposeMapper">

    <resultMap id="BizTaskResultMap" type="com.ctsi.huaihua.entity.dto.BizTaskDTO">
        <id column="id" jdbcType="BIGINT" property="taskSupervisionId"/>
        <id column="sup_company_id" jdbcType="BIGINT" property="supCompanyId"/>
        <result column="task_decompose_id" jdbcType="BIGINT" property="taskDecomposeId"/>
        <result column="task_number" jdbcType="VARCHAR" property="taskNumber"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="duty_people_department_name" jdbcType="VARCHAR" property="dutyPeopleDepartmentName"/>
        <result column="duty_people_company_name" jdbcType="VARCHAR" property="dutyPeopleCompanyName"/>

        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="sup_create_time" jdbcType="TIMESTAMP" property="supCreateTime"/>
        <result column="due_time" jdbcType="DATE" property="dueTime"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="sms_send_time" jdbcType="DATE" property="smsSendTime"/>
        <result column="sup_due_time" jdbcType="DATE" property="supDueTime"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="query_type" jdbcType="VARCHAR" property="queryType"/>
        <result column="task_tag" jdbcType="VARCHAR" property="taskTag"/>
        <result column="task_source" jdbcType="VARCHAR" property="taskSource"/>
        <result column="dec_task_score" jdbcType="VARCHAR" property="decomposeTaskSource"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="has_publish" jdbcType="INTEGER" property="hasPublish"/>
        <result column="form_id" jdbcType="INTEGER" property="formId"/>
        <result column="has_finish" jdbcType="INTEGER" property="hasFinish"/>
        <result column="has_finish_time" jdbcType="INTEGER" property="hasFinishTime"/>
        <result column="sup_has_finish" jdbcType="INTEGER" property="supHasFinish"/>
        <result column="task_add_type" jdbcType="INTEGER" property="taskAddType"/>
        <result column="task_transfer_id" jdbcType="BIGINT" property="taskTransferId"/>
        <result column="has_sign" jdbcType="INTEGER" property="hasSign"/>
        <result column="has_transfer" jdbcType="INTEGER" property="hasTransfer"/>
        <result column="duty_people_name" jdbcType="VARCHAR" property="dutyPeople"/>
        <result column="duty_people_name" jdbcType="VARCHAR" property="dutyPeopleName"/>
        <result column="duty_people_id" jdbcType="BIGINT" property="dutyPeopleId"/>
        <result column="contact_people_id" jdbcType="BIGINT" property="contactPeopleId"/>
        <result column="contact_people_name" jdbcType="VARCHAR" property="contactPeopleName"/>
        <result column="duty_people_post_name" jdbcType="VARCHAR" property="dutyPeoplePostName"/>
        <result column="has_feedback" jdbcType="INTEGER" property="hasFeedback"/>
        <result column="new_feedback_sign" jdbcType="INTEGER" property="newFeedbackSign"/>
        <result column="new_finish_sign" jdbcType="INTEGER" property="newFinishSign"/>
        <result column="duty_people_company_id" jdbcType="INTEGER" property="dutyPeopleCompanyId"/>
        <result column="new_feedback_time" jdbcType="INTEGER" property="newFeedbackTime"/>
        <result column="task_delay_status" jdbcType="INTEGER" property="taskDelayStatus"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="degree_difficulty" jdbcType="VARCHAR" property="degreeDifficulty"/>
        <result column="sms_reminder" jdbcType="VARCHAR" property="smsReminder"/>
        <result column="preview" javaType="INTEGER" property="preview"/>
        <result column="degree_urgency" jdbcType="VARCHAR" property="degreeUrgency"/>
        <result column="inspector_task_level" jdbcType="VARCHAR" property="inspectorTaskLevel"/>
        <result column="inspector_items" jdbcType="VARCHAR" property="inspectorItems"/>
        <result column="task_score" jdbcType="VARCHAR" property="actualScore"/>
    </resultMap>

    <resultMap id="BizHomeTaskResultMap" type="com.ctsi.huaihua.entity.dto.BizHomeTaskDTO">
        <id column="id" jdbcType="BIGINT" property="taskSupervisionId"/>
        <result column="task_decompose_id" jdbcType="BIGINT" property="taskDecomposeId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="due_time" jdbcType="DATE" property="dueTime"/>
        <result column="query_type" jdbcType="VARCHAR" property="queryType"/>
        <result column="duty_people_id" jdbcType="BIGINT" property="dutyPeopleId"/>
    </resultMap>

    <sql id="selectTask">
        SELECT sup_task.id,
               dec_task.task_score AS dec_task_score,
               dec_task.id as task_decompose_id,
               sup_task.task_number,
               sup_task.degree_urgency,
               sup_task.title,
               sup_task.task_description,
               dec_task.preview,
               sup_task.create_name,
               sup_task.create_time as sup_create_time,
               sup_task.company_id as sup_company_id,
               sup_task.department_name,
               sup_task.company_name,
               dec_task.create_time,
               dec_task.end_date as due_time,
               sup_task.due_time as sup_due_time,
               dec_task.receive_time,
               sup_task.task_source as query_type,
               sup_task.form_id,
               sup_task.task_tag,
               sup_task.task_source,
               sup_task.task_type,
               sup_task.task_level,

               sup_task.has_publish,
                dec_task.task_score,
               dec_task.has_sign,
               sup_task.has_finish as sup_has_finish,
               dec_task.has_finish ,
               dec_task.has_finish_time ,
               sup_task.task_add_type,
               sup_task.task_transfer_id,
               dec_task.has_transfer,
               dec_task.duty_people_name,
               dec_task.duty_people_id,
               dec_task.contact_people_id,
               dec_task.duty_people_post_name,
               dec_task.contact_people_name,
               dec_task.has_feedback,
               dec_task.new_feedback_sign,
               dec_task.new_finish_sign,
               dec_task.task_delay_status,
               dec_task.content,
               dec_task.degree_difficulty,
               dec_task.sms_reminder,
               dec_task.start_date,
               dec_task.end_date,
               dec_task.sms_send_time,
               dec_task.duty_people_department_name,
               dec_task.duty_people_company_name,
               dec_task.duty_people_company_id,
               dec_task.new_feedback_time,
               sup_task.inspector_task_level,
               sup_task.inspector_items,
               sup_task.difficulty,
               sup_task.lead_people_id,
               sup_task.lead_people_name
    </sql>


    <sql id="selectFrom">
        FROM biz_task_decompose as dec_task
        INNER JOIN biz_task_supervision as sup_task
        on sup_task.id = dec_task.sub_tbl_fk
        WHERE
        sup_task.deleted = 0
        and dec_task.deleted = 0 and sup_task.has_publish in (1,2)

        <!-- 重要性 -->
        <if test="bizTask.inspectorTaskLevel != null and bizTask.inspectorTaskLevel != ''">
            and sup_task.inspector_task_level = #{bizTask.inspectorTaskLevel}
        </if>

        <!-- 交办事项 -->
        <if test="bizTask.inspectorItems != null and bizTask.inspectorItems != ''">
            and sup_task.inspector_items = #{bizTask.inspectorItems}
        </if>
        <!--接收时间-->
        <if test="bizTask.receiveTime != null and bizTask.receiveTime != ''">
            and dec_task.receive_time = #{bizTask.receiveTime}
        </if>

        <!--发布状态-->
        <if test="bizTask.hasPublish != null ">
            and sup_task.has_publish = #{bizTask.hasPublish}
        </if>


        <!-- 是否区分单位 -->
        <if test="bizTask.dutyPeopleCompanyId != null and bizTask.dutyPeopleCompanyId != ''">
            and dec_task.duty_people_company_id = #{bizTask.dutyPeopleCompanyId}
        </if>

        <!-- taskTag任务标签 -->
        <if test="bizTask.taskTag != null and bizTask.taskTag != ''">
            and sup_task.task_tag = #{bizTask.taskTag}
        </if>
        <!-- taskSource任务来源 -->
        <if test="bizTask.taskSource != null and bizTask.taskSource != ''">
            and sup_task.task_source = #{bizTask.taskSource}
        </if>

        <!-- 是否有反馈 -->
        <if test="bizTask.hasFeedback != null ">
            and dec_task.has_feedback = #{bizTask.hasFeedback}
        </if>
        <!-- 是否有最新的反馈标志 -->
        <if test="bizTask.newFeedbackSign != null and bizTask.newFeedbackSign != ''">
            and dec_task.new_feedback_sign = #{bizTask.newFeedbackSign}
        </if>
        <!--主任务id-->
        <if test="bizTask.taskSupervisionId != null and bizTask.taskSupervisionId != ''">
            and sup_task.id = #{bizTask.taskSupervisionId}
        </if>

        <!--子任务id-->
        <if test="bizTask.taskDecomposeId != null and bizTask.taskDecomposeId != ''">
            and dec_task.id = #{bizTask.taskDecomposeId}
        </if>
        <!--子任务id集合-->
        <if test="null != bizTask.bizTaskDecomposeIdList and bizTask.bizTaskDecomposeIdList.size > 0">
            and dec_task.id IN
            <foreach collection="bizTask.bizTaskDecomposeIdList" item="decomposeId" open="(" close=")" separator=",">
                #{decomposeId}
            </foreach>
        </if>

        <!--任务标题-->
        <if test="bizTask.title != null and bizTask.title != ''">
            and sup_task.title like concat('%',#{bizTask.title},'%')
        </if>

        <!--分解任务是否办结-->
        <if test="bizTask.hasFinish != null">
            and dec_task.has_finish = #{bizTask.hasFinish}
        </if>

        <!--主任务是否办结-->
        <if test="bizTask.supHasFinish != null">
            and sup_task.has_finish = #{bizTask.supHasFinish}
        </if>

        <!--主任务创建人部门ID-->
        <if test="bizTask.departmentId != null">
            and sup_task.department_id = #{bizTask.departmentId}
        </if>


        <!--任务类型-->
        <if test="bizTask.taskType != null and bizTask.taskType != ''">
            and sup_task.task_type = #{bizTask.taskType}
        </if>
        <!--已签收状态 可能包含撤回数据-->
        <if test="bizTask.hasSign == 1 ">
            and ( dec_task.has_sign = #{bizTask.hasSign} or (dec_task.receive_time is not null and dec_task.has_sign =2
            ))
        </if>
        <!---->
        <!-- 未签收状态 隐藏含撤回数据 -->
        <if test=' bizTask.hasSign != null and bizTask.hasSign == 0 '>
            and ( dec_task.has_sign = #{bizTask.hasSign} )
        </if>

        <!--只取已经签收的数据-->
        <if test="bizTask.hasSign == 99 ">
            and dec_task.has_sign = 1
        </if>


        <!--任务来自转办-->
        <if test="bizTask.hasTransfer != null ">
            and dec_task.has_transfer = #{bizTask.hasTransfer}
        </if>
        <!--责任人id-->
        <if test="bizTask.dutyPeopleId != null and bizTask.dutyPeopleId != ''">
            and (duty_people_id = #{bizTask.dutyPeopleId} or contact_people_id = #{bizTask.dutyPeopleId} )
        </if>

        <!--主任务单位id-->
        <if test="bizTask.supCompanyId != null and bizTask.supCompanyId != ''">
            and sup_task.company_id = #{bizTask.supCompanyId}
        </if>

        <!--主任务创建人id过滤-->
        <if test="bizTask.supCreateById != null and bizTask.supCreateById != '' and bizTask.supHasFinish ==0 ">
            and dec_task.conclude_people_id LIKE concat('%',#{bizTask.supCreateById},'%')
        </if>

        <!--主任务创建人id过滤，已办结的数据-->
        <!--        <if test="bizTask.supCreateById != null and bizTask.supCreateById != '' and bizTask.supHasFinish ==1 ">-->
        <!--&#45;&#45;             and (sup_task.create_by = #{bizTask.supCreateById}-->
        <!--&#45;&#45;             or sup_task.lead_people_id = #{bizTask.supCreateById})-->
        <!--            and dec_task.conclude_people_id = #{bizTask.supCreateById}-->
        <!--        </if>-->
        <if test="bizTask.supCreateById != null and bizTask.supCreateById != '' and bizTask.supHasFinish ==1 ">
            and sup_task.create_by = #{bizTask.supCreateById}
        </if>

        <!--移动端searchKey-->
        <if test="bizTask.searchKey != null and bizTask.searchKey != ''">
            and sup_task.title like concat('%',#{bizTask.searchKey},'%')
        </if>
        <!--taskAddType 主任务是否是转办-->
        <if test="bizTask.taskAddType != null ">
            and sup_task.task_add_type = #{bizTask.taskAddType}
        </if>


        <!--交办日期范围搜索-->
        <if test="bizTask.startTime != null and bizTask.startTime != ''">
            AND date_format(dec_task.receive_time,'%y%m%d') >= date_format(#{bizTask.startTime},'%y%m%d')
        </if>
        <if test="bizTask.endTime != null and bizTask.endTime != ''">
            AND date_format(#{bizTask.endTime},'%y%m%d') >= date_format(dec_task.receive_time,'%y%m%d')
        </if>

        <!-- app 1正常，2逾期 3 临期 -->
        <if test='bizTask.queryType == "1" '>
            and DATEDIFF( sup_task.due_time,DATE(NOW())) &gt; 3 and dec_task.has_finish != 1
        </if>

        <if test='bizTask.queryType == "2" '>
            and DATEDIFF( sup_task.due_time,DATE(NOW())) &lt; 0 and dec_task.has_finish != 1
        </if>

        <if test='bizTask.queryType == "3" '>
            and 3 &gt;= DATEDIFF(sup_task.due_time,DATE(NOW())) AND DATEDIFF(sup_task.due_time,DATE(NOW())) &gt;= 0 and
            dec_task.has_finish != 1
        </if>
        <!-- startDate任务开始时间小于现在     任务结束时间(endDate)大于现在 -->
        <if test="bizTask.atStartDate2EndDate != null and bizTask.atStartDate2EndDate != '' ">
            <![CDATA[
                AND date_format(dec_task.start_date,'%Y-%m-%d')  <  #{bizTask.atStartDate2EndDate}
                AND date_format(dec_task.end_date,'%Y-%m-%d') >=  #{bizTask.atStartDate2EndDate}
            ]]>
        </if>

        <!--smsReminder 短信提醒方式  2 代表每周;3 代表每月-->
        <if test="bizTask.smsReminder != null and bizTask.smsReminder != ''">
            and dec_task.sms_reminder = #{bizTask.smsReminder}
        </if>

        <!--责任人名称-->
        <if test="bizTask.dutyPeople != null and bizTask.dutyPeople != ''">
            and (dec_task.duty_people_name like concat('%',#{bizTask.dutyPeople},'%') )
        </if>


        <!--紧急-->
        <if test="bizTask.degreeUrgency != null  and bizTask.degreeUrgency != '' ">
            and (sup_task.degree_urgency = #{bizTask.degreeUrgency} )
        </if>

        <!--签收人-->
        <if test="bizTask.signPeopleId != null  and bizTask.signPeopleId != '' ">
            and (dec_task.sign_people_id = #{bizTask.signPeopleId} or contact_people_id = #{bizTask.signPeopleId})
        </if>

        <if test="bizTask.sortSub != null and bizTask.sortSub != '' and bizTask.sortSub == 1">
            <!-- 成果管理 已办结,办结时间降序 -->
            ORDER BY dec_task.CREATE_TIME desc
        </if>

    </sql>


    <sql id="selectFromSub">
        FROM biz_task_decompose as dec_task
        INNER JOIN biz_task_supervision as sup_task
        on sup_task.id = dec_task.sub_tbl_fk
        INNER JOIN biz_task_score_sub AS btsu
        on btsu.biz_task_decompose = dec_task.id
        WHERE
        sup_task.deleted = 0
        and dec_task.deleted = 0 and sup_task.has_publish in (1,2)
        <!--接收时间-->
        <if test="bizTask.receiveTime != null and bizTask.receiveTime != ''">
            and dec_task.receive_time = #{bizTask.receiveTime}
        </if>

        <!--发布状态-->
        <if test="bizTask.hasPublish != null ">
            and sup_task.has_publish = #{bizTask.hasPublish}
        </if>


        <!-- 是否区分单位 -->
        <if test="bizTask.dutyPeopleCompanyId != null and bizTask.dutyPeopleCompanyId != ''">
            and dec_task.duty_people_company_id = #{bizTask.dutyPeopleCompanyId}
        </if>

        <!-- taskTag任务标签 -->
        <if test="bizTask.taskTag != null and bizTask.taskTag != ''">
            and sup_task.task_tag = #{bizTask.taskTag}
        </if>
        <!-- taskSource任务来源 -->
        <if test="bizTask.taskSource != null and bizTask.taskSource != ''">
            and sup_task.task_source = #{bizTask.taskSource}
        </if>

        <!-- 是否有反馈 -->
        <if test="bizTask.hasFeedback != null ">
            and dec_task.has_feedback = #{bizTask.hasFeedback}
        </if>


        <!--主任务id-->
        <if test="bizTask.taskSupervisionId != null and bizTask.taskSupervisionId != ''">
            and sup_task.id = #{bizTask.taskSupervisionId}
        </if>

        <!--子任务id-->
        <if test="bizTask.taskDecomposeId != null and bizTask.taskDecomposeId != ''">
            and dec_task.id = #{bizTask.taskDecomposeId}
        </if>
        <!--子任务id集合-->
        <if test="null != bizTask.bizTaskDecomposeIdList and bizTask.bizTaskDecomposeIdList.size > 0">
            and dec_task.id IN
            <foreach collection="bizTask.bizTaskDecomposeIdList" item="decomposeId" open="(" close=")" separator=",">
                #{decomposeId}
            </foreach>
        </if>

        <!--任务标题-->
        <if test="bizTask.title != null and bizTask.title != ''">
            and sup_task.title like concat('%',#{bizTask.title},'%')
        </if>

        <!--分解任务是否办结-->
        <if test="bizTask.hasFinish != null">
            and dec_task.has_finish = #{bizTask.hasFinish}
        </if>

        <!--主任务是否办结-->
        <if test="bizTask.supHasFinish != null">
            and sup_task.has_finish = #{bizTask.supHasFinish}
        </if>


        <!--任务类型-->
        <if test="bizTask.taskType != null and bizTask.taskType != ''">
            and sup_task.task_type = #{bizTask.taskType}
        </if>
        <!--已签收状态 可能包含已经撤回数据-->
        <if test="bizTask.hasSign == 1 ">
            and ( dec_task.has_sign = #{bizTask.hasSign} or (dec_task.receive_time is not null and dec_task.has_sign =2
            ))
        </if>
        <!---->
        <!-- 未签收状态 隐藏含撤回数据 -->
        <if test=' bizTask.hasSign != null and bizTask.hasSign == 0  '>
            and ( dec_task.has_sign = #{bizTask.hasSign} )


        </if>

        <!--只取已经签收的数据-->
        <if test="bizTask.hasSign == 99 ">
            and dec_task.has_sign = 1
        </if>


        <!--任务来自转办-->
        <if test="bizTask.hasTransfer != null ">
            and dec_task.has_transfer = #{bizTask.hasTransfer}
        </if>
        <!--责任人id-->
        <if test="bizTask.dutyPeopleId != null and bizTask.dutyPeopleId != ''">
            and (duty_people_id = #{bizTask.dutyPeopleId} or contact_people_id = #{bizTask.dutyPeopleId})
        </if>

        <!--主任务单位id-->
        <if test="bizTask.supCompanyId != null and bizTask.supCompanyId != ''">
            and sup_task.company_id = #{bizTask.supCompanyId}
        </if>

        <!--主任务创建人id过滤-->
        <if test="bizTask.supCreateById != null and bizTask.supCreateById != ''">
            and sup_task.create_by = #{bizTask.supCreateById}
        </if>


        <!--移动端searchKey-->
        <if test="bizTask.searchKey != null and bizTask.searchKey != ''">
            and sup_task.title like concat('%',#{bizTask.searchKey},'%')
        </if>
        <!--taskAddType 主任务是否是转办-->
        <if test="bizTask.taskAddType != null ">
            and sup_task.task_add_type = #{bizTask.taskAddType}
        </if>


        <!--交办日期范围搜索-->
        <if test="bizTask.startTime != null and bizTask.startTime != ''">
            AND date_format(dec_task.create_time,'%y%m%d') >= date_format(#{bizTask.startTime},'%y%m%d')
        </if>
        <if test="bizTask.endTime != null and bizTask.endTime != ''">
            AND date_format(#{bizTask.endTime},'%y%m%d') >= date_format(dec_task.create_time,'%y%m%d')
        </if>

        <!-- app 1正常，2逾期 3 临期 -->
        <if test='bizTask.queryType == "1" '>
            and DATEDIFF( sup_task.due_time,DATE(NOW())) &gt; 3 and dec_task.has_finish != 1
        </if>

        <if test='bizTask.queryType == "2" '>
            and DATEDIFF( sup_task.due_time,DATE(NOW())) &lt; 0 and dec_task.has_finish != 1
        </if>

        <if test='bizTask.queryType == "3" '>
            and 3 &gt;= DATEDIFF(sup_task.due_time,DATE(NOW())) AND DATEDIFF(sup_task.due_time,DATE(NOW())) &gt;= 0 and
            dec_task.has_finish != 1
        </if>
        <!-- startDate任务开始时间小于现在     任务结束时间(endDate)大于现在 -->
        <if test="bizTask.atStartDate2EndDate != null and bizTask.atStartDate2EndDate != '' ">
            <![CDATA[
                AND date_format(dec_task.start_date,'%Y-%m-%d')  <  #{bizTask.atStartDate2EndDate}
                AND date_format(dec_task.end_date,'%Y-%m-%d') >=  #{bizTask.atStartDate2EndDate}
            ]]>
        </if>

        <!--smsReminder 短信提醒方式  2 代表每周;3 代表每月-->
        <if test="bizTask.smsReminder != null and bizTask.smsReminder != ''">
            and dec_task.sms_reminder = #{bizTask.smsReminder}
        </if>


        <if test="bizTask.sortSub != null and bizTask.sortSub != '' and bizTask.sortSub == 1">
            <!-- 成果管理 已办结,办结时间降序 -->
            ORDER BY dec_task.CREATE_TIME desc
        </if>
    </sql>


    <select id="selectCommonTaskList" parameterType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            resultMap="BizTaskResultMap">
        <include refid="selectTask"/>
        <include refid="selectFrom"/>


        ORDER BY

        <!-- app 临期自定义排序 -->
        <if test='bizTask.sortField != null and bizTask.sortField != ""  and bizTask.sortType != null and bizTask.sortType != ""   '>
            ${bizTask.sortField} ${bizTask.sortType} ,
        </if>
        <!--交办日期 降序 selectCommonTaskSubList-->
        dec_task.CREATE_TIME desc,sup_task.due_time,dec_task.receive_time desc
    </select>


    <select id="selectCommonTaskListCount" parameterType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            resultType="java.lang.Integer">
        SELECT COUNT(a.sup_id) FROM (SELECT DISTINCT sup_task.id AS sup_id
        <include refid="selectFrom"/>) AS a
    </select>

    <!-- 查询未签收的数据 -->
    <select id="selectNoSignCommonTaskList" parameterType="com.ctsi.huaihua.entity.dto.NoHasSignTaskDTO"
            resultMap="BizTaskResultMap">
        <include refid="selectTask"/>
        FROM biz_task_decompose as dec_task
        INNER JOIN biz_task_supervision as sup_task
        on sup_task.id = dec_task.sub_tbl_fk
        WHERE
        sup_task.deleted = 0
        and dec_task.deleted = 0 and sup_task.has_publish =1
        and dec_task.has_sign = 0

        <!--任务标题-->
        <if test="bizTask.title != null and bizTask.title != ''">
            and sup_task.title like concat('%',#{bizTask.title},'%')
        </if>

        <!--交办日期范围搜索-->
        <if test="bizTask.startTime != null and bizTask.startTime != ''">
            AND date_format(dec_task.create_time,'%y%m%d') >= date_format(#{bizTask.startTime},'%y%m%d')
        </if>
        <if test="bizTask.endTime != null and bizTask.endTime != ''">
            AND date_format(#{bizTask.endTime},'%y%m%d') >= date_format(dec_task.create_time,'%y%m%d')
        </if>

        <!--责任人名称-->
        <if test="bizTask.queryType != null and bizTask.queryType ==1 ">
            and (dec_task.duty_people_id = #{bizTask.dutyPeopleId} )
        </if>


        <!--责任过滤及其取单位的-->
        <if test="bizTask.queryType != null and bizTask.queryType ==2 ">
            and (dec_task.duty_people_id = #{bizTask.dutyPeopleId} or dec_task.contact_people_id =
            #{bizTask.dutyPeopleId})
        </if>

        <!--紧急-->
        <if test="bizTask.degreeUrgency != null  and bizTask.degreeUrgency != '' ">
            and (sup_task.degree_urgency = #{bizTask.degreeUrgency} )
        </if>
        ORDER BY

        <!--交办日期 降序 selectCommonTaskSubList-->
        dec_task.CREATE_TIME desc,sup_task.due_time,dec_task.receive_time desc
    </select>


    <select id="selectCommonTaskSubList" parameterType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            resultMap="BizTaskResultMap">
        <include refid="selectTask"/>
        <include refid="selectFromSub"/>
        GROUP BY dec_task.id
        ORDER BY dec_task.CREATE_TIME desc
    </select>


    <!-- 反馈管理-->
    <select id="selectFeedbackTaskList" parameterType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            resultMap="BizTaskResultMap">
        <include refid="selectTask"/>
        <include refid="selectFrom"/>

        <!--签收日期 接收时间 降序排序 -->
        ORDER BY dec_task.has_finish desc ,dec_task.receive_time desc
    </select>

    <!-- 成果管理 成果收件箱-->
    <select id="selectBoxTaskList" parameterType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            resultMap="BizTaskResultMap">
        <include refid="selectTask"/>
        <include refid="selectFrom"/>

        <!-- 最新反馈降序,截止时间升序 -->
        ORDER BY dec_task.new_feedback_sign desc, dec_task.end_date asc
    </select>

    <!-- 成果管理 已办结-->
    <select id="selectFinishTaskBoxList" parameterType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            resultMap="BizTaskResultMap">
        <include refid="selectTask"/>
        <include refid="selectFrom"/>

        <if test="bizTask.sortSub == null and bizTask.sortSub == ''">
            <!-- 成果管理 已办结,办结时间降序 -->
            ORDER BY dec_task.has_finish_time desc
        </if>
    </select>

    <!--分页查询已减的子任务-->
    <select id="selectReducedTask" parameterType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            resultMap="BizTaskResultMap">
        <include refid="selectTask"/>
        FROM
        biz_task_decompose AS dec_task
        INNER JOIN biz_task_supervision AS sup_task ON sup_task.id = dec_task.sub_tbl_fk
        WHERE
        sup_task.deleted = 0
        and dec_task.deleted = 0 and sup_task.has_publish in (1,2)
        <!--接收时间-->
        <if test="bizTask.receiveTime != null and bizTask.receiveTime != ''">
            and dec_task.receive_time = #{bizTask.receiveTime}
        </if>

        <!--发布状态-->
        <if test="bizTask.hasPublish != null ">
            and sup_task.has_publish = #{bizTask.hasPublish}
        </if>


        <!-- 是否区分单位 -->
        <if test="bizTask.dutyPeopleCompanyId != null and bizTask.dutyPeopleCompanyId != ''">
            and dec_task.duty_people_company_id = #{bizTask.dutyPeopleCompanyId}
        </if>

        <!-- taskTag任务标签 -->
        <if test="bizTask.taskTag != null and bizTask.taskTag != ''">
            and sup_task.task_tag = #{bizTask.taskTag}
        </if>
        <!-- taskSource任务来源 -->
        <if test="bizTask.taskSource != null and bizTask.taskSource != ''">
            and sup_task.task_source = #{bizTask.taskSource}
        </if>

        <!-- 是否有反馈 -->
        <if test="bizTask.hasFeedback != null ">
            and dec_task.has_feedback = #{bizTask.hasFeedback}
        </if>


        <!--主任务id-->
        <if test="bizTask.taskSupervisionId != null and bizTask.taskSupervisionId != ''">
            and sup_task.id = #{bizTask.taskSupervisionId}
        </if>

        <!--子任务id-->
        <if test="bizTask.taskDecomposeId != null and bizTask.taskDecomposeId != ''">
            and dec_task.id = #{bizTask.taskDecomposeId}
        </if>
        <!--子任务id集合-->
        <if test="null != bizTask.bizTaskDecomposeIdList and bizTask.bizTaskDecomposeIdList.size > 0">
            and dec_task.id IN
            <foreach collection="bizTask.bizTaskDecomposeIdList" item="decomposeId" open="(" close=")" separator=",">
                #{decomposeId}
            </foreach>
        </if>

        <!--任务标题-->
        <if test="bizTask.title != null and bizTask.title != ''">
            and sup_task.title like concat('%',#{bizTask.title},'%')
        </if>

        <!--分解任务是否办结-->
        <if test="bizTask.hasFinish != null">
            and dec_task.has_finish = #{bizTask.hasFinish}
        </if>

        <!--主任务是否办结-->
        <if test="bizTask.supHasFinish != null">
            and sup_task.has_finish = #{bizTask.supHasFinish}
        </if>


        <!--任务类型-->
        <if test="bizTask.taskType != null and bizTask.taskType != ''">
            and sup_task.task_type = #{bizTask.taskType}
        </if>
        <!--已签收状态 可能包含撤回数据-->
        <if test="bizTask.hasSign == 1 ">
            and ( dec_task.has_sign = #{bizTask.hasSign} or (dec_task.receive_time is not null and dec_task.has_sign =2
            ))
        </if>
        <!---->
        <!-- 未签收状态 隐藏含撤回数据 -->
        <if test=' bizTask.hasSign != null and bizTask.hasSign == 0 '>
            and ( dec_task.has_sign = #{bizTask.hasSign} )
        </if>

        <!--只取已经签收的数据-->
        <if test="bizTask.hasSign == 99 ">
            and dec_task.has_sign = 1
        </if>


        <!--任务来自转办-->
        <if test="bizTask.hasTransfer != null ">
            and dec_task.has_transfer = #{bizTask.hasTransfer}
        </if>
        <!--责任人id-->
        <if test="bizTask.dutyPeopleId != null and bizTask.dutyPeopleId != ''">
            and (duty_people_id = #{bizTask.dutyPeopleId} or contact_people_id = #{bizTask.dutyPeopleId})
        </if>

        <!--主任务单位id-->
        <if test="bizTask.supCompanyId != null and bizTask.supCompanyId != ''">
            and sup_task.company_id = #{bizTask.supCompanyId}
        </if>

        <!--主任务创建人id过滤-->
        <if test="bizTask.supCreateById != null and bizTask.supCreateById != ''">
            and sup_task.create_by = #{bizTask.supCreateById}
        </if>


        <!--移动端searchKey-->
        <if test="bizTask.searchKey != null and bizTask.searchKey != ''">
            and sup_task.title like concat('%',#{bizTask.searchKey},'%')
        </if>
        <!--taskAddType 主任务是否是转办-->
        <if test="bizTask.taskAddType != null ">
            and sup_task.task_add_type = #{bizTask.taskAddType}
        </if>


        <!--交办日期范围搜索-->
        <if test="bizTask.startTime != null and bizTask.startTime != ''">
            AND date_format(dec_task.create_time,'%y%m%d') >= date_format(#{bizTask.startTime},'%y%m%d')
        </if>
        <if test="bizTask.endTime != null and bizTask.endTime != ''">
            AND date_format(#{bizTask.endTime},'%y%m%d') >= date_format(dec_task.create_time,'%y%m%d')
        </if>

        <!-- app 1正常，2逾期 3 临期 -->
        <if test='bizTask.queryType == "1" '>
            and DATEDIFF( sup_task.due_time,DATE(NOW())) &gt; 3 and dec_task.has_finish != 1
        </if>

        <if test='bizTask.queryType == "2" '>
            and DATEDIFF( sup_task.due_time,DATE(NOW())) &lt; 0 and dec_task.has_finish != 1
        </if>

        <if test='bizTask.queryType == "3" '>
            and 3 &gt;= DATEDIFF(sup_task.due_time,DATE(NOW())) AND DATEDIFF(sup_task.due_time,DATE(NOW())) &gt;= 0 and
            dec_task.has_finish != 1
        </if>
        <!-- startDate任务开始时间小于现在     任务结束时间(endDate)大于现在 -->
        <if test="bizTask.atStartDate2EndDate != null and bizTask.atStartDate2EndDate != '' ">
            <![CDATA[
                AND date_format(dec_task.start_date,'%Y-%m-%d')  <  #{bizTask.atStartDate2EndDate}
                AND date_format(dec_task.end_date,'%Y-%m-%d') >=  #{bizTask.atStartDate2EndDate}
            ]]>
        </if>

        <!--smsReminder 短信提醒方式  2 代表每周;3 代表每月-->
        <if test="bizTask.smsReminder != null and bizTask.smsReminder != ''">
            and dec_task.sms_reminder = #{bizTask.smsReminder}
        </if>

        <!--责任人名称-->
        <if test="bizTask.dutyPeople != null and bizTask.dutyPeople != ''">
            and (dec_task.duty_people_name like concat('%',#{bizTask.dutyPeople},'%') )
        </if>

        GROUP BY sup_task.id

        <if test="bizTask.sortSub != null and bizTask.sortSub != '' and bizTask.sortSub == 1">
            <!-- 成果管理 已办结,办结时间降序 -->
            ORDER BY dec_task.CREATE_TIME desc
        </if>

        <if test="bizTask.sortSub == null and bizTask.sortSub == ''">
            <!-- 成果管理 已办结,办结时间降序 -->
            ORDER BY dec_task.has_finish_time desc
        </if>

    </select>


    <!-- Mysql获取发布和我的承办 -->
    <select id="selectSupAndDecomposeTaskList" parameterType="com.ctsi.huaihua.entity.dto.BizHomeTaskDTO"
            resultMap="BizHomeTaskResultMap">

        select t.sup_id as id ,t.form_id,t.title,t.decompose_id as task_decompose_id,t.due_time,t.CREATE_TIME from (

        select sup.id as sup_id,sup.form_id,sup.title,0 as decompose_id,sup.due_time,sup.CREATE_TIME
        from biz_task_supervision sup
        where sup.deleted = 0 and sup.create_by = #{bizTask.dutyPeopleId} and sup.has_publish = 1
        and sup.has_finish = 0
        union all

        select dec_task.sub_tbl_fk as sup_id,ts.form_id,ts.title,dec_task.id as decompose_id,dec_task.end_date as
        due_time,dec_task.CREATE_TIME
        from biz_task_decompose as dec_task INNER JOIN biz_task_supervision ts
        on dec_task.sub_tbl_fk = ts.id

        where dec_task.deleted = 0 and dec_task.has_feedback = 0 and dec_task.has_sign = 1 and ts.deleted = 0 and
        ts.has_publish = 1 and ts.has_finish = 0
        and (dec_task.duty_people_id =#{bizTask.dutyPeopleId} or dec_task.contact_people_id =#{bizTask.dutyPeopleId} ))
        t where 1= 1
        <!--1正常，2逾期 3 临期-->
        <if test='bizTask.queryType == "1" '>
            and DATEDIFF( t.due_time,DATE(NOW())) &gt; 3
        </if>

        <if test='bizTask.queryType == "2" '>
            and DATEDIFF( t.due_time,DATE(NOW())) &lt; 0
        </if>

        <if test='bizTask.queryType == "3" '>
            and 3 &gt;= DATEDIFF(t.due_time,DATE(NOW())) AND DATEDIFF(t.due_time,DATE(NOW())) &gt;= 0
        </if>

        order by CREATE_TIME desc

    </select>


    <!-- 达梦获取发布和我的承办 -->
    <select id="selectDMSupAndDecomposeTaskList" parameterType="com.ctsi.huaihua.entity.dto.BizHomeTaskDTO"
            resultMap="BizHomeTaskResultMap">

        select t.sup_id as id ,t.form_id,t.title,t.decompose_id as task_decompose_id,t.due_time,t.CREATE_TIME from (

        select sup.id as sup_id,sup.form_id,sup.title,0 as decompose_id,sup.due_time,sup.CREATE_TIME
        from biz_task_supervision sup
        where sup.deleted = 0 and sup.create_by = #{bizTask.dutyPeopleId} and sup.has_publish = 1
        and sup.has_finish = 0
        union all

        select dec_task.sub_tbl_fk as sup_id,ts.form_id,ts.title,dec_task.id as decompose_id,dec_task.end_date as
        due_time,dec_task.CREATE_TIME
        from biz_task_decompose as dec_task INNER JOIN biz_task_supervision ts
        on dec_task.sub_tbl_fk = ts.id

        where dec_task.deleted = 0 and dec_task.has_feedback = 0 and dec_task.has_sign = 1 and ts.deleted = 0 and
        ts.has_publish = 1 and ts.has_finish = 0
        and (dec_task.duty_people_id =#{bizTask.dutyPeopleId} or dec_task.contact_people_id =#{bizTask.dutyPeopleId} ))
        t where 1= 1
        <!--1正常，2逾期 3 临期-->
        <if test='bizTask.queryType == "1" '>
            and DATEDIFF(dd,CURDATE(),t.due_time) &gt; 3
        </if>

        <if test='bizTask.queryType == "2" '>
            and DATEDIFF(dd,CURDATE(),t.due_time) &lt; 0
        </if>

        <if test='bizTask.queryType == "3" '>
            and 3 &gt;= DATEDIFF(dd,CURDATE(),t.due_time) AND DATEDIFF(dd,CURDATE(),t.due_time) &gt;= 0
        </if>

        order by CREATE_TIME desc

    </select>

    <!-- 人大金仓获取发布和我的承办 -->
    <select id="selectKingbaseSupAndDecomposeTaskList" parameterType="com.ctsi.huaihua.entity.dto.BizHomeTaskDTO"
            resultMap="BizHomeTaskResultMap">

        select t.sup_id as id ,t.form_id,t.title,t.decompose_id as task_decompose_id,t.due_time,t.CREATE_TIME from (

        select sup.id as sup_id,sup.form_id,sup.title,0 as decompose_id,sup.due_time,sup.CREATE_TIME
        from biz_task_supervision sup
        where sup.deleted = 0 and sup.create_by = #{bizTask.dutyPeopleId} and sup.has_publish = 1
        and sup.has_finish = 0
        union all

        select dec_task.sub_tbl_fk as sup_id,ts.form_id,ts.title,dec_task.id as decompose_id,dec_task.end_date as
        due_time,dec_task.CREATE_TIME
        from biz_task_decompose as dec_task INNER JOIN biz_task_supervision ts
        on dec_task.sub_tbl_fk = ts.id

        where dec_task.deleted = 0 and dec_task.has_feedback = 0 and dec_task.has_sign = 1 and ts.deleted = 0 and
        ts.has_publish = 1 and ts.has_finish = 0
        and (dec_task.duty_people_id =#{bizTask.dutyPeopleId} or dec_task.contact_people_id =#{bizTask.dutyPeopleId} ))
        t where 1= 1
        <!--1正常，2逾期 3 临期-->
        <if test='bizTask.queryType == "1" '>
            and extract(day from age(t.due_time,date(now()))) &gt; 3
        </if>

        <if test='bizTask.queryType == "2" '>
            and extract(day from age(t.due_time,date(now()))) &lt; 0
        </if>

        <if test='bizTask.queryType == "3" '>
            and 3 &gt;= extract(day from age(t.due_time,date(now()))) AND extract(day from age(t.due_time,date(now())))
            &gt;= 0
        </if>

        order by CREATE_TIME desc

    </select>

    <select id="selectTasks"
            parameterType="com.ctsi.huaihua.entity.dto.BizCompanyNumberOfTasksDTO"
            resultType="com.ctsi.huaihua.entity.dto.BizCompanyNumberOfTasksDTO">
        SELECT
        DATE_FORMAT( create_time, '%Y-%m' ) year,
        count(*) count
        FROM
        biz_task_supervision
        WHERE
        company_id = #{cnot.companyId}
        AND CREATE_TIME >= #{cnot.startTime}
        AND CREATE_TIME &lt;= #{cnot.endTime}
        GROUP BY
        year
    </select>
    <select id="receiveTask" resultMap="BizTaskResultMap">
        SELECT
        dec_task.has_finish_time, dec_task.duty_people_company_id, sup_task.company_id, dec_task.duty_people_id
        FROM
        biz_task_decompose AS dec_task
        INNER JOIN biz_task_supervision AS sup_task ON sup_task.id = dec_task.sub_tbl_fk
        WHERE
        sup_task.deleted = 0
        AND dec_task.deleted = 0
        AND dec_task.receive_time IS NOT NULL
        AND sup_task.has_publish = 1
        <if test="null != hasFinish and hasFinish != '' or hasFinish == 0">
            AND sup_task.has_finish = #{hasFinish}
            AND dec_task.has_finish = #{hasFinish}
        </if>
        <if test="null != companyIds and companyIds.size > 0">
            AND dec_task.duty_people_company_id IN
            <foreach collection="companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            AND sup_task.company_id NOT IN
            <foreach collection="companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="null != queryType and queryType != '' and  queryType == 1">
            AND sup_task.CREATE_TIME >= #{startTime}
            AND sup_task.CREATE_TIME &lt;= #{endTime}
        </if>

        <if test="null!=queryType and queryType != '' and  queryType == 2">
            AND sup_task.has_finish_time >= #{startTime}
            AND sup_task.has_finish_time &lt;= #{endTime}
        </if>

        GROUP BY dec_task.sub_tbl_fk,dec_task.duty_people_company_id
    </select>


    <select id="selectBizTaskScores" resultMap="BizTaskResultMap">
        SELECT
        dec_task.duty_people_id,bts.actual_score AS dec_task_score
        FROM
        biz_task_decompose dec_task
        LEFT JOIN biz_task_supervision sup_task ON dec_task.sub_tbl_fk = sup_task.id
        LEFT JOIN biz_task_score bts ON bts.biz_task_decompose = dec_task.id
        WHERE
        sup_task.deleted = 0
        AND dec_task.deleted = 0
        AND bts.deleted = 0
        AND sup_task.has_publish IN ( 1, 2 )
        AND dec_task.has_feedback = 1
        AND dec_task.has_finish = 1
        AND sup_task.has_finish = 1
        AND dec_task.CREATE_TIME >= #{startTime}
        AND dec_task.CREATE_TIME &lt;= #{endTime}
        <if test="null != userIds and userIds.size > 0">
            AND dec_task.duty_people_id IN
            <foreach collection="userIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        GROUP BY dec_task.sub_tbl_fk,dec_task.duty_people_id
    </select>

    <select id="selectUserTasks" resultType="com.ctsi.huaihua.entity.dto.BizTaskDTO"
            parameterType="com.ctsi.huaihua.entity.dto.BizTaskStatisticsDTO">
        SELECT
        <foreach collection="condition.returnFields" item="item" separator=",">
            ${item}
        </foreach>
        FROM
        biz_task_supervision AS sup_task
        LEFT JOIN biz_task_decompose AS dec_task ON sup_task.id = dec_task.sub_tbl_fk
        <where>
            sup_task.deleted = 0
            AND dec_task.deleted = 0
            <if test="null != condition.hasPublish and condition.hasPublish != '' or condition.hasPublish == 0 ">
                AND sup_task.has_publish = #{condition.hasPublish}
            </if>
            <if test="null != condition.hasSign and condition.hasSign != '' or condition.hasSign == 0 ">
                AND dec_task.has_sign = #{condition.hasSign}
            </if>
            <if test="null != condition.supHasFinish and condition.supHasFinish != '' or condition.supHasFinish == 0 ">
                AND sup_task.has_finish = #{condition.supHasFinish}
            </if>
            <if test="null != condition.decHasFinish and condition.decHasFinish != '' or condition.decHasFinish == 0 ">
                AND dec_task.has_finish = #{condition.decHasFinish}
            </if>
            <if test="null != condition.userIds and condition.userIds.size > 0">
                AND dec_task.duty_people_id IN
                <foreach collection="condition.userIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.userCompanyId and condition.userCompanyId.size > 0">
                AND dec_task.duty_people_company_id IN
                <foreach collection="condition.userCompanyId" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.taskStartTime">
                AND sup_task.CREATE_TIME >= #{condition.taskStartTime}
            </if>
            <if test="null != condition.taskEndTime">
                AND sup_task.CREATE_TIME &lt;= #{condition.taskEndTime}
            </if>
            <if test="null != condition.hasFinishStartTime">
                AND sup_task.has_finish_time >= #{condition.hasFinishStartTime}
            </if>
            <if test="null != condition.hasFinishEndTime">
                AND sup_task.has_finish_time &lt;= #{condition.hasFinishEndTime}
            </if>
            <if test="null != condition.filterCompanyId and condition.filterCompanyId.size > 0">
                AND sup_task.company_id NOT IN
                <foreach collection="condition.filterCompanyId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.taskCompanyIds and condition.taskCompanyIds.size > 0">
                AND dec_task.company_id in
                <foreach collection="condition.taskCompanyIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.subTaskCompanyIds and condition.subTaskCompanyIds.size > 0">
                AND sup_task.company_id in
                <foreach collection="condition.subTaskCompanyIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.subTaskIds and condition.subTaskIds.size > 0">
                AND dec_task.sub_tbl_fk in
                <foreach collection="condition.subTaskIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        <if test="null != condition.groupingFields and condition.groupingFields.size > 0">
            GROUP BY
            <foreach collection="condition.groupingFields" item="item" separator=",">
                ${item}
            </foreach>
        </if>
    </select>

    <select id="received" resultType="com.ctsi.huaihua.entity.dto.BizTaskDTO">
        SELECT
        sup_task.company_id,sup_task.CREATE_TIME
        FROM
        biz_task_supervision sup_task
        INNER JOIN biz_task_decompose dec_task ON sup_task.id = dec_task.sub_tbl_fk
        WHERE
        sup_task.deleted = 0 AND dec_task.deleted = 0
        AND sup_task.has_publish = 1
        <if test="null != companyIds and companyIds.size > 0">
            AND dec_task.company_id IN
            <foreach collection="companyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="null != hasFinish and hasFinish != '' or hasFinish == 0">
            AND sup_task.has_finish = #{hasFinish}
        </if>
        <if test="null!=queryType and queryType != '' and  queryType == 1">
            AND sup_task.CREATE_TIME >= #{startTime}
            AND sup_task.CREATE_TIME &lt;= #{endTime}
        </if>

        <if test="null!=queryType and queryType != '' and  queryType == 2">
            AND sup_task.has_finish_time >= #{startTime}
            AND sup_task.has_finish_time &lt;= #{endTime}
        </if>
        GROUP BY dec_task.sub_tbl_fk
    </select>

    <select id="selectTaskDecomposeCount" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            biz_task_decompose
        WHERE
            deleted = 0
            AND sub_tbl_fk =  #{subTblFk} AND has_finish = 0
            and ( contact_people_id !=  #{userId} AND duty_people_id !=  #{userId})
    </select>

    <delete id="deleteBySubTblFk">
        delete FROM biz_task_decompose where sub_tbl_fk =  #{subTblFk}
    </delete>

</mapper>
