<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.huaihua.mapper.BizPunishScoreRecordMapper">

    <select id="selectBizTaskSupervisions" resultType="com.ctsi.huaihua.entity.dto.BizPunishScoreRecordDTO">
        SELECT
        bupsr.user_id,bpsr.score
        FROM
        biz_punish_score_record bpsr
        INNER JOIN biz_user_punish_score_rel bupsr ON bpsr.id = bupsr.punish_score_record_id
        WHERE bpsr.deleted = 0 and bupsr.deleted=0 and bpsr.has_reviewer = 1
        AND bpsr.CREATE_TIME >= #{startTime}
        AND bpsr.CREATE_TIME &lt;= #{endTime}
        <if test="null != userIds and userIds.size > 0">
            AND bupsr.user_id IN
            <foreach collection="userIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
