<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.huaihua.mapper.BizTaskScoreAddMapper">

    <resultMap id="TaskSupervisionAndScoreAddMap" type="com.ctsi.huaihua.entity.dto.BizTaskSupervisionAndScoreAddDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="SUP_CREATE_NAME" jdbcType="VARCHAR" property="supCreateName"/>
        <result column="SUP_CREATE_TIME" jdbcType="DATE" property="supCreateTime"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="task_source" jdbcType="VARCHAR" property="taskSource"/>
        <result column="examine_method" jdbcType="VARCHAR" property="examineMethod"/>
        <result column="due_time" jdbcType="DATE" property="dueTime"/>
        <result column="duty_people" jdbcType="LONGVARCHAR" property="dutyPeople"/>
        <result column="task_transfer_decompose_id" jdbcType="BIGINT" property="taskTransferDecomposeId"/>
        <result column="score_add_has_reviewer" jdbcType="INTEGER" property="hasReviewer"/>
        <result column="score_add_id" jdbcType="BIGINT" property="taskScoreAddId"/>
        <result column="biz_task_decompose_id" jdbcType="BIGINT" property="bizTaskDecomposeId"/>
        <result column="score_add_has_read" jdbcType="INTEGER" property="taskScoreAddHasRead"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="score" jdbcType="DOUBLE" property="score"/>
        <result column="score_add_content" jdbcType="VARCHAR" property="scoreAddContent"/>
        <result column="score_add_reason" jdbcType="VARCHAR" property="scoreAddReason"/>
        <result column="refuse_reason" jdbcType="VARCHAR" property="refuseReason"/>
        <result column="score_add_create_name" jdbcType="VARCHAR" property="scoreAddCreateName"/>
        <result column="score_add_create_time" jdbcType="DATE" property="scoreAddCreateTime"/>


    </resultMap>

    <select id="queryPageTaskSupervisionAndScoreAddInfo" parameterType="com.ctsi.huaihua.entity.dto.BizTaskSupervisionAndScoreAddDTO" resultMap="TaskSupervisionAndScoreAddMap">
        select bts.id, bts.CREATE_NAME SUP_CREATE_NAME, bts.CREATE_TIME SUP_CREATE_TIME, bts.title, bts.task_source, bts.examine_method, bts.due_time, bts.duty_people,
        bts.task_type, bts.task_transfer_decompose_id, btsa.has_reviewer score_add_has_reviewer,
        btsa.id score_add_id, btsa.biz_task_decompose biz_task_decompose_id, btsa.has_read score_add_has_read, btsa.score, btsa.score_add_content, btsa.score_add_reason,
        btsa.refuse_reason, btsa.create_name score_add_create_name, btsa.create_time score_add_create_time
        from biz_task_supervision bts
        INNER JOIN biz_task_score_add btsa
        ON bts.id = btsa.biz_task_supervision_id
        where bts.deleted = 0
        AND btsa.deleted = 0
        <if test="dto.title != null and dto.title != ''">
            AND bts.title like concat('%',#{dto.title},'%')
        </if>
        <if test = "dto.bizTaskSupervisionIdList != null  and dto.bizTaskSupervisionIdList.size() > 0">
            AND bts.id IN
            <foreach collection="dto.bizTaskSupervisionIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test = "dto.taskScoreAddIdList != null and dto.taskScoreAddIdList.size() > 0">
            AND btsa.id IN
            <foreach collection="dto.taskScoreAddIdList" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
        </if>
        ORDER BY
        <!-- 如果是加分申请接收人且查看的是未审核，根据接收时间降序排序 -->
        <if test="dto.scoreAddReviewerId != null and dto.scoreAddReviewerId != '' and dto.hasReviewerList != null and dto.hasReviewerList.size() == 1">
            btsa.CREATE_TIME DESC,
        </if>
        <!-- 如果是加分申请接收人且查看的是已审核，根据审核时间降序排序 -->
        <if test="dto.scoreAddReviewerId != null and dto.scoreAddReviewerId != '' and dto.hasReviewerList != null and dto.hasReviewerList.size() > 1">
            btsa.audit_time DESC,
        </if>
        <!-- 如果是加分申请人且查看的内容是未审核的加分申请，需要按加分申请创建时间降序排序 -->
        <if test="dto.scoreAddCreateId != null and dto.scoreAddCreateId != '' and dto.hasReviewerList != null and dto.hasReviewerList.size() == 1">
            btsa.CREATE_TIME DESC,
        </if>
        <!-- 如果是加分申请人且查看的内容是已审核的加分申请，需要优先未阅的数据优先显示，且根据通过时间降序排序 -->
        <if test="dto.scoreAddCreateId != null and dto.scoreAddCreateId != '' and dto.hasReviewerList != null and dto.hasReviewerList.size() > 1">
            btsa.has_read ASC, btsa.audit_time DESC,
        </if>
        bts.create_time DESC, btsa.id
    </select>

</mapper>
