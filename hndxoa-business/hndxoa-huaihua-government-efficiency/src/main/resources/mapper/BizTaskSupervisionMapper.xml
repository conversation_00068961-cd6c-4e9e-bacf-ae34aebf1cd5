<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.huaihua.mapper.BizTaskSupervisionMapper">

    <resultMap id="BizDeTaskFinishCtDTOResultMap" type="com.ctsi.huaihua.entity.dto.BizDeTaskFinishCtDTO">

        <result column="finishCount" jdbcType="INTEGER" property="finishCount"/>
        <result column="duty_people_id" jdbcType="BIGINT" property="dutyPeopleId"/>
        <result column="duty_people_name" jdbcType="VARCHAR" property="dutyPeopleName"/>
        <result column="duty_people_post_name" jdbcType="VARCHAR" property="dutyPeoplePostName"/>
        <result column="duty_people_department_name" jdbcType="VARCHAR" property="dutyPeopleDepartmentName"/>
        <result column="duty_people_company_name" jdbcType="VARCHAR" property="dutyPeopleCompanyName"/>

    </resultMap>


    <!-- 获取发布和我的承办 -->
    <select id="queryDecomposeTaskGroupDutyPeople" parameterType="com.ctsi.huaihua.entity.dto.BizHomeTaskDTO"
            resultMap="BizDeTaskFinishCtDTOResultMap">

        SELECT
        COUNT(duty_people_id) AS finishCount,
        duty_people_id,
        t.duty_people_name,
        t.duty_people_department_name,
        t.duty_people_company_name,
        t.duty_people_post_name
        FROM
        biz_task_decompose t
        WHERE 1=1 AND deleted = 0 AND t.has_finish=1

        <!--单位-->
        <if test="taskFinishCtDTO.dutyPeopleCompanyId != null and taskFinishCtDTO.dutyPeopleCompanyId != ''">
            and duty_people_company_id = #{taskFinishCtDTO.dutyPeopleCompanyId}
        </if>

        GROUP BY t.duty_people_id
        ORDER BY finishCount DESC


    </select>

    <!-- 获取主任务列表 -->
    <select id="queryList" parameterType="com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO"
            resultType="com.ctsi.huaihua.entity.BizTaskSupervision">
        select DISTINCT sup_task.id,sup_task.title,sup_task.task_type AS taskType,sup_task.degree_urgency AS
        degreeUrgency,
        sup_task.task_tag AS taskTag,sup_task.examine_method AS examineMethod,sup_task.due_time AS
        dueTime,sup_task.has_publish AS hasPublish,sup_task.has_finish AS hasFinish,
        sup_task.department_name AS departmentName,sup_task.mobile,sup_task.company_name AS
        companyName,sup_task.task_number AS taskNumber,sup_task.task_level AS taskLevel,
        sup_task.task_add_type AS taskAddType,sup_task.form_id AS formId,sup_task.new_sup_feedback_sign AS
        newSupFeedbackSign,sup_task.sup_has_feedback AS supHasFeedback,
        sup_task.lead_people_id AS leadPeopleId,sup_task.create_name AS createName,sup_task.CREATE_TIME AS createTime
        FROM biz_task_supervision as sup_task
        INNER JOIN biz_task_decompose as dec_task
        on sup_task.id = dec_task.sub_tbl_fk
        WHERE
        sup_task.deleted = 0 and dec_task.deleted = 0
        <if test="dto.hasFinish != null">
            and sup_task.has_finish = #{dto.hasFinish}
            and dec_task.has_finish = #{dto.hasFinish}
        </if>

        <if test="dto.supHasFeedback != null">
            and sup_task.sup_has_feedback = #{dto.supHasFeedback}
            AND dec_task.has_feedback = 1
        </if>

        <if test="dto.companyId != null and dto.companyId != '' ">
            and sup_task.company_id = #{dto.companyId}
        </if>

        <if test="dto.companyId != null and dto.companyId != '' ">
            and sup_task.company_id = #{dto.companyId}
        </if>
        <if test="dto.leadPeopleId != null and dto.leadPeopleId != '' and dto.hasFinish ==0 ">
            and dec_task.conclude_people_id LIKE concat('%',#{dto.leadPeopleId},'%')
        </if>

        <if test="dto.leadPeopleId != null and dto.leadPeopleId != '' and dto.hasFinish ==1 ">
            and sup_task.CREATE_BY = #{dto.leadPeopleId}
        </if>
        order by sup_task.new_sup_feedback_sign desc,sup_task.due_time asc


    </select>

    <sql id="selectTask">
        SELECT sup_task.id,
               dec_task.task_score AS dec_task_score,
               dec_task.id as task_decompose_id,
               sup_task.task_number,
               sup_task.degree_urgency,
               sup_task.title,
               sup_task.task_description,
               dec_task.preview,
               sup_task.create_name,
               sup_task.create_time as sup_create_time,
               sup_task.company_id as sup_company_id,
               sup_task.department_name,
               sup_task.company_name,
               dec_task.create_time,
               dec_task.end_date as due_time,
               sup_task.due_time as sup_due_time,
               dec_task.receive_time,
               sup_task.task_source as query_type,
               sup_task.form_id,
               sup_task.task_tag,
               sup_task.task_source,
               sup_task.task_type,

               sup_task.has_publish,
                sup_task.task_score,
               dec_task.has_sign,
               sup_task.has_finish as sup_has_finish,
               dec_task.has_finish ,
               dec_task.has_finish_time ,
               sup_task.task_add_type,
               sup_task.task_transfer_id,
               dec_task.has_transfer,
               dec_task.duty_people_name,
               dec_task.duty_people_id,
               dec_task.contact_people_id,
               dec_task.duty_people_post_name,
               dec_task.contact_people_name,
               dec_task.has_feedback,
               dec_task.new_feedback_sign,
               dec_task.new_finish_sign,
               dec_task.task_delay_status,
               dec_task.content,
               dec_task.degree_difficulty,
               dec_task.sms_reminder,
               dec_task.start_date,
               dec_task.end_date,
               dec_task.sms_send_time,
               dec_task.duty_people_department_name,
               dec_task.duty_people_company_name,
               dec_task.duty_people_company_id,
               dec_task.new_feedback_time,
               sup_task.inspector_task_level,
               sup_task.inspector_items,
               sup_task.difficulty,
               sup_task.lead_people_id
    </sql>

    <resultMap id="BizTaskResultMap" type="com.ctsi.huaihua.entity.dto.BizTaskDTO">
        <id column="id" jdbcType="BIGINT" property="taskSupervisionId"/>
        <id column="sup_company_id" jdbcType="BIGINT" property="supCompanyId"/>
        <result column="task_decompose_id" jdbcType="BIGINT" property="taskDecomposeId"/>
        <result column="task_number" jdbcType="VARCHAR" property="taskNumber"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="duty_people_department_name" jdbcType="VARCHAR" property="dutyPeopleDepartmentName"/>
        <result column="duty_people_company_name" jdbcType="VARCHAR" property="dutyPeopleCompanyName"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="sup_create_time" jdbcType="TIMESTAMP" property="supCreateTime"/>
        <result column="due_time" jdbcType="DATE" property="dueTime"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="sms_send_time" jdbcType="DATE" property="smsSendTime"/>
        <result column="sup_due_time" jdbcType="DATE" property="supDueTime"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="query_type" jdbcType="VARCHAR" property="queryType"/>
        <result column="task_tag" jdbcType="VARCHAR" property="taskTag"/>
        <result column="task_source" jdbcType="VARCHAR" property="taskSource"/>
        <result column="dec_task_score" jdbcType="VARCHAR" property="decomposeTaskSource"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="has_publish" jdbcType="INTEGER" property="hasPublish"/>
        <result column="form_id" jdbcType="INTEGER" property="formId"/>
        <result column="has_finish" jdbcType="INTEGER" property="hasFinish"/>
        <result column="has_finish_time" jdbcType="INTEGER" property="hasFinishTime"/>
        <result column="sup_has_finish" jdbcType="INTEGER" property="supHasFinish"/>
        <result column="task_add_type" jdbcType="INTEGER" property="taskAddType"/>
        <result column="task_transfer_id" jdbcType="BIGINT" property="taskTransferId"/>
        <result column="has_sign" jdbcType="INTEGER" property="hasSign"/>
        <result column="has_transfer" jdbcType="INTEGER" property="hasTransfer"/>
        <result column="duty_people_name" jdbcType="VARCHAR" property="dutyPeople"/>
        <result column="duty_people_name" jdbcType="VARCHAR" property="dutyPeopleName"/>
        <result column="duty_people_id" jdbcType="BIGINT" property="dutyPeopleId"/>
        <result column="contact_people_id" jdbcType="BIGINT" property="contactPeopleId"/>
        <result column="contact_people_name" jdbcType="VARCHAR" property="contactPeopleName"/>
        <result column="duty_people_post_name" jdbcType="VARCHAR" property="dutyPeoplePostName"/>
        <result column="has_feedback" jdbcType="INTEGER" property="hasFeedback"/>
        <result column="new_feedback_sign" jdbcType="INTEGER" property="newFeedbackSign"/>
        <result column="new_finish_sign" jdbcType="INTEGER" property="newFinishSign"/>
        <result column="duty_people_company_id" jdbcType="INTEGER" property="dutyPeopleCompanyId"/>
        <result column="new_feedback_time" jdbcType="INTEGER" property="newFeedbackTime"/>
        <result column="task_delay_status" jdbcType="INTEGER" property="taskDelayStatus"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="degree_difficulty" jdbcType="VARCHAR" property="degreeDifficulty"/>
        <result column="sms_reminder" jdbcType="VARCHAR" property="smsReminder"/>
        <result column="preview" javaType="INTEGER" property="preview"/>
        <result column="degree_urgency" jdbcType="VARCHAR" property="degreeUrgency"/>
        <result column="inspector_task_level" jdbcType="VARCHAR" property="inspectorTaskLevel"/>
        <result column="inspector_items" jdbcType="VARCHAR" property="inspectorItems"/>
        <result column="task_score" jdbcType="VARCHAR" property="actualScore"/>
    </resultMap>

    <select id="statisticalAssignment" resultMap="BizTaskResultMap">
        SELECT
        sup_task.has_finish_time, sup_task.company_id AS sup_company_id
        FROM
        biz_task_decompose AS dec_task
        INNER JOIN biz_task_supervision AS sup_task ON sup_task.id = dec_task.sub_tbl_fk
        WHERE
        sup_task.deleted = 0
        AND dec_task.deleted = 0
        AND sup_task.has_publish = 1
        <if test="null != hasFinish and hasFinish != '' or hasFinish==0 ">
            AND sup_task.has_finish = #{hasFinish}
        </if>
        <if test="null != companyIds and companyIds.size > 0 or hasFinish==0 ">
            AND dec_task.company_id in
            <foreach collection="companyIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="null != startTime">
            AND sup_task.CREATE_TIME >= #{startTime}
        </if>
        <if test="null != endTime">
            AND sup_task.CREATE_TIME &lt;= #{endTime}
        </if>
        GROUP BY dec_task.sub_tbl_fk,dec_task.duty_people_id
    </select>
    <select id="queryReceiveConcludeTaskCount" resultType="com.ctsi.huaihua.entity.dto.BizTaskDTO">
        SELECT
        dec_task.duty_people_company_id,
        dec_task.duty_people_id
        FROM
        biz_task_supervision sup_task
        LEFT JOIN biz_task_decompose dec_task ON sup_task.id = dec_task.sub_tbl_fk
        <where>
            sup_task.has_publish = 1
            AND dec_task.receive_time IS NOT NULL
            AND dec_task.has_sign = 1
            AND dec_task.deleted = 0
            AND sup_task.deleted = 0
            <if test="null != conditions.queryType">
                AND sup_task.has_finish = 1
                AND dec_task.has_finish = 1
            </if>

            <if test="null != conditions.companyId and conditions.companyId.size > 0">
                AND dec_task.duty_people_company_id IN
                <foreach collection="conditions.companyId" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                <!--                AND sup_task.company_id NOT IN-->
                <!--                <foreach collection="conditions.companyId" item="id" index="index" open="(" close=")" separator=",">-->
                <!--                    #{id}-->
                <!--                </foreach>-->
            </if>

            <if test="null != conditions.userId and conditions.userId.size > 0">
                AND dec_task.duty_people_id IN
                <foreach collection="conditions.userId" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                <!--                <if test="null != conditions.userCompanyId and conditions.userCompanyId.size > 0">-->
                <!--                    AND sup_task.company_id NOT IN-->
                <!--                    <foreach collection="conditions.userCompanyId" item="id" index="index" open="(" close=")"-->
                <!--                             separator=",">-->
                <!--                        #{id}-->
                <!--                    </foreach>-->
                <!--                </if>-->
            </if>


            <if test="null != conditions.startTime">
                AND dec_task.receive_time >= #{conditions.startTime}
            </if>
            <if test="null != conditions.endTime">
                AND dec_task.receive_time &lt;= #{conditions.endTime}
            </if>
        </where>
        GROUP BY
        dec_task.sub_tbl_fk,
        dec_task.duty_people_company_id
    </select>


</mapper>
