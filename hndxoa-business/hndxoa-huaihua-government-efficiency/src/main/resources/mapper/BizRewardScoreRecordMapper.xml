<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.huaihua.mapper.BizRewardScoreRecordMapper">

    <select id="selectBizTaskScoreAdds" resultType="com.ctsi.huaihua.entity.dto.BizPunishScoreRecordDTO">
        SELECT
        burs.user_id,brsr.score
        FROM
        biz_reward_score_record brsr
        INNER JOIN biz_user_reward_score_rel burs ON brsr.id = burs.reward_score_record_id
        WHERE
        brsr.deleted = 0
        AND burs.deleted = 0
        AND brsr.has_reviewer = 1
        AND brsr.CREATE_TIME >= #{startTime}
        AND brsr.CREATE_TIME &lt;= #{endTime}
        <if test="null != userIds and userIds.size > 0">
            AND burs.user_id IN
            <foreach collection="userIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
