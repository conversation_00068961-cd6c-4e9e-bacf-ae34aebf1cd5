<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.huaihua.mapper.BizTaskDelayMapper">

    <resultMap id="TaskSupervisionAndDelayMap" type="com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="CREATE_NAME" jdbcType="VARCHAR" property="createName"/>
        <result column="CREATE_TIME" jdbcType="DATE" property="createTime"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="degree_urgency" jdbcType="VARCHAR" property="degreeUrgency"/>
        <result column="task_source" jdbcType="VARCHAR" property="taskSource"/>
        <result column="task_tag" jdbcType="VARCHAR" property="taskTag"/>
        <result column="examine_method" jdbcType="VARCHAR" property="examineMethod"/>
        <result column="due_time" jdbcType="DATE" property="dueTime"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="annex" jdbcType="VARCHAR" property="annex"/>
        <result column="has_publish" jdbcType="INTEGER" property="hasPublish"/>
        <result column="has_finish" jdbcType="INTEGER" property="hasFinish"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="task_number" jdbcType="VARCHAR" property="taskNumber"/>
        <result column="task_level" jdbcType="INTEGER" property="taskLevel"/>
        <result column="task_add_type" jdbcType="INTEGER" property="taskAddType"/>
        <result column="duty_people" jdbcType="LONGVARCHAR" property="dutyPeople"/>
        <result column="task_transfer_id" jdbcType="BIGINT" property="taskTransferId"/>
        <result column="task_transfer_decompose_id" jdbcType="BIGINT" property="taskTransferDecomposeId"/>
        <result column="form_id" jdbcType="BIGINT" property="formId"/>
        <result column="delay_before_end_date" jdbcType="DATE" property="delayBeforeEndDate"/>
        <result column="delay_end_date" jdbcType="DATE" property="delayEndDate"/>
        <result column="delay_has_reviewer" jdbcType="INTEGER" property="hasReviewer"/>
        <result column="delay_has_read" jdbcType="INTEGER" property="taskDelayHasRead"/>
        <result column="delay_id" jdbcType="BIGINT" property="taskDelayId"/>
        <result column="biz_task_decompose_id" jdbcType="BIGINT" property="bizTaskDecomposeId"/>
        <result column="manual_delay_end_date" jdbcType="DATE" property="manualDelayEndDate"/>

    </resultMap>

    <select id="queryPageTaskSupervisionAndDelayInfo" parameterType="com.ctsi.huaihua.entity.dto.BizTaskSupervisionDTO" resultMap="TaskSupervisionAndDelayMap">
        select bts.id, bts.CREATE_NAME, bts.CREATE_TIME, bts.title, bts.task_type, bts.degree_urgency, bts.task_source, bts.task_tag,
            bts.examine_method, bts.due_time, bts.task_description, bts.annex, bts.has_publish, bts.has_finish, bts.department_name, bts.mobile, bts.company_name,
            bts.task_number, bts.task_level, bts.task_add_type, duty_people, bts.task_transfer_id, bts.task_transfer_decompose_id, bts.form_id, btd.delay_before_end_date delay_before_end_date,
            btd.delay_end_date delay_end_date, btd.has_reviewer delay_has_reviewer, btd.has_read delay_has_read, btd.id delay_id, btd.biz_task_decompose biz_task_decompose_id,
            btd.manual_delay_end_date manual_delay_end_date
        from biz_task_supervision bts
        INNER JOIN biz_task_delay btd
        ON bts.id = btd.biz_task_supervision_id
        where bts.deleted = 0
        AND btd.deleted = 0
        <if test="dto.title != null and dto.title != ''">
            AND bts.title like concat('%',#{dto.title},'%')
        </if>
        <if test="dto.startTime != null  and dto.startTime != ''">
                AND bts.create_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null  and dto.endTime != ''">
            AND bts.create_time &lt;= #{dto.endTime}
        </if>
        <if test = "null != dto.bizTaskSupervisionIdList and dto.bizTaskSupervisionIdList.size > 0">
            AND bts.id IN
            <foreach collection="dto.bizTaskSupervisionIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test = "null != dto.taskDelayIdList and dto.taskDelayIdList.size > 0">
            AND btd.id IN
            <foreach collection="dto.taskDelayIdList" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
        </if>
        ORDER BY
        <!-- 如果是延期接收人且查看的是未审核，根据接收时间降序排序 -->
        <if test="dto.delayReviewerId != null and dto.delayReviewerId != '' and dto.hasReviewerList.size() == 1">
            btd.CREATE_TIME DESC,
        </if>
        <!-- 如果是延期接收人且查看的是已审核，根据审核时间降序排序 -->
        <if test="dto.delayReviewerId != null and dto.delayReviewerId != '' and dto.hasReviewerList.size() > 1">
            btd.audit_time DESC,
        </if>
        <!-- 如果是延期申请人且查看的内容是未审核的延期申请，需要延期申请创建时间降序排序 -->
        <if test="dto.delayCreateId != null and dto.delayCreateId != '' and dto.hasReviewerList.size() == 1">
            btd.CREATE_TIME DESC,
        </if>
        <!-- 如果是延期申请人且查看的内容是已审核的延期申请，需要优先未阅的数据优先显示，且根据通过时间降序排序 -->
        <if test="dto.delayCreateId != null and dto.delayCreateId != '' and dto.hasReviewerList.size() > 1">
            btd.has_read ASC, btd.audit_time DESC,
        </if>
        bts.create_time DESC, btd.id
    </select>

</mapper>
