package com.ctsi.hndxoa.issues.service;

import com.ctsi.hndxoa.issues.entity.dto.BizPaperlessIssuesDTO;
import com.ctsi.hndxoa.issues.entity.BizPaperlessIssues;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface IBizPaperlessIssuesService extends SysBaseServiceI<BizPaperlessIssues> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizPaperlessIssuesDTO> queryListPage(BizPaperlessIssuesDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizPaperlessIssuesDTO> queryList(BizPaperlessIssuesDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizPaperlessIssuesDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizPaperlessIssuesDTO create(BizPaperlessIssuesDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizPaperlessIssuesDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);


    /**
     * 根据会议id 删除所有议题和 用户
     * @param id
     * @return
     */
    int deleteByMeetingId(Long id);
     /**
     * 是否存在
     *
     * existByBizPaperlessIssuesId
     * @param code
     * @return
     */
    boolean existByBizPaperlessIssuesId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizPaperlessIssuesDTO> dataList);


    /**
     * 查询会议的议程数据,带附件,不分页
     * @param bizPaperlessIssuesDTO
     * @return
     */
    List<BizPaperlessIssuesDTO> queryListAndAnnex(BizPaperlessIssuesDTO bizPaperlessIssuesDTO);
}
