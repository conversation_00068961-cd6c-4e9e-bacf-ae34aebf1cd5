<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.paperlessConference.mapper.BizPaperlessMeetingsMapper">

    <select id="appQueryListPage"
            resultType="com.ctsi.hndxoa.paperlessConference.entity.dto.BizPaperlessMeetingsDTO">

        SELECT
               a.id,
            a.title,
            a.create_name,
            a.department_name,
            a.meet_start_time,
            a.meet_end_time,
            a.meeting_status,
            a.meeting_venue,
            a.annex,
        a.conference_host_name,
        a.conference_host_id,
        a.create_by,
        a.create_name,
            a.create_time
        FROM
            biz_paperless_meetings a
                LEFT JOIN biz_meetings_issues_user b
                          ON a.id = b.meetings_id
                                 where 1=1
                                   and a.deleted =0
                                   and b.deleted =0

                                   and b.user_id = #{userId}


        <if test="searchKey != null and searchKey != ''">
           and a.title like concat('%',#{searchKey},'%')

        </if>
                                   <!-- 会议状态<meetingStatus> 0:未开始 会议时间升序  1:进行中 会议时间降序  2:已结束 会议时间降序 -->
        <if test="meetingStatus == 2">
            and meeting_status = 2
            GROUP BY a.id
            ORDER BY meet_start_time DESC ,a.id
        </if>
        <if test="meetingStatus == 0">
            AND a.meet_start_time &gt;= NOW() and meeting_status != 2
            GROUP BY a.id
            ORDER BY meet_start_time asc , meet_end_time asc ,a.id
        </if>
        <if test="meetingStatus == 1">
            AND a.meet_start_time &lt;= NOW() and meeting_status != 2
            GROUP BY a.id
            ORDER BY meet_start_time DESC ,a.id
        </if>


    </select>
</mapper>
