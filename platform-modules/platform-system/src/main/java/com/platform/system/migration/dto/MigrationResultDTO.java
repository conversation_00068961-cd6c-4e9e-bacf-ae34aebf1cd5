package com.platform.system.migration.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 迁移结果数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class MigrationResultDTO {

    /**
     * 迁移是否成功
     */
    private Boolean success;

    /**
     * 迁移消息
     */
    private String message;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 数据统计信息
     */
    private MigrationStatisticsDTO statistics;

    /**
     * 迁移到SysOrg的记录数
     */
    private Integer sysOrgMigratedCount = 0;

    /**
     * 迁移到SysDept的记录数
     */
    private Integer sysDeptMigratedCount = 0;

    /**
     * 最终SysOrg表记录数
     */
    private Long finalSysOrgCount = 0L;

    /**
     * 最终SysDept表记录数
     */
    private Long finalSysDeptCount = 0L;

    /**
     * 验证错误信息
     */
    private List<String> validationErrors;

    /**
     * 详细的迁移步骤日志
     */
    private List<String> migrationLogs;

    /**
     * 获取迁移耗时（毫秒）
     */
    public Long getDurationMillis() {
        if (startTime != null && endTime != null) {
            return java.time.Duration.between(startTime, endTime).toMillis();
        }
        return null;
    }

    /**
     * 添加迁移日志
     */
    public void addLog(String log) {
        if (migrationLogs == null) {
            migrationLogs = new java.util.ArrayList<>();
        }
        migrationLogs.add(LocalDateTime.now() + ": " + log);
    }
}
