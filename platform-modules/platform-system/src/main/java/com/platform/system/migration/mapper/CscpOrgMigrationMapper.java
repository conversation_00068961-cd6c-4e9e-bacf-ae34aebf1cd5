package com.platform.system.migration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.platform.system.migration.dto.CscpOrgMigrationDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * CscpOrg迁移数据访问层
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Mapper
public interface CscpOrgMigrationMapper extends BaseMapper<CscpOrgMigrationDTO> {

    /**
     * 统计总记录数
     */
    @Select("SELECT COUNT(*) FROM cscp_org")
    Long countTotal();

    /**
     * 按类型统计记录数
     */
    @Select("SELECT COUNT(*) FROM cscp_org WHERE type = #{type} AND (deleted = 0 OR deleted IS NULL)")
    Long countByType(@Param("type") Integer type);

    /**
     * 统计已删除记录数
     */
    @Select("SELECT COUNT(*) FROM cscp_org WHERE deleted = 1")
    Long countDeleted();

    /**
     * 查找孤儿节点
     */
    @Select("SELECT id FROM cscp_org o1 WHERE o1.parent_id IS NOT NULL AND o1.parent_id != 0 " +
            "AND NOT EXISTS (SELECT 1 FROM cscp_org o2 WHERE o2.id = o1.parent_id AND (o2.deleted = 0 OR o2.deleted IS NULL)) " +
            "AND (o1.deleted = 0 OR o1.deleted IS NULL)")
    List<Long> findOrphanNodes();

    /**
     * 查找循环引用
     */
    @Select("WITH RECURSIVE org_path AS ( " +
            "  SELECT id, parent_id, CAST(id AS VARCHAR(1000)) as path, 1 as level " +
            "  FROM cscp_org WHERE (deleted = 0 OR deleted IS NULL) " +
            "  UNION ALL " +
            "  SELECT o.id, o.parent_id, CONCAT(op.path, ',', o.id), op.level + 1 " +
            "  FROM cscp_org o " +
            "  INNER JOIN org_path op ON o.parent_id = op.id " +
            "  WHERE op.level < 10 AND (o.deleted = 0 OR o.deleted IS NULL) " +
            "    AND FIND_IN_SET(o.id, op.path) = 0 " +
            ") " +
            "SELECT DISTINCT o.id FROM cscp_org o " +
            "INNER JOIN org_path op ON o.parent_id = op.id " +
            "WHERE FIND_IN_SET(o.id, op.path) > 0")
    List<Long> findCircularReferences();

    /**
     * 统计名称为空的记录数
     */
    @Select("SELECT COUNT(*) FROM cscp_org WHERE (org_name IS NULL OR org_name = '') AND (deleted = 0 OR deleted IS NULL)")
    Long countNullNames();

    /**
     * 查询机构和单位数据（type=1,2）
     */
    @Select("SELECT * FROM cscp_org WHERE type IN (1, 2) AND (deleted = 0 OR deleted IS NULL) ORDER BY level, order_by")
    List<CscpOrgMigrationDTO> selectOrgAndUnitData();

    /**
     * 查询部门数据（type=3）
     */
    @Select("SELECT * FROM cscp_org WHERE type = 3 AND (deleted = 0 OR deleted IS NULL) ORDER BY level, order_by")
    List<CscpOrgMigrationDTO> selectDeptData();

    /**
     * 根据ID查询单条记录
     */
    @Select("SELECT * FROM cscp_org WHERE id = #{id}")
    CscpOrgMigrationDTO selectById(@Param("id") Long id);

    /**
     * 查询指定ID的所有父级机构
     */
    @Select("WITH RECURSIVE parent_orgs AS ( " +
            "  SELECT id, parent_id, org_name, type, level " +
            "  FROM cscp_org WHERE id = #{id} " +
            "  UNION ALL " +
            "  SELECT o.id, o.parent_id, o.org_name, o.type, o.level " +
            "  FROM cscp_org o " +
            "  INNER JOIN parent_orgs p ON o.id = p.parent_id " +
            "  WHERE o.id != p.id " +
            ") " +
            "SELECT * FROM parent_orgs WHERE type = 2 ORDER BY level DESC LIMIT 1")
    CscpOrgMigrationDTO findParentUnit(@Param("id") Long id);

    /**
     * 查询指定父ID下的所有子节点
     */
    @Select("SELECT * FROM cscp_org WHERE parent_id = #{parentId} AND (deleted = 0 OR deleted IS NULL) ORDER BY order_by")
    List<CscpOrgMigrationDTO> selectChildrenByParentId(@Param("parentId") Long parentId);

    /**
     * 查询所有根节点
     */
    @Select("SELECT * FROM cscp_org WHERE (parent_id IS NULL OR parent_id = 0) AND (deleted = 0 OR deleted IS NULL) ORDER BY order_by")
    List<CscpOrgMigrationDTO> selectRootNodes();

    /**
     * 查询最大层级
     */
    @Select("SELECT MAX(level) FROM cscp_org WHERE (deleted = 0 OR deleted IS NULL)")
    Integer selectMaxLevel();

    /**
     * 统计根节点数量
     */
    @Select("SELECT COUNT(*) FROM cscp_org WHERE (parent_id IS NULL OR parent_id = 0) AND (deleted = 0 OR deleted IS NULL)")
    Long countRootNodes();

    /**
     * 查询指定类型的所有记录
     */
    @Select("SELECT * FROM cscp_org WHERE type = #{type} AND (deleted = 0 OR deleted IS NULL) ORDER BY level, order_by")
    List<CscpOrgMigrationDTO> selectByType(@Param("type") Integer type);

    /**
     * 根据orgIdPath查找所属单位
     */
    @Select("SELECT * FROM cscp_org WHERE type = 2 AND FIND_IN_SET(id, #{orgIdPath}) > 0 " +
            "AND (deleted = 0 OR deleted IS NULL) ORDER BY level DESC LIMIT 1")
    CscpOrgMigrationDTO findUnitByOrgIdPath(@Param("orgIdPath") String orgIdPath);

    /**
     * 查询部门的直接父部门
     */
    @Select("SELECT * FROM cscp_org WHERE id = #{parentId} AND type = 3 AND (deleted = 0 OR deleted IS NULL)")
    CscpOrgMigrationDTO selectDeptParent(@Param("parentId") Long parentId);
}
