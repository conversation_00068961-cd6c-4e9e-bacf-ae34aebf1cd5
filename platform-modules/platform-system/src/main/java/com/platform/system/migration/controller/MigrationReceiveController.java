package com.platform.system.migration.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.domain.SysDept;
import com.platform.system.domain.SysOrg;
import com.platform.system.migration.MigrationDataReceiver;
import com.platform.system.migration.dto.MigrationDataDTO;
import com.platform.system.migration.dto.MigrationReceiveResultDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 迁移数据接收控制器
 * 在be-platform-cloud项目中接收来自hnsw-tongyi-jigouyonghu的迁移数据
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Api(tags = "迁移数据接收接口")
@RestController
@RequestMapping("/migration/receive")
@RequiredArgsConstructor
public class MigrationReceiveController {

    private final MigrationDataReceiver migrationDataReceiver;

    /**
     * 接收SysOrg迁移数据
     */
    @ApiOperation(value = "接收SysOrg迁移数据", notes = "接收并保存来自源项目的SysOrg数据")
    @PostMapping("/sys-org")
    public ResponseResult<MigrationReceiveResultDTO> receiveSysOrgData(@RequestBody List<SysOrg> sysOrgList) {
        log.info("接收SysOrg迁移数据请求，数量: {}", sysOrgList.size());
        
        try {
            MigrationReceiveResultDTO result = migrationDataReceiver.receiveSysOrgData(sysOrgList);
            
            if (result.getSuccess()) {
                return ResponseResult.ok(result, "SysOrg数据接收成功");
            } else {
                return ResponseResult.fail(result.getMessage(), result);
            }
            
        } catch (Exception e) {
            log.error("接收SysOrg数据失败", e);
            return ResponseResult.fail("接收SysOrg数据失败: " + e.getMessage());
        }
    }

    /**
     * 接收SysDept迁移数据
     */
    @ApiOperation(value = "接收SysDept迁移数据", notes = "接收并保存来自源项目的SysDept数据")
    @PostMapping("/sys-dept")
    public ResponseResult<MigrationReceiveResultDTO> receiveSysDeptData(@RequestBody List<SysDept> sysDeptList) {
        log.info("接收SysDept迁移数据请求，数量: {}", sysDeptList.size());
        
        try {
            MigrationReceiveResultDTO result = migrationDataReceiver.receiveSysDeptData(sysDeptList);
            
            if (result.getSuccess()) {
                return ResponseResult.ok(result, "SysDept数据接收成功");
            } else {
                return ResponseResult.fail(result.getMessage(), result);
            }
            
        } catch (Exception e) {
            log.error("接收SysDept数据失败", e);
            return ResponseResult.fail("接收SysDept数据失败: " + e.getMessage());
        }
    }

    /**
     * 接收完整的迁移数据包
     */
    @ApiOperation(value = "接收完整迁移数据包", notes = "接收包含SysOrg和SysDept的完整迁移数据")
    @PostMapping("/migration-data")
    public ResponseResult<MigrationReceiveResultDTO> receiveMigrationData(@RequestBody MigrationDataDTO migrationData) {
        log.info("接收完整迁移数据包请求，总数量: {}", migrationData.getTotalCount());
        
        try {
            MigrationReceiveResultDTO result = migrationDataReceiver.receiveMigrationData(migrationData);
            
            if (result.getSuccess()) {
                return ResponseResult.ok(result, "迁移数据包接收成功");
            } else {
                return ResponseResult.fail(result.getMessage(), result);
            }
            
        } catch (Exception e) {
            log.error("接收迁移数据包失败", e);
            return ResponseResult.fail("接收迁移数据包失败: " + e.getMessage());
        }
    }

    /**
     * 清空目标表数据
     */
    @ApiOperation(value = "清空目标表数据", notes = "清空SysOrg和SysDept表数据，用于重新迁移")
    @PostMapping("/clear")
    public ResponseResult<MigrationReceiveResultDTO> clearTargetData() {
        log.info("清空目标表数据请求");
        
        try {
            MigrationReceiveResultDTO result = migrationDataReceiver.clearTargetData();
            
            if (result.getSuccess()) {
                return ResponseResult.ok(result, "目标表数据清空成功");
            } else {
                return ResponseResult.fail(result.getMessage(), result);
            }
            
        } catch (Exception e) {
            log.error("清空目标表数据失败", e);
            return ResponseResult.fail("清空目标表数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证接收的数据
     */
    @ApiOperation(value = "验证接收的数据", notes = "验证已接收数据的完整性和正确性")
    @GetMapping("/validate")
    public ResponseResult<MigrationReceiveResultDTO> validateReceivedData() {
        log.info("验证接收数据请求");
        
        try {
            MigrationReceiveResultDTO result = migrationDataReceiver.validateReceivedData();
            return ResponseResult.ok(result, "数据验证完成");
            
        } catch (Exception e) {
            log.error("数据验证失败", e);
            return ResponseResult.fail("数据验证失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据关系完整性
     */
    @ApiOperation(value = "检查数据关系完整性", notes = "检查部门与机构的关联关系是否正确")
    @GetMapping("/check-integrity")
    public ResponseResult<MigrationReceiveResultDTO> checkDataIntegrity() {
        log.info("检查数据关系完整性请求");
        
        try {
            MigrationReceiveResultDTO result = migrationDataReceiver.checkDataIntegrity();
            return ResponseResult.ok(result, "数据关系完整性检查完成");
            
        } catch (Exception e) {
            log.error("数据关系完整性检查失败", e);
            return ResponseResult.fail("数据关系完整性检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取接收状态
     */
    @ApiOperation(value = "获取接收状态", notes = "获取当前数据接收状态")
    @GetMapping("/status")
    public ResponseResult<String> getReceiveStatus() {
        // 这里可以实现状态查询逻辑
        return ResponseResult.ok("数据接收服务正常运行");
    }
}
