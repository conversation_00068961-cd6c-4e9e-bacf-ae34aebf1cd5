package com.platform.system.migration.controller;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.migration.OrgMigrationService;
import com.platform.system.migration.dto.MigrationResultDTO;
import com.platform.system.migration.dto.MigrationStatisticsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 组织机构数据迁移控制器
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Api(tags = "组织机构数据迁移")
@RestController
@RequestMapping("/migration/org")
@RequiredArgsConstructor
public class OrgMigrationController {

    private final OrgMigrationService orgMigrationService;

    /**
     * 执行数据迁移
     */
    @ApiOperation(value = "执行组织机构数据迁移", notes = "将CscpOrg数据迁移到SysOrg和SysDept")
    @PostMapping("/execute")
    public ResponseResult<MigrationResultDTO> executeMigration() {
        log.info("开始执行组织机构数据迁移...");
        
        try {
            MigrationResultDTO result = orgMigrationService.executeMigration();
            
            if (result.getSuccess()) {
                return ResponseResult.ok(result, "数据迁移成功完成");
            } else {
                return ResponseResult.fail(result.getMessage(), result);
            }
            
        } catch (Exception e) {
            log.error("数据迁移执行失败", e);
            return ResponseResult.fail("数据迁移执行失败: " + e.getMessage());
        }
    }

    /**
     * 预览迁移统计信息
     */
    @ApiOperation(value = "预览迁移统计信息", notes = "查看迁移前的数据统计和验证结果")
    @GetMapping("/preview")
    public ResponseResult<MigrationStatisticsDTO> previewMigration() {
        log.info("预览迁移统计信息...");
        
        try {
            MigrationStatisticsDTO statistics = orgMigrationService.previewMigrationStatistics();
            return ResponseResult.ok(statistics, "统计信息获取成功");
            
        } catch (Exception e) {
            log.error("获取迁移统计信息失败", e);
            return ResponseResult.fail("获取迁移统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证迁移结果
     */
    @ApiOperation(value = "验证迁移结果", notes = "验证迁移后的数据完整性和正确性")
    @GetMapping("/validate")
    public ResponseResult<MigrationResultDTO> validateMigration() {
        log.info("验证迁移结果...");
        
        try {
            MigrationResultDTO result = orgMigrationService.validateMigrationResult();
            return ResponseResult.ok(result, "迁移结果验证完成");
            
        } catch (Exception e) {
            log.error("迁移结果验证失败", e);
            return ResponseResult.fail("迁移结果验证失败: " + e.getMessage());
        }
    }

    /**
     * 回滚迁移数据
     */
    @ApiOperation(value = "回滚迁移数据", notes = "清空迁移的数据，恢复到迁移前状态")
    @PostMapping("/rollback")
    public ResponseResult<Void> rollbackMigration() {
        log.info("开始回滚迁移数据...");
        
        try {
            orgMigrationService.rollbackMigration();
            return ResponseResult.ok("迁移数据回滚成功");
            
        } catch (Exception e) {
            log.error("迁移数据回滚失败", e);
            return ResponseResult.fail("迁移数据回滚失败: " + e.getMessage());
        }
    }

    /**
     * 获取迁移进度
     */
    @ApiOperation(value = "获取迁移进度", notes = "获取当前迁移任务的进度信息")
    @GetMapping("/progress")
    public ResponseResult<String> getMigrationProgress() {
        // 这里可以实现一个进度跟踪机制
        // 由于当前是同步执行，暂时返回简单状态
        return ResponseResult.ok("迁移任务状态查询功能待实现");
    }

    /**
     * 清理迁移缓存
     */
    @ApiOperation(value = "清理迁移缓存", notes = "清理迁移过程中产生的缓存数据")
    @PostMapping("/clear-cache")
    public ResponseResult<Void> clearMigrationCache() {
        log.info("清理迁移缓存...");
        
        try {
            // 这里可以实现缓存清理逻辑
            return ResponseResult.ok("迁移缓存清理成功");
            
        } catch (Exception e) {
            log.error("迁移缓存清理失败", e);
            return ResponseResult.fail("迁移缓存清理失败: " + e.getMessage());
        }
    }
}
