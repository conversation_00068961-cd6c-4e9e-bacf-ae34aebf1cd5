package com.platform.system.migration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.platform.system.api.domain.SysDept;
import com.platform.system.domain.SysOrg;
import com.platform.system.migration.dto.MigrationDataDTO;
import com.platform.system.migration.dto.MigrationReceiveResultDTO;
import com.platform.system.service.SysDeptService;
import com.platform.system.service.SysOrgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 迁移数据接收服务
 * 在be-platform-cloud项目中接收来自hnsw-tongyi-jigouyonghu的迁移数据
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MigrationDataReceiver {

    private final SysOrgService sysOrgService;
    private final SysDeptService sysDeptService;

    /**
     * 接收并保存SysOrg迁移数据
     */
    @Transactional(rollbackFor = Exception.class)
    public MigrationReceiveResultDTO receiveSysOrgData(List<SysOrg> sysOrgList) {
        log.info("开始接收SysOrg迁移数据，数量: {}", sysOrgList.size());
        
        MigrationReceiveResultDTO result = new MigrationReceiveResultDTO();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 批量保存SysOrg数据
            boolean success = sysOrgService.saveBatch(sysOrgList);
            
            if (success) {
                result.setSuccess(true);
                result.setReceivedCount(sysOrgList.size());
                result.setMessage("SysOrg数据接收成功");
                log.info("SysOrg数据接收成功，数量: {}", sysOrgList.size());
            } else {
                result.setSuccess(false);
                result.setMessage("SysOrg数据保存失败");
                log.error("SysOrg数据保存失败");
            }
            
        } catch (Exception e) {
            log.error("接收SysOrg数据失败", e);
            result.setSuccess(false);
            result.setMessage("接收SysOrg数据失败: " + e.getMessage());
            throw e;
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 接收并保存SysDept迁移数据
     */
    @Transactional(rollbackFor = Exception.class)
    public MigrationReceiveResultDTO receiveSysDeptData(List<SysDept> sysDeptList) {
        log.info("开始接收SysDept迁移数据，数量: {}", sysDeptList.size());
        
        MigrationReceiveResultDTO result = new MigrationReceiveResultDTO();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 批量保存SysDept数据
            boolean success = sysDeptService.saveBatch(sysDeptList);
            
            if (success) {
                result.setSuccess(true);
                result.setReceivedCount(sysDeptList.size());
                result.setMessage("SysDept数据接收成功");
                log.info("SysDept数据接收成功，数量: {}", sysDeptList.size());
            } else {
                result.setSuccess(false);
                result.setMessage("SysDept数据保存失败");
                log.error("SysDept数据保存失败");
            }
            
        } catch (Exception e) {
            log.error("接收SysDept数据失败", e);
            result.setSuccess(false);
            result.setMessage("接收SysDept数据失败: " + e.getMessage());
            throw e;
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 接收完整的迁移数据包
     */
    @Transactional(rollbackFor = Exception.class)
    public MigrationReceiveResultDTO receiveMigrationData(MigrationDataDTO migrationData) {
        log.info("开始接收完整迁移数据包");
        
        MigrationReceiveResultDTO result = new MigrationReceiveResultDTO();
        result.setStartTime(LocalDateTime.now());
        
        try {
            int totalReceived = 0;
            
            // 接收SysOrg数据
            if (migrationData.getSysOrgList() != null && !migrationData.getSysOrgList().isEmpty()) {
                MigrationReceiveResultDTO orgResult = receiveSysOrgData(migrationData.getSysOrgList());
                if (!orgResult.getSuccess()) {
                    throw new RuntimeException("SysOrg数据接收失败: " + orgResult.getMessage());
                }
                totalReceived += orgResult.getReceivedCount();
            }
            
            // 接收SysDept数据
            if (migrationData.getSysDeptList() != null && !migrationData.getSysDeptList().isEmpty()) {
                MigrationReceiveResultDTO deptResult = receiveSysDeptData(migrationData.getSysDeptList());
                if (!deptResult.getSuccess()) {
                    throw new RuntimeException("SysDept数据接收失败: " + deptResult.getMessage());
                }
                totalReceived += deptResult.getReceivedCount();
            }
            
            result.setSuccess(true);
            result.setReceivedCount(totalReceived);
            result.setMessage("迁移数据包接收成功");
            
            log.info("完整迁移数据包接收成功，总数量: {}", totalReceived);
            
        } catch (Exception e) {
            log.error("接收迁移数据包失败", e);
            result.setSuccess(false);
            result.setMessage("接收迁移数据包失败: " + e.getMessage());
            throw e;
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 清空目标表数据（用于重新迁移）
     */
    @Transactional(rollbackFor = Exception.class)
    public MigrationReceiveResultDTO clearTargetData() {
        log.info("开始清空目标表数据");
        
        MigrationReceiveResultDTO result = new MigrationReceiveResultDTO();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 清空SysDept表
            sysDeptService.remove(Wrappers.lambdaQuery());
            
            // 清空SysOrg表
            sysOrgService.remove(Wrappers.lambdaQuery());
            
            result.setSuccess(true);
            result.setMessage("目标表数据清空成功");
            
            log.info("目标表数据清空成功");
            
        } catch (Exception e) {
            log.error("清空目标表数据失败", e);
            result.setSuccess(false);
            result.setMessage("清空目标表数据失败: " + e.getMessage());
            throw e;
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 验证接收的数据
     */
    public MigrationReceiveResultDTO validateReceivedData() {
        log.info("开始验证接收的数据");
        
        MigrationReceiveResultDTO result = new MigrationReceiveResultDTO();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 统计接收的数据
            long sysOrgCount = sysOrgService.count();
            long sysDeptCount = sysDeptService.count();
            
            result.setSuccess(true);
            result.setReceivedCount((int) (sysOrgCount + sysDeptCount));
            result.setMessage(String.format("数据验证完成，SysOrg: %d, SysDept: %d", sysOrgCount, sysDeptCount));
            
            log.info("数据验证完成，SysOrg: {}, SysDept: {}", sysOrgCount, sysDeptCount);
            
        } catch (Exception e) {
            log.error("数据验证失败", e);
            result.setSuccess(false);
            result.setMessage("数据验证失败: " + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 检查数据关系完整性
     */
    public MigrationReceiveResultDTO checkDataIntegrity() {
        log.info("开始检查数据关系完整性");
        
        MigrationReceiveResultDTO result = new MigrationReceiveResultDTO();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 检查部门的orgId关联
            LambdaQueryWrapper<SysDept> deptWrapper = Wrappers.lambdaQuery();
            deptWrapper.isNull(SysDept::getOrgId);
            long nullOrgIdCount = sysDeptService.count(deptWrapper);
            
            // 检查ancestors字段
            LambdaQueryWrapper<SysDept> ancestorsWrapper = Wrappers.lambdaQuery();
            ancestorsWrapper.isNull(SysDept::getAncestors);
            long nullAncestorsCount = sysDeptService.count(ancestorsWrapper);
            
            if (nullOrgIdCount == 0 && nullAncestorsCount == 0) {
                result.setSuccess(true);
                result.setMessage("数据关系完整性检查通过");
            } else {
                result.setSuccess(false);
                result.setMessage(String.format("数据关系完整性检查失败，空orgId: %d, 空ancestors: %d", 
                    nullOrgIdCount, nullAncestorsCount));
            }
            
            log.info("数据关系完整性检查完成，空orgId: {}, 空ancestors: {}", nullOrgIdCount, nullAncestorsCount);
            
        } catch (Exception e) {
            log.error("数据关系完整性检查失败", e);
            result.setSuccess(false);
            result.setMessage("数据关系完整性检查失败: " + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }
}
