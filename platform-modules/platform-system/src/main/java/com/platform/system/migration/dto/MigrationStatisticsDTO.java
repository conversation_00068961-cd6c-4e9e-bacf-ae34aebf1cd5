package com.platform.system.migration.dto;

import lombok.Data;

import java.util.List;

/**
 * 迁移统计数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class MigrationStatisticsDTO {

    /**
     * CscpOrg表总记录数
     */
    private Long totalCscpOrgCount = 0L;

    /**
     * 虚拟机构数量（type=1）
     */
    private Long virtualOrgCount = 0L;

    /**
     * 单位数量（type=2）
     */
    private Long unitOrgCount = 0L;

    /**
     * 部门数量（type=3）
     */
    private Long deptOrgCount = 0L;

    /**
     * 已删除的记录数
     */
    private Long deletedCount = 0L;

    /**
     * 有效记录数
     */
    private Long validCount = 0L;

    /**
     * 数据验证错误
     */
    private List<String> validationErrors;

    /**
     * 孤儿节点数量
     */
    private Long orphanNodeCount = 0L;

    /**
     * 循环引用数量
     */
    private Long circularReferenceCount = 0L;

    /**
     * 空名称记录数
     */
    private Long nullNameCount = 0L;

    /**
     * 最大层级深度
     */
    private Integer maxLevel = 0;

    /**
     * 根节点数量
     */
    private Long rootNodeCount = 0L;

    /**
     * 计算有效记录数
     */
    public void calculateValidCount() {
        this.validCount = this.totalCscpOrgCount - this.deletedCount;
    }

    /**
     * 验证数据一致性
     */
    public boolean isDataConsistent() {
        return (virtualOrgCount + unitOrgCount + deptOrgCount) == validCount;
    }
}
