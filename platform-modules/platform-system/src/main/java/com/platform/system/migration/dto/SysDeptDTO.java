package com.platform.system.migration.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * SysDept数据传输对象
 * 避免直接依赖目标项目的实体类
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class SysDeptDTO {

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门状态：0-正常，1-停用
     */
    private String status;

    /**
     * 删除标志：0-正常，2-删除
     */
    private String delFlag;

    /**
     * 所属机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateBy;
}
