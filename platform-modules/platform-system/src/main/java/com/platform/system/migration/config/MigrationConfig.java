package com.platform.system.migration.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 迁移配置类
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "migration.org")
public class MigrationConfig {

    /**
     * 是否启用迁移功能
     */
    private boolean enabled = true;

    /**
     * 批量处理大小
     */
    private int batchSize = 1000;

    /**
     * 是否在迁移前备份数据
     */
    private boolean backupBeforeMigration = true;

    /**
     * 是否在迁移后验证数据
     */
    private boolean validateAfterMigration = true;

    /**
     * 是否允许覆盖已存在的数据
     */
    private boolean allowOverwrite = false;

    /**
     * 迁移超时时间（秒）
     */
    private long timeoutSeconds = 3600;

    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = true;

    /**
     * 数据验证配置
     */
    private ValidationConfig validation = new ValidationConfig();

    /**
     * 性能配置
     */
    private PerformanceConfig performance = new PerformanceConfig();

    @Data
    public static class ValidationConfig {
        /**
         * 是否检查孤儿节点
         */
        private boolean checkOrphanNodes = true;

        /**
         * 是否检查循环引用
         */
        private boolean checkCircularReferences = true;

        /**
         * 是否检查空名称
         */
        private boolean checkNullNames = true;

        /**
         * 是否验证关系完整性
         */
        private boolean validateRelationships = true;

        /**
         * 最大允许的孤儿节点数
         */
        private int maxOrphanNodes = 0;

        /**
         * 最大允许的循环引用数
         */
        private int maxCircularReferences = 0;
    }

    @Data
    public static class PerformanceConfig {
        /**
         * 是否使用并行处理
         */
        private boolean useParallelProcessing = false;

        /**
         * 并行处理线程数
         */
        private int parallelThreads = 4;

        /**
         * 是否使用批量插入
         */
        private boolean useBatchInsert = true;

        /**
         * 批量插入大小
         */
        private int batchInsertSize = 500;

        /**
         * 是否启用事务优化
         */
        private boolean enableTransactionOptimization = true;
    }
}
