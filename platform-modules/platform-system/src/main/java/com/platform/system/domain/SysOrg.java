package com.platform.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

/**
 * @Description: 组织机构实体
 * @author: tr
 * @Date: 2024/3/15 10:06
 */
@Data
@ToString
@TableName("sys_org")
public class SysOrg  extends BaseEntity{

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 机构（主体）名称
	 */
	private String name;

	/** 机构编码 **/
	private String orgCode;

	/**
	 * 组织机构类型
	 */
	private Integer orgType;

	/**
	 * 父级机构Id
	 */
	private Long parentId;

	/**
	 * 联系人
	 */
	private String contactsPerson;

	/**
	 * 联系电话
	 */
	private String contactsPhone;

	/**
	 * 单位联系方式(座机)
	 */
	private String contactsWay;

	/**
	 * 所在地址名称
	 */
	private String dzName;

	/**
	 * 所在地址code
	 */
	private String dzCode;

	/** 所在地址父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY **/
	private String dzPcodePath;

	/**
	 * 所在地址区划级别
	 */
	private Integer dzRegionLevel;

	/**
	 * 详细地址
	 */
	private String dzDetailAddress;

	/**
	 * 角色组Id
	 */
	private Long roleGroupId;

	/**
	 * 管辖范围编码
	 */
	private String guCode;

	/** 管辖地址父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY **/
	private String guPcodePath;

	/**
	 * 管辖范围全称
	 */
	private String guName;

	/**
	 * 管辖范围区划级别
	 */
	private Integer guRegionLevel;

	/**
	 * 状态（0正常 1停用）
	 */
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	@TableField(
		value = "del_flag",
		fill = FieldFill.INSERT
	)
	private String delFlag;

	/** 扩展属性 **/
	private String extProperties;

	/** 第三方机构编码 **/
	private String orgCodeThird;

	/** 第三方上级机构编码 **/
	private String parentCodeThird;

	/** 第三方机构层级路径 **/
	private String codePathThird;

	/** 机构层级 **/
	private String orgLevel;
}
