package com.platform.system.migration.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * CscpOrg迁移数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class CscpOrgMigrationDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 描述
     */
    private String description;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 顺序
     */
    private Integer orderBy;

    /**
     * 机构编码
     */
    private String code;

    /**
     * 机构完整编码
     */
    private String pathCode;

    /**
     * 机构级别
     */
    private Integer level;

    /**
     * 机构类型 1表示虚拟，2表示单位 3表示部门
     */
    private Integer type;

    /**
     * 地区id
     */
    private String lanId;

    /**
     * 地区名称
     */
    private String lan;

    /**
     * 业务时是否发送短信，1表示默认发送，0表示不发送
     */
    private Integer hasSms;

    /**
     * 业务时是否加水印，1表示默认加水印，0表示不加水印
     */
    private Integer hasWatermark;

    /**
     * 是否分屏
     */
    private Integer splitview;

    /**
     * 单位共享云盘空间大小，单位GB，默认2G
     */
    private Integer cloudDiskSpaceSize;

    /**
     * 机构简称
     */
    private String orgAbbreviation;

    /**
     * 是否考核
     */
    private Boolean checkUp;

    /**
     * 单位级别
     */
    private String unitLevel;

    /**
     * 主管部门
     */
    private String authorityDepartment;

    /**
     * 详细类型
     */
    private String detailType;

    /**
     * 分管领导ID
     */
    private Long branchLeaderId;

    /**
     * 实际考核人数
     */
    private Integer assessmentPeopleCount;

    /**
     * 群组号
     */
    private String groupNumber;

    /**
     * 单位类型(0:党委办 1：政府办)
     */
    private Integer unitType;

    /**
     * CRM受理的客户类型（单位类型：1：标准型，2：项目型）
     */
    private String crmTenantType;

    /**
     * 客户经理姓名
     */
    private String nameOfAccountManager;

    /**
     * 客户经理电话
     */
    private String customerManagerTelephone;

    /**
     * 客户管理员姓名
     */
    private String customerAdministratorName;

    /**
     * 客户管理员联系电话
     */
    private String customerAdministratorTelephone;

    /**
     * 账号数
     */
    private Integer numberOfAccounts;

    /**
     * 项目合同期限
     */
    private String projectContractPeriod;

    /**
     * 项目总金额
     */
    private String totalProjectAmount;

    /**
     * sms的name
     */
    private String smsName;

    /**
     * sms的key
     */
    private String smsKey;

    /**
     * 模型别称
     */
    private String modelDataType;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 公文OID
     */
    private String oid;

    /**
     * 档案标识-统一信用代码
     */
    private String creditCode;

    /**
     * 档案标识-代字编码
     */
    private String dzCode;

    /**
     * 档案标识-全宗号
     */
    private String dossierNumber;

    /**
     * 档案标识-组织机构编码
     */
    private String organizationCode;

    /**
     * 请示批件默认编号值
     */
    private String managementDefaultCode;

    /**
     * 收文批件默认编号值
     */
    private String managementReceiveCode;

    /**
     * 保密级别code
     */
    private String securityLevelCode;

    /**
     * 保密级别code
     */
    private String securityLevelCodeName;

    /**
     * 控制首页：个人中心的处事呈批件是否显示，0（默认）：不显示，1：显示
     */
    private String showHomeCpj;

    /**
     * 机构唯一编码
     */
    private String soleCode;

    /**
     * 行政区域编码
     */
    private String regionCode;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 最大编号
     */
    private Integer maxNumber;

    /**
     * 商信机构ID
     */
    private String strId;

    /**
     * 商信父级机构ID
     */
    private String strParentId;

    /**
     * 商信机构信任号
     */
    private String strTrustNo;

    /**
     * 商信父级机构信任号
     */
    private String strParentTrustNo;

    /**
     * 卫士通统一身份认证系统组织机构ID
     */
    private String westoneOrgId;

    /**
     * 卫士通统一身份认证系统上级组织机构ID
     */
    private String westoneOrgParentId;

    /**
     * 跳转对应的服务ID
     */
    private Long serviceId;

    /**
     * 组织机构编码HMAC值，完整性校验
     */
    private String hmacOrgCode;

    /**
     * 外部机构id
     */
    private String externalId;

    /**
     * 外部机构来源
     */
    private String orgOrigin;

    /**
     * 存储从根节点到当前节点的完整层级code路径（如：A|B|C）
     */
    private String orgCodePath;

    /**
     * 存储从根节点到父节点的完整层级id路径（如：1|2）
     */
    private String orgIdPath;

    /**
     * orderByPath
     */
    private String orderByPath;

    /**
     * 别名
     */
    private String aliasName;

    /**
     * 授权推送应用
     */
    private String pushAppCode;

    /**
     * 机构类型(0:省,1:市,2:县,3:乡镇)
     */
    private String orgLevelType;

    /**
     * 机构标签, 多个标签用英文逗号分隔
     */
    private String orgLabel;

    /**
     * 机构分类[00:默认;01:学校;02:医院]
     */
    private String orgClassify;

    /**
     * 值班电话
     */
    private String dutyPhone;

    /**
     * 传真电话
     */
    private String faxPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 单位ID
     */
    private Long companyId;

    /**
     * 逻辑删除
     */
    private Integer deleted;
}
