package com.platform.system.migration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.platform.system.api.domain.SysDept;
import com.platform.system.domain.SysOrg;
import com.platform.system.migration.dto.CscpOrgMigrationDTO;
import com.platform.system.migration.mapper.CscpOrgMigrationMapper;
import com.platform.system.service.SysDeptService;
import com.platform.system.service.SysOrgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织机构迁移辅助工具类
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrgMigrationHelper {

    private final CscpOrgMigrationMapper cscpOrgMigrationMapper;
    private final SysOrgService sysOrgService;
    private final SysDeptService sysDeptService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将CscpOrg转换为SysOrg
     */
    public SysOrg convertToSysOrg(CscpOrgMigrationDTO cscpOrg) {
        SysOrg sysOrg = new SysOrg();
        
        // 基本字段映射
        sysOrg.setId(cscpOrg.getId());
        sysOrg.setName(cscpOrg.getOrgName());
        sysOrg.setOrgCode(StringUtils.hasText(cscpOrg.getOrgCode()) ? cscpOrg.getOrgCode() : cscpOrg.getCode());
        
        // 机构类型转换：1(虚拟)→0, 2(单位)→1
        sysOrg.setOrgType(cscpOrg.getType() == 1 ? 0 : 1);
        sysOrg.setParentId(cscpOrg.getParentId());
        
        // 地址信息
        sysOrg.setDzName(cscpOrg.getLan());
        sysOrg.setDzCode(cscpOrg.getLanId());
        
        // 状态和删除标志
        sysOrg.setStatus("0"); // 默认正常状态
        sysOrg.setDelFlag(cscpOrg.getDeleted() != null && cscpOrg.getDeleted() == 1 ? "2" : "0");
        
        // 审计字段
        sysOrg.setCreateTime(cscpOrg.getCreateTime());
        sysOrg.setCreateBy(cscpOrg.getCreateBy());
        sysOrg.setUpdateTime(cscpOrg.getUpdateTime());
        sysOrg.setUpdateBy(cscpOrg.getUpdateBy());
        
        // 扩展属性JSON
        sysOrg.setExtProperties(buildExtProperties(cscpOrg));
        
        return sysOrg;
    }

    /**
     * 将CscpOrg转换为SysDept
     */
    public SysDept convertToSysDept(CscpOrgMigrationDTO cscpOrg) {
        SysDept sysDept = new SysDept();
        
        // 基本字段映射
        sysDept.setDeptId(cscpOrg.getId());
        sysDept.setDeptName(cscpOrg.getOrgName());
        sysDept.setOrderNum(cscpOrg.getOrderBy());
        
        // 父部门ID需要重新计算（只考虑部门间关系）
        sysDept.setParentId(calculateDeptParentId(cscpOrg));
        
        // ancestors稍后统一构建
        sysDept.setAncestors("");
        
        // 状态
        sysDept.setStatus("0"); // 默认正常状态
        sysDept.setDelFlag(cscpOrg.getDeleted() != null && cscpOrg.getDeleted() == 1 ? "2" : "0");
        
        // 所属机构ID
        sysDept.setOrgId(findOrgIdForDept(cscpOrg.getId()));
        
        // 审计字段
        sysDept.setCreateTime(cscpOrg.getCreateTime());
        sysDept.setCreateBy(cscpOrg.getCreateBy());
        sysDept.setUpdateTime(cscpOrg.getUpdateTime());
        sysDept.setUpdateBy(cscpOrg.getUpdateBy());
        
        return sysDept;
    }

    /**
     * 构建扩展属性JSON
     */
    private String buildExtProperties(CscpOrgMigrationDTO cscpOrg) {
        Map<String, Object> extProps = new HashMap<>();
        
        // 添加原有的重要字段到扩展属性
        addIfNotNull(extProps, "description", cscpOrg.getDescription());
        addIfNotNull(extProps, "orgAbbreviation", cscpOrg.getOrgAbbreviation());
        addIfNotNull(extProps, "unitLevel", cscpOrg.getUnitLevel());
        addIfNotNull(extProps, "authorityDepartment", cscpOrg.getAuthorityDepartment());
        addIfNotNull(extProps, "detailType", cscpOrg.getDetailType());
        addIfNotNull(extProps, "checkUp", cscpOrg.getCheckUp());
        addIfNotNull(extProps, "cloudDiskSpaceSize", cscpOrg.getCloudDiskSpaceSize());
        addIfNotNull(extProps, "hasSms", cscpOrg.getHasSms());
        addIfNotNull(extProps, "hasWatermark", cscpOrg.getHasWatermark());
        addIfNotNull(extProps, "splitview", cscpOrg.getSplitview());
        addIfNotNull(extProps, "creditCode", cscpOrg.getCreditCode());
        addIfNotNull(extProps, "organizationCode", cscpOrg.getOrganizationCode());
        addIfNotNull(extProps, "regionCode", cscpOrg.getRegionCode());
        addIfNotNull(extProps, "soleCode", cscpOrg.getSoleCode());
        addIfNotNull(extProps, "orgLevelType", cscpOrg.getOrgLevelType());
        addIfNotNull(extProps, "orgLabel", cscpOrg.getOrgLabel());
        addIfNotNull(extProps, "orgClassify", cscpOrg.getOrgClassify());
        addIfNotNull(extProps, "dutyPhone", cscpOrg.getDutyPhone());
        addIfNotNull(extProps, "faxPhone", cscpOrg.getFaxPhone());
        
        // 业务相关字段
        addIfNotNull(extProps, "branchLeaderId", cscpOrg.getBranchLeaderId());
        addIfNotNull(extProps, "assessmentPeopleCount", cscpOrg.getAssessmentPeopleCount());
        addIfNotNull(extProps, "groupNumber", cscpOrg.getGroupNumber());
        addIfNotNull(extProps, "unitType", cscpOrg.getUnitType());
        addIfNotNull(extProps, "crmTenantType", cscpOrg.getCrmTenantType());
        
        // 联系信息
        addIfNotNull(extProps, "nameOfAccountManager", cscpOrg.getNameOfAccountManager());
        addIfNotNull(extProps, "customerManagerTelephone", cscpOrg.getCustomerManagerTelephone());
        addIfNotNull(extProps, "customerAdministratorName", cscpOrg.getCustomerAdministratorName());
        addIfNotNull(extProps, "customerAdministratorTelephone", cscpOrg.getCustomerAdministratorTelephone());
        
        // 项目信息
        addIfNotNull(extProps, "numberOfAccounts", cscpOrg.getNumberOfAccounts());
        addIfNotNull(extProps, "projectContractPeriod", cscpOrg.getProjectContractPeriod());
        addIfNotNull(extProps, "totalProjectAmount", cscpOrg.getTotalProjectAmount());
        
        try {
            return objectMapper.writeValueAsString(extProps);
        } catch (JsonProcessingException e) {
            log.error("构建扩展属性JSON失败", e);
            return "{}";
        }
    }

    /**
     * 添加非空值到Map
     */
    private void addIfNotNull(Map<String, Object> map, String key, Object value) {
        if (value != null) {
            if (value instanceof String && StringUtils.hasText((String) value)) {
                map.put(key, value);
            } else if (!(value instanceof String)) {
                map.put(key, value);
            }
        }
    }

    /**
     * 计算部门的父部门ID（只考虑部门间关系）
     */
    private Long calculateDeptParentId(CscpOrgMigrationDTO cscpOrg) {
        if (cscpOrg.getParentId() == null || cscpOrg.getParentId() == 0) {
            return 0L;
        }
        
        // 检查父节点是否也是部门
        CscpOrgMigrationDTO parent = cscpOrgMigrationMapper.selectDeptParent(cscpOrg.getParentId());
        if (parent != null && parent.getType() == 3) {
            return parent.getId();
        }
        
        return 0L; // 如果父节点不是部门，则设为根部门
    }

    /**
     * 为部门查找所属的机构ID
     */
    public Long findOrgIdForDept(Long deptId) {
        CscpOrgMigrationDTO dept = cscpOrgMigrationMapper.selectById(deptId);
        if (dept == null) {
            return null;
        }
        
        // 如果有orgIdPath，从中查找单位
        if (StringUtils.hasText(dept.getOrgIdPath())) {
            CscpOrgMigrationDTO unit = cscpOrgMigrationMapper.findUnitByOrgIdPath(dept.getOrgIdPath());
            if (unit != null) {
                return unit.getId();
            }
        }
        
        // 递归向上查找单位
        return findParentUnit(dept.getParentId());
    }

    /**
     * 递归查找父级单位
     */
    private Long findParentUnit(Long parentId) {
        if (parentId == null || parentId == 0) {
            return null;
        }
        
        CscpOrgMigrationDTO parent = cscpOrgMigrationMapper.selectById(parentId);
        if (parent == null) {
            return null;
        }
        
        // 如果是单位，直接返回
        if (parent.getType() == 2) {
            return parent.getId();
        }
        
        // 继续向上查找
        return findParentUnit(parent.getParentId());
    }

    /**
     * 修复部门的父子关系
     */
    public void fixDeptParentRelations() {
        List<SysDept> allDepts = sysDeptService.list();
        
        for (SysDept dept : allDepts) {
            // 重新计算parentId
            CscpOrgMigrationDTO originalDept = cscpOrgMigrationMapper.selectById(dept.getDeptId());
            if (originalDept != null) {
                Long newParentId = calculateDeptParentId(originalDept);
                if (!Objects.equals(dept.getParentId(), newParentId)) {
                    dept.setParentId(newParentId);
                    sysDeptService.updateById(dept);
                }
            }
        }
    }

    /**
     * 构建部门的ancestors字段
     */
    public void buildDeptAncestors() {
        List<SysDept> allDepts = sysDeptService.list();
        Map<Long, SysDept> deptMap = allDepts.stream()
            .collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
        
        for (SysDept dept : allDepts) {
            String ancestors = buildAncestorsPath(dept.getDeptId(), dept.getParentId(), deptMap);
            dept.setAncestors(ancestors);
            sysDeptService.updateById(dept);
        }
    }

    /**
     * 构建ancestors路径
     */
    private String buildAncestorsPath(Long deptId, Long parentId, Map<Long, SysDept> deptMap) {
        if (parentId == null || parentId == 0) {
            return "0";
        }
        
        List<Long> ancestorIds = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        Long currentId = parentId;
        
        while (currentId != null && currentId != 0 && !visited.contains(currentId)) {
            visited.add(currentId);
            ancestorIds.add(currentId);
            
            SysDept currentDept = deptMap.get(currentId);
            if (currentDept != null) {
                currentId = currentDept.getParentId();
            } else {
                break;
            }
        }
        
        // 反转列表，从根到父的顺序
        Collections.reverse(ancestorIds);
        ancestorIds.add(0, 0L); // 添加根节点
        
        return ancestorIds.stream()
            .map(String::valueOf)
            .collect(Collectors.joining(","));
    }
}
