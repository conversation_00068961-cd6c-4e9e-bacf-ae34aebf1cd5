package com.platform.system.migration.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 迁移接收结果数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class MigrationReceiveResultDTO {

    /**
     * 接收是否成功
     */
    private Boolean success;

    /**
     * 接收消息
     */
    private String message;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 接收的记录数
     */
    private Integer receivedCount = 0;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 获取接收耗时（毫秒）
     */
    public Long getDurationMillis() {
        if (startTime != null && endTime != null) {
            return java.time.Duration.between(startTime, endTime).toMillis();
        }
        return null;
    }
}
