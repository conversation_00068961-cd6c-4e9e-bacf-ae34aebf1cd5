package com.platform.system.migration.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * CscpOrg兼容性数据传输对象
 * 用于在迁移后提供与原CscpOrg相同的接口
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class CscpOrgCompatibilityDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 描述
     */
    private String description;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 顺序
     */
    private Integer orderBy;

    /**
     * 机构编码
     */
    private String code;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构完整编码
     */
    private String pathCode;

    /**
     * 机构级别
     */
    private Integer level;

    /**
     * 机构类型 1表示虚拟，2表示单位 3表示部门
     */
    private Integer type;

    /**
     * 地区id
     */
    private String lanId;

    /**
     * 地区名称
     */
    private String lan;

    /**
     * 业务时是否发送短信，1表示默认发送，0表示不发送
     */
    private Integer hasSms;

    /**
     * 业务时是否加水印，1表示默认加水印，0表示不加水印
     */
    private Integer hasWatermark;

    /**
     * 是否分屏
     */
    private Integer splitview;

    /**
     * 单位共享云盘空间大小，单位GB，默认2G
     */
    private Integer cloudDiskSpaceSize;

    /**
     * 机构简称
     */
    private String orgAbbreviation;

    /**
     * 是否考核
     */
    private Boolean checkUp;

    /**
     * 单位级别
     */
    private String unitLevel;

    /**
     * 主管部门
     */
    private String authorityDepartment;

    /**
     * 详细类型
     */
    private String detailType;

    /**
     * 分管领导ID
     */
    private Long branchLeaderId;

    /**
     * 实际考核人数
     */
    private Integer assessmentPeopleCount;

    /**
     * 群组号
     */
    private String groupNumber;

    /**
     * 单位类型(0:党委办 1：政府办)
     */
    private Integer unitType;

    /**
     * CRM受理的客户类型（单位类型：1：标准型，2：项目型）
     */
    private String crmTenantType;

    /**
     * 客户经理姓名
     */
    private String nameOfAccountManager;

    /**
     * 客户经理电话
     */
    private String customerManagerTelephone;

    /**
     * 客户管理员姓名
     */
    private String customerAdministratorName;

    /**
     * 客户管理员联系电话
     */
    private String customerAdministratorTelephone;

    /**
     * 账号数
     */
    private Integer numberOfAccounts;

    /**
     * 项目合同期限
     */
    private String projectContractPeriod;

    /**
     * 项目总金额
     */
    private String totalProjectAmount;

    /**
     * 档案标识-统一信用代码
     */
    private String creditCode;

    /**
     * 档案标识-组织机构编码
     */
    private String organizationCode;

    /**
     * 机构唯一编码
     */
    private String soleCode;

    /**
     * 行政区域编码
     */
    private String regionCode;

    /**
     * 机构类型(0:省,1:市,2:县,3:乡镇)
     */
    private String orgLevelType;

    /**
     * 机构标签, 多个标签用英文逗号分隔
     */
    private String orgLabel;

    /**
     * 机构分类[00:默认;01:学校;02:医院]
     */
    private String orgClassify;

    /**
     * 值班电话
     */
    private String dutyPhone;

    /**
     * 传真电话
     */
    private String faxPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 单位ID
     */
    private Long companyId;

    /**
     * 逻辑删除
     */
    private Integer deleted;

    /**
     * 子节点列表（用于树形结构）
     */
    private List<CscpOrgCompatibilityDTO> children;

    /**
     * 是否有子节点
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    /**
     * 获取机构编码（优先返回orgCode，如果为空则返回code）
     */
    public String getEffectiveOrgCode() {
        return orgCode != null ? orgCode : code;
    }

    /**
     * 判断是否为虚拟机构
     */
    public boolean isVirtualOrg() {
        return type != null && type == 1;
    }

    /**
     * 判断是否为单位
     */
    public boolean isUnit() {
        return type != null && type == 2;
    }

    /**
     * 判断是否为部门
     */
    public boolean isDept() {
        return type != null && type == 3;
    }

    /**
     * 判断是否为根节点
     */
    public boolean isRoot() {
        return parentId == null || parentId == 0;
    }

    /**
     * 判断是否已删除
     */
    public boolean isDeleted() {
        return deleted != null && deleted == 1;
    }

    /**
     * 获取类型描述
     */
    public String getTypeDescription() {
        if (type == null) {
            return "未知";
        }
        switch (type) {
            case 1:
                return "虚拟机构";
            case 2:
                return "单位";
            case 3:
                return "部门";
            default:
                return "未知";
        }
    }
}
