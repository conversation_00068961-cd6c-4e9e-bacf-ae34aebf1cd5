package com.platform.system.migration.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * SysOrg数据传输对象
 * 避免直接依赖目标项目的实体类
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class SysOrgDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构名称
     */
    private String name;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构类型：0-虚拟机构，1-实体单位
     */
    private Integer orgType;

    /**
     * 父级机构ID
     */
    private Long parentId;

    /**
     * 地址名称
     */
    private String dzName;

    /**
     * 地址编码
     */
    private String dzCode;

    /**
     * 地址父CODE路径
     */
    private String dzPcodePath;

    /**
     * 地址区划级别
     */
    private String dzRegionLevel;

    /**
     * 详细地址
     */
    private String dzDetailAddress;

    /**
     * 联系人
     */
    private String contactsPerson;

    /**
     * 联系电话
     */
    private String contactsPhone;

    /**
     * 座机
     */
    private String contactsWay;

    /**
     * 管辖范围编码
     */
    private String guCode;

    /**
     * 管辖地址父CODE路径
     */
    private String guPcodePath;

    /**
     * 管辖范围全称
     */
    private String guName;

    /**
     * 管辖范围区划级别
     */
    private String guRegionLevel;

    /**
     * 角色组ID
     */
    private Long roleGroupId;

    /**
     * 状态：0-正常，1-停用
     */
    private String status;

    /**
     * 删除标志：0-正常，2-删除
     */
    private String delFlag;

    /**
     * 扩展属性（JSON格式）
     */
    private String extProperties;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateBy;
}
