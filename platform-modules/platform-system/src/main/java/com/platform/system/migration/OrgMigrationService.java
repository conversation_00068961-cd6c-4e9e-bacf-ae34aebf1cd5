package com.platform.system.migration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.platform.system.api.domain.SysDept;
import com.platform.system.domain.SysOrg;
import com.platform.system.migration.dto.CscpOrgMigrationDTO;
import com.platform.system.migration.dto.MigrationResultDTO;
import com.platform.system.migration.dto.MigrationStatisticsDTO;
import com.platform.system.migration.mapper.CscpOrgMigrationMapper;
import com.platform.system.service.SysDeptService;
import com.platform.system.service.SysOrgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织机构数据迁移服务
 * 将CscpOrg单表结构迁移到SysOrg和SysDept微服务化架构
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrgMigrationService {

    private final CscpOrgMigrationMapper cscpOrgMigrationMapper;
    private final SysOrgService sysOrgService;
    private final SysDeptService sysDeptService;
    private final OrgMigrationHelper migrationHelper;

    /**
     * 执行完整的数据迁移
     */
    @Transactional(rollbackFor = Exception.class)
    public MigrationResultDTO executeMigration() {
        log.info("开始执行组织机构数据迁移...");
        
        MigrationResultDTO result = new MigrationResultDTO();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 1. 数据预处理和验证
            MigrationStatisticsDTO statistics = preProcessAndValidate();
            result.setStatistics(statistics);
            
            // 2. 迁移SysOrg数据（虚拟机构和单位）
            migrateSysOrgData(result);
            
            // 3. 迁移SysDept数据（部门）
            migrateSysDeptData(result);
            
            // 4. 修复关系和构建ancestors
            fixRelationshipsAndAncestors(result);
            
            // 5. 验证迁移结果
            validateMigrationResult(result);
            
            result.setSuccess(true);
            result.setMessage("数据迁移成功完成");
            
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            result.setSuccess(false);
            result.setMessage("数据迁移失败: " + e.getMessage());
            throw e;
        } finally {
            result.setEndTime(LocalDateTime.now());
            log.info("数据迁移完成，耗时: {}ms", 
                result.getEndTime().toEpochSecond(java.time.ZoneOffset.UTC) - 
                result.getStartTime().toEpochSecond(java.time.ZoneOffset.UTC));
        }
        
        return result;
    }

    /**
     * 数据预处理和验证
     */
    private MigrationStatisticsDTO preProcessAndValidate() {
        log.info("开始数据预处理和验证...");
        
        MigrationStatisticsDTO statistics = new MigrationStatisticsDTO();
        
        // 统计原始数据
        statistics.setTotalCscpOrgCount(cscpOrgMigrationMapper.countTotal());
        statistics.setVirtualOrgCount(cscpOrgMigrationMapper.countByType(1));
        statistics.setUnitOrgCount(cscpOrgMigrationMapper.countByType(2));
        statistics.setDeptOrgCount(cscpOrgMigrationMapper.countByType(3));
        
        // 检查数据完整性
        List<String> validationErrors = validateDataIntegrity();
        if (!CollectionUtils.isEmpty(validationErrors)) {
            statistics.setValidationErrors(validationErrors);
            throw new RuntimeException("数据验证失败: " + String.join(", ", validationErrors));
        }
        
        log.info("数据统计: 总计={}, 虚拟机构={}, 单位={}, 部门={}", 
            statistics.getTotalCscpOrgCount(),
            statistics.getVirtualOrgCount(),
            statistics.getUnitOrgCount(),
            statistics.getDeptOrgCount());
            
        return statistics;
    }

    /**
     * 验证数据完整性
     */
    private List<String> validateDataIntegrity() {
        List<String> errors = new ArrayList<>();
        
        // 检查是否有孤儿节点
        List<Long> orphanIds = cscpOrgMigrationMapper.findOrphanNodes();
        if (!CollectionUtils.isEmpty(orphanIds)) {
            errors.add("发现孤儿节点: " + orphanIds);
        }
        
        // 检查是否有循环引用
        List<Long> circularIds = cscpOrgMigrationMapper.findCircularReferences();
        if (!CollectionUtils.isEmpty(circularIds)) {
            errors.add("发现循环引用: " + circularIds);
        }
        
        // 检查必要字段是否为空
        long nullNameCount = cscpOrgMigrationMapper.countNullNames();
        if (nullNameCount > 0) {
            errors.add("发现" + nullNameCount + "条记录的名称为空");
        }
        
        return errors;
    }

    /**
     * 迁移SysOrg数据（虚拟机构和单位）
     */
    private void migrateSysOrgData(MigrationResultDTO result) {
        log.info("开始迁移SysOrg数据...");
        
        // 获取需要迁移到SysOrg的数据（type=1虚拟机构, type=2单位）
        List<CscpOrgMigrationDTO> orgList = cscpOrgMigrationMapper.selectOrgAndUnitData();
        
        if (CollectionUtils.isEmpty(orgList)) {
            log.warn("没有找到需要迁移的机构和单位数据");
            return;
        }
        
        List<SysOrg> sysOrgList = new ArrayList<>();
        
        for (CscpOrgMigrationDTO cscpOrg : orgList) {
            SysOrg sysOrg = migrationHelper.convertToSysOrg(cscpOrg);
            sysOrgList.add(sysOrg);
        }
        
        // 批量插入
        boolean success = sysOrgService.saveBatch(sysOrgList);
        if (!success) {
            throw new RuntimeException("SysOrg数据批量插入失败");
        }
        
        result.setSysOrgMigratedCount(sysOrgList.size());
        log.info("SysOrg数据迁移完成，共迁移{}条记录", sysOrgList.size());
    }

    /**
     * 迁移SysDept数据（部门）
     */
    private void migrateSysDeptData(MigrationResultDTO result) {
        log.info("开始迁移SysDept数据...");
        
        // 获取需要迁移到SysDept的数据（type=3部门）
        List<CscpOrgMigrationDTO> deptList = cscpOrgMigrationMapper.selectDeptData();
        
        if (CollectionUtils.isEmpty(deptList)) {
            log.warn("没有找到需要迁移的部门数据");
            return;
        }
        
        List<SysDept> sysDeptList = new ArrayList<>();
        
        for (CscpOrgMigrationDTO cscpOrg : deptList) {
            SysDept sysDept = migrationHelper.convertToSysDept(cscpOrg);
            sysDeptList.add(sysDept);
        }
        
        // 批量插入
        boolean success = sysDeptService.saveBatch(sysDeptList);
        if (!success) {
            throw new RuntimeException("SysDept数据批量插入失败");
        }
        
        result.setSysDeptMigratedCount(sysDeptList.size());
        log.info("SysDept数据迁移完成，共迁移{}条记录", sysDeptList.size());
    }

    /**
     * 修复关系和构建ancestors
     */
    private void fixRelationshipsAndAncestors(MigrationResultDTO result) {
        log.info("开始修复关系和构建ancestors...");
        
        // 1. 修复部门的orgId关联
        fixDeptOrgIdRelations();
        
        // 2. 重新计算部门的parentId（只考虑部门间关系）
        fixDeptParentIdRelations();
        
        // 3. 构建部门的ancestors字段
        buildDeptAncestors();
        
        log.info("关系修复和ancestors构建完成");
    }

    /**
     * 修复部门的orgId关联
     */
    private void fixDeptOrgIdRelations() {
        // 实现逻辑：为每个部门找到其所属的单位ID
        List<SysDept> allDepts = sysDeptService.list();
        
        for (SysDept dept : allDepts) {
            Long orgId = migrationHelper.findOrgIdForDept(dept.getDeptId());
            if (orgId != null) {
                dept.setOrgId(orgId);
                sysDeptService.updateById(dept);
            }
        }
    }

    /**
     * 修复部门的parentId关系
     */
    private void fixDeptParentIdRelations() {
        // 实现逻辑：重新计算部门间的父子关系
        migrationHelper.fixDeptParentRelations();
    }

    /**
     * 构建部门的ancestors字段
     */
    private void buildDeptAncestors() {
        // 实现逻辑：递归构建ancestors路径
        migrationHelper.buildDeptAncestors();
    }

    /**
     * 验证迁移结果
     */
    private void validateMigrationResult(MigrationResultDTO result) {
        log.info("开始验证迁移结果...");
        
        // 验证数据数量
        long sysOrgCount = sysOrgService.count();
        long sysDeptCount = sysDeptService.count();
        
        result.setFinalSysOrgCount(sysOrgCount);
        result.setFinalSysDeptCount(sysDeptCount);
        
        // 验证关系完整性
        List<String> relationErrors = validateRelationIntegrity();
        if (!CollectionUtils.isEmpty(relationErrors)) {
            result.setValidationErrors(relationErrors);
            throw new RuntimeException("关系验证失败: " + String.join(", ", relationErrors));
        }
        
        log.info("迁移结果验证通过，SysOrg: {}, SysDept: {}", sysOrgCount, sysDeptCount);
    }

    /**
     * 验证关系完整性
     */
    private List<String> validateRelationIntegrity() {
        List<String> errors = new ArrayList<>();
        
        // 验证部门的orgId关联是否正确
        LambdaQueryWrapper<SysDept> deptWrapper = Wrappers.lambdaQuery();
        deptWrapper.isNull(SysDept::getOrgId);
        long nullOrgIdCount = sysDeptService.count(deptWrapper);
        if (nullOrgIdCount > 0) {
            errors.add("发现" + nullOrgIdCount + "个部门的orgId为空");
        }
        
        // 验证ancestors字段是否正确构建
        LambdaQueryWrapper<SysDept> ancestorsWrapper = Wrappers.lambdaQuery();
        ancestorsWrapper.isNull(SysDept::getAncestors);
        long nullAncestorsCount = sysDeptService.count(ancestorsWrapper);
        if (nullAncestorsCount > 0) {
            errors.add("发现" + nullAncestorsCount + "个部门的ancestors为空");
        }
        
        return errors;
    }

    /**
     * 预览迁移统计信息
     */
    public MigrationStatisticsDTO previewMigrationStatistics() {
        log.info("获取迁移统计信息...");

        MigrationStatisticsDTO statistics = new MigrationStatisticsDTO();

        // 统计原始数据
        statistics.setTotalCscpOrgCount(cscpOrgMigrationMapper.countTotal());
        statistics.setVirtualOrgCount(cscpOrgMigrationMapper.countByType(1));
        statistics.setUnitOrgCount(cscpOrgMigrationMapper.countByType(2));
        statistics.setDeptOrgCount(cscpOrgMigrationMapper.countByType(3));
        statistics.setDeletedCount(cscpOrgMigrationMapper.countDeleted());
        statistics.calculateValidCount();

        // 数据质量检查
        statistics.setOrphanNodeCount((long) cscpOrgMigrationMapper.findOrphanNodes().size());
        statistics.setCircularReferenceCount((long) cscpOrgMigrationMapper.findCircularReferences().size());
        statistics.setNullNameCount(cscpOrgMigrationMapper.countNullNames());
        statistics.setMaxLevel(cscpOrgMigrationMapper.selectMaxLevel());
        statistics.setRootNodeCount(cscpOrgMigrationMapper.countRootNodes());

        return statistics;
    }

    /**
     * 验证迁移结果
     */
    public MigrationResultDTO validateMigrationResult() {
        log.info("验证迁移结果...");

        MigrationResultDTO result = new MigrationResultDTO();
        result.setStartTime(LocalDateTime.now());

        try {
            // 验证数据数量
            long sysOrgCount = sysOrgService.count();
            long sysDeptCount = sysDeptService.count();

            result.setFinalSysOrgCount(sysOrgCount);
            result.setFinalSysDeptCount(sysDeptCount);

            // 验证关系完整性
            List<String> relationErrors = validateRelationIntegrity();
            result.setValidationErrors(relationErrors);

            if (CollectionUtils.isEmpty(relationErrors)) {
                result.setSuccess(true);
                result.setMessage("迁移结果验证通过");
            } else {
                result.setSuccess(false);
                result.setMessage("迁移结果验证失败: " + String.join(", ", relationErrors));
            }

        } catch (Exception e) {
            log.error("迁移结果验证失败", e);
            result.setSuccess(false);
            result.setMessage("迁移结果验证失败: " + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
        }

        return result;
    }

    /**
     * 回滚迁移数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void rollbackMigration() {
        log.info("开始回滚迁移数据...");

        // 清空新表数据
        sysDeptService.remove(Wrappers.lambdaQuery());
        sysOrgService.remove(Wrappers.lambdaQuery());

        log.info("迁移数据回滚完成");
    }
}
