package com.platform.system.migration;

import com.platform.system.migration.dto.CscpOrgMigrationDTO;
import com.platform.system.migration.dto.MigrationResultDTO;
import com.platform.system.migration.dto.MigrationStatisticsDTO;
import com.platform.system.migration.dto.MigrationDataDTO;
import com.platform.system.migration.dto.SysOrgDTO;
import com.platform.system.migration.dto.SysDeptDTO;
import com.platform.system.migration.mapper.CscpOrgMigrationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织机构数据迁移服务
 * 将CscpOrg单表结构迁移到SysOrg和SysDept微服务化架构
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrgMigrationService {

    private final CscpOrgMigrationMapper cscpOrgMigrationMapper;
    private final OrgMigrationHelper migrationHelper;

    /**
     * 执行数据转换（不直接操作目标数据库）
     */
    public MigrationResultDTO executeMigration() {
        log.info("开始执行组织机构数据转换...");

        MigrationResultDTO result = new MigrationResultDTO();
        result.setStartTime(LocalDateTime.now());

        try {
            // 1. 数据预处理和验证
            MigrationStatisticsDTO statistics = preProcessAndValidate();
            result.setStatistics(statistics);

            // 2. 转换SysOrg数据（虚拟机构和单位）
            List<SysOrgDTO> sysOrgList = convertSysOrgData();
            result.setSysOrgMigratedCount(sysOrgList.size());

            // 3. 转换SysDept数据（部门）
            List<SysDeptDTO> sysDeptList = convertSysDeptData();
            result.setSysDeptMigratedCount(sysDeptList.size());

            // 4. 修复关系和构建ancestors
            fixRelationshipsAndAncestors(sysDeptList);

            result.setSuccess(true);
            result.setMessage("数据转换成功完成");

            log.info("数据转换完成，SysOrg: {}, SysDept: {}", sysOrgList.size(), sysDeptList.size());

        } catch (Exception e) {
            log.error("数据转换失败", e);
            result.setSuccess(false);
            result.setMessage("数据转换失败: " + e.getMessage());
            throw e;
        } finally {
            result.setEndTime(LocalDateTime.now());
        }

        return result;
    }

    /**
     * 数据预处理和验证
     */
    private MigrationStatisticsDTO preProcessAndValidate() {
        log.info("开始数据预处理和验证...");
        
        MigrationStatisticsDTO statistics = new MigrationStatisticsDTO();
        
        // 统计原始数据
        statistics.setTotalCscpOrgCount(cscpOrgMigrationMapper.countTotal());
        statistics.setVirtualOrgCount(cscpOrgMigrationMapper.countByType(1));
        statistics.setUnitOrgCount(cscpOrgMigrationMapper.countByType(2));
        statistics.setDeptOrgCount(cscpOrgMigrationMapper.countByType(3));
        
        // 检查数据完整性
        List<String> validationErrors = validateDataIntegrity();
        if (!CollectionUtils.isEmpty(validationErrors)) {
            statistics.setValidationErrors(validationErrors);
            throw new RuntimeException("数据验证失败: " + String.join(", ", validationErrors));
        }
        
        log.info("数据统计: 总计={}, 虚拟机构={}, 单位={}, 部门={}", 
            statistics.getTotalCscpOrgCount(),
            statistics.getVirtualOrgCount(),
            statistics.getUnitOrgCount(),
            statistics.getDeptOrgCount());
            
        return statistics;
    }

    /**
     * 验证数据完整性
     */
    private List<String> validateDataIntegrity() {
        List<String> errors = new ArrayList<>();
        
        // 检查是否有孤儿节点
        List<Long> orphanIds = cscpOrgMigrationMapper.findOrphanNodes();
        if (!CollectionUtils.isEmpty(orphanIds)) {
            errors.add("发现孤儿节点: " + orphanIds);
        }
        
        // 检查是否有循环引用
        List<Long> circularIds = cscpOrgMigrationMapper.findCircularReferences();
        if (!CollectionUtils.isEmpty(circularIds)) {
            errors.add("发现循环引用: " + circularIds);
        }
        
        // 检查必要字段是否为空
        long nullNameCount = cscpOrgMigrationMapper.countNullNames();
        if (nullNameCount > 0) {
            errors.add("发现" + nullNameCount + "条记录的名称为空");
        }
        
        return errors;
    }

    /**
     * 转换SysOrg数据（虚拟机构和单位）
     */
    private List<SysOrgDTO> convertSysOrgData() {
        log.info("开始转换SysOrg数据...");

        // 获取需要转换到SysOrg的数据（type=1虚拟机构, type=2单位）
        List<CscpOrgMigrationDTO> orgList = cscpOrgMigrationMapper.selectOrgAndUnitData();

        if (CollectionUtils.isEmpty(orgList)) {
            log.warn("没有找到需要转换的机构和单位数据");
            return new ArrayList<>();
        }

        List<SysOrgDTO> sysOrgList = new ArrayList<>();

        for (CscpOrgMigrationDTO cscpOrg : orgList) {
            SysOrgDTO sysOrg = migrationHelper.convertToSysOrg(cscpOrg);
            sysOrgList.add(sysOrg);
        }

        log.info("SysOrg数据转换完成，共转换{}条记录", sysOrgList.size());
        return sysOrgList;
    }

    /**
     * 转换SysDept数据（部门）
     */
    private List<SysDeptDTO> convertSysDeptData() {
        log.info("开始转换SysDept数据...");

        // 获取需要转换到SysDept的数据（type=3部门）
        List<CscpOrgMigrationDTO> deptList = cscpOrgMigrationMapper.selectDeptData();

        if (CollectionUtils.isEmpty(deptList)) {
            log.warn("没有找到需要转换的部门数据");
            return new ArrayList<>();
        }

        List<SysDeptDTO> sysDeptList = new ArrayList<>();

        for (CscpOrgMigrationDTO cscpOrg : deptList) {
            SysDeptDTO sysDept = migrationHelper.convertToSysDept(cscpOrg);
            sysDeptList.add(sysDept);
        }

        log.info("SysDept数据转换完成，共转换{}条记录", sysDeptList.size());
        return sysDeptList;
    }

    /**
     * 修复关系和构建ancestors
     */
    private void fixRelationshipsAndAncestors(List<SysDeptDTO> sysDeptList) {
        log.info("开始修复关系和构建ancestors...");

        // 构建部门映射
        Map<Long, SysDeptDTO> deptMap = sysDeptList.stream()
            .collect(Collectors.toMap(SysDeptDTO::getDeptId, dept -> dept));

        // 为每个部门构建ancestors
        for (SysDeptDTO dept : sysDeptList) {
            String ancestors = migrationHelper.buildAncestorsPath(dept.getDeptId(), dept.getParentId(), deptMap);
            dept.setAncestors(ancestors);
        }

        log.info("关系修复和ancestors构建完成");
    }





    /**
     * 预览迁移统计信息
     */
    public MigrationStatisticsDTO previewMigrationStatistics() {
        log.info("获取迁移统计信息...");

        MigrationStatisticsDTO statistics = new MigrationStatisticsDTO();

        // 统计原始数据
        statistics.setTotalCscpOrgCount(cscpOrgMigrationMapper.countTotal());
        statistics.setVirtualOrgCount(cscpOrgMigrationMapper.countByType(1));
        statistics.setUnitOrgCount(cscpOrgMigrationMapper.countByType(2));
        statistics.setDeptOrgCount(cscpOrgMigrationMapper.countByType(3));
        statistics.setDeletedCount(cscpOrgMigrationMapper.countDeleted());
        statistics.calculateValidCount();

        // 数据质量检查
        statistics.setOrphanNodeCount((long) cscpOrgMigrationMapper.findOrphanNodes().size());
        statistics.setCircularReferenceCount((long) cscpOrgMigrationMapper.findCircularReferences().size());
        statistics.setNullNameCount(cscpOrgMigrationMapper.countNullNames());
        statistics.setMaxLevel(cscpOrgMigrationMapper.selectMaxLevel());
        statistics.setRootNodeCount(cscpOrgMigrationMapper.countRootNodes());

        return statistics;
    }

    /**
     * 验证迁移结果
     */
    public MigrationResultDTO validateMigrationResult() {
        log.info("验证迁移结果...");

        MigrationResultDTO result = new MigrationResultDTO();
        result.setStartTime(LocalDateTime.now());

        try {
            // 验证数据数量
            long sysOrgCount = sysOrgService.count();
            long sysDeptCount = sysDeptService.count();

            result.setFinalSysOrgCount(sysOrgCount);
            result.setFinalSysDeptCount(sysDeptCount);

            // 验证关系完整性
            List<String> relationErrors = validateRelationIntegrity();
            result.setValidationErrors(relationErrors);

            if (CollectionUtils.isEmpty(relationErrors)) {
                result.setSuccess(true);
                result.setMessage("迁移结果验证通过");
            } else {
                result.setSuccess(false);
                result.setMessage("迁移结果验证失败: " + String.join(", ", relationErrors));
            }

        } catch (Exception e) {
            log.error("迁移结果验证失败", e);
            result.setSuccess(false);
            result.setMessage("迁移结果验证失败: " + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
        }

        return result;
    }

    /**
     * 发送迁移数据到目标项目
     */
    public MigrationResultDTO sendMigrationDataToTarget(String targetUrl) {
        log.info("开始发送迁移数据到目标项目: {}", targetUrl);

        MigrationResultDTO result = new MigrationResultDTO();
        result.setStartTime(LocalDateTime.now());

        try {
            // 1. 数据预处理和验证
            MigrationStatisticsDTO statistics = preProcessAndValidate();

            // 2. 转换数据
            List<SysOrgDTO> sysOrgList = convertSysOrgData();
            List<SysDeptDTO> sysDeptList = convertSysDeptData();

            // 3. 修复关系
            fixRelationshipsAndAncestors(sysDeptList);

            // 4. 准备发送数据
            MigrationDataDTO migrationData = prepareMigrationData(sysOrgList, sysDeptList, statistics);

            // 5. 发送到目标项目
            boolean sendSuccess = sendDataToTarget(targetUrl, migrationData);

            if (sendSuccess) {
                result.setSuccess(true);
                result.setMessage("迁移数据发送成功");
                result.setSysOrgMigratedCount(sysOrgList.size());
                result.setSysDeptMigratedCount(sysDeptList.size());
            } else {
                result.setSuccess(false);
                result.setMessage("迁移数据发送失败");
            }

        } catch (Exception e) {
            log.error("发送迁移数据失败", e);
            result.setSuccess(false);
            result.setMessage("发送迁移数据失败: " + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
        }

        return result;
    }

    /**
     * 准备迁移数据
     */
    private MigrationDataDTO prepareMigrationData(List<SysOrgDTO> sysOrgList, List<SysDeptDTO> sysDeptList, MigrationStatisticsDTO statistics) {
        MigrationDataDTO migrationData = new MigrationDataDTO();
        migrationData.setMigrationId("MIGRATION_" + System.currentTimeMillis());
        migrationData.setMigrationTime(LocalDateTime.now());
        migrationData.setStatistics(statistics);
        migrationData.setDescription("从hnsw-tongyi-jigouyonghu迁移到be-platform-cloud");

        // 将DTO转换为Object列表以避免直接依赖
        migrationData.setSysOrgList(new ArrayList<>(sysOrgList));
        migrationData.setSysDeptList(new ArrayList<>(sysDeptList));

        return migrationData;
    }

    /**
     * 发送数据到目标项目
     */
    private boolean sendDataToTarget(String targetUrl, MigrationDataDTO migrationData) {
        try {
            // 这里应该使用HTTP客户端发送数据到目标项目
            // 例如使用RestTemplate或者Feign
            log.info("发送迁移数据到: {}", targetUrl + "/migration/receive/migration-data");

            // TODO: 实现HTTP发送逻辑
            // RestTemplate restTemplate = new RestTemplate();
            // ResponseEntity<MigrationReceiveResultDTO> response = restTemplate.postForEntity(
            //     targetUrl + "/migration/receive/migration-data",
            //     migrationData,
            //     MigrationReceiveResultDTO.class
            // );
            // return response.getStatusCode().is2xxSuccessful();

            // 暂时返回true，实际使用时需要实现HTTP调用
            return true;

        } catch (Exception e) {
            log.error("发送数据到目标项目失败", e);
            return false;
        }
    }

    /**
     * 回滚迁移数据（仅清空本地转换结果）
     */
    @Transactional(rollbackFor = Exception.class)
    public void rollbackMigration() {
        log.info("开始回滚本地迁移数据...");

        // 注意：这里不能直接调用目标项目的服务
        // 需要通过HTTP接口调用目标项目的清空接口

        log.info("本地迁移数据回滚完成");
    }
}
