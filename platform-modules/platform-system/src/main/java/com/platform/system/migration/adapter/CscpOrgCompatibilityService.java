package com.platform.system.migration.adapter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.platform.system.api.domain.SysDept;
import com.platform.system.domain.SysOrg;
import com.platform.system.migration.dto.CscpOrgCompatibilityDTO;
import com.platform.system.service.SysDeptService;
import com.platform.system.service.SysOrgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * CscpOrg兼容性服务
 * 提供迁移后的兼容性接口，使原有业务代码能够继续工作
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CscpOrgCompatibilityService {

    private final SysOrgService sysOrgService;
    private final SysDeptService sysDeptService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 根据ID查询组织机构信息（兼容原CscpOrg接口）
     */
    public CscpOrgCompatibilityDTO findById(Long id) {
        // 先在SysOrg中查找
        SysOrg sysOrg = sysOrgService.getById(id);
        if (sysOrg != null) {
            return convertFromSysOrg(sysOrg);
        }
        
        // 再在SysDept中查找
        SysDept sysDept = sysDeptService.getById(id);
        if (sysDept != null) {
            return convertFromSysDept(sysDept);
        }
        
        return null;
    }

    /**
     * 查询所有组织机构（兼容原CscpOrg接口）
     */
    public List<CscpOrgCompatibilityDTO> findAll() {
        List<CscpOrgCompatibilityDTO> result = new ArrayList<>();
        
        // 获取所有SysOrg
        List<SysOrg> sysOrgList = sysOrgService.list();
        for (SysOrg sysOrg : sysOrgList) {
            result.add(convertFromSysOrg(sysOrg));
        }
        
        // 获取所有SysDept
        List<SysDept> sysDeptList = sysDeptService.list();
        for (SysDept sysDept : sysDeptList) {
            result.add(convertFromSysDept(sysDept));
        }
        
        // 按ID排序
        result.sort(Comparator.comparing(CscpOrgCompatibilityDTO::getId));
        
        return result;
    }

    /**
     * 根据类型查询组织机构
     */
    public List<CscpOrgCompatibilityDTO> findByType(Integer type) {
        List<CscpOrgCompatibilityDTO> result = new ArrayList<>();
        
        if (type == 1 || type == 2) {
            // 查询SysOrg
            LambdaQueryWrapper<SysOrg> orgWrapper = Wrappers.lambdaQuery();
            orgWrapper.eq(SysOrg::getOrgType, type == 1 ? 0 : 1);
            orgWrapper.eq(SysOrg::getDelFlag, "0");
            
            List<SysOrg> sysOrgList = sysOrgService.list(orgWrapper);
            for (SysOrg sysOrg : sysOrgList) {
                result.add(convertFromSysOrg(sysOrg));
            }
        } else if (type == 3) {
            // 查询SysDept
            LambdaQueryWrapper<SysDept> deptWrapper = Wrappers.lambdaQuery();
            deptWrapper.eq(SysDept::getDelFlag, "0");
            
            List<SysDept> sysDeptList = sysDeptService.list(deptWrapper);
            for (SysDept sysDept : sysDeptList) {
                result.add(convertFromSysDept(sysDept));
            }
        }
        
        return result;
    }

    /**
     * 根据父ID查询子节点
     */
    public List<CscpOrgCompatibilityDTO> findByParentId(Long parentId) {
        List<CscpOrgCompatibilityDTO> result = new ArrayList<>();
        
        // 查询SysOrg中的子节点
        LambdaQueryWrapper<SysOrg> orgWrapper = Wrappers.lambdaQuery();
        orgWrapper.eq(SysOrg::getParentId, parentId);
        orgWrapper.eq(SysOrg::getDelFlag, "0");
        
        List<SysOrg> sysOrgList = sysOrgService.list(orgWrapper);
        for (SysOrg sysOrg : sysOrgList) {
            result.add(convertFromSysOrg(sysOrg));
        }
        
        // 查询SysDept中的子节点
        LambdaQueryWrapper<SysDept> deptWrapper = Wrappers.lambdaQuery();
        deptWrapper.eq(SysDept::getParentId, parentId);
        deptWrapper.eq(SysDept::getDelFlag, "0");
        
        List<SysDept> sysDeptList = sysDeptService.list(deptWrapper);
        for (SysDept sysDept : sysDeptList) {
            result.add(convertFromSysDept(sysDept));
        }
        
        return result;
    }

    /**
     * 构建树形结构（兼容原CscpOrg接口）
     */
    public List<CscpOrgCompatibilityDTO> buildTree() {
        List<CscpOrgCompatibilityDTO> allNodes = findAll();
        return buildTreeFromList(allNodes);
    }

    /**
     * 根据机构编码查询
     */
    public CscpOrgCompatibilityDTO findByOrgCode(String orgCode) {
        if (!StringUtils.hasText(orgCode)) {
            return null;
        }
        
        // 先在SysOrg中查找
        LambdaQueryWrapper<SysOrg> orgWrapper = Wrappers.lambdaQuery();
        orgWrapper.eq(SysOrg::getOrgCode, orgCode);
        orgWrapper.eq(SysOrg::getDelFlag, "0");
        
        SysOrg sysOrg = sysOrgService.getOne(orgWrapper);
        if (sysOrg != null) {
            return convertFromSysOrg(sysOrg);
        }
        
        return null;
    }

    /**
     * 查询指定节点的所有父级节点
     */
    public List<CscpOrgCompatibilityDTO> findParents(Long id) {
        List<CscpOrgCompatibilityDTO> parents = new ArrayList<>();
        
        CscpOrgCompatibilityDTO current = findById(id);
        while (current != null && current.getParentId() != null && current.getParentId() != 0) {
            CscpOrgCompatibilityDTO parent = findById(current.getParentId());
            if (parent != null) {
                parents.add(0, parent); // 插入到开头，保持从根到父的顺序
                current = parent;
            } else {
                break;
            }
        }
        
        return parents;
    }

    /**
     * 查询指定节点的所有子节点（递归）
     */
    public List<CscpOrgCompatibilityDTO> findAllChildren(Long id) {
        List<CscpOrgCompatibilityDTO> children = new ArrayList<>();
        findChildrenRecursive(id, children);
        return children;
    }

    /**
     * 递归查找子节点
     */
    private void findChildrenRecursive(Long parentId, List<CscpOrgCompatibilityDTO> children) {
        List<CscpOrgCompatibilityDTO> directChildren = findByParentId(parentId);
        for (CscpOrgCompatibilityDTO child : directChildren) {
            children.add(child);
            findChildrenRecursive(child.getId(), children);
        }
    }

    /**
     * 从列表构建树形结构
     */
    private List<CscpOrgCompatibilityDTO> buildTreeFromList(List<CscpOrgCompatibilityDTO> allNodes) {
        Map<Long, CscpOrgCompatibilityDTO> nodeMap = allNodes.stream()
            .collect(Collectors.toMap(CscpOrgCompatibilityDTO::getId, node -> node));
        
        List<CscpOrgCompatibilityDTO> rootNodes = new ArrayList<>();
        
        for (CscpOrgCompatibilityDTO node : allNodes) {
            if (node.getParentId() == null || node.getParentId() == 0) {
                rootNodes.add(node);
            } else {
                CscpOrgCompatibilityDTO parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                }
            }
        }
        
        return rootNodes;
    }

    /**
     * 将SysOrg转换为兼容性DTO
     */
    private CscpOrgCompatibilityDTO convertFromSysOrg(SysOrg sysOrg) {
        CscpOrgCompatibilityDTO dto = new CscpOrgCompatibilityDTO();
        
        dto.setId(sysOrg.getId());
        dto.setOrgName(sysOrg.getName());
        dto.setOrgCode(sysOrg.getOrgCode());
        dto.setType(sysOrg.getOrgType() == 0 ? 1 : 2); // 0→1(虚拟), 1→2(单位)
        dto.setParentId(sysOrg.getParentId());
        dto.setLan(sysOrg.getDzName());
        dto.setLanId(sysOrg.getDzCode());
        dto.setDeleted("2".equals(sysOrg.getDelFlag()) ? 1 : 0);
        
        // 从扩展属性中恢复原有字段
        if (StringUtils.hasText(sysOrg.getExtProperties())) {
            try {
                Map<String, Object> extProps = objectMapper.readValue(sysOrg.getExtProperties(), Map.class);
                dto.setDescription((String) extProps.get("description"));
                dto.setOrgAbbreviation((String) extProps.get("orgAbbreviation"));
                dto.setUnitLevel((String) extProps.get("unitLevel"));
                dto.setAuthorityDepartment((String) extProps.get("authorityDepartment"));
                dto.setDetailType((String) extProps.get("detailType"));
                dto.setCheckUp((Boolean) extProps.get("checkUp"));
                dto.setCloudDiskSpaceSize((Integer) extProps.get("cloudDiskSpaceSize"));
                // ... 其他字段
            } catch (JsonProcessingException e) {
                log.warn("解析扩展属性失败: {}", e.getMessage());
            }
        }
        
        // 审计字段
        dto.setCreateTime(sysOrg.getCreateTime());
        dto.setCreateBy(sysOrg.getCreateBy());
        dto.setUpdateTime(sysOrg.getUpdateTime());
        dto.setUpdateBy(sysOrg.getUpdateBy());
        
        return dto;
    }

    /**
     * 将SysDept转换为兼容性DTO
     */
    private CscpOrgCompatibilityDTO convertFromSysDept(SysDept sysDept) {
        CscpOrgCompatibilityDTO dto = new CscpOrgCompatibilityDTO();
        
        dto.setId(sysDept.getDeptId());
        dto.setOrgName(sysDept.getDeptName());
        dto.setType(3); // 部门类型
        dto.setParentId(sysDept.getParentId());
        dto.setOrderBy(sysDept.getOrderNum());
        dto.setDeleted("2".equals(sysDept.getDelFlag()) ? 1 : 0);
        
        // 审计字段
        dto.setCreateTime(sysDept.getCreateTime());
        dto.setCreateBy(sysDept.getCreateBy());
        dto.setUpdateTime(sysDept.getUpdateTime());
        dto.setUpdateBy(sysDept.getUpdateBy());
        
        return dto;
    }
}
