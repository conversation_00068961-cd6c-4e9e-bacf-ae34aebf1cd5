package com.platform.system.migration;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 迁移状态管理器
 * 用于跟踪迁移进度和状态
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Component
public class MigrationStatusManager {

    private final ConcurrentHashMap<String, MigrationStatus> migrationStatuses = new ConcurrentHashMap<>();
    private final AtomicLong migrationIdGenerator = new AtomicLong(1);

    /**
     * 开始新的迁移任务
     */
    public String startMigration(String taskName) {
        String migrationId = "MIGRATION_" + migrationIdGenerator.getAndIncrement();
        
        MigrationStatus status = new MigrationStatus();
        status.setMigrationId(migrationId);
        status.setTaskName(taskName);
        status.setStatus(MigrationStatusEnum.RUNNING);
        status.setStartTime(LocalDateTime.now());
        status.setProgress(0);
        
        migrationStatuses.put(migrationId, status);
        
        log.info("开始迁移任务: {} [{}]", taskName, migrationId);
        return migrationId;
    }

    /**
     * 更新迁移进度
     */
    public void updateProgress(String migrationId, int progress, String message) {
        MigrationStatus status = migrationStatuses.get(migrationId);
        if (status != null) {
            status.setProgress(progress);
            status.setCurrentStep(message);
            status.setLastUpdateTime(LocalDateTime.now());
            
            log.info("迁移进度更新 [{}]: {}% - {}", migrationId, progress, message);
        }
    }

    /**
     * 完成迁移任务
     */
    public void completeMigration(String migrationId, boolean success, String message) {
        MigrationStatus status = migrationStatuses.get(migrationId);
        if (status != null) {
            status.setStatus(success ? MigrationStatusEnum.COMPLETED : MigrationStatusEnum.FAILED);
            status.setEndTime(LocalDateTime.now());
            status.setProgress(100);
            status.setResultMessage(message);
            
            log.info("迁移任务完成 [{}]: {} - {}", migrationId, 
                success ? "成功" : "失败", message);
        }
    }

    /**
     * 取消迁移任务
     */
    public void cancelMigration(String migrationId, String reason) {
        MigrationStatus status = migrationStatuses.get(migrationId);
        if (status != null) {
            status.setStatus(MigrationStatusEnum.CANCELLED);
            status.setEndTime(LocalDateTime.now());
            status.setResultMessage("任务被取消: " + reason);
            
            log.info("迁移任务被取消 [{}]: {}", migrationId, reason);
        }
    }

    /**
     * 获取迁移状态
     */
    public MigrationStatus getMigrationStatus(String migrationId) {
        return migrationStatuses.get(migrationId);
    }

    /**
     * 清理已完成的迁移状态
     */
    public void cleanupCompletedMigrations() {
        migrationStatuses.entrySet().removeIf(entry -> {
            MigrationStatus status = entry.getValue();
            return status.getStatus() != MigrationStatusEnum.RUNNING &&
                   status.getEndTime() != null &&
                   status.getEndTime().isBefore(LocalDateTime.now().minusHours(24));
        });
    }

    /**
     * 迁移状态枚举
     */
    public enum MigrationStatusEnum {
        RUNNING("运行中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        MigrationStatusEnum(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 迁移状态数据类
     */
    @Data
    public static class MigrationStatus {
        /**
         * 迁移ID
         */
        private String migrationId;

        /**
         * 任务名称
         */
        private String taskName;

        /**
         * 迁移状态
         */
        private MigrationStatusEnum status;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 最后更新时间
         */
        private LocalDateTime lastUpdateTime;

        /**
         * 进度百分比 (0-100)
         */
        private int progress;

        /**
         * 当前步骤描述
         */
        private String currentStep;

        /**
         * 结果消息
         */
        private String resultMessage;

        /**
         * 处理的记录数
         */
        private AtomicLong processedRecords = new AtomicLong(0);

        /**
         * 成功处理的记录数
         */
        private AtomicLong successRecords = new AtomicLong(0);

        /**
         * 失败的记录数
         */
        private AtomicLong failedRecords = new AtomicLong(0);

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 获取耗时（毫秒）
         */
        public Long getDurationMillis() {
            if (startTime != null) {
                LocalDateTime endTimeToUse = endTime != null ? endTime : LocalDateTime.now();
                return java.time.Duration.between(startTime, endTimeToUse).toMillis();
            }
            return null;
        }

        /**
         * 增加处理记录数
         */
        public void incrementProcessedRecords() {
            processedRecords.incrementAndGet();
        }

        /**
         * 增加成功记录数
         */
        public void incrementSuccessRecords() {
            successRecords.incrementAndGet();
        }

        /**
         * 增加失败记录数
         */
        public void incrementFailedRecords() {
            failedRecords.incrementAndGet();
        }
    }
}
