package com.platform.system.migration.adapter;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.migration.dto.CscpOrgCompatibilityDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CscpOrg兼容性控制器
 * 提供与原CscpOrg相同的API接口，确保业务连续性
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Api(tags = "组织机构兼容性接口")
@RestController
@RequestMapping("/compatibility/cscp-org")
@RequiredArgsConstructor
public class CscpOrgCompatibilityController {

    private final CscpOrgCompatibilityService cscpOrgCompatibilityService;

    /**
     * 根据ID查询组织机构信息
     */
    @ApiOperation(value = "根据ID查询组织机构", notes = "兼容原CscpOrg的findById接口")
    @GetMapping("/{id}")
    public ResponseResult<CscpOrgCompatibilityDTO> findById(
            @ApiParam(value = "机构ID", required = true) @PathVariable Long id) {
        
        log.debug("查询组织机构信息, ID: {}", id);
        
        CscpOrgCompatibilityDTO result = cscpOrgCompatibilityService.findById(id);
        if (result != null) {
            return ResponseResult.ok(result, "查询成功");
        } else {
            return ResponseResult.fail("未找到指定的组织机构");
        }
    }

    /**
     * 查询所有组织机构
     */
    @ApiOperation(value = "查询所有组织机构", notes = "兼容原CscpOrg的findAll接口")
    @GetMapping("/all")
    public ResponseResult<List<CscpOrgCompatibilityDTO>> findAll() {
        
        log.debug("查询所有组织机构信息");
        
        List<CscpOrgCompatibilityDTO> result = cscpOrgCompatibilityService.findAll();
        return ResponseResult.ok(result, "查询成功");
    }

    /**
     * 根据类型查询组织机构
     */
    @ApiOperation(value = "根据类型查询组织机构", notes = "兼容原CscpOrg的findByType接口")
    @GetMapping("/type/{type}")
    public ResponseResult<List<CscpOrgCompatibilityDTO>> findByType(
            @ApiParam(value = "机构类型：1-虚拟机构，2-单位，3-部门", required = true) @PathVariable Integer type) {
        
        log.debug("根据类型查询组织机构, type: {}", type);
        
        if (type == null || type < 1 || type > 3) {
            return ResponseResult.fail("机构类型参数错误，应为1-3之间的整数");
        }
        
        List<CscpOrgCompatibilityDTO> result = cscpOrgCompatibilityService.findByType(type);
        return ResponseResult.ok(result, "查询成功");
    }

    /**
     * 根据父ID查询子节点
     */
    @ApiOperation(value = "根据父ID查询子节点", notes = "兼容原CscpOrg的findByParentId接口")
    @GetMapping("/parent/{parentId}")
    public ResponseResult<List<CscpOrgCompatibilityDTO>> findByParentId(
            @ApiParam(value = "父级机构ID", required = true) @PathVariable Long parentId) {
        
        log.debug("根据父ID查询子节点, parentId: {}", parentId);
        
        List<CscpOrgCompatibilityDTO> result = cscpOrgCompatibilityService.findByParentId(parentId);
        return ResponseResult.ok(result, "查询成功");
    }

    /**
     * 构建树形结构
     */
    @ApiOperation(value = "构建组织机构树", notes = "兼容原CscpOrg的buildTree接口")
    @GetMapping("/tree")
    public ResponseResult<List<CscpOrgCompatibilityDTO>> buildTree() {
        
        log.debug("构建组织机构树形结构");
        
        List<CscpOrgCompatibilityDTO> result = cscpOrgCompatibilityService.buildTree();
        return ResponseResult.ok(result, "构建成功");
    }

    /**
     * 根据机构编码查询
     */
    @ApiOperation(value = "根据机构编码查询", notes = "兼容原CscpOrg的findByOrgCode接口")
    @GetMapping("/code/{orgCode}")
    public ResponseResult<CscpOrgCompatibilityDTO> findByOrgCode(
            @ApiParam(value = "机构编码", required = true) @PathVariable String orgCode) {
        
        log.debug("根据机构编码查询, orgCode: {}", orgCode);
        
        CscpOrgCompatibilityDTO result = cscpOrgCompatibilityService.findByOrgCode(orgCode);
        if (result != null) {
            return ResponseResult.ok(result, "查询成功");
        } else {
            return ResponseResult.fail("未找到指定编码的组织机构");
        }
    }

    /**
     * 查询指定节点的所有父级节点
     */
    @ApiOperation(value = "查询父级节点", notes = "查询指定节点的所有父级节点")
    @GetMapping("/{id}/parents")
    public ResponseResult<List<CscpOrgCompatibilityDTO>> findParents(
            @ApiParam(value = "机构ID", required = true) @PathVariable Long id) {
        
        log.debug("查询父级节点, id: {}", id);
        
        List<CscpOrgCompatibilityDTO> result = cscpOrgCompatibilityService.findParents(id);
        return ResponseResult.ok(result, "查询成功");
    }

    /**
     * 查询指定节点的所有子节点（递归）
     */
    @ApiOperation(value = "查询所有子节点", notes = "递归查询指定节点的所有子节点")
    @GetMapping("/{id}/children")
    public ResponseResult<List<CscpOrgCompatibilityDTO>> findAllChildren(
            @ApiParam(value = "机构ID", required = true) @PathVariable Long id) {
        
        log.debug("查询所有子节点, id: {}", id);
        
        List<CscpOrgCompatibilityDTO> result = cscpOrgCompatibilityService.findAllChildren(id);
        return ResponseResult.ok(result, "查询成功");
    }

    /**
     * 根据多个条件查询
     */
    @ApiOperation(value = "条件查询", notes = "根据多个条件查询组织机构")
    @PostMapping("/search")
    public ResponseResult<List<CscpOrgCompatibilityDTO>> search(
            @ApiParam(value = "查询条件") @RequestBody CscpOrgSearchRequest request) {
        
        log.debug("条件查询组织机构: {}", request);
        
        // 这里可以根据需要实现复杂的查询逻辑
        List<CscpOrgCompatibilityDTO> result;
        
        if (request.getType() != null) {
            result = cscpOrgCompatibilityService.findByType(request.getType());
        } else if (request.getParentId() != null) {
            result = cscpOrgCompatibilityService.findByParentId(request.getParentId());
        } else {
            result = cscpOrgCompatibilityService.findAll();
        }
        
        // 可以在这里添加更多的过滤条件
        if (request.getOrgName() != null && !request.getOrgName().trim().isEmpty()) {
            result = result.stream()
                .filter(org -> org.getOrgName() != null && 
                              org.getOrgName().contains(request.getOrgName().trim()))
                .collect(java.util.stream.Collectors.toList());
        }
        
        return ResponseResult.ok(result, "查询成功");
    }

    /**
     * 查询统计信息
     */
    @ApiOperation(value = "查询统计信息", notes = "查询组织机构的统计信息")
    @GetMapping("/statistics")
    public ResponseResult<CscpOrgStatistics> getStatistics() {
        
        log.debug("查询组织机构统计信息");
        
        List<CscpOrgCompatibilityDTO> allOrgs = cscpOrgCompatibilityService.findAll();
        
        CscpOrgStatistics statistics = new CscpOrgStatistics();
        statistics.setTotalCount(allOrgs.size());
        statistics.setVirtualOrgCount((int) allOrgs.stream().filter(org -> org.getType() == 1).count());
        statistics.setUnitCount((int) allOrgs.stream().filter(org -> org.getType() == 2).count());
        statistics.setDeptCount((int) allOrgs.stream().filter(org -> org.getType() == 3).count());
        statistics.setDeletedCount((int) allOrgs.stream().filter(CscpOrgCompatibilityDTO::isDeleted).count());
        
        return ResponseResult.ok(statistics, "查询成功");
    }

    /**
     * 查询条件请求对象
     */
    @lombok.Data
    public static class CscpOrgSearchRequest {
        private Integer type;
        private Long parentId;
        private String orgName;
        private String orgCode;
        private Integer deleted;
    }

    /**
     * 统计信息对象
     */
    @lombok.Data
    public static class CscpOrgStatistics {
        private Integer totalCount;
        private Integer virtualOrgCount;
        private Integer unitCount;
        private Integer deptCount;
        private Integer deletedCount;
    }
}
