package com.platform.system.migration.dto;

import com.platform.system.api.domain.SysDept;
import com.platform.system.domain.SysOrg;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 迁移数据传输对象
 * 用于在两个项目间传输迁移数据
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
public class MigrationDataDTO {

    /**
     * 迁移批次ID
     */
    private String migrationId;

    /**
     * 迁移时间
     */
    private LocalDateTime migrationTime;

    /**
     * SysOrg数据列表
     */
    private List<SysOrg> sysOrgList;

    /**
     * SysDept数据列表
     */
    private List<SysDept> sysDeptList;

    /**
     * 迁移统计信息
     */
    private MigrationStatisticsDTO statistics;

    /**
     * 迁移说明
     */
    private String description;

    /**
     * 获取总记录数
     */
    public int getTotalCount() {
        int count = 0;
        if (sysOrgList != null) {
            count += sysOrgList.size();
        }
        if (sysDeptList != null) {
            count += sysDeptList.size();
        }
        return count;
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return getTotalCount() > 0;
    }
}
