# Spring
spring:
  cloud:
    nacos:
      username: tyjg
      password: qE5=qR3]eD4}
      discovery:
        # 服务注册地址
        server-addr: 10.50.0.62:8848
        # 命名空间
        namespace: tyjg-dev
        group: ${nacos.group:DEFAULT_GROUP}
      config:
        # 配置中心地址
        server-addr: 10.50.0.62:8848
        # 配置文件格式
        file-extension: yml
        # 命名空间
        namespace: tyjg-dev
        group: ${nacos.group:DEFAULT_GROUP}
        # 共享配置
        shared-configs:
          - {dataId: common-application.yml,refresh: true}
          - {dataId: common-datasource.yml,refresh: true}
          - {dataId: common-cache.yml,refresh: true}