-- 组织机构数据迁移SQL脚本
-- 用于手动执行数据迁移或作为参考

-- =====================================================
-- 第一步：数据预处理和验证
-- =====================================================

-- 1.1 检查数据完整性
SELECT '数据完整性检查' as step;

-- 统计原始数据
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as virtual_org_count,
    SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as unit_org_count,
    SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as dept_org_count,
    SUM(CASE WHEN deleted = 1 THEN 1 ELSE 0 END) as deleted_count
FROM cscp_org;

-- 检查孤儿节点
SELECT '孤儿节点检查' as check_type, COUNT(*) as count
FROM cscp_org o1 
WHERE o1.parent_id IS NOT NULL AND o1.parent_id != 0 
AND NOT EXISTS (
    SELECT 1 FROM cscp_org o2 
    WHERE o2.id = o1.parent_id AND (o2.deleted = 0 OR o2.deleted IS NULL)
) 
AND (o1.deleted = 0 OR o1.deleted IS NULL);

-- 检查空名称记录
SELECT '空名称记录检查' as check_type, COUNT(*) as count
FROM cscp_org 
WHERE (org_name IS NULL OR org_name = '') 
AND (deleted = 0 OR deleted IS NULL);

-- =====================================================
-- 第二步：迁移SysOrg数据（虚拟机构和单位）
-- =====================================================

SELECT '开始迁移SysOrg数据' as step;

-- 2.1 清空目标表（如果需要）
-- DELETE FROM sys_org;

-- 2.2 迁移虚拟机构和单位到SysOrg
INSERT INTO sys_org (
    id, name, org_code, org_type, parent_id,
    dz_name, dz_code, status, del_flag, ext_properties,
    create_time, create_by, update_time, update_by
)
SELECT 
    id,
    org_name,
    COALESCE(org_code, code) as org_code,
    CASE WHEN type = 1 THEN 0 ELSE 1 END as org_type, -- 1(虚拟)→0, 2(单位)→1
    parent_id,
    lan as dz_name,
    lan_id as dz_code,
    '0' as status, -- 默认正常状态
    CASE WHEN deleted = 1 THEN '2' ELSE '0' END as del_flag,
    JSON_OBJECT(
        'description', description,
        'orgAbbreviation', org_abbreviation,
        'unitLevel', unit_level,
        'authorityDepartment', authority_department,
        'detailType', detail_type,
        'checkUp', check_up,
        'cloudDiskSpaceSize', cloud_disk_space_size,
        'hasSms', has_sms,
        'hasWatermark', has_watermark,
        'splitview', splitview,
        'creditCode', credit_code,
        'organizationCode', organization_code,
        'regionCode', region_code,
        'soleCode', sole_code,
        'orgLevelType', org_level_type,
        'orgLabel', org_label,
        'orgClassify', org_classify,
        'dutyPhone', duty_phone,
        'faxPhone', fax_phone
    ) as ext_properties,
    create_time,
    create_by,
    update_time,
    update_by
FROM cscp_org 
WHERE type IN (1, 2) 
AND (deleted = 0 OR deleted IS NULL)
ORDER BY level, order_by;

-- 验证SysOrg迁移结果
SELECT '验证SysOrg迁移结果' as step;
SELECT 
    COUNT(*) as migrated_count,
    SUM(CASE WHEN org_type = 0 THEN 1 ELSE 0 END) as virtual_org_count,
    SUM(CASE WHEN org_type = 1 THEN 1 ELSE 0 END) as unit_org_count
FROM sys_org;

-- =====================================================
-- 第三步：迁移SysDept数据（部门）
-- =====================================================

SELECT '开始迁移SysDept数据' as step;

-- 3.1 清空目标表（如果需要）
-- DELETE FROM sys_dept;

-- 3.2 迁移部门到SysDept
INSERT INTO sys_dept (
    dept_id, dept_name, parent_id, ancestors, order_num,
    leader, phone, email, status, org_id, del_flag,
    create_time, create_by, update_time, update_by
)
SELECT 
    id as dept_id,
    org_name as dept_name,
    -- 重新计算parent_id（只考虑部门间关系）
    CASE 
        WHEN EXISTS(SELECT 1 FROM cscp_org p WHERE p.id = co.parent_id AND p.type = 3)
        THEN co.parent_id 
        ELSE 0 
    END as parent_id,
    '' as ancestors, -- 稍后统一构建
    order_by as order_num,
    NULL as leader,
    NULL as phone,
    NULL as email,
    '0' as status, -- 默认正常状态
    -- 查找所属单位ID（向上查找type=2的父节点）
    (SELECT id FROM cscp_org 
     WHERE type = 2 
     AND (co.org_id_path LIKE CONCAT('%|', id, '|%') 
          OR co.org_id_path LIKE CONCAT(id, '|%') 
          OR co.org_id_path LIKE CONCAT('%|', id))
     AND (deleted = 0 OR deleted IS NULL) 
     ORDER BY level DESC LIMIT 1) as org_id,
    CASE WHEN deleted = 1 THEN '2' ELSE '0' END as del_flag,
    create_time,
    create_by,
    update_time,
    update_by
FROM cscp_org co
WHERE type = 3 
AND (deleted = 0 OR deleted IS NULL)
ORDER BY level, order_by;

-- 验证SysDept迁移结果
SELECT '验证SysDept迁移结果' as step;
SELECT 
    COUNT(*) as migrated_count,
    COUNT(CASE WHEN org_id IS NOT NULL THEN 1 END) as with_org_id_count,
    COUNT(CASE WHEN org_id IS NULL THEN 1 END) as without_org_id_count
FROM sys_dept;

-- =====================================================
-- 第四步：修复关系和构建ancestors
-- =====================================================

SELECT '开始修复关系和构建ancestors' as step;

-- 4.1 修复部门的orgId关联（对于没有orgIdPath的记录）
UPDATE sys_dept 
SET org_id = (
    WITH RECURSIVE parent_search AS (
        SELECT id, parent_id, type, 1 as level
        FROM cscp_org 
        WHERE id = sys_dept.dept_id
        
        UNION ALL
        
        SELECT o.id, o.parent_id, o.type, ps.level + 1
        FROM cscp_org o
        INNER JOIN parent_search ps ON o.id = ps.parent_id
        WHERE ps.level < 10 AND o.type != 2
    )
    SELECT id FROM parent_search WHERE type = 2 LIMIT 1
)
WHERE org_id IS NULL;

-- 4.2 构建部门的ancestors字段
-- 这个需要通过程序递归处理，SQL较难实现
-- 可以使用存储过程或者在应用程序中处理

-- =====================================================
-- 第五步：数据验证
-- =====================================================

SELECT '开始数据验证' as step;

-- 验证迁移数量
SELECT 
    'SysOrg' as table_name,
    COUNT(*) as final_count
FROM sys_org
UNION ALL
SELECT 
    'SysDept' as table_name,
    COUNT(*) as final_count
FROM sys_dept;

-- 验证部门的orgId关联
SELECT 
    '部门orgId关联验证' as check_type,
    COUNT(CASE WHEN org_id IS NULL THEN 1 END) as null_org_id_count,
    COUNT(CASE WHEN org_id IS NOT NULL THEN 1 END) as valid_org_id_count
FROM sys_dept;

-- 验证ancestors字段
SELECT 
    'ancestors字段验证' as check_type,
    COUNT(CASE WHEN ancestors IS NULL OR ancestors = '' THEN 1 END) as null_ancestors_count,
    COUNT(CASE WHEN ancestors IS NOT NULL AND ancestors != '' THEN 1 END) as valid_ancestors_count
FROM sys_dept;

-- =====================================================
-- 第六步：创建索引（如果需要）
-- =====================================================

-- 为新表创建必要索引
CREATE INDEX IF NOT EXISTS idx_sys_org_parent_id ON sys_org(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_org_org_code ON sys_org(org_code);
CREATE INDEX IF NOT EXISTS idx_sys_org_org_type ON sys_org(org_type);

CREATE INDEX IF NOT EXISTS idx_sys_dept_org_id ON sys_dept(org_id);
CREATE INDEX IF NOT EXISTS idx_sys_dept_parent_id ON sys_dept(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_dept_status ON sys_dept(status);

SELECT '数据迁移完成' as result;
