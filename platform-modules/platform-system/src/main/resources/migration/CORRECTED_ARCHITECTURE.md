# 修正后的迁移架构设计

## 问题分析

原始设计中存在严重的跨工程依赖问题：
- 在be-platform-cloud中创建的OrgMigrationController试图导入hnsw-tongyi-jigouyonghu中的服务
- 跨工程的直接类依赖导致编译错误
- 违反了微服务架构的独立性原则

## 修正后的架构

### 架构原则
1. **项目独立性**: 每个项目只依赖自己的代码
2. **通过接口通信**: 项目间通过HTTP API进行数据传输
3. **DTO传输**: 使用数据传输对象避免直接依赖实体类

### 项目职责划分

#### hnsw-tongyi-jigouyonghu（源项目）
**职责**: 数据提取、转换、发送
**核心组件**:
```
├── OrgMigrationService (数据转换服务)
├── OrgMigrationHelper (转换辅助工具)
├── CscpOrgMigrationMapper (源数据访问)
├── OrgMigrationController (迁移控制接口)
├── dto/
│   ├── CscpOrgMigrationDTO (源数据DTO)
│   ├── SysOrgDTO (目标机构DTO)
│   ├── SysDeptDTO (目标部门DTO)
│   ├── MigrationDataDTO (传输数据包)
│   ├── MigrationResultDTO (迁移结果)
│   └── MigrationStatisticsDTO (统计信息)
└── CscpOrgCompatibilityService (兼容性服务)
```

#### be-platform-cloud（目标项目）
**职责**: 数据接收、存储、验证
**核心组件**:
```
├── MigrationDataReceiver (数据接收服务)
├── MigrationReceiveController (接收控制接口)
└── dto/
    ├── MigrationDataDTO (传输数据包)
    ├── MigrationReceiveResultDTO (接收结果)
    └── MigrationStatisticsDTO (统计信息)
```

### 数据流转图

```
hnsw-tongyi-jigouyonghu                    be-platform-cloud
┌─────────────────────────┐               ┌─────────────────────────┐
│  CscpOrg (源数据)        │               │                         │
│           ↓             │               │                         │
│  OrgMigrationService    │               │                         │
│  ├── 数据验证           │               │                         │
│  ├── 数据转换           │               │                         │
│  └── 关系修复           │               │                         │
│           ↓             │               │                         │
│  MigrationDataDTO       │    HTTP API   │  MigrationDataReceiver  │
│  (数据传输包)           │ ──────────→   │  ├── 数据接收           │
│                         │               │  ├── 数据验证           │
│                         │               │  └── 数据存储           │
│                         │               │           ↓             │
│                         │               │  SysOrg + SysDept       │
└─────────────────────────┘               └─────────────────────────┘
```

### API接口设计

#### 源项目接口 (hnsw-tongyi-jigouyonghu)
```
POST /migration/org/execute                 # 执行迁移
GET  /migration/org/preview                 # 预览统计
POST /migration/org/send-to-target         # 发送到目标项目
GET  /compatibility/cscp-org/all           # 兼容性接口
```

#### 目标项目接口 (be-platform-cloud)
```
POST /migration/receive/migration-data     # 接收迁移数据包
POST /migration/receive/sys-org           # 接收SysOrg数据
POST /migration/receive/sys-dept          # 接收SysDept数据
POST /migration/receive/clear             # 清空目标数据
GET  /migration/receive/validate          # 验证接收数据
```

### 迁移流程

#### 方式一：程序化迁移
1. **源项目**: 调用 `OrgMigrationService.sendMigrationDataToTarget(targetUrl)`
2. **数据转换**: 在源项目中完成所有数据转换和关系修复
3. **HTTP传输**: 通过HTTP API发送MigrationDataDTO到目标项目
4. **目标接收**: 目标项目接收并存储到SysOrg和SysDept表

#### 方式二：手动迁移
1. **源项目**: 调用 `OrgMigrationService.executeMigration()` 生成转换数据
2. **导出数据**: 将转换后的数据导出为JSON或SQL
3. **目标项目**: 通过API或SQL脚本导入数据

### 关键修正点

#### 1. 移除跨工程依赖
**修正前**:
```java
// 错误：在be-platform-cloud中导入hnsw-tongyi-jigouyonghu的类
import com.platform.system.migration.OrgMigrationService; // ❌
```

**修正后**:
```java
// 正确：每个项目只使用自己的类
// hnsw-tongyi-jigouyonghu中
public class OrgMigrationService { ... }

// be-platform-cloud中  
public class MigrationDataReceiver { ... }
```

#### 2. 使用DTO避免实体依赖
**修正前**:
```java
// 错误：源项目直接使用目标项目的实体类
public SysOrg convertToSysOrg(CscpOrgMigrationDTO cscpOrg) // ❌
```

**修正后**:
```java
// 正确：使用DTO传输对象
public SysOrgDTO convertToSysOrg(CscpOrgMigrationDTO cscpOrg) // ✅
```

#### 3. HTTP通信替代直接调用
**修正前**:
```java
// 错误：直接调用目标项目的服务
sysOrgService.saveBatch(sysOrgList); // ❌
```

**修正后**:
```java
// 正确：通过HTTP API发送数据
RestTemplate restTemplate = new RestTemplate();
restTemplate.postForEntity(targetUrl + "/migration/receive/sys-org", 
    sysOrgList, MigrationReceiveResultDTO.class); // ✅
```

### 部署和使用

#### 开发环境
1. 启动be-platform-cloud项目（目标项目）
2. 启动hnsw-tongyi-jigouyonghu项目（源项目）
3. 在源项目中调用迁移接口，指定目标项目URL

#### 生产环境
1. 确保两个项目都已部署并可以相互访问
2. 配置正确的目标项目URL
3. 执行迁移操作

### 配置示例

#### 源项目配置
```yaml
migration:
  target:
    url: http://be-platform-cloud:8080  # 目标项目地址
    timeout: 30000                      # 超时时间
```

#### 目标项目配置
```yaml
migration:
  receive:
    enabled: true                       # 启用接收功能
    max-batch-size: 1000               # 最大批次大小
```

## 总结

修正后的架构完全解决了跨工程依赖问题：
- ✅ **项目独立**: 每个项目可以独立编译和部署
- ✅ **职责清晰**: 源项目负责转换，目标项目负责接收
- ✅ **通信标准**: 通过HTTP API进行标准化通信
- ✅ **易于维护**: 清晰的模块划分和接口定义
- ✅ **可扩展性**: 支持多种迁移方式和数据格式

这个修正后的架构符合微服务设计原则，确保了系统的可维护性和扩展性。
