# 组织机构数据迁移工具使用指南

## 概述

本工具用于将hnsw-tongyi-jigouyonghu项目的CscpOrg单表结构迁移到be-platform-cloud项目的SysOrg和SysDept微服务化架构。

## 迁移策略

### 数据分类
- **虚拟机构** (type=1) → SysOrg (org_type=0)
- **单位** (type=2) → SysOrg (org_type=1)  
- **部门** (type=3) → SysDept

### 字段映射

#### CscpOrg → SysOrg
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| id | id | 主键直接映射 |
| org_name | name | 机构名称 |
| org_code/code | org_code | 机构编码 |
| type | org_type | 类型转换 |
| parent_id | parent_id | 父级机构ID |
| lan | dz_name | 地区名称 |
| lan_id | dz_code | 地区编码 |
| 其他字段 | ext_properties | JSON扩展属性 |

#### CscpOrg → SysDept  
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| id | dept_id | 主键直接映射 |
| org_name | dept_name | 部门名称 |
| order_by | order_num | 显示顺序 |
| 计算得出 | org_id | 所属机构ID |
| 重新计算 | parent_id | 父部门ID |
| 重新构建 | ancestors | 祖级列表 |

## 使用方法

### 1. API接口方式

#### 预览迁移统计
```http
GET /migration/org/preview
```

#### 执行迁移
```http
POST /migration/org/execute
```

#### 验证结果
```http
GET /migration/org/validate
```

#### 回滚数据
```http
POST /migration/org/rollback
```

### 2. 程序调用方式

```java
@Autowired
private OrgMigrationService orgMigrationService;

// 预览统计信息
MigrationStatisticsDTO statistics = orgMigrationService.previewMigrationStatistics();

// 执行迁移
MigrationResultDTO result = orgMigrationService.executeMigration();

// 验证结果
MigrationResultDTO validation = orgMigrationService.validateMigrationResult();

// 回滚（如果需要）
orgMigrationService.rollbackMigration();
```

### 3. SQL脚本方式

直接执行 `org_migration_script.sql` 文件中的SQL语句。

## 配置说明

在 `application.yml` 中添加配置：

```yaml
migration:
  org:
    enabled: true
    batch-size: 1000
    backup-before-migration: true
    validate-after-migration: true
    allow-overwrite: false
    timeout-seconds: 3600
    verbose-logging: true
    validation:
      check-orphan-nodes: true
      check-circular-references: true
      check-null-names: true
      validate-relationships: true
      max-orphan-nodes: 0
      max-circular-references: 0
    performance:
      use-parallel-processing: false
      parallel-threads: 4
      use-batch-insert: true
      batch-insert-size: 500
      enable-transaction-optimization: true
```

## 迁移步骤

### 第一步：数据预处理和验证
1. 统计原始数据
2. 检查数据完整性
3. 验证关系正确性
4. 识别数据质量问题

### 第二步：迁移SysOrg数据
1. 迁移虚拟机构 (type=1)
2. 迁移单位 (type=2)
3. 保持层级关系
4. 构建扩展属性JSON

### 第三步：迁移SysDept数据
1. 迁移部门 (type=3)
2. 重新计算父子关系
3. 关联所属机构
4. 预留ancestors字段

### 第四步：修复关系
1. 修复部门的orgId关联
2. 重新计算部门parentId
3. 构建ancestors路径
4. 验证关系完整性

### 第五步：数据验证
1. 验证迁移数量
2. 检查关系完整性
3. 验证业务逻辑
4. 生成验证报告

## 注意事项

### 迁移前准备
1. **备份数据**：确保原始数据已备份
2. **环境检查**：确认目标环境配置正确
3. **权限验证**：确保有足够的数据库操作权限
4. **依赖检查**：确认相关服务正常运行

### 迁移过程中
1. **监控进度**：通过日志或API监控迁移进度
2. **性能观察**：注意数据库性能和内存使用
3. **错误处理**：及时处理迁移过程中的错误
4. **事务管理**：确保事务正确提交或回滚

### 迁移后验证
1. **数据完整性**：验证数据数量和内容正确性
2. **关系正确性**：检查父子关系和关联关系
3. **业务功能**：测试相关业务功能正常
4. **性能测试**：验证查询性能满足要求

## 常见问题

### Q1: 迁移失败如何处理？
A1: 
1. 查看详细错误日志
2. 检查数据完整性
3. 使用回滚功能恢复
4. 修复问题后重新迁移

### Q2: 如何处理孤儿节点？
A2:
1. 在迁移前修复数据
2. 或者在配置中允许孤儿节点
3. 迁移后手动处理

### Q3: ancestors字段如何构建？
A3:
ancestors字段通过递归算法自动构建，格式为 "0,parent1,parent2"

### Q4: 扩展属性如何使用？
A4:
扩展属性以JSON格式存储在ext_properties字段中，可以通过JSON函数查询和更新。

## 性能优化

### 批量处理
- 使用批量插入减少数据库交互
- 分批处理大量数据避免内存溢出

### 索引优化
- 迁移前创建必要索引
- 迁移后优化查询性能

### 事务优化
- 合理控制事务大小
- 避免长时间锁表

## 监控和日志

### 日志级别
- INFO: 关键步骤和进度信息
- WARN: 数据质量问题和警告
- ERROR: 迁移错误和异常

### 监控指标
- 迁移进度百分比
- 处理记录数
- 成功/失败记录数
- 迁移耗时

## 联系支持

如果在使用过程中遇到问题，请：
1. 查看详细日志
2. 检查配置文件
3. 参考本文档
4. 联系技术支持团队
