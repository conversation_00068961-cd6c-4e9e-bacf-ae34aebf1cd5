package com.platform.system.migration;

import com.platform.system.migration.dto.MigrationResultDTO;
import com.platform.system.migration.dto.MigrationStatisticsDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 组织机构迁移服务测试类
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class OrgMigrationServiceTest {

    @Autowired
    private OrgMigrationService orgMigrationService;

    /**
     * 测试预览迁移统计信息
     */
    @Test
    public void testPreviewMigrationStatistics() {
        log.info("开始测试预览迁移统计信息...");
        
        MigrationStatisticsDTO statistics = orgMigrationService.previewMigrationStatistics();
        
        assertNotNull(statistics);
        assertNotNull(statistics.getTotalCscpOrgCount());
        assertNotNull(statistics.getVirtualOrgCount());
        assertNotNull(statistics.getUnitOrgCount());
        assertNotNull(statistics.getDeptOrgCount());
        
        log.info("统计信息: 总计={}, 虚拟机构={}, 单位={}, 部门={}", 
            statistics.getTotalCscpOrgCount(),
            statistics.getVirtualOrgCount(),
            statistics.getUnitOrgCount(),
            statistics.getDeptOrgCount());
        
        // 验证数据一致性
        assertTrue(statistics.isDataConsistent(), "数据一致性验证失败");
        
        log.info("预览迁移统计信息测试通过");
    }

    /**
     * 测试数据迁移执行
     */
    @Test
    public void testExecuteMigration() {
        log.info("开始测试数据迁移执行...");
        
        // 先预览统计信息
        MigrationStatisticsDTO preStats = orgMigrationService.previewMigrationStatistics();
        log.info("迁移前统计: {}", preStats);
        
        // 执行迁移
        MigrationResultDTO result = orgMigrationService.executeMigration();
        
        assertNotNull(result);
        assertTrue(result.getSuccess(), "迁移应该成功: " + result.getMessage());
        assertNotNull(result.getStatistics());
        assertNotNull(result.getDurationMillis());
        
        // 验证迁移数量
        assertTrue(result.getSysOrgMigratedCount() >= 0, "SysOrg迁移数量应该大于等于0");
        assertTrue(result.getSysDeptMigratedCount() >= 0, "SysDept迁移数量应该大于等于0");
        
        log.info("迁移结果: SysOrg={}, SysDept={}, 耗时={}ms", 
            result.getSysOrgMigratedCount(),
            result.getSysDeptMigratedCount(),
            result.getDurationMillis());
        
        log.info("数据迁移执行测试通过");
    }

    /**
     * 测试迁移结果验证
     */
    @Test
    public void testValidateMigrationResult() {
        log.info("开始测试迁移结果验证...");
        
        // 先执行迁移
        MigrationResultDTO migrationResult = orgMigrationService.executeMigration();
        assertTrue(migrationResult.getSuccess(), "迁移应该成功");
        
        // 验证迁移结果
        MigrationResultDTO validationResult = orgMigrationService.validateMigrationResult();
        
        assertNotNull(validationResult);
        assertNotNull(validationResult.getFinalSysOrgCount());
        assertNotNull(validationResult.getFinalSysDeptCount());
        
        log.info("验证结果: SysOrg最终数量={}, SysDept最终数量={}", 
            validationResult.getFinalSysOrgCount(),
            validationResult.getFinalSysDeptCount());
        
        // 如果有验证错误，打印出来
        if (validationResult.getValidationErrors() != null && !validationResult.getValidationErrors().isEmpty()) {
            log.warn("验证发现问题: {}", validationResult.getValidationErrors());
        }
        
        log.info("迁移结果验证测试通过");
    }

    /**
     * 测试回滚功能
     */
    @Test
    public void testRollbackMigration() {
        log.info("开始测试回滚功能...");
        
        // 先执行迁移
        MigrationResultDTO migrationResult = orgMigrationService.executeMigration();
        assertTrue(migrationResult.getSuccess(), "迁移应该成功");
        
        // 验证迁移后有数据
        MigrationResultDTO beforeRollback = orgMigrationService.validateMigrationResult();
        assertTrue(beforeRollback.getFinalSysOrgCount() > 0 || beforeRollback.getFinalSysDeptCount() > 0, 
            "迁移后应该有数据");
        
        // 执行回滚
        orgMigrationService.rollbackMigration();
        
        // 验证回滚后数据被清空
        MigrationResultDTO afterRollback = orgMigrationService.validateMigrationResult();
        assertEquals(0L, afterRollback.getFinalSysOrgCount(), "回滚后SysOrg应该为空");
        assertEquals(0L, afterRollback.getFinalSysDeptCount(), "回滚后SysDept应该为空");
        
        log.info("回滚功能测试通过");
    }

    /**
     * 测试完整的迁移流程
     */
    @Test
    public void testCompleteMigrationFlow() {
        log.info("开始测试完整的迁移流程...");
        
        try {
            // 1. 预览统计信息
            MigrationStatisticsDTO previewStats = orgMigrationService.previewMigrationStatistics();
            log.info("步骤1 - 预览统计: {}", previewStats);
            
            // 2. 执行迁移
            MigrationResultDTO migrationResult = orgMigrationService.executeMigration();
            assertTrue(migrationResult.getSuccess(), "迁移应该成功");
            log.info("步骤2 - 迁移完成: {}", migrationResult.getMessage());
            
            // 3. 验证结果
            MigrationResultDTO validationResult = orgMigrationService.validateMigrationResult();
            log.info("步骤3 - 验证结果: {}", validationResult.getMessage());
            
            // 4. 回滚（测试环境）
            orgMigrationService.rollbackMigration();
            log.info("步骤4 - 回滚完成");
            
            log.info("完整迁移流程测试通过");
            
        } catch (Exception e) {
            log.error("完整迁移流程测试失败", e);
            fail("完整迁移流程测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试数据完整性验证
     */
    @Test
    public void testDataIntegrityValidation() {
        log.info("开始测试数据完整性验证...");
        
        MigrationStatisticsDTO statistics = orgMigrationService.previewMigrationStatistics();
        
        // 验证基本统计数据
        assertNotNull(statistics.getTotalCscpOrgCount());
        assertTrue(statistics.getTotalCscpOrgCount() >= 0);
        
        // 验证数据一致性
        Long totalByType = statistics.getVirtualOrgCount() + 
                          statistics.getUnitOrgCount() + 
                          statistics.getDeptOrgCount();
        
        log.info("按类型统计总数: {}, 有效记录数: {}", totalByType, statistics.getValidCount());
        
        // 检查数据质量
        if (statistics.getOrphanNodeCount() > 0) {
            log.warn("发现{}个孤儿节点", statistics.getOrphanNodeCount());
        }
        
        if (statistics.getCircularReferenceCount() > 0) {
            log.warn("发现{}个循环引用", statistics.getCircularReferenceCount());
        }
        
        if (statistics.getNullNameCount() > 0) {
            log.warn("发现{}个空名称记录", statistics.getNullNameCount());
        }
        
        log.info("数据完整性验证测试通过");
    }
}
