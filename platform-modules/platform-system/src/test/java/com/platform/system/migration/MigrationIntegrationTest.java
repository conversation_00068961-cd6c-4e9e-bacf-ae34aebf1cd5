package com.platform.system.migration;

import com.platform.system.migration.adapter.CscpOrgCompatibilityService;
import com.platform.system.migration.dto.CscpOrgCompatibilityDTO;
import com.platform.system.migration.dto.MigrationResultDTO;
import com.platform.system.migration.dto.MigrationStatisticsDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 迁移集成测试
 * 测试完整的迁移流程和兼容性功能
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Transactional
public class MigrationIntegrationTest {

    @Autowired
    private OrgMigrationService orgMigrationService;

    @Autowired
    private CscpOrgCompatibilityService compatibilityService;

    private static MigrationResultDTO migrationResult;
    private static MigrationStatisticsDTO previewStats;

    @Test
    @Order(1)
    @DisplayName("1. 预览迁移统计信息")
    public void testPreviewMigrationStatistics() {
        log.info("=== 开始测试：预览迁移统计信息 ===");
        
        previewStats = orgMigrationService.previewMigrationStatistics();
        
        assertNotNull(previewStats, "统计信息不能为空");
        assertNotNull(previewStats.getTotalCscpOrgCount(), "总记录数不能为空");
        assertTrue(previewStats.getTotalCscpOrgCount() >= 0, "总记录数应该大于等于0");
        
        log.info("原始数据统计:");
        log.info("  总记录数: {}", previewStats.getTotalCscpOrgCount());
        log.info("  虚拟机构: {}", previewStats.getVirtualOrgCount());
        log.info("  单位: {}", previewStats.getUnitOrgCount());
        log.info("  部门: {}", previewStats.getDeptOrgCount());
        log.info("  已删除: {}", previewStats.getDeletedCount());
        log.info("  有效记录: {}", previewStats.getValidCount());
        
        // 验证数据一致性
        assertTrue(previewStats.isDataConsistent(), "数据一致性验证失败");
        
        // 检查数据质量
        if (previewStats.getOrphanNodeCount() > 0) {
            log.warn("发现{}个孤儿节点", previewStats.getOrphanNodeCount());
        }
        
        if (previewStats.getCircularReferenceCount() > 0) {
            log.warn("发现{}个循环引用", previewStats.getCircularReferenceCount());
        }
        
        if (previewStats.getNullNameCount() > 0) {
            log.warn("发现{}个空名称记录", previewStats.getNullNameCount());
        }
        
        log.info("=== 预览迁移统计信息测试通过 ===\n");
    }

    @Test
    @Order(2)
    @DisplayName("2. 执行数据迁移")
    public void testExecuteMigration() {
        log.info("=== 开始测试：执行数据迁移 ===");
        
        migrationResult = orgMigrationService.executeMigration();
        
        assertNotNull(migrationResult, "迁移结果不能为空");
        assertTrue(migrationResult.getSuccess(), "迁移应该成功: " + migrationResult.getMessage());
        assertNotNull(migrationResult.getStatistics(), "迁移统计信息不能为空");
        assertNotNull(migrationResult.getDurationMillis(), "迁移耗时不能为空");
        
        log.info("迁移执行结果:");
        log.info("  迁移状态: {}", migrationResult.getSuccess() ? "成功" : "失败");
        log.info("  迁移消息: {}", migrationResult.getMessage());
        log.info("  SysOrg迁移数量: {}", migrationResult.getSysOrgMigratedCount());
        log.info("  SysDept迁移数量: {}", migrationResult.getSysDeptMigratedCount());
        log.info("  迁移耗时: {}ms", migrationResult.getDurationMillis());
        
        // 验证迁移数量合理性
        assertTrue(migrationResult.getSysOrgMigratedCount() >= 0, "SysOrg迁移数量应该大于等于0");
        assertTrue(migrationResult.getSysDeptMigratedCount() >= 0, "SysDept迁移数量应该大于等于0");
        
        // 验证迁移数量与预览统计的一致性
        if (previewStats != null) {
            long expectedOrgCount = previewStats.getVirtualOrgCount() + previewStats.getUnitOrgCount();
            assertEquals(expectedOrgCount, migrationResult.getSysOrgMigratedCount().longValue(), 
                "SysOrg迁移数量应该等于虚拟机构+单位数量");
            
            assertEquals(previewStats.getDeptOrgCount(), migrationResult.getSysDeptMigratedCount().longValue(),
                "SysDept迁移数量应该等于部门数量");
        }
        
        log.info("=== 执行数据迁移测试通过 ===\n");
    }

    @Test
    @Order(3)
    @DisplayName("3. 验证迁移结果")
    public void testValidateMigrationResult() {
        log.info("=== 开始测试：验证迁移结果 ===");
        
        MigrationResultDTO validationResult = orgMigrationService.validateMigrationResult();
        
        assertNotNull(validationResult, "验证结果不能为空");
        assertNotNull(validationResult.getFinalSysOrgCount(), "最终SysOrg数量不能为空");
        assertNotNull(validationResult.getFinalSysDeptCount(), "最终SysDept数量不能为空");
        
        log.info("迁移结果验证:");
        log.info("  验证状态: {}", validationResult.getSuccess() ? "通过" : "失败");
        log.info("  验证消息: {}", validationResult.getMessage());
        log.info("  最终SysOrg数量: {}", validationResult.getFinalSysOrgCount());
        log.info("  最终SysDept数量: {}", validationResult.getFinalSysDeptCount());
        
        // 验证最终数量与迁移数量的一致性
        if (migrationResult != null) {
            assertEquals(migrationResult.getSysOrgMigratedCount().longValue(), 
                validationResult.getFinalSysOrgCount().longValue(),
                "最终SysOrg数量应该等于迁移数量");
            
            assertEquals(migrationResult.getSysDeptMigratedCount().longValue(),
                validationResult.getFinalSysDeptCount().longValue(),
                "最终SysDept数量应该等于迁移数量");
        }
        
        // 检查验证错误
        if (validationResult.getValidationErrors() != null && !validationResult.getValidationErrors().isEmpty()) {
            log.warn("验证发现问题:");
            for (String error : validationResult.getValidationErrors()) {
                log.warn("  - {}", error);
            }
        }
        
        log.info("=== 验证迁移结果测试通过 ===\n");
    }

    @Test
    @Order(4)
    @DisplayName("4. 测试兼容性接口")
    public void testCompatibilityService() {
        log.info("=== 开始测试：兼容性接口 ===");
        
        // 测试查询所有数据
        List<CscpOrgCompatibilityDTO> allOrgs = compatibilityService.findAll();
        assertNotNull(allOrgs, "查询所有数据不能为空");
        
        log.info("兼容性接口测试:");
        log.info("  查询到的总记录数: {}", allOrgs.size());
        
        // 验证数据完整性
        if (migrationResult != null) {
            long expectedTotal = migrationResult.getSysOrgMigratedCount() + migrationResult.getSysDeptMigratedCount();
            assertEquals(expectedTotal, allOrgs.size(), "兼容性接口查询的总数应该等于迁移的总数");
        }
        
        // 测试按类型查询
        List<CscpOrgCompatibilityDTO> virtualOrgs = compatibilityService.findByType(1);
        List<CscpOrgCompatibilityDTO> units = compatibilityService.findByType(2);
        List<CscpOrgCompatibilityDTO> depts = compatibilityService.findByType(3);
        
        log.info("  虚拟机构数量: {}", virtualOrgs.size());
        log.info("  单位数量: {}", units.size());
        log.info("  部门数量: {}", depts.size());
        
        // 验证类型查询的正确性
        assertEquals(virtualOrgs.size() + units.size() + depts.size(), allOrgs.size(),
            "按类型查询的总数应该等于全部数据的总数");
        
        // 测试树形结构构建
        List<CscpOrgCompatibilityDTO> tree = compatibilityService.buildTree();
        assertNotNull(tree, "树形结构不能为空");
        
        log.info("  树形结构根节点数量: {}", tree.size());
        
        // 测试单个查询
        if (!allOrgs.isEmpty()) {
            CscpOrgCompatibilityDTO firstOrg = allOrgs.get(0);
            CscpOrgCompatibilityDTO foundOrg = compatibilityService.findById(firstOrg.getId());
            assertNotNull(foundOrg, "根据ID查询应该能找到数据");
            assertEquals(firstOrg.getId(), foundOrg.getId(), "查询到的数据ID应该一致");
            
            log.info("  单个查询测试通过，查询ID: {}, 名称: {}", foundOrg.getId(), foundOrg.getOrgName());
        }
        
        log.info("=== 兼容性接口测试通过 ===\n");
    }

    @Test
    @Order(5)
    @DisplayName("5. 测试业务逻辑兼容性")
    public void testBusinessLogicCompatibility() {
        log.info("=== 开始测试：业务逻辑兼容性 ===");
        
        List<CscpOrgCompatibilityDTO> allOrgs = compatibilityService.findAll();
        
        if (!allOrgs.isEmpty()) {
            // 测试类型判断方法
            for (CscpOrgCompatibilityDTO org : allOrgs) {
                assertNotNull(org.getType(), "机构类型不能为空");
                assertTrue(org.getType() >= 1 && org.getType() <= 3, "机构类型应该在1-3之间");
                
                // 测试类型判断方法
                if (org.getType() == 1) {
                    assertTrue(org.isVirtualOrg(), "类型为1的应该是虚拟机构");
                    assertFalse(org.isUnit(), "类型为1的不应该是单位");
                    assertFalse(org.isDept(), "类型为1的不应该是部门");
                } else if (org.getType() == 2) {
                    assertFalse(org.isVirtualOrg(), "类型为2的不应该是虚拟机构");
                    assertTrue(org.isUnit(), "类型为2的应该是单位");
                    assertFalse(org.isDept(), "类型为2的不应该是部门");
                } else if (org.getType() == 3) {
                    assertFalse(org.isVirtualOrg(), "类型为3的不应该是虚拟机构");
                    assertFalse(org.isUnit(), "类型为3的不应该是单位");
                    assertTrue(org.isDept(), "类型为3的应该是部门");
                }
                
                // 测试类型描述
                assertNotNull(org.getTypeDescription(), "类型描述不能为空");
                assertTrue(org.getTypeDescription().length() > 0, "类型描述不能为空字符串");
            }
            
            log.info("  业务逻辑方法测试通过，测试了{}条记录", allOrgs.size());
            
            // 测试父子关系查询
            CscpOrgCompatibilityDTO firstOrg = allOrgs.get(0);
            if (firstOrg.getParentId() != null && firstOrg.getParentId() != 0) {
                List<CscpOrgCompatibilityDTO> parents = compatibilityService.findParents(firstOrg.getId());
                assertNotNull(parents, "父级节点查询结果不能为空");
                log.info("  父级节点查询测试通过，ID: {}, 父级数量: {}", firstOrg.getId(), parents.size());
            }
            
            // 测试子节点查询
            List<CscpOrgCompatibilityDTO> children = compatibilityService.findAllChildren(firstOrg.getId());
            assertNotNull(children, "子节点查询结果不能为空");
            log.info("  子节点查询测试通过，ID: {}, 子节点数量: {}", firstOrg.getId(), children.size());
        }
        
        log.info("=== 业务逻辑兼容性测试通过 ===\n");
    }

    @Test
    @Order(6)
    @DisplayName("6. 性能测试")
    public void testPerformance() {
        log.info("=== 开始测试：性能测试 ===");
        
        long startTime = System.currentTimeMillis();
        
        // 测试大量查询的性能
        for (int i = 0; i < 10; i++) {
            List<CscpOrgCompatibilityDTO> allOrgs = compatibilityService.findAll();
            assertNotNull(allOrgs, "查询结果不能为空");
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("  10次查询所有数据耗时: {}ms", duration);
        assertTrue(duration < 10000, "10次查询耗时应该小于10秒"); // 性能要求
        
        // 测试树形结构构建性能
        startTime = System.currentTimeMillis();
        List<CscpOrgCompatibilityDTO> tree = compatibilityService.buildTree();
        endTime = System.currentTimeMillis();
        duration = endTime - startTime;
        
        log.info("  构建树形结构耗时: {}ms", duration);
        assertTrue(duration < 5000, "构建树形结构耗时应该小于5秒"); // 性能要求
        
        log.info("=== 性能测试通过 ===\n");
    }

    @Test
    @Order(7)
    @DisplayName("7. 清理测试数据")
    public void testCleanup() {
        log.info("=== 开始测试：清理测试数据 ===");
        
        // 在测试环境中回滚数据
        orgMigrationService.rollbackMigration();
        
        // 验证回滚结果
        MigrationResultDTO validationResult = orgMigrationService.validateMigrationResult();
        assertEquals(0L, validationResult.getFinalSysOrgCount(), "回滚后SysOrg应该为空");
        assertEquals(0L, validationResult.getFinalSysDeptCount(), "回滚后SysDept应该为空");
        
        log.info("  测试数据清理完成");
        log.info("=== 清理测试数据完成 ===\n");
    }
}
