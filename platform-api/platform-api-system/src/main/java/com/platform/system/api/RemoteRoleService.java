package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysRoleQueryReq;
import com.platform.system.api.domain.response.SysRoleResp;
import com.platform.system.api.factory.RemoteRoleFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 角色服务
 * @author: tr
 * @date: 2024年01月31日 19:27
 */
@FeignClient(contextId = "remoteRoleService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteRoleFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteRoleService {

    @PostMapping("/role/list")
    ResponseResult<List<SysRoleResp>> listRoles(@RequestBody SysRoleQueryReq sysRoleQueryReq, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping(value = "/role/getById")
    ResponseResult<SysRoleResp> getRoleById(@RequestParam("roleId") Long roleId, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);
}
