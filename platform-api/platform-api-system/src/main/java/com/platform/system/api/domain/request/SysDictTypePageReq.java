package com.platform.system.api.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 系统-字典类型请求参数实体类
 * @author: tr
 * @date: 2024年06月14日 10:27
 */
@Data
@ToString
public class SysDictTypePageReq{

    /** 字典名称 */
    @ApiModelProperty(value = "字典名称")
    private String dictName;

    /** 字典类型 */
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private String status;

    @ApiModelProperty("当前页数")
    private int pageNum = 1;
    @ApiModelProperty("每页记录数")
    private int pageSize = 10;

    @ApiModelProperty("开始时间")
    private String beginTime;
    @ApiModelProperty("结束时间")
    private String endTime;

    /** 字典类型的集合 */
    @ApiModelProperty(value = "字典类型的集合")
    private List<String> dictTypeList;
}
