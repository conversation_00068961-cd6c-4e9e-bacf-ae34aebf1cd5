package com.platform.system.api.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Description: 组织机构扩展信息入参
 * @author: tr
 * @date: 2024年05月15日 11:16
 */
@Data
@ToString
public class SysOrgExtendReq {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，同组织机构ID一致
     */
    @ApiModelProperty(value = "主键ID，同组织机构ID一致")
    private Long id;

    /**
     * 扩展字段字符类型1
     */
    @ApiModelProperty(value = "扩展字段字符类型1")
    private String extendStr1;

    /**
     * 扩展字段字符类型2
     */
    @ApiModelProperty(value = "扩展字段字符类型2")
    private String extendStr2;

    /**
     * 扩展字段字符类型3
     */
    @ApiModelProperty(value = "扩展字段字符类型3")
    private String extendStr3;

    /**
     * 扩展字段字符类型4
     */
    @ApiModelProperty(value = "扩展字段字符类型4")
    private String extendStr4;

    /**
     * 扩展字段字符类型5
     */
    @ApiModelProperty(value = "扩展字段字符类型5")
    private String extendStr5;

    /**
     * 扩展字段字符类型6
     */
    @ApiModelProperty(value = "扩展字段字符类型6")
    private String extendStr6;

    /**
     * 扩展字段字符类型7
     */
    @ApiModelProperty(value = "扩展字段字符类型7")
    private String extendStr7;

    /**
     * 扩展字段字符类型8
     */
    @ApiModelProperty(value = "扩展字段字符类型8")
    private String extendStr8;

    /**
     * 扩展字段字符类型9
     */
    @ApiModelProperty(value = "扩展字段字符类型9")
    private String extendStr9;

    /**
     * 扩展字段字符类型10
     */
    @ApiModelProperty(value = "扩展字段字符类型10")
    private String extendStr10;

    /**
     * 扩展字段字符类型11
     */
    @ApiModelProperty(value = "扩展字段字符类型11")
    private String extendStr11;

    /**
     * 扩展字段字符类型12
     */
    @ApiModelProperty(value = "扩展字段字符类型12")
    private String extendStr12;

    /**
     * 扩展字段字符类型13
     */
    @ApiModelProperty(value = "扩展字段字符类型13")
    private String extendStr13;

    /**
     * 扩展字段字符类型14
     */
    @ApiModelProperty(value = "扩展字段字符类型14")
    private String extendStr14;

    /**
     * 扩展字段字符类型15
     */
    @ApiModelProperty(value = "扩展字段字符类型15")
    private String extendStr15;

    /**
     * 扩展字段整型类型1
     */
    @ApiModelProperty(value = "扩展字段整型类型1")
    private Long extendInt1;

    /**
     * 扩展字段整型类型2
     */
    @ApiModelProperty(value = "扩展字段整型类型2")
    private Long extendInt2;

    /**
     * 扩展字段整型类型3
     */
    @ApiModelProperty(value = "扩展字段整型类型3")
    private Long extendInt3;

    /**
     * 扩展字段小数类型1
     */
    @ApiModelProperty(value = "扩展字段小数类型1")
    private Double extendDouble1;

    /**
     * 扩展字段小数类型2
     */
    @ApiModelProperty(value = "扩展字段小数类型2")
    private Double extendDouble2;

    /**
     * 扩展字段小数类型3
     */
    @ApiModelProperty(value = "扩展字段小数类型3")
    private Double extendDouble3;

    /**
     * 扩展字段时间类型1
     */
    @ApiModelProperty(value = "扩展字段时间类型1")
    private Date extendDatetime1;

    /**
     * 扩展字段时间类型2
     */
    @ApiModelProperty(value = "扩展字段时间类型2")
    private Date extendDatetime2;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
