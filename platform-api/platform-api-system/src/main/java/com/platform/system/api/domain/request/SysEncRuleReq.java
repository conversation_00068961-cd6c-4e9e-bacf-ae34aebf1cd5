package com.platform.system.api.domain.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ctdi.base.biz.domain.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysEncRuleReq
{
    /**
     * 表主键
     */
    @ApiModelProperty(value = "表主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String columnName;

    /**
     * 加密类型
     */
    @ApiModelProperty(value = "加密类型")
    private String encType;

}
