package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteCountryAddressService;
import com.platform.system.api.domain.request.SysCountryAddressQueryReq;
import com.platform.system.api.domain.response.SysCountryAddressResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 地址码服务降级处理
 * @author: tr
 * @date: 2024年04月24日 14:57
 */
@Slf4j
@Component
public class RemoteCountryAddressFallbackFactory implements FallbackFactory<RemoteCountryAddressService> {

    @Override
    public RemoteCountryAddressService create(Throwable throwable) {
        log.error("地址码服务调用失败:{}", throwable.getMessage());
        return new RemoteCountryAddressService(){
            @Override
            public ResponseResult<List<SysCountryAddressResp>> list(SysCountryAddressQueryReq sysCountryAddressQueryReq, String token) {
                return ResponseResult.fail("获取地址码列表失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysCountryAddressResp>> listByPCodes(String[] codeList) {
                return ResponseResult.fail("根据父code获取地址码列表失败:" + throwable.getMessage());
            }
        };
    }
}
