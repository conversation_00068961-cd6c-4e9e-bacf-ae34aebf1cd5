package com.platform.system.api.domain.request;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @Description: 组织机构新增或修改入参
 * @author: tr
 * @Date: 2024/3/15 10:06
 */
@ApiModel("组织机构新增或修改入参")
@Data
@ToString
public class SysOrgReq {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	private Long id;

	/**
	 * 机构（主体）名称
	 */
	@NotBlank(message = "机构名称不能为空")
	@Pattern(regexp = "^[\\u4e00-\\u9fa5_a-zA-Z0-9_]{1,100}", message = "机构名称格式不正确，支持中英文字母、数字和下划线组合，长度1-100个字符")
	@ApiModelProperty("机构（主体）名称")
	@Excel(name = "机构名称", cellType = Excel.ColumnType.STRING)
	private String name;

	/**
	 * 组织机构类型
	 */
	@ApiModelProperty("组织机构类型")
	private Integer orgType;

	/**
	 * 父级机构Id
	 */
	@ApiModelProperty("父级机构Id，如果是顶级机构则为0")
	private Long parentId;

	/**
	 * 联系人
	 */
	@Size(min = 0, max = 30, message = "联系人不能超过30个字符")
	@Pattern(regexp = "^$|^[\\u4e00-\\u9fa5_a-zA-Z0-9_]{2,30}", message = "联系人格式不正确，支持中英文字母、数字和下划线组合，长度不超过30个字符")
	@ApiModelProperty("联系人")
	private String contactsPerson;

	/**
	 * 联系电话
	 */
	@Pattern(regexp = "^$|^1[3-9]\\d{9}$|^1[3-9]\\d{1}[-\\s]\\d{4}[-\\s]\\d{4}$|^\\(1[3-9]\\d{1}\\)\\d{4}-\\d{4}$|^0\\d{3}-\\d{7}$|^0\\d{2}-\\d{8}$", message = "联系电话格式不正确")
	@ApiModelProperty("联系电话")
	private String contactsPhone;

	/**
	 * 单位联系方式(座机)
	 */
	@ApiModelProperty("单位联系方式(座机)")
	@Pattern(regexp = "^$|^1[3-9]\\d{9}$|^1[3-9]\\d{1}[-\\s]\\d{4}[-\\s]\\d{4}$|^\\(1[3-9]\\d{1}\\)\\d{4}-\\d{4}$|^0\\d{3}-\\d{7}$|^0\\d{2}-\\d{8}$", message = "联系方式格式不正确")
	private String contactsWay;

	/** 备注 **/
	@Size(min = 0, max = 500, message = "机构描述长度不能超过500个字符")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 所在地址名称
	 */
	@Size(min = 0, max = 200, message = "详细地址不能超过200个字符")
	@ApiModelProperty("所在地址名称")
	private String dzName;

	/**
	 * 所在地址code
	 */
	@ApiModelProperty("所在地址code")
	private String dzCode;

	/**
	 * 所在地址区划级别
	 */
	@ApiModelProperty("所在地址区划级别")
	private Integer dzRegionLevel;

	/**
	 * 详细地址
	 */
	@ApiModelProperty("详细地址")
	private String dzDetailAddress;

	/**
	 * 角色组Id
	 */
	@ApiModelProperty("角色组Id")
	private Long roleGroupId;

	/**
	 * 管辖范围编码
	 */
	@ApiModelProperty("管辖范围编码")
	private String guCode;

	/**
	 * 管辖范围全称
	 */
	@ApiModelProperty("管辖范围全称")
	private String guName;

	/**
	 * 管辖范围区划级别
	 */
	@ApiModelProperty("管辖范围区划级别")
	private Integer guRegionLevel;

	/**
	 * 组织机构扩展信息
	 */
	@ApiModelProperty("组织机构扩展信息")
	private SysOrgExtendReq sysOrgExtendReq;

	/** 机构编码 **/
	@ApiModelProperty("第三方机构编码")
	@Excel(name = "机构代码", cellType = Excel.ColumnType.STRING)
	private String orgCodeThird;

	@ApiModelProperty("第三方上级机构编码")
	@Excel(name = "上级机构", cellType = Excel.ColumnType.STRING)
	private String parentCodeThird;

	@ApiModelProperty("第三方机构层级路径")
	@Excel(name = "机构层级路径", cellType = Excel.ColumnType.STRING)
	private String codePathThird;

	@ApiModelProperty("机构层级")
	@Excel(name = "机构层级", cellType = Excel.ColumnType.NUMERIC)
	private String orgLevel;
}
