package com.platform.system.api.domain;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
@Data
public class SysUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;

    /** 部门ID */
    private Long deptId;

    /** 用户账号 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户类型 **/
    private String userType;

    /** 用户邮箱 */
    private String email;

    /** 手机号码 */
    private String phonenumber;

    /** 用户性别 */
    private String sex;

    /** 用户头像 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 帐号状态（0正常 1停用） */
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField(
        value = "del_flag",
        fill = FieldFill.INSERT
    )
    private String delFlag;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    private Date loginDate;

    /** 部门对象 */
    @TableField(exist = false)
    private SysDept dept;

    /** 角色对象 */
    @TableField(exist = false)
    private List<SysRole> roles;

    /** 角色组 */
    @TableField(exist = false)
    private Long[] roleIds;

    /** 岗位组 */
    @TableField(exist = false)
    private Long[] postIds;

    /** 角色ID */
    @TableField(exist = false)
    private Long roleId;

    /** 证件类型 **/
    private String certificateType;
    /** 证件号码 **/
    private String certificateNo;
    /** 扩展字段，JSON格式 **/
    private String extProperties;

    /** 最后登陆的orgID **/
    private Long lastLoginOrgId;

    /** 最后登陆的角色 **/
    private String lastLoginRole;

    /** 修改密码标识（0-否，1-是） **/
    private String updatePwdFlag;

    /**
     * 20250819后新增字段
     */

    /**
     * 为兼容加密模糊查询，对姓名进行拆分，如果姓名为两位，拆分成两位，如果姓名为3为的，拆分1为和2,3为，4位个拆分一般
     * 例如  李四   拆分为  realNamestart  为李    realNameEnd  为四
     * 李四五  拆分为  realNamestart  为李    realNameEnd  为四五
     * 李四五六  拆分为  realNamestart  为李四    realNameEnd  为五六
     */
    @ApiModelProperty(value = "姓名的开头")
    private String realNameStart;


    /**
     * 名
     */
    @ApiModelProperty(value = "名")
    private String realNameEnd;

    /**
     * 电话开头4位
     */
    @ApiModelProperty(value = "电话开头")
    private String mobileStart;


    /**
     * 电话中间4位
     */
    @ApiModelProperty(value = "电话中间")
    private String mobileMiddle;


    /**
     * 电话结尾4位
     */
    @ApiModelProperty(value = "电话结尾")
    private String mobileEnd;

    public SysUser()
    {

    }

    public SysUser(Long userId)
    {
        this.userId = userId;
    }
}
