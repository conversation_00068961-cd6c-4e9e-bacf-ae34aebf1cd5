package com.platform.system.api.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 字典数据表 sys_dict_data
 * 
 * <AUTHOR>
 */
@Data
public class SysDictDataDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 字典编码 */
    @ApiModelProperty(value = "字典编码")
    private Long dictCode;

    /** 字典排序 */
    @ApiModelProperty(value = "字典排序")
    private Long dictSort;

    /** 字典标签 */
    @ApiModelProperty(value = "字典标签")
    private String dictLabel;

    /** 字典键值 */
    @ApiModelProperty(value = "字典键值")
    private String dictValue;

    /** 字典类型 */
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    /** 样式属性（其他样式扩展） */
    @ApiModelProperty(value = "样式属性（其他样式扩展）")
    private String cssClass;

    /** 表格字典样式 */
    @ApiModelProperty(value = "表格字典样式")
    private String listClass;

    /** 是否默认（Y是 N否） */
    @ApiModelProperty(value = "是否默认（Y是 N否）")
    private String isDefault;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private String status;
}
