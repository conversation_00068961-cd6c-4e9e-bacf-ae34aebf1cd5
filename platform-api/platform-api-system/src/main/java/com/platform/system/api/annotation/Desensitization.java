package com.platform.system.api.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.common.core.enums.DesensitizeTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

//脱敏注解
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@JacksonAnnotationsInside
@JsonSerialize(using = DesensitizeSerialize.class)
public @interface Desensitization {

    DesensitizeTypeEnum value() default DesensitizeTypeEnum.ALL;

    /**
     * 保留不脱敏的位数
     */
    int num() default 0;
}
