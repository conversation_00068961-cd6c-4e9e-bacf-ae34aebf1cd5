package com.platform.system.api.domain.response;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 字典类型响应实体类
 * @author: tr
 * @date: 2024年06月14日 10:12
 */
@Data
@ToString
public class SysDictTypeResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 字典ID */
    @ApiModelProperty(value = "字典ID")
    @Excel(name = "字典主键", cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dictId;

    /** 字典名称 */
    @ApiModelProperty(value = "字典名称")
    @Excel(name = "字典名称")
    private String dictName;

    /** 字典类型 */
    @ApiModelProperty(value = "字典类型")
    @Excel(name = "字典类型")
    private String dictType;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "字典详情数据列表")
    private List<SysDictDataResp> sysDictDataRespList;
}
