package com.platform.system.api.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel("全国地区编码实体类")
@Data
@ToString
public class SysCountryAddressQueryReq {

	private static final long serialVersionUID = 1L;


	/**
	 * 父CODE（如果有层级关系使用）
	 */
	 @ApiModelProperty("父CODE（如果有层级关系使用）")
	private String pcode;

	/**
	 * 如果有层级关系，从根节点开始至本节点的层级
	 */
	 @ApiModelProperty("层级关系，")
	private Integer regionLevel;
}
