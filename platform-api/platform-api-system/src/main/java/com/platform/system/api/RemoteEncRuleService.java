package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysEncRuleReq;
import com.platform.system.api.domain.response.SysEncRuleResp;
import com.platform.system.api.factory.RemoteDeptFallbackFactory;
import com.platform.system.api.factory.RemoteEncRuleFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 部分服务接口
 * @author: tr
 * @date: 2024年01月31日 18:24
 */
@FeignClient(contextId = "remoteEncRuleService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteEncRuleFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteEncRuleService {

    @PostMapping("/encRule/list")
    ResponseResult<List<SysEncRuleResp>> list(@RequestBody SysEncRuleReq sysEncRuleReq);
}
