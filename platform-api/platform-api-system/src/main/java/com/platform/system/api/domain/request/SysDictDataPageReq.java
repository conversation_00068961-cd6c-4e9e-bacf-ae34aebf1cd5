package com.platform.system.api.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 字典数据表 sys_dict_data
 * 
 * <AUTHOR>
 */
@Data
public class SysDictDataPageReq
{
    private static final long serialVersionUID = 1L;

    /** 字典标签 */
    @ApiModelProperty(value = "字典标签")
    private String dictLabel;

    /** 字典类型 */
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private String status;

    @ApiModelProperty("当前页数")
    private int pageNum = 1;
    @ApiModelProperty("每页记录数")
    private int pageSize = 10;
}
