package com.platform.system.api.domain.response;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 字典数据响应实体类
 * @author: tr
 * @date: 2024年06月14日 10:20
 */
@Data
@ToString
public class SysDictDataResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 字典编码 */
    @ApiModelProperty(value = "字典编码")
    @Excel(name = "字典编码", cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dictCode;

    /** 字典排序 */
    @ApiModelProperty(value = "字典排序")
    @Excel(name = "字典排序", cellType = Excel.ColumnType.NUMERIC)
    private Long dictSort;

    /** 字典标签 */
    @ApiModelProperty(value = "字典标签")
    @Excel(name = "字典标签")
    private String dictLabel;

    /** 字典键值 */
    @ApiModelProperty(value = "字典键值")
    @Excel(name = "字典键值")
    private String dictValue;

    /** 字典类型 */
    @ApiModelProperty(value = "字典类型")
    @Excel(name = "字典类型")
    private String dictType;

    /** 样式属性（其他样式扩展） */
    @ApiModelProperty(value = "样式属性（其他样式扩展）")
    private String cssClass;

    /** 表格字典样式 */
    @ApiModelProperty(value = "表格字典样式")
    private String listClass;

    /** 是否默认（Y是 N否） */
    @ApiModelProperty(value = "是否默认（Y是 N否）")
    @Excel(name = "是否默认", readConverterExp = "Y=是,N=否")
    private String isDefault;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
