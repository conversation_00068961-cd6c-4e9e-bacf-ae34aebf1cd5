package com.platform.system.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
public class SysDeptDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /** 父部门ID */
    @ApiModelProperty(value = "父部门ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 祖级列表 */
    @ApiModelProperty(value = "祖级列表")
    private String ancestors;

    /** 部门名称 */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /** 显示顺序 */
    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;

    /** 负责人 */
    @ApiModelProperty(value = "负责人")
    private String leader;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /** 邮箱 */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /** 部门状态:0正常,1停用 */
    @ApiModelProperty(value = "部门状态:0正常,1停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    /** 父部门名称 */
    @ApiModelProperty(value = "父部门名称")
    private String parentName;
    
    /** 子部门 */
    @ApiModelProperty(value = "子部门")
    private List<SysDeptDTO> children = new ArrayList<SysDeptDTO>();

}
