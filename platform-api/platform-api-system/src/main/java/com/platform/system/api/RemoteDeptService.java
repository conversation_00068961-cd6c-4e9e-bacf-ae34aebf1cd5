package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.api.factory.RemoteDeptFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 部分服务接口
 * @author: tr
 * @date: 2024年01月31日 18:24
 */
@FeignClient(contextId = "remoteDeptService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteDeptFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteDeptService {

    @PostMapping("/dept/list")
    ResponseResult<List<SysDeptResp>> list(@RequestBody SysDeptQueryReq sysDeptQueryReq, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("/dept/getById")
    ResponseResult<SysDeptResp> getDeptById(@RequestParam("deptId") Long deptId, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @PostMapping("/dept/list")
    ResponseResult<List<SysDeptResp>> list(@RequestBody SysDeptQueryReq sysDeptQueryReq);
}
