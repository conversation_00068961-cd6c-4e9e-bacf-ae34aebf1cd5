package com.platform.system.api.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;
import lombok.Data;

/**
 * 字典类型表 sys_dict_type
 * 
 * <AUTHOR>
 */
@Data
public class SysDictType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 字典ID */
    @TableId(value = "dict_id", type = IdType.ASSIGN_ID)
    private Long dictId;

    /** 字典名称 */
    private String dictName;

    /** 字典类型 */
    private String dictType;

    /** 状态（0正常 1停用） */
    private String status;
}
