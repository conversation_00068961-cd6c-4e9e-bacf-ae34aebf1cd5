package com.platform.system.api.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;


/**
 * @Description: 组织机构新增或修改入参，不继承PageReq，由于定义feign接口，get请求，使用@SpringQueryMap参数转换报错（有Map类型报错）
 * @author: tr
 * @Date: 2024/3/15 10:06
 */
@ApiModel("组织机构分页查询入参")
@Data
@ToString
public class SysOrgPageReq {

	private static final long serialVersionUID = 1L;

	/**
	 * 机构（主体）名称
	 */
	@ApiModelProperty("机构（主体）名称")
	private String name;

	/**
	 * 组织机构类型
	 */
	@ApiModelProperty("组织机构类型")
	private Integer orgType;

	/**
	 * 所在地址code
	 */
	@ApiModelProperty("所在地址code")
	private String dzCode;

	/**
	 * 角色组Id
	 */
	@ApiModelProperty("角色组Id")
	private Long roleGroupId;

	/**
	 * 管辖范围编码
	 */
	@ApiModelProperty("管辖范围编码")
	private String guCode;

	/**
	 * 管辖地址父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY
	 */
	@ApiModelProperty("管辖地址父CODE，Pcode，所在地址的父类，查询该父类及以下的所有机构信息")
	private String guPcode;

	@ApiModelProperty("当前页数")
	private int pageNum = 1;
	@ApiModelProperty("每页记录数")
	private int pageSize = 10;

	@ApiModelProperty("开始时间")
	private String beginTime;
	@ApiModelProperty("结束时间")
	private String endTime;

	/**
	 * 所在地址codeP
	 */
	@ApiModelProperty("所在地址Pcode，所在地址的父类，查询该父类及以下的所有机构信息")
	private String dzPcode;

	/**
	 * 地址码信息集合
	 */
	@ApiModelProperty("地址码信息集合")
	private List<String> dzCodeAllList;

	/**
	 * 扩展字段字符类型1
	 */
	@ApiModelProperty(value = "扩展字段字符类型1，支持精确查询")
	private String extendStr1;

	/**
	 * 扩展字段字符类型2
	 */
	@ApiModelProperty(value = "扩展字段字符类型2，支持精确查询")
	private String extendStr2;

	/**
	 * 扩展字段字符类型3
	 */
	@ApiModelProperty(value = "扩展字段字符类型3，支持精确查询")
	private String extendStr3;

	/**
	 * 扩展字段字符类型4
	 */
	@ApiModelProperty(value = "扩展字段字符类型4，支持模糊查询")
	private String extendStr4;

	/**
	 * 扩展字段字符类型5
	 */
	@ApiModelProperty(value = "扩展字段字符类型5，支持模糊查询")
	private String extendStr5;

	/**
	 * 扩展字段字符类型6
	 */
	@ApiModelProperty(value = "扩展字段字符类型6，支持模糊查询")
	private String extendStr6;

	/**
	 * 扩展字段字符类型7
	 */
	@ApiModelProperty(value = "扩展字段字符类型7，接收多个值，支持多个查询")
	private String[] extendStr7s;

	/**
	 * 扩展字段字符类型8
	 */
	@ApiModelProperty(value = "扩展字段字符类型8")
	private String extendStr8;

	/**
	 * 扩展字段字符类型9
	 */
	@ApiModelProperty(value = "扩展字段字符类型9")
	private String extendStr9;

	/**
	 * 扩展字段字符类型10
	 */
	@ApiModelProperty(value = "扩展字段字符类型10")
	private String extendStr10;

	/**
	 * 扩展字段整型类型1
	 */
	@ApiModelProperty(value = "扩展字段整型类型1")
	private Long extendInt1;

	/**
	 * 扩展字段整型类型2
	 */
	@ApiModelProperty(value = "扩展字段整型类型2")
	private Long extendInt2;

	/**
	 * 扩展字段整型类型3
	 */
	@ApiModelProperty(value = "扩展字段整型类型3")
	private Long extendInt3;

	/**
	 * 扩展字段小数类型1
	 */
	@ApiModelProperty(value = "扩展字段小数类型1")
	private Double extendDouble1;

	/**
	 * 扩展字段小数类型2
	 */
	@ApiModelProperty(value = "扩展字段小数类型2")
	private Double extendDouble2;

	/**
	 * 扩展字段小数类型3
	 */
	@ApiModelProperty(value = "扩展字段小数类型3")
	private Double extendDouble3;

	/**
	 * 扩展字段时间类型1
	 */
	@ApiModelProperty(value = "扩展字段时间类型1")
	private String extendDatetime1;

	/**
	 * 扩展字段时间类型1开始时间
	 */
	@ApiModelProperty(value = "扩展字段时间类型1开始时间")
	private String extendDatetime1Begin;

	/**
	 * 扩展字段时间类型1结束时间
	 */
	@ApiModelProperty(value = "扩展字段时间类型1结束时间")
	private String extendDatetime1End;
}
