package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysSyncdataLogReq;
import com.platform.system.api.domain.response.SysSyncdataLogResp;
import com.platform.system.api.factory.RemoteSyncdataLogServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description: 远程调用系统服务接口
 * @author: tr
 * @date: 2025年03月26日 20:16
 */
@FeignClient(contextId = "remoteSyndataLogService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteSyncdataLogServiceFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteSyncdataLogService {

    @GetMapping("/syncdata/log/listStayByBatchNumber")
    ResponseResult<List<SysSyncdataLogResp>> listStayByBatchNumber(@RequestParam("batchNumber") String batchNumber);

    @PostMapping("/syncdata/log/updateSyncStatusByBatchNumber")
    ResponseResult updateSyncStatusByBatchNumber(@RequestBody SysSyncdataLogReq sysSyncdataLogReq);
}
