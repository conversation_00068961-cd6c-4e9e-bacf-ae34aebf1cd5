package com.platform.system.api.domain.response.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class DeptV3Resp {

    /** 部门ID */

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /** 父部门ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 祖级列表 */
    private String ancestors;

    /** 部门名称 */
    private String deptName;

    /** 显示顺序 */
    private Integer orderNum;

    /** 负责人 */
    private String leader;

    /** 联系电话 */
    private String phone;

    /** 邮箱 */
    private String email;

    /** 部门状态:0正常,1停用 */
    private String status;

    /** 删除标志（0代表存在 2代表删除） **/
    private String delFlag;
}
