package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 组织机构出入参
 * @author: tr
 * @Date: 2024/3/15 10:06
 */
@ApiModel("组织机构查询出参")
@Data
@ToString
public class SysOrgResp implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/**
	 * 机构（主体）名称
	 */
	@ApiModelProperty("机构（主体）名称")
	private String name;

	/**
	 * 机构编码
	 */
	@ApiModelProperty("机构编码")
	private String orgCode;

	/**
	 * 组织机构类型
	 */
	@ApiModelProperty("组织机构类型")
	private Integer orgType;

	/**
	 * 父级机构Id
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long parentId;

	/**
	 * 联系人
	 */
	@ApiModelProperty("联系人")
	private String contactsPerson;

	/**
	 * 联系电话
	 */
	@ApiModelProperty("联系电话")
	private String contactsPhone;

	/**
	 * 单位联系方式(座机)
	 */
	@ApiModelProperty("单位联系方式(座机)")
	private String contactsWay;

	/** 备注 **/
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 所在地址名称
	 */
	@ApiModelProperty("所在地址名称")
	private String dzName;

	/**
	 * 所在地址code
	 */
	@ApiModelProperty("所在地址code")
	private String dzCode;

	/**
	 * 所在地址区划级别
	 */
	@ApiModelProperty("所在地址区划级别")
	private Integer dzRegionLevel;

	/**
	 * 详细地址
	 */
	@ApiModelProperty("详细地址")
	private String dzDetailAddress;

	/**
	 * 角色组Id
	 */
	@ApiModelProperty("角色组Id")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long roleGroupId;

	/**
	 * 管辖范围编码
	 */
	@ApiModelProperty("管辖范围编码")
	private String guCode;

	/**
	 * 管辖范围全称
	 */
	@ApiModelProperty("管辖范围全称")
	private String guName;

	/**
	 * 管辖范围区划级别
	 */
	@ApiModelProperty("管辖范围区划级别")
	private Integer guRegionLevel;

	/** 创建人 **/
	@ApiModelProperty("创建人")
	private String createBy;

	/** 创建时间 **/
	@ApiModelProperty("创建时间")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss",
			timezone = "GMT+8"
	)
	private Date createTime;


	/**
	 * 所在地址名称
	 */
	@ApiModelProperty("所在地址全称")
	private String dzFullName;

	/**
	 * 组织机构扩展信息
	 */
	@ApiModelProperty("组织机构扩展信息")
	private SysOrgExtendResp sysOrgExtendResp;

	/** 机构编码 **/
	@ApiModelProperty("第三方机构编码")
	private String orgCodeThird;

	@ApiModelProperty("第三方上级机构编码")
	private String parentCodeThird;

	@ApiModelProperty("第三方机构层级路径")
	private String codePathThird;

	@ApiModelProperty("机构层级")
	private String orgLevel;

	@ApiModelProperty("部门信息")
	private SysDeptResp sysDeptResp;
}
