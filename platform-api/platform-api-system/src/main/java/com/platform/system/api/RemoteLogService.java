package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysOperLogReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import com.platform.system.api.domain.SysLogininfor;
import com.platform.system.api.factory.RemoteLogFallbackFactory;

/**
 * 日志服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteLogService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteLogFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteLogService
{
    /**
     * 保存系统日志
     *
     * @param sysOperLog 日志实体
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/operlog/save")
    public ResponseResult<Boolean> saveLog(@RequestBody SysOperLogReq sysOperLogReq) throws Exception;

    /**
     * 保存访问记录
     *
     * @param sysLogininfor 访问实体
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/logininfor/save")
    public ResponseResult<Boolean> saveLogininfor(@RequestBody SysLogininfor sysLogininfor, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
