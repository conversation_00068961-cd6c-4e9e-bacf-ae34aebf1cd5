package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.response.SysDictDataResp;
import com.platform.system.api.factory.RemoteDictDataFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 字典服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDictDataService",value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteDictDataFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteDictDataService
{

    /**
     * 根据字典类型查询字典数据
     * @param dictType 字典数据
     * @return 字典数据
     */
    @GetMapping(value = "/dict/data/selectDictDataByDictTypeAndDictValue")
    ResponseResult<SysDictDataResp> selectDictDataByDictTypeAndDictValue(@RequestParam("dictType") String dictType
            , @RequestParam("dictValue")String dictValue, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @PostMapping(value = "/dict/data/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseResult<String> importData(@RequestPart("file") MultipartFile file
            , @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);
}
