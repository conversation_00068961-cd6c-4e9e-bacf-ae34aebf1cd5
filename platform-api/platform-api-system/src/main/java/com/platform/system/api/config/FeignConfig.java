package com.platform.system.api.config;

import com.platform.common.core.constant.SecurityConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * @Description:
 * @author: tr
 * @date: 2024年04月26日 17:32
 */
public class FeignConfig implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header(SecurityConstants.FROM_SOURCE, SecurityConstants.INNER);
    }
}
