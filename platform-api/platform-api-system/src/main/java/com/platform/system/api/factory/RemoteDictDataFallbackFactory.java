package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteDictDataService;
import com.platform.system.api.domain.response.SysDictDataResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * 字典服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteDictDataFallbackFactory implements FallbackFactory<RemoteDictDataService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteDictDataFallbackFactory.class);

    @Override
    public RemoteDictDataService create(Throwable throwable)
    {
        log.error("字典服务调用失败:{}", throwable.getMessage());
        return new RemoteDictDataService()
        {
            @Override
            public ResponseResult<SysDictDataResp> selectDictDataByDictTypeAndDictValue(@RequestParam("dictType") String dictType,
                                                                                        @RequestParam("dictValue") String dictValue, String token) {
                return ResponseResult.fail("获取字典失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<String> importData(MultipartFile file, String token) {
                return ResponseResult.fail("导入字典失败:" + throwable.getMessage());
            }
        };
    }
}
