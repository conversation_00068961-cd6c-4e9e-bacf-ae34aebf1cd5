package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysDictTypePageReq;
import com.platform.system.api.domain.response.SysDictTypeResp;
import com.platform.system.api.factory.RemoteDictTypeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 字典类型远程调用服务接口
 * @author: tr
 * @date: 2024年06月14日 10:11
 */
@FeignClient(contextId = "remoteDictTypeService",value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteDictTypeFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteDictTypeService {

    @GetMapping("/dict/type/listAll")
    ResponseResult<List<SysDictTypeResp>> list(@SpringQueryMap SysDictTypePageReq sysDictTypePageReq
            , @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @PostMapping(value = "/dict/type/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseResult<String> importData(@RequestPart("file") MultipartFile file
            , @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);
}
