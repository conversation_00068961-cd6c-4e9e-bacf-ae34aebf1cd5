package com.platform.system.api.domain.response;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.ctdi.common.starter.toolbox.annotation.Excel.ColumnType;
import com.ctdi.common.starter.toolbox.annotation.Excel.Type;
import com.ctdi.common.starter.toolbox.annotation.Excels;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.platform.common.core.enums.DesensitizeTypeEnum;
import com.platform.system.api.annotation.Desensitization;
import com.platform.system.api.domain.SysDept;
import com.platform.system.api.domain.SysRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysUserResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    @Excel(name = "用户序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    @Excel(name = "部门编号", type = Type.IMPORT)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /** 用户账号 */
    @Desensitization(value = DesensitizeTypeEnum.LEFT, num = 1)
    @ApiModelProperty(value = "用户账号")
    @Excel(name = "登录名称")
    private String userName;

    /** 用户昵称 */
    @ApiModelProperty(value = "用户昵称")
    @Excel(name = "用户名称")
    private String nickName;

    /** 用户类型 **/
    @ApiModelProperty(value = "用户类型")
    private String userType;

    /** 用户邮箱 */
    @ApiModelProperty(value = "用户邮箱")
    @Excel(name = "用户邮箱")
    private String email;

    /** 手机号码 */
    @ApiModelProperty(value = "手机号码")
    @Excel(name = "手机号码")
    private String phonenumber;

    /** 用户性别 */
    @ApiModelProperty(value = "用户性别")
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 用户头像 */
    @ApiModelProperty(value = "用户头像")
    private String avatar;

    /** 帐号状态（0正常 1停用） */
    @ApiModelProperty(value = "帐号状态（0正常 1停用）")
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    /** 最后登录IP */
    @ApiModelProperty(value = "最后登录IP")
    @Excel(name = "最后登录IP", type = Type.EXPORT)
    private String loginIp;

    /** 最后登录时间 */
    @ApiModelProperty(value = "最后登录时间")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date loginDate;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTime;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 证件类型 */
    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    /** 证件号码 */
    @ApiModelProperty(value = "证件号码")
    private String certificateNo;

    /** 部门对象 */
    @ApiModelProperty(value = "部门对象")
    @Excels({
        @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
        @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
    })
    private SysDept dept;

    /** 角色对象 */
    @ApiModelProperty(value = "角色对象")
    private List<SysRole> roles;

    /** 角色组 */
    @ApiModelProperty(value = "角色组")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long[] roleIds;

    /** 岗位组 */
    @ApiModelProperty(value = "岗位组")
    private Long[] postIds;

    /** 角色ID */
    @ApiModelProperty(value = "角色ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long roleId;

    /** 组织机构ID数组 */
    @ApiModelProperty(value = "组织机构ID数组")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long[] orgIds;

    /** 用户与角色集合，关联组织机构 */
    @ApiModelProperty(value = "用户与角色集合，关联组织机构")
    private List<SysUserRoleResp> userRoleList;

    /** 用户信息扩展数据返回对象 */
    @ApiModelProperty(value = "用户信息扩展数据返回对象")
    private SysUserExtendResp sysUserExtendResp;

    /** 机构管辖范围地址码级别 */
    @ApiModelProperty(value = "机构管辖范围地址码级别")
    private String guRegionLevel;

    /** 组织机构对象 **/
    @ApiModelProperty(value = "组织机构对象")
    private SysOrgResp sysOrgResp;

    /** 最后登陆的orgID **/
    @ApiModelProperty(value = "最后登陆的orgID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lastLoginOrgId;

    /** 最后登陆的机构名称 **/
    @ApiModelProperty(value = "最后登陆的机构名称")
    private String lastLoginOrgName;

    /** 最后登陆的角色 **/
    @ApiModelProperty(value = "最后登陆的角色")
    private String lastLoginRole;

    /** 是否切换机构和角色开关 true 是 false 否 **/
    @ApiModelProperty(value = "是否切换机构和角色开关 true 是 false 否")
    private Boolean switchOrgRole;
}
