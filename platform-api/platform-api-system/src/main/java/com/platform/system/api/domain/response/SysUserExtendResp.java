package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
public class SysUserExtendResp implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ApiModelProperty(value = "主键ID，同用户ID一致")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/**
	 * 扩展字段字符类型1
	 */
	@ApiModelProperty(value = "扩展字段字符类型1")
	private String extendStr1;

	/**
	 * 扩展字段字符类型2
	 */
	@ApiModelProperty(value = "扩展字段字符类型2")
	private String extendStr2;

	/**
	 * 扩展字段字符类型3
	 */
	@ApiModelProperty(value = "扩展字段字符类型3")
	private String extendStr3;

	/**
	 * 扩展字段字符类型4
	 */
	@ApiModelProperty(value = "扩展字段字符类型4")
	private String extendStr4;

	/**
	 * 扩展字段字符类型5
	 */
	@ApiModelProperty(value = "扩展字段字符类型5")
	private String extendStr5;

	/**
	 * 扩展字段字符类型6
	 */
	@ApiModelProperty(value = "扩展字段字符类型6")
	private String extendStr6;

	/**
	 * 扩展字段字符类型7
	 */
	@ApiModelProperty(value = "扩展字段字符类型7")
	private String extendStr7;

	/**
	 * 扩展字段字符类型8
	 */
	@ApiModelProperty(value = "扩展字段字符类型8")
	private String extendStr8;

	/**
	 * 扩展字段字符类型8的数组类型
	 */
	@ApiModelProperty(value = "扩展字段字符类型8的数组类型")
	private String[] extendStr8s;

	/**
	 * 扩展字段字符类型9
	 */
	@ApiModelProperty(value = "扩展字段字符类型9")
	private String extendStr9;

	/**
	 * 扩展字段字符类型10
	 */
	@ApiModelProperty(value = "扩展字段字符类型10")
	private String extendStr10;

	/**
	 * 扩展字段整型类型1
	 */
	@ApiModelProperty(value = "扩展字段整型类型1")
	private Long extendInt1;

	/**
	 * 扩展字段整型类型2
	 */
	@ApiModelProperty(value = "扩展字段整型类型2")
	private Long extendInt2;

	/**
	 * 扩展字段整型类型3
	 */
	@ApiModelProperty(value = "扩展字段整型类型3")
	private Long extendInt3;

	/**
	 * 扩展字段小数类型1
	 */
	@ApiModelProperty(value = "扩展字段小数类型1")
	private Double extendDouble1;

	/**
	 * 扩展字段小数类型2
	 */
	@ApiModelProperty(value = "扩展字段小数类型2")
	private Double extendDouble2;

	/**
	 * 扩展字段小数类型3
	 */
	@ApiModelProperty(value = "扩展字段小数类型3")
	private Double extendDouble3;

	/**
	 * 扩展字段时间类型1
	 */
	@ApiModelProperty(value = "扩展字段时间类型1")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss",
			timezone = "GMT+8"
	)
	private Date extendDatetime1;

	/**
	 * 扩展字段时间类型2
	 */
	@ApiModelProperty(value = "扩展字段时间类型2")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss",
			timezone = "GMT+8"
	)
	private Date extendDatetime2;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
