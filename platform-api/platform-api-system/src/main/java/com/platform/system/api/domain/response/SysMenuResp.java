package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 菜单权限表 sys_menu
 * 
 * <AUTHOR>
 */
@Data
public class SysMenuResp implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 菜单ID */
    @ApiModelProperty(value = "菜单ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long menuId;

    /** 应用ID */
    @ApiModelProperty(value = "菜单ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long appId;

    /** 菜单名称 */
    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    /** 父菜单名称 */
    @ApiModelProperty(value = "父菜单名称")
    private String parentName;

    /** 父菜单ID */
    @ApiModelProperty(value = "父菜单ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 显示顺序 */
    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;

    /** 路由地址 */
    @ApiModelProperty(value = "路由地址")
    private String path;

    /** 组件路径 */
    @ApiModelProperty(value = "组件路径")
    private String component;

    /** 路由参数 */
    @ApiModelProperty(value = "路由参数")
    private String query;

    /** 是否为外链（0是 1否） */
    @ApiModelProperty(value = "是否为外链（0是 1否）")
    private String isFrame;

    /** 是否缓存（0缓存 1不缓存） */
    @ApiModelProperty(value = "是否缓存（0缓存 1不缓存）")
    private String isCache;

    /** 类型（M目录 C菜单 F按钮） */
    @ApiModelProperty(value = "类型（M目录 C菜单 F按钮）")
    private String menuType;

    /** 显示状态（0显示 1隐藏） */
    @ApiModelProperty(value = "显示状态（0显示 1隐藏）")
    private String visible;
    
    /** 菜单状态（0正常 1停用） */
    @ApiModelProperty(value = "菜单状态（0正常 1停用）")
    private String status;

    /** 权限字符串 */
    @ApiModelProperty(value = "权限字符串")
    private String perms;

    /** 菜单图标 */
    @ApiModelProperty(value = "菜单图标")
    private String icon;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTime;

    /** 子菜单 */
    @ApiModelProperty(value = "子菜单")
    private List<SysMenuResp> children = new ArrayList<SysMenuResp>();
}
