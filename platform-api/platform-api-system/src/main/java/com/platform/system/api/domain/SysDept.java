package com.platform.system.api.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;
import com.ctdi.common.starter.mybatisplus.entity.BaseEntity;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysDept extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 部门ID */

    @TableId(value = "dept_id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /** 父部门ID */
    private Long parentId;

    /** 祖级列表 */
    private String ancestors;

    /** 部门名称 */
    private String deptName;

    /** 显示顺序 */
    private Integer orderNum;

    /** 负责人 */
    private String leader;

    /** 联系电话 */
    private String phone;

    /** 邮箱 */
    private String email;

    /** 部门状态:0正常,1停用 */
    private String status;

    /** 机构Id 部门所属机构 */
    private Long orgId;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField(
        value = "del_flag",
        fill = FieldFill.INSERT
    )
    private String delFlag;


    @TableField(exist = false)
    private String remark;

    /** 父部门名称 */
    @TableField(exist = false)
    private String parentName;

}
