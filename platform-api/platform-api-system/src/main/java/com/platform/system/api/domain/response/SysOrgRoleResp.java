package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 组织机构出入参
 * @author: tr
 * @Date: 2024/3/15 10:06
 */
@ApiModel("组织机构以及下属角色查询出参")
@Data
@ToString
public class SysOrgRoleResp extends SysOrgResp {

	@ApiModelProperty("角色信息")
	private List<SysRoleResp> roles;
}
