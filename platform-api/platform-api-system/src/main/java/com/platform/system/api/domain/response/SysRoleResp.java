package com.platform.system.api.domain.response;

import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.ctdi.common.starter.toolbox.annotation.Excel.ColumnType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 角色表 sys_role
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysRoleResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    @ApiModelProperty(value = "角色ID")
    @Excel(name = "角色序号", cellType = ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long roleId;

    /** 角色名称 */
    @ApiModelProperty(value = "角色名称")
    @Excel(name = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 0, max = 30, message = "角色名称长度不能超过30个字符")
    private String roleName;

    /** 角色权限 */
    @ApiModelProperty(value = "角色权限")
    @Excel(name = "角色权限")
    @NotBlank(message = "权限字符不能为空")
    @Size(min = 0, max = 100, message = "权限字符长度不能超过100个字符")
    private String roleKey;

    /** 角色排序 */
    @ApiModelProperty(value = "角色排序")
    @Excel(name = "角色排序")
    @NotNull(message = "显示顺序不能为空")
    private Integer roleSort;

    /** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
    @ApiModelProperty(value = "数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）")
    @Excel(name = "数据范围", readConverterExp = "1=所有数据权限,2=自定义数据权限,3=本部门数据权限,4=本部门及以下数据权限,5=仅本人数据权限")
    private String dataScope;

    /** 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    @ApiModelProperty(value = "菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）")
    private boolean menuCheckStrictly;

    /** 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ） */
    @ApiModelProperty(value = "部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ）")
    private boolean deptCheckStrictly;

    /** 角色状态（0正常 1停用） */
    @ApiModelProperty(value = "角色状态（0正常 1停用）")
    @Excel(name = "角色状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTime;

    /** 用户是否存在此角色标识 默认不存在 */
    @ApiModelProperty(value = "用户是否存在此角色标识 默认不存在")
    private boolean flag = false;

    /** 部门组（数据权限） */
    @ApiModelProperty(value = "部门组（数据权限）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long[] deptIds;

    /** 角色菜单权限 */
    @ApiModelProperty(value = "角色菜单权限")
    private Set<String> permissions;

    /** 角色与菜单关系 */
    @ApiModelProperty(value = "角色与菜单关系")
    private List<SysRoleMenuResp> roleMenuList;

    /** 应用组 **/
    @ApiModelProperty(value = "应用组")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long[] appIds;

    @ApiModelProperty(value = "备注")
    private String remark;

    public boolean isAdmin()
    {
        return isAdmin(this.roleId);
    }

    public static boolean isAdmin(Long roleId)
    {
        return roleId != null && 1L == roleId;
    }
}
