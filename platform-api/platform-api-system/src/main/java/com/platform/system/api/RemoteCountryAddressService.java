package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysCountryAddressQueryReq;
import com.platform.system.api.domain.response.SysCountryAddressResp;
import com.platform.system.api.factory.RemoteCountryAddressFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description: 地址码服务接口
 * @author: tr
 * @date: 2024年04月24日 14:57
 */
@FeignClient(contextId = "remoteCountryAddressService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteCountryAddressFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteCountryAddressService {

    @GetMapping("/country/list")
    ResponseResult<List<SysCountryAddressResp>> list(
            @SpringQueryMap SysCountryAddressQueryReq sysCountryAddressQueryReq
            , @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("/country/listByPCodes")
    ResponseResult<List<SysCountryAddressResp>> listByPCodes(@RequestParam("codeList") String[] codeList);
}
