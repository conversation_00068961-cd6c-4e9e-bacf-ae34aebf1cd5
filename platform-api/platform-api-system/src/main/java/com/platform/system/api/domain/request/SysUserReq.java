package com.platform.system.api.domain.request;

import com.ctdi.base.core.xss.Xss;
import com.ctdi.common.starter.toolbox.annotation.Excel;
import com.ctdi.common.starter.toolbox.annotation.Excel.Type;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 用户新增入参，继承用户重置密码入参，密码的规则校验
 * 
 * <AUTHOR>
 */
@Data
public class SysUserReq extends SysUserRestPwdReq
{
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    @Excel(name = "部门编号", type = Type.IMPORT)
    private Long deptId;

    /** 用户账号 */
    @ApiModelProperty(value = "用户名称")
    @Excel(name = "登录名称")
    @Xss(message = "用户名称不能包含脚本字符")
    @NotBlank(message = "用户名称不能为空")
    @Size(min = 0, max = 30, message = "用户名称长度不能超过30个字符")
    private String userName;

    /** 用户昵称 */
    @ApiModelProperty(value = "用户昵称")
    @Excel(name = "用户名称")
    @Xss(message = "用户昵称不能包含脚本字符")
    @NotBlank(message = "用户昵称不能为空")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickName;

    /** 用户类型 */
    @ApiModelProperty(value = "用户类型")
    private String userType;

    /** 用户邮箱 */
    @ApiModelProperty(value = "用户邮箱")
    @Excel(name = "用户邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /** 手机号码 */
    @ApiModelProperty(value = "手机号码")
    @Excel(name = "手机号码")
    @Pattern(regexp = "^$|^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    private String phonenumber;

    /** 用户性别 */
    @ApiModelProperty(value = "用户性别")
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 帐号状态（0正常 1停用） */
    @ApiModelProperty(value = "帐号状态（0正常 1停用）")
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 证件类型 **/
    @ApiModelProperty(value = "证件类型")
    private String certificateType;
    /** 证件号码 **/
    @ApiModelProperty(value = "证件号码")
    private String certificateNo;

    /** 角色组 */
    @ApiModelProperty(value = "角色组")
    private Long[] roleIds;

    /** 岗位组 */
    @ApiModelProperty(value = "岗位组")
    private Long[] postIds;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 组织机构ID集合 */
    @ApiModelProperty(value = "组织机构ID集合")
    private Long[] orgIds;

    /** 用户与角色集合，关联组织机构 */
    @ApiModelProperty(value = "用户与角色集合，关联组织机构")
    private List<SysUserRoleReq> userRoleList;

    /** 用户信息扩展属性对象 **/
    @ApiModelProperty(value = "用户信息扩展属性对象")
    private SysUserExtendReq sysUserExtendReq;
}
