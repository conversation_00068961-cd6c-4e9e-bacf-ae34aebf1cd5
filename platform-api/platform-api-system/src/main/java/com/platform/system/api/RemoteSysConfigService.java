package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.factory.RemoteSysConfigFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 系统参数配置
 *
 * <AUTHOR>
 * @date 2025/08/15 15:09
 */
@FeignClient(contextId = "remoteSysConfigService", value = "${platform.system.name}", url = "${platform.system.url}", fallbackFactory = RemoteSysConfigFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteSysConfigService {

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping("/config/configKey")
    ResponseResult<String> getConfigKey(@RequestParam("configKey") String configKey);

}
