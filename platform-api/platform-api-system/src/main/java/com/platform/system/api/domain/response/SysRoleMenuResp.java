package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description:
 * @author: tr
 * @date: 2024年04月01日 15:57
 */
@Data
public class SysRoleMenuResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 菜单组 */
    @ApiModelProperty(value = "菜单组")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long[] menuIds;

    /** 应用ID */
    @ApiModelProperty(value = "菜单ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long appId;
}
