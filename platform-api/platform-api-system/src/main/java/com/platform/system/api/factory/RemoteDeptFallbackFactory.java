package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteDeptService;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.response.SysDeptResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 部门服务降级处理
 * @author: tr
 * @date: 2024年01月31日 18:24
 */
@Slf4j
@Component
public class RemoteDeptFallbackFactory implements FallbackFactory<RemoteDeptService> {

    @Override
    public RemoteDeptService create(Throwable throwable) {
        log.error("部门服务调用失败:{}", throwable.getMessage());
        return new RemoteDeptService()
        {
            @Override
            public ResponseResult<List<SysDeptResp>> list(SysDeptQueryReq sysDeptQueryReq, String token) {
                return ResponseResult.fail("获取部门列表失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<SysDeptResp> getDeptById(Long deptId, String token) {
                return ResponseResult.fail("获取部门信息失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysDeptResp>> list(SysDeptQueryReq sysDeptQueryReq) {
                return ResponseResult.fail("获取部门列表失败:" + throwable.getMessage());
            }
        };
    }
}
