package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.RemoteDeptService;
import com.platform.system.api.RemoteEncRuleService;
import com.platform.system.api.domain.request.SysDeptQueryReq;
import com.platform.system.api.domain.request.SysEncRuleReq;
import com.platform.system.api.domain.response.SysDeptResp;
import com.platform.system.api.domain.response.SysEncRuleResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 加密规则服务降级处理
 * @author: tr
 * @date: 2024年01月31日 18:24
 */
@Slf4j
@Component
public class RemoteEncRuleFallbackFactory implements FallbackFactory<RemoteEncRuleService> {

    @Override
    public RemoteEncRuleService create(Throwable throwable) {
        log.error("加密规则服务调用失败:{}", throwable.getMessage());
        return new RemoteEncRuleService() {
            @Override
            public ResponseResult<List<SysEncRuleResp>> list(SysEncRuleReq sysEncRuleReq) {
                return ResponseResult.ok(Collections.emptyList());
            }
        };
    }
}
