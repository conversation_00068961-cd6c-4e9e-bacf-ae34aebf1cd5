package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.platform.system.api.RemoteOrgService;
import com.platform.system.api.domain.request.SysOrgPageReq;
import com.platform.system.api.domain.request.SysOrgReq;
import com.platform.system.api.domain.response.SysOrgResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 组织机构服务降级处理
 * @author: tr
 * @date: 2024年04月16日 9:01
 */
@Slf4j
@Component
public class RemoteOrgFallbackFactory implements FallbackFactory<RemoteOrgService> {

    @Override
    public RemoteOrgService create(Throwable throwable) {
        log.error("组织机构服务调用失败:{}", throwable.getMessage());
        return new RemoteOrgService()
        {
            @Override
            public ResponseResult<PageResult<SysOrgResp>> page(SysOrgPageReq sysOrgPageReq, String token) {
                return ResponseResult.fail("获取多个组织机构失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<SysOrgResp> getById(Long id, String token) {
                return ResponseResult.fail("获取单个组织机构失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysOrgResp>> listByUserId(Long userId, String token) {
                return ResponseResult.fail("获取用户所属组织机构(分页)失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult update(SysOrgReq sysOrgReq, String token) {
                return ResponseResult.fail("修改组织机构失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysOrgResp>> list(SysOrgPageReq sysOrgPageReq, String token) {
                return ResponseResult.fail("获取用户所属组织机构失败:" + throwable.getMessage());
            }
        };
    }
}
