package com.platform.system.api.domain.request;

import com.ctdi.base.biz.page.PageReq;
import com.ctdi.base.core.xss.Xss;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysUserPageReq extends PageReq {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    private String userName;

    /** 账号昵称 */
    @ApiModelProperty(value = "账号昵称")
    private String nickName;

    /** 手机号码 */
    @ApiModelProperty(value = "手机号码")
    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    private String phonenumber;

    /** 帐号状态（0正常 1停用） */
    @ApiModelProperty(value = "帐号状态（0正常 1停用）")
    private String status;

    /** 组织机构ID数组 */
    @ApiModelProperty(value = "组织机构ID数组")
    private Long[] orgIds;

    /** 角色编码 **/
    @ApiModelProperty(value = "角色编码")
    private String roleCode;

    /** 角色ID **/
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    /**
     * 扩展字段字符类型1
     */
    @ApiModelProperty(value = "扩展字段字符类型1，支持精确查询")
    private String extendStr1;

    /**
     * 扩展字段字符类型2
     */
    @ApiModelProperty(value = "扩展字段字符类型2，支持精确查询")
    private String extendStr2;

    /**
     * 扩展字段字符类型3
     */
    @ApiModelProperty(value = "扩展字段字符类型3，支持精确查询")
    private String extendStr3;

    /**
     * 扩展字段字符类型4
     */
    @ApiModelProperty(value = "扩展字段字符类型4，支持模糊查询")
    private String extendStr4;

    /**
     * 扩展字段字符类型5
     */
    @ApiModelProperty(value = "扩展字段字符类型5，支持模糊查询")
    private String extendStr5;

    /**
     * 扩展字段字符类型6
     */
    @ApiModelProperty(value = "扩展字段字符类型6，支持模糊查询")
    private String extendStr6;

    /**
     * 扩展字段字符类型7
     */
    @ApiModelProperty(value = "扩展字段字符类型7，接收多个值，支持多个查询")
    private String[] extendStr7s;

    /**
     * 扩展字段字符类型8
     */
    @ApiModelProperty(value = "扩展字段字符类型8")
    private String extendStr8;

    /**
     * 扩展字段字符类型9
     */
    @ApiModelProperty(value = "扩展字段字符类型9")
    private String extendStr9;

    /**
     * 扩展字段字符类型10
     */
    @ApiModelProperty(value = "扩展字段字符类型10")
    private String extendStr10;

    /**
     * 扩展字段整型类型1
     */
    @ApiModelProperty(value = "扩展字段整型类型1")
    private Long extendInt1;

    /**
     * 扩展字段整型类型2
     */
    @ApiModelProperty(value = "扩展字段整型类型2")
    private Long extendInt2;

    /**
     * 扩展字段整型类型3
     */
    @ApiModelProperty(value = "扩展字段整型类型3")
    private Long extendInt3;

    /**
     * 扩展字段小数类型1
     */
    @ApiModelProperty(value = "扩展字段小数类型1")
    private Double extendDouble1;

    /**
     * 扩展字段小数类型2
     */
    @ApiModelProperty(value = "扩展字段小数类型2")
    private Double extendDouble2;

    /**
     * 扩展字段小数类型3
     */
    @ApiModelProperty(value = "扩展字段小数类型3")
    private Double extendDouble3;

    /**
     * 扩展字段时间类型1
     */
    @ApiModelProperty(value = "扩展字段时间类型1")
    private String extendDatetime1;

    /**
     * 扩展字段时间类型1开始时间
     */
    @ApiModelProperty(value = "扩展字段时间类型1开始时间")
    private String extendDatetime1Begin;

    /**
     * 扩展字段时间类型1结束时间
     */
    @ApiModelProperty(value = "扩展字段时间类型1结束时间")
    private String extendDatetime1End;

    /**
     * 管辖范围编码，查询本级及本级以下机构的用户
     */
    @ApiModelProperty("管辖范围编码，查询本级及本级以下机构的用户")
    private String guCode;

    /**
     * 用户ID列表
     */
    @ApiModelProperty(value = "用户ID列表")
    private List<Long> userIdList;

    @ApiModelProperty(value = "开始时间")
    private Date beginTimeDate;
    @ApiModelProperty(value = "结束时间")
    private Date endTimeDate;

    /**
     * 用户ID列表排除条件，不包含这些用户ID
     */
    @ApiModelProperty(value = "用户ID列表排除条件，不包含这些用户ID")
    private List<Long> userIdListNo;

    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;
}
