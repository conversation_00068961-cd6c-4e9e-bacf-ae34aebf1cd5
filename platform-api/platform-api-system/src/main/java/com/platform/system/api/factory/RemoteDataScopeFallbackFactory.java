package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteDataScopeService;
import com.platform.system.api.domain.request.SysDataScopeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 数据权限服务降级处理
 * @author: tr
 * @date: 2024年06月25日 9:07
 */
@Slf4j
@Component
public class RemoteDataScopeFallbackFactory implements FallbackFactory<RemoteDataScopeService> {

    @Override
    public RemoteDataScopeService create(Throwable throwable) {
        log.error("数据权限服务调用失败:{}", throwable.getMessage());
        return new RemoteDataScopeService() {
            @Override
            public ResponseResult saveBatch(List<SysDataScopeReq> sysDataScopeReqList) {
                return ResponseResult.fail("新增数据权限失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult deleteByDataId(Long[] dataIds, String dataType) {
                return ResponseResult.fail("删除数据权限失败:" + throwable.getMessage());
            }
        };
    }
}
