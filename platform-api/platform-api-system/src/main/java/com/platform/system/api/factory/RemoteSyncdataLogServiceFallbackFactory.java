package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteSyncdataLogService;
import com.platform.system.api.domain.request.SysSyncdataLogReq;
import com.platform.system.api.domain.response.SysSyncdataLogResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: RemoteSyndataLogService的降级处理
 * @author: tr
 * @date: 2025年03月26日 20:16
 */
@Slf4j
@Component
public class RemoteSyncdataLogServiceFallbackFactory implements FallbackFactory<RemoteSyncdataLogService> {

    @Override
    public RemoteSyncdataLogService create(Throwable throwable) {
        log.error("系统服务调用失败:{}", throwable.getMessage());
        return new RemoteSyncdataLogService() {
            @Override
            public ResponseResult<List<SysSyncdataLogResp>> listStayByBatchNumber(String batchNumber) {
                return ResponseResult.fail("查询待下发的日志报错:" + throwable.getMessage());
            }

            @Override
            public ResponseResult updateSyncStatusByBatchNumber(SysSyncdataLogReq sysSyncdataLogReq) {
                return ResponseResult.fail("更新日志状态失败:" + throwable.getMessage());
            }
        };
    }
}
