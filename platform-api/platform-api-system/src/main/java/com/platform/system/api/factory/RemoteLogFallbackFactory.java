package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.domain.request.SysOperLogReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.platform.system.api.RemoteLogService;
import com.platform.system.api.domain.SysLogininfor;

/**
 * 日志服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteLogFallbackFactory implements FallbackFactory<RemoteLogService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteLogFallbackFactory.class);

    @Override
    public RemoteLogService create(Throwable throwable)
    {
        log.error("日志服务调用失败:{}", throwable.getMessage());
        return new RemoteLogService()
        {
            @Override
            public ResponseResult<Boolean> saveLog(SysOperLogReq sysOperLogReq)
            {
                return ResponseResult.fail("保存操作日志失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<Boolean> saveLogininfor(SysLogininfor sysLogininfor, String source)
            {
                return ResponseResult.fail("保存登录日志失败:" + throwable.getMessage());
            }
        };

    }
}
