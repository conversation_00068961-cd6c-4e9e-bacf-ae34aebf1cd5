package com.platform.system.api.domain.response.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.platform.system.api.domain.response.SysUserExtendResp;
import lombok.Data;

import java.util.List;


/**
 * @Description: OpenApi接口-用户信息返回类
 * @author: tr
 * @date: 2024年03月22日 16:56
 */
@Data
public class UserV3Resp {

    /** 用户ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 用户账号 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户邮箱 */
    private String email;

    /** 手机号码 */
    private String phonenumber;

    /** 用户性别 */
    private String sex;

    /** 帐号状态（0正常 1停用） */
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 证件类型 **/
    private String certificateType;
    /** 证件号码 **/
    private String certificateNo;

    /** 部门对象 */
    private DeptV3Resp dept;

    /** 组织机构ID集合 */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> orgIds;

    /** 用户信息扩展数据返回对象 */
    private SysUserExtendResp sysUserExtendResp;
}
