package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysDeptResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /** 父部门ID */
    @ApiModelProperty(value = "父部门ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 祖级列表 */
    @ApiModelProperty(value = "祖级列表")
    private String ancestors;

    /** 部门名称 */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /** 显示顺序 */
    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;

    /** 负责人 */
    @ApiModelProperty(value = "负责人")
    private String leader;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /** 邮箱 */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /** 部门状态:0正常,1停用 */
    @ApiModelProperty(value = "部门状态:0正常,1停用")
    private String status;

    /** 机构Id 部门所属机构 */
    @ApiModelProperty(value = "部门所属机构")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orgId;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    /** 父部门名称 */
    @ApiModelProperty(value = "父部门名称")
    private String parentName;
    
    /** 子部门 */
    @ApiModelProperty(value = "子部门")
    private List<SysDeptResp> children = new ArrayList<SysDeptResp>();

}
