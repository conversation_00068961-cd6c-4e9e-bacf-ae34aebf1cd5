package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@ApiModel("API权限配置")
@Data
@ToString
public class SysApiResp implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("主键ID")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/** 应用ID */
	@ApiModelProperty("应用ID")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long appId;

	/**
	 * 类型（1-目录，2-API权限）
	 */
	@ApiModelProperty("类型（Y-应用，1-目录，2-API权限）")
	private String type;

	/**
	 * 父ID
	 */
	@ApiModelProperty("父ID")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long parentId;

	/** 父ID路径，以逗号分隔 **/
	@ApiModelProperty("父ID路径，以逗号分隔，第一级传0")
	private String parentIdPath;

	/**
	 * API名称
	 */
	@ApiModelProperty("API名称")
	private String name;

	@ApiModelProperty("请求方式（POST、GET、PUT、DELETE）")
	private String requestMethod;
	
	/**
	 * API权限字符或路径
	 */
	@ApiModelProperty("API权限字符或路径")
	private String perms;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 是否授权
	 */
	@ApiModelProperty("是否授权（0--未授权，1-已授权）")
	private Integer isAuthorization;

	/** 创建时间 */
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss",
			timezone = "GMT+8"
	)
	private Date createTime;

	/** 显示顺序 */
	@ApiModelProperty(value = "显示顺序")
	private Integer orderNum;
}
