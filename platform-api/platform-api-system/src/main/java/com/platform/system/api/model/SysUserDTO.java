package com.platform.system.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysUserDTO implements Serializable{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /** 用户昵称 */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /** 用户类型 */
    @ApiModelProperty(value = "用户类型")
    private String userType;

    /** 用户邮箱 */
    @ApiModelProperty(value = "用户邮箱")
    private String email;

    /** 手机号码 */
    @ApiModelProperty(value = "手机号码")
    private String phonenumber;

    /** 用户性别 */
    @ApiModelProperty(value = "用户性别")
    private String sex;

    /** 用户头像 */
    @ApiModelProperty(value = "用户头像")
    private String avatar;

    /** 帐号状态（0正常 1停用） */
    @ApiModelProperty(value = "帐号状态（0正常 1停用）")
    private String status;

    /** 最后登录IP */
    @ApiModelProperty(value = "最后登录IP")
    private String loginIp;

    /** 最后登录时间 */
    @ApiModelProperty(value = "最后登录时间")
    private Date loginDate;

    /** 部门对象 */
    @ApiModelProperty(value = "部门对象")
    private SysDeptDTO deptDTO;

    /** 角色对象 */
    @ApiModelProperty(value = "角色对象")
    private List<SysRoleDTO> rolesDTO;

    @ApiModelProperty(value = "证件类型")
    private String certificateType;


    @ApiModelProperty(value = "证件号码")
    private String certificateNo;

    /** 修改密码标识（0-否，1-是） **/
    @ApiModelProperty(value = "修改密码标识（0-否，1-是）")
    private String updatePwdFlag;

}
