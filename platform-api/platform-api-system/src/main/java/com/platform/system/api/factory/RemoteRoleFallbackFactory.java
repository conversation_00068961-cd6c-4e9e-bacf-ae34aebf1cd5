package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteRoleService;
import com.platform.system.api.domain.request.SysRoleQueryReq;
import com.platform.system.api.domain.response.SysRoleResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description: 角色服务降级处理
 * @author: tr
 * @date: 2024年01月31日 19:27
 */
@Component
@Slf4j
public class RemoteRoleFallbackFactory implements FallbackFactory<RemoteRoleService> {

    @Override
    public RemoteRoleService create(Throwable throwable) {
        log.error("角色服务调用失败:{}", throwable.getMessage());
        return new RemoteRoleService() {
            @Override
            public ResponseResult<List<SysRoleResp>> listRoles(@RequestBody SysRoleQueryReq sysRoleQueryReq, String token) {
                return ResponseResult.fail("获取角色列表失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<SysRoleResp> getRoleById(Long roleId, String token) {
                return ResponseResult.fail("获取单个角色失败:" + throwable.getMessage());
            }
        };
    }
}
