package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysUserPageReq;
import com.platform.system.api.domain.request.SysUserReq;
import com.platform.system.api.domain.response.SysUserResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.factory.RemoteUserFallbackFactory;
import com.platform.system.api.model.LoginUser;

import java.util.List;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteUserFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    ResponseResult<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    ResponseResult<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * @Description: 获取当前用户信息
     * @author: tr
     * @Date: 2024/2/6 15:55
     * @param: [token]
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<com.platform.system.api.model.LoginUser>
     */
    @GetMapping("/user/getCurrUser")
    ResponseResult<LoginUser> getCurrUserInfo(@RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    /**
     * @Description: 查询用户列表
     * @author: tr
     * @Date: 2024/2/27 15:50
     * @param: [sysUserQueryReq]
     * @returnValue: com.ctdi.base.biz.domain.ResponseResult<java.util.List<com.platform.system.api.domain.response.SysUserResp>>
     */
    @PostMapping("/user/list")
    ResponseResult<List<SysUserResp>> listUserInfo(@RequestBody SysUserPageReq sysUserPageReq, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("/user/getById")
    ResponseResult<SysUserResp> getUserById(@RequestParam("userId") Long userId, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("/user/listByRoleIds")
    ResponseResult<List<SysUserResp>> listByRoleIds(@RequestParam("roldIdList") Long[] roldIdList, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("/user/listByOrgIds")
    ResponseResult<List<SysUserResp>> listByOrgIds(@RequestParam("orgIdList") Long[] orgIdList, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("/user/listByOrgIdsAndRoleIds")
    ResponseResult<List<SysUserResp>> listByOrgIdsAndRoleIds(@RequestParam("orgIdList") Long[] orgIdList, @RequestParam("roldIdList") Long[] roldIdList, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @PostMapping("/user/page")
    ResponseResult<PageResult<SysUserResp>> page(@RequestBody SysUserPageReq sysUserPageReq, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("/user/listByRoleCodeAndGuCode")
    ResponseResult<List<SysUserResp>> listByRoleCodeAndGuCode(@RequestParam("roleCode") String roleCode
            , @RequestParam("guCode") String guCode);

    @PutMapping("/user/updateBatch")
    ResponseResult updateBatch(@RequestBody List<SysUserReq> sysUserReqList);

    @GetMapping("/user/listByIdList")
    ResponseResult<List<SysUserResp>> listByIdList(@RequestParam("userIdList") Long[] userIdList);
}
