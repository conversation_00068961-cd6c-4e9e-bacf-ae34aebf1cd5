package com.platform.system.api.domain.request;

import com.ctdi.base.biz.page.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Description: 角色查询入参对象
 * @author: tr
 * @date: 2024年02月02日 14:56
 */
@Data
@ToString
public class SysRoleQueryReq extends PageReq {

    /** 角色ID */
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    /** 角色名称 */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /** 角色权限 */
    @ApiModelProperty(value = "角色权限")
    private String roleKey;

    /** 角色状态（0正常 1停用） */
    @ApiModelProperty(value = "角色状态（0正常 1停用）")
    private String status;

    /** 创建时间 **/
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
