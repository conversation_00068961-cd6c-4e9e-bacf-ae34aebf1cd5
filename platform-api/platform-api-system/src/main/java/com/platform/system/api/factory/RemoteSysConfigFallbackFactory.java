package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteSysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 调用失败处理
 * <AUTHOR>
 * @date 2025/08/15 15:12
 */
@Slf4j
@Component
public class RemoteSysConfigFallbackFactory implements FallbackFactory<RemoteSysConfigService> {

    @Override
    public RemoteSysConfigService create(Throwable cause) {
        log.error("系统服务调用失败:{}", cause.getMessage());
        return new RemoteSysConfigService() {

            @Override
            public ResponseResult<String> getConfigKey(String configKey) {
                return ResponseResult.fail("获取配置失败:" + cause.getMessage());
            }
        };
    }
}
