package com.platform.system.api.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: 用户角色入参
 * @author: tr
 * @date: 2024年03月29日 11:15
 */
@Data
@ToString
public class SysUserRoleReq {

    /** 角色ID数组 */
    @ApiModelProperty(value = "角色ID数组")
    private Long[] roleIds;

    /** 组织机构ID */
    @ApiModelProperty(value = "组织机构ID")
    private Long orgId;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 角色ID */
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;
}
