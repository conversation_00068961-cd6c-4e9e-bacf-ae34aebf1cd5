package com.platform.system.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.platform.system.api.domain.response.SysOrgExtendResp;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * @Description: 组织机构信息
 * @author: tr
 * @Date: 2024/3/15 10:06
 */
@Data
@ToString
public class SysOrgDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long id;

	/**
	 * 机构（主体）名称
	 */
	private String name;

	/**
	 * 机构编码
	 */
	private String orgCode;

	/**
	 * 组织机构类型
	 */
	private Integer orgType;

	/**
	 * 所在地址名称
	 */
	private String dzName;

	/**
	 * 所在地址code
	 */
	private String dzCode;

	/**
	 * 所在地址区划级别
	 */
	private Integer dzRegionLevel;

	/**
	 * 管辖范围编码
	 */
	private String guCode;

	/**
	 * 管辖范围全称
	 */
	private String guName;

	/**
	 * 管辖范围区划级别
	 */
	private Integer guRegionLevel;

	/**
	 * 联系人
	 */
	private String contactsPerson;

	/**
	 * 联系电话
	 */
	private String contactsPhone;

	/**
	 * 单位联系方式(座机)
	 */
	private String contactsWay;

	/** 备注 **/
	private String remark;

	/**
	 * 详细地址
	 */
	private String dzDetailAddress;

	/**
	 * 角色组Id
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long roleGroupId;

	/**
	 * 所在地址名称
	 */
	private String dzFullName;

	/**
	 * 组织机构扩展信息
	 */
	private SysOrgExtendResp sysOrgExtendResp;
}
