package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysDataScopeReq;
import com.platform.system.api.factory.RemoteDataScopeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 数据权限服务远程接口
 * @author: tr
 * @date: 2024年06月25日 9:06
 */
@FeignClient(contextId = "remoteDataScopeService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteDataScopeFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteDataScopeService {

    @PostMapping("datascope/saveBatch")
    ResponseResult saveBatch(@RequestBody List<SysDataScopeReq> sysDataScopeReqList);

    @DeleteMapping("datascope/deleteByDataId")
    ResponseResult deleteByDataId(@RequestParam("dataIds") Long[] dataIds, @RequestParam("dataType") String dataType);
}
