package com.platform.system.api;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.platform.common.core.constant.SecurityConstants;
import com.platform.system.api.config.FeignConfig;
import com.platform.system.api.domain.request.SysOrgPageReq;
import com.platform.system.api.domain.request.SysOrgReq;
import com.platform.system.api.domain.response.SysOrgResp;
import com.platform.system.api.factory.RemoteOrgFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 组织机构服务接口
 * @author: tr
 * @date: 2024年04月16日 9:01
 */
@FeignClient(contextId = "remoteOrgService", value = "${platform.system.name}"
        , url = "${platform.system.url}"
        , fallbackFactory = RemoteOrgFallbackFactory.class, configuration = FeignConfig.class)
public interface RemoteOrgService {

    @GetMapping("org/page")
    ResponseResult<PageResult<SysOrgResp>> page(@SpringQueryMap SysOrgPageReq sysOrgPageReq, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("org/list")
    ResponseResult<List<SysOrgResp>> list(@SpringQueryMap SysOrgPageReq sysOrgPageReq, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("org/getById")
    ResponseResult<SysOrgResp> getById(@RequestParam("id") Long id, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @GetMapping("org/listByUserId")
    ResponseResult<List<SysOrgResp>> listByUserId(@RequestParam("userId") Long userId, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);

    @PostMapping("org/update")
    ResponseResult update(@RequestBody SysOrgReq sysOrgReq, @RequestHeader(SecurityConstants.AUTHORIZATION_HEADER) String token);
}
