package com.platform.system.api.domain.request;

import com.ctdi.base.biz.domain.BaseReq;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门表 sys_dept
 * 
 * <AUTHOR>
 */
@Data
@ToString
public class SysDeptQueryReq extends BaseReq
{
    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /** 父部门ID */
    @ApiModelProperty(value = "父部门ID")
    private Long parentId;

    /** 部门名称 */
    @NotBlank(message = "部门名称不能为空")
    @Size(min = 0, max = 30, message = "部门名称长度不能超过30个字符")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /** 部门状态:0正常,1停用 */
    @ApiModelProperty(value = "部门状态:0正常,1停用")
    private String status;

    /** 机构Id 部门所属机构 */
    @NotNull(message = "所属机构不能为空")
    @ApiModelProperty(value = "机构Id 部门所属机构")
    private Long orgId;

    @ApiModelProperty(value = "主键ID列表")
    private List<Long> deptIdList;

    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

}
