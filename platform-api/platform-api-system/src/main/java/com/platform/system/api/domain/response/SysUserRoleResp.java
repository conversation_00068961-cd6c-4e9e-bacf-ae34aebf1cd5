package com.platform.system.api.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 用户与角色返回参数
 * @author: tr
 * @date: 2024年04月01日 18:52
 */
@Data
public class SysUserRoleResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 角色ID数组 */
    @ApiModelProperty(value = "角色ID数组")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long[] roleIds;

    /** 组织机构ID */
    @ApiModelProperty(value = "组织机构ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orgId;


    /** 部门ID */
    @ApiModelProperty(value = "部门ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;
}
