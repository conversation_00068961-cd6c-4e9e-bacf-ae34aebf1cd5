package com.platform.system.api.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@ApiModel("全国地区编码实体类")
@Data
@ToString
public class SysCountryAddressResp implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	 @ApiModelProperty("主键ID")
	private String id;

	/**
	 * 地区编码CODE
	 */
	 @ApiModelProperty("地区编码CODE")
	private String code;

	/**
	 * 父CODE（如果有层级关系使用）
	 */
	 @ApiModelProperty("父CODE（如果有层级关系使用）")
	private String pcode;

	/**
	 * 父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY
	 */
	 @ApiModelProperty("父CODE路径（半角分号分隔），规则：CODE1;CODE2;CODE3，例如：CN;BJ;CY")
	private String pcodePath;

	/**
	 * 如果有层级关系，从根节点开始至本节点的层级
	 */
	 @ApiModelProperty("如果有层级关系，从根节点开始至本节点的层级")
	private Integer regionLevel;

	/**
	 * 字典表-地区类型
	 */
	 @ApiModelProperty("字典表-地区类型")
	private String regionType;

	/**
	 * 城市类型
	 */
	 @ApiModelProperty("城市类型")
	private String regionCityType;

	/**
	 * 国家行政区划代码-例如：110000
	 */
	 @ApiModelProperty("国家行政区划代码-例如：110000")
	private String regionOffcialCode;

	/**
	 * 全称聚合，例如：中国;北京市;朝阳区;
	 */
	 @ApiModelProperty("全称聚合，例如：中国;北京市;朝阳区;")
	private String regionFullName;

	/**
	 * 全称，例如：北京市
	 */
	 @ApiModelProperty("全称，例如：北京市")
	private String regionName;

	/**
	 * 简称，例如：北京
	 */
	 @ApiModelProperty("简称，例如：北京")
	private String regionShortname;

	/**
	 * 行政简称，例如：京
	 */
	 @ApiModelProperty("行政简称，例如：京")
	private String regionOffcialshortname;

	/**
	 * 国际化预留
	 */
	 @ApiModelProperty("国际化预留")
	private String regionName1;

	/**
	 * 国际化预留
	 */
	 @ApiModelProperty("国际化预留")
	private String regionName2;

	/**
	 * 区号
	 */
	 @ApiModelProperty("区号")
	private String regionCitycode;

	/**
	 * 邮编
	 */
	 @ApiModelProperty("邮编")
	private String regionZipcode;

	/**
	 * 简称拼音，例如：BEIJING
	 */
	 @ApiModelProperty("简称拼音，例如：BEIJING")
	private String regionPinyin;

	/**
	 * 简称拼音首字母，例如：BJ
	 */
	 @ApiModelProperty("简称拼音首字母，例如：BJ")
	private String regionPinyinFirstchar;

	/**
	 * 经度
	 */
	 @ApiModelProperty("经度")
	private String regionLng;

	/**
	 * 纬度
	 */
	 @ApiModelProperty("纬度")
	private String regionLat;

	/**
	 * 地区排序
	 */
	 @ApiModelProperty("地区排序")
	private Integer regionSort;

	/**
	 * 备注
	 */
	 @ApiModelProperty("备注")
	private String regionRemark;

	/**
	 * 地区_属性1（备用字段）
	 */
	 @ApiModelProperty("地区_属性1（备用字段）")
	private String regionAttr1;

	/**
	 * 地区_属性2（备用字段）
	 */
	 @ApiModelProperty("地区_属性2（备用字段）")
	private String regionAttr2;

	/**
	 * 教育局备案，有效期时间，单位：年
	 */
	 @ApiModelProperty("教育局备案，有效期时间，单位：年")
	private String regionAttr3;

	/**
	 * 字典表-数据状态
	 */
	 @ApiModelProperty("字典表-数据状态")
	private String dataStatus;

	/**
	 * 版本
	 */
	 @ApiModelProperty("版本")
	private Integer version;

	/**
	 * 用户账号表CODE
	 */
	 @ApiModelProperty("用户账号表CODE")
	private String creatorAccountcode;

	/**
	 * 用户名
	 */
	 @ApiModelProperty("用户名")
	private String creatorName;

	/**
	 * 用户账号表CODE
	 */
	 @ApiModelProperty("用户账号表CODE")
	private String updaterAccountcode;

	/**
	 * 用户名
	 */
	 @ApiModelProperty("用户名")
	private String updaterName;

}
