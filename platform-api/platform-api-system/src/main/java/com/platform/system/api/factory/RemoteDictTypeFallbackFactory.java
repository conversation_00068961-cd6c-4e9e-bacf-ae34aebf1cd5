package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.platform.system.api.RemoteDictTypeService;
import com.platform.system.api.domain.request.SysDictTypePageReq;
import com.platform.system.api.domain.response.SysDictTypeResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 字典服务降级处理
 * @author: tr
 * @date: 2024年06月14日 10:42
 */
@Component
public class RemoteDictTypeFallbackFactory implements FallbackFactory<RemoteDictTypeService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteDictTypeFallbackFactory.class);

    @Override
    public RemoteDictTypeService create(Throwable throwable) {
        log.error("字典类型服务调用失败:{}", throwable.getMessage());
        return new RemoteDictTypeService() {
            @Override
            public ResponseResult<List<SysDictTypeResp>> list(SysDictTypePageReq sysDictTypePageReq, String token) {
                return ResponseResult.fail("获取字典类型列表失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<String> importData(MultipartFile file, String token) {
                return ResponseResult.fail("导入字典类型失败:" + throwable.getMessage());
            }
        };
    }
}
