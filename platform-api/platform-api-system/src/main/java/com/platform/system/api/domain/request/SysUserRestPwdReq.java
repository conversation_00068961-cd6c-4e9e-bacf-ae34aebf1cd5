package com.platform.system.api.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

/**
 * @Description: 用户重置密码的入参
 * @author: tr
 * @Date: 2024/3/7 9:40
 */
@Data
public class SysUserRestPwdReq
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 密码 */
    @ApiModelProperty(value = "密码")
//    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>]).{10,20}$", message = "密码为10-20位，数字、字符、大写字母和小写字母四者的组合")
    private String password;

}
