package com.platform.system.api.factory;

import com.ctdi.base.biz.domain.ResponseResult;
import com.ctdi.base.biz.page.PageResult;
import com.platform.system.api.domain.request.SysUserPageReq;
import com.platform.system.api.domain.request.SysUserReq;
import com.platform.system.api.domain.response.SysUserResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.platform.system.api.RemoteUserService;
import com.platform.system.api.domain.SysUser;
import com.platform.system.api.model.LoginUser;

import java.util.List;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public ResponseResult<LoginUser> getUserInfo(String username, String source)
            {
                return ResponseResult.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return ResponseResult.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<LoginUser> getCurrUserInfo(String token) {
                return ResponseResult.fail("获取当前用户失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysUserResp>> listUserInfo(SysUserPageReq sysUserPageReq, String token) {
                return ResponseResult.fail("获取用户列表失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<SysUserResp> getUserById(Long userId, String token) {
                return ResponseResult.fail("获取单个用户失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysUserResp>> listByRoleIds(Long[] roldIdList, String token) {
                return ResponseResult.fail("根据角色ID集合查询用户信息失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysUserResp>> listByOrgIds(Long[] orgIdList, String token) {
                return ResponseResult.fail("根据组织机构ID集合查询用户信息失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysUserResp>> listByOrgIdsAndRoleIds(Long[] orgIdList, Long[] roldIdList, String token) {
                return ResponseResult.fail("根据组织机构ID集合和角色ID集合查询用户信息失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<PageResult<SysUserResp>> page(SysUserPageReq sysUserPageReq, String token) {
                return ResponseResult.fail("查询用户列表信息失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysUserResp>> listByRoleCodeAndGuCode(String roleCode, String guCode) {
                return ResponseResult.fail("根据地址码和角色ID查询用户列表信息失败:" + throwable.getMessage());
            }

            @Override
            public ResponseResult updateBatch(List<SysUserReq> sysUserReqList) {
                return ResponseResult.fail("批量更新用户信息失败:"+throwable.getMessage());
            }

            @Override
            public ResponseResult<List<SysUserResp>> listByIdList(Long[] userIdList) {
                return ResponseResult.fail("根据用户ID集合查询用户信息失败:"+throwable.getMessage());
            }
        };
    }
}
