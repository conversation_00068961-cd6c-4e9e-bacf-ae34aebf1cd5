package com.ctsi.ssdc.admin.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Generator
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "批量删除用户返回结果(需要走删除审批的相关用户)VO")
public class BatchUserDisableResultVO {

    private static final long serialVersionUID = 3709242178201712793L;


    @ApiModelProperty(value = "需要走删除审批的相关用户ID")
    private Long deleteId;

    @ApiModelProperty(value = "原因")
    private String reason;

}
