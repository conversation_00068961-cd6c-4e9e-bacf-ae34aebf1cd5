package com.ctsi.ssdc.admin.domain;


import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> Generator
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "cscp_org_line", description = "机构-系统条线")
public class CscpOrgLine extends BaseEntity {

    @ApiModelProperty(value = "机构ID")
    private Long orgId;

    @ApiModelProperty(value = "条线名称")
    private String lineName;

}