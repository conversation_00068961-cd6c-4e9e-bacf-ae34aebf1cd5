package com.ctsi.ssdc.admin.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullValueFromPlaintextDesHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构管理
 */
@ApiModel(value = "机构实体类")
@Data
@TableName(autoResultMap = true)
public class CscpOrg implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelIgnore
    private Long id;

    @TableField(fill = FieldFill.UPDATE, select = false)
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.UPDATE, select = false)
    @ExcelIgnore
    private Long updateBy;

    @TableField(fill = FieldFill.UPDATE, select = false)
    @ExcelIgnore
    private String updateName;

    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private String createName;

    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    @TableField(fill = FieldFill.INSERT)
    @ExcelIgnore
    private Long tenantId;

    @ApiModelProperty(value = "单位ID")
    @ExcelIgnore
    private Long companyId;

    @TableLogic()
    @TableField(select = false, fill = FieldFill.INSERT)
    @ExcelIgnore
    private Integer deleted;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long parentId;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    private Integer orderBy;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    private String code;

    /**
     * 机构完整编码
     */
    @ApiModelProperty(value = "机构完整编码")
    private String pathCode;

    /**
     * 机构级别
     */
    @ApiModelProperty(value = "机构级别")
    private Integer level;

    /**
     * 机构类型 1表示虚拟，2表示单位 3表示部门
     */
    @ApiModelProperty(value = "机构类型 1表示虚拟，2表示单位 3表示部门")
    private Integer type;


    /**
     * 地区id
     */
    @ApiModelProperty(value = "地区id")
    private String lanId;

    /**
     * 地区名称
     */
    @ApiModelProperty(value = "地区名称")
    private String lan;


    @ApiModelProperty(value = "业务时是否发送短信，1表示默认发送，0表示不发送")
    private int hasSms;

    @ApiModelProperty(value = "业务时是否加水印，1表示默认加水印，0表示不加水印")
    private Integer hasWatermark;

    /**
     * 是否分屏
     */
    @ApiModelProperty(value = "正文编辑是否分屏，1表示分屏，0表示不分屏")
    private Integer splitview;

    /**
     * 单位共享云盘空间大小，单位GB，默认2G
     */
    @ApiModelProperty(value = "单位共享云盘空间大小，单位GB，默认2G")
    private Integer cloudDiskSpaceSize;

    @ApiModelProperty(value = "机构简称")
    private String orgAbbreviation;

    @ApiModelProperty(value = "是否考核")
    private Boolean checkUp;

    @ApiModelProperty("单位级别")
    private String unitLevel;

    @ApiModelProperty("主管部门")
    private String authorityDepartment;

    @ApiModelProperty("详细类型")
    private String detailType;

    @ApiModelProperty("分管领导ID")
    private Long branchLeaderId;

    @TableField(exist = false)
    private List<CscpOrg> children;

    @TableField(exist = false)
    private String unitFlag;

    /**
     * 内设机构的上级单位ID
     * */
    @TableField(exist = false)
    private Long orgBelongCompanyId;
    /**
     * 内设机构的上级单位社会信用代码
     * */
    @TableField(exist = false)
    private String orgBelongCompanyCreditCode;

    @ApiModelProperty("实际考核人数")
    private Integer assessmentPeopleCount;

    @ApiModelProperty(value = "群组号")
    private String groupNumber;

    @ApiModelProperty("单位类型(0:党委办 1：政府办)")
    private Integer unitType;

    /**
     * CRM受理的客户类型（单位类型：1：标准型，2：项目型）
     */
    @ApiModelProperty(value = "客户类型")
    private String crmTenantType;

    /**
     * 客户经理姓名
     */
    @ApiModelProperty(value = "客户经理姓名")
    private String nameOfAccountManager;

    /**
     * 客户经理电话
     */
    @ApiModelProperty(value = "客户经理电话")
    private String customerManagerTelephone;

    /**
     * 客户管理员姓名
     */
    @ApiModelProperty(value = "客户管理员姓名")
    private String customerAdministratorName;

    /**
     * 客户管理员联系电话
     */
    @ApiModelProperty(value = "客户管理员联系电话")
    private String customerAdministratorTelephone;

    /**
     * 账号数
     */
    @ApiModelProperty(value = "账号数")
    private Integer numberOfAccounts;

    /**
     * 项目合同期限
     */
    @ApiModelProperty(value = "项目合同期限")
    private String projectContractPeriod;

    /**
     * 项目总金额
     */
    @ApiModelProperty(value = "项目总金额")
    private String totalProjectAmount;

    /**
     * 操作标识
     */
    @TableField(exist = false)
    private String oPFlag;

    /**
     * sms的name
     */
    @ApiModelProperty(value = "sms的name")
    private String smsName;

    /**
     * sms的key
     */
    @ApiModelProperty(value = "sms的key")
    private String smsKey;

    /**
     * 模型别称
     */
    @ApiModelProperty(value = "模型别称")
    private String modelDataType;

    /**
     * 模型名称
     */
    @ApiModelProperty(value = "模型名称")
    private String modelName;

    /**
     * 公文OID
     */
    @ApiModelProperty(value = "公文OID")
    private String oid;

    /**
     * 档案标识-统一信用代码
     */
    @ApiModelProperty(value = "档案标识-统一信用代码")
    private String creditCode;

    /**
     * 档案标识-代字编码
     */
    @ApiModelProperty(value = "档案标识-代字编码")
    private String dzCode;

    /**
     * 档案标识-全宗号
     */
    @ApiModelProperty(value = "档案标识-全宗号")
    private String dossierNumber;



    /**
     * 档案标识-组织机构编码
     */
    @ApiModelProperty(value = "档案标识-组织机构编码")
    private String organizationCode;

    @ApiModelProperty(value = "请示批件默认编号值")
    private String managementDefaultCode;

    @ApiModelProperty(value = "收文批件默认编号值")
    private String managementReceiveCode;

    @ApiModelProperty(value = "保密级别code")
    @TableField(value = "security_level_code")
    private String securityLevelCode;

    @ApiModelProperty(value = "保密级别code")
    @TableField(value = "security_level_code_name")
    private String securityLevelCodeName;

    @ApiModelProperty(value = "控制首页：个人中心的处事呈批件是否显示，0（默认）：不显示，1：显示")
    private String showHomeCpj;

    @ApiModelProperty(value = "机构唯一编码")
    private String soleCode;

    @ApiModelProperty(value = "行政区域编码")
    private String regionCode;

    @ApiModelProperty(value = "机构编码")
    @TableField(typeHandler = WestoneStrFullValueFromPlaintextDesHandler.class)
    private String orgCode;

    @ApiModelProperty(value = "最大编号")
    private Integer maxNumber;

    @ApiModelProperty(value = "商信机构ID")
    private String strId;

    @ApiModelProperty(value = "商信父级机构ID")
    private String strParentId;

    @ApiModelProperty(value = "商信机构信任号")
    private String strTrustNo;

    @ApiModelProperty(value = "商信父级机构信任号")
    private String strParentTrustNo;

    @ApiModelProperty(value = "卫士通统一身份认证系统组织机构ID")
    private String westoneOrgId;

    @ApiModelProperty(value = "卫士通统一身份认证系统上级组织机构ID")
    private String westoneOrgParentId;

    @ApiModelProperty(value = "跳转对应的服务ID")
    private Long serviceId;

    @ApiModelProperty("组织机构编码HMAC值，完整性校验")
    private String hmacOrgCode;

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("同步记录ID")
    @TableField(exist = false)
    private Long syncHistoryId;

    @ApiModelProperty("外部机构id")
    private String externalId;

    @ApiModelProperty("外部机构来源")
    private String orgOrigin;

    @ApiModelProperty("存储从根节点到当前节点的完整层级code路径（如：A|B|C）")
    private String orgCodePath;

    @ApiModelProperty("存储从根节点到父节点的完整层级id路径（如：1|2）")
    private String orgIdPath;
    @ApiModelProperty("orderByPath")
    private String orderByPath;

    @ApiModelProperty("别名")
    private String aliasName;

    @ApiModelProperty("授权推送应用")
    private String pushAppCode;

    @ApiModelProperty("机构类型(0:省,1:市,2:县,3:乡镇)")
    private String orgLevelType;

    @ApiModelProperty("机构标签, 多个标签用英文逗号分隔")
    private String orgLabel;

    @ApiModelProperty("机构分类[00:默认;01:学校;02:医院]")
    private String orgClassify;
    @ApiModelProperty("值班电话")
    private String dutyPhone;
    @ApiModelProperty("传真电话")
    private String faxPhone;
}
