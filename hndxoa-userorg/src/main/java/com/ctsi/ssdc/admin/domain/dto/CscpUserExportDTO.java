package com.ctsi.ssdc.admin.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberDivisionFromBase64DesHandler;
import com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler;
import com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler;
import com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullValueFromPlaintextDesHandler;
import lombok.Data;

@Data
public class CscpUserExportDTO {

    private Long id;

    /**
     * 登录名称
     */
    private String loginName;

    private String userName;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 用户是否显示
     */
    private String display;

    private String displayStr;

    /**
     * 是否统计
     */
    private String statistics;

    private String statisticsStr;
    /**
     * 是否为部门领导
     */
    private String departmentHead;

    private String departmentHeadStr;

    /**
     * 用户表排序号
     */
    private Integer orderBy;

    /**
     * 用户部门中间表排序号
     */
    private Integer sort;

    /**
     * 职务
     */
    private String post;

    /**
     * 职级
     */
    private String rank;

    /**
     * 身份证是否缺失
     */
    private String idCardStr;
}
