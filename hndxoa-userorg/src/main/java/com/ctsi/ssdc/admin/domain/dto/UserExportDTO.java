package com.ctsi.ssdc.admin.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname UserExportDTO
 * @Description
 * @Date 2021/12/31/0031 12:57
 */
@Data
@ApiModel(value="用户导出模板", description="用户导出模板")
public class UserExportDTO implements Serializable {

    private static final long serialVersionUID = 7401835566303049924L;

    @ApiModelProperty(value = "用户名")
    @ExcelProperty("用户名")
    private String userName;

    @ApiModelProperty(value = "用户登录名称")
    @ExcelIgnore
    private String loginName;

    @ApiModelProperty(value = "用户密码")
    @ExcelIgnore
    private String password;

    @ApiModelProperty(value = "真实姓名")
    @ExcelProperty("真实姓名")
    private String realName;

    @ApiModelProperty(value = "手机号码")
    @ExcelProperty("手机号码")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    @ExcelProperty("邮箱")
    private String email;

    @ApiModelProperty(value = "办公电话")
    @ExcelIgnore
    private String officePhone;

    @ApiModelProperty(value = "部门ID")
    @ExcelIgnore
    private Long departmentId;

    @ApiModelProperty(value = "部门名称")
    @ExcelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty(value = "单位ID")
    @ExcelIgnore
    private Long companyId;

    @ApiModelProperty(value = "单位名称")
    @ExcelProperty("单位名称")
    private String companyName;

    @ApiModelProperty(value = "租户ID")
    @ExcelIgnore
    private Long tenantId;

    @ApiModelProperty(value = "用户是否显示")
    @ExcelProperty("用户是否显示")
    private String displayStr;

    @ApiModelProperty(value = "是否统计")
    @ExcelProperty("是否统计")
    private String statisticsStr;

    @ApiModelProperty(value = "用户是否显示")
    @ExcelIgnore
    private Boolean display;

    @ApiModelProperty(value = "是否统计")
    @ExcelIgnore
    private Boolean statistics;

    @ApiModelProperty("是否为部门领导")
    @ExcelProperty("是否为部门领导")
    private String departmentHeadStr;

    @ApiModelProperty("是否为部门领导")
    @ExcelIgnore
    private Integer departmentHead;

    @ApiModelProperty(value = "用户表排序号")
    @ExcelProperty("用户表排序号")
    private Integer orderBy;

    @ApiModelProperty(value = "用户部门中间表排序号")
    @ExcelProperty("用户部门中间表排序号")
    private Integer sort;

    @ApiModelProperty(value = "职务")
    @ExcelProperty("职务")
    private String post;

    @ApiModelProperty(value = "职级")
    @ExcelProperty("职级")
    private String rank;

    @ApiModelProperty(value = "身份证缺失")
    @ExcelProperty("身份证缺失")
    private String idCardStr;
}
