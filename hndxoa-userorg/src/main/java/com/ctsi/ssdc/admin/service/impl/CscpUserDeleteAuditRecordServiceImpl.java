package com.ctsi.ssdc.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUserDeleteAuditRecord;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDeleteAuditRecordDTO;
import com.ctsi.ssdc.admin.domain.vo.BatchUserDisableResultVO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserDeleteAuditRecordMapper;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpUserDeleteAuditRecordService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CscpUserDeleteAuditRecordServiceImpl extends ServiceImpl<CscpUserDeleteAuditRecordMapper, CscpUserDeleteAuditRecord> implements CscpUserDeleteAuditRecordService {
    @Autowired
    private CscpUserRepository userRepository;
    @Autowired
    private CscpUserDeleteAuditRecordMapper userDeleteAuditRecordMapper;
    @Autowired
    private CscpUserOrgService cscpUserOrgService;
    @Autowired
    private CscpOrgRepository cscpOrgRepository;

//    @Transactional
//    @Override
//    public void submitDeleteAudit(CscpUserDeleteAuditRecordDTO auditRecordDTO) {
//        // 先查询被删除用户的其他信息（部门相关）
//        CscpUserDTO cscpUserDTO1 = new CscpUserDTO();
//        cscpUserDTO1.setUserIds(auditRecordDTO.getDeleteUserIds());
//        List<CscpUserDTO> cscpUserOtherInfo = userRepository.otherQueryCscpUserListNoAdmin(cscpUserDTO1);
//        Map<Long, List<CscpUserDTO>> cscpUserOtherMap = cscpUserOtherInfo.stream().collect(Collectors.groupingBy(CscpUserDTO::getId));
//
//
//        List<CscpOrgDTO> orgsList;
//        for (Long deleteUserId : auditRecordDTO.getDeleteUserIds()) {
//
//            //获取被删除用户的所有相关单位机构
//            orgsList = cscpUserOrgService.queryOrgByUserId(deleteUserId);
//            CscpUserDTO deleteUser = userRepository.selectUserById(deleteUserId);
//            Set<CscpOrg> regionRoleOrgs = new HashSet<>();
//            //获取被删除用户的上级/省直、市区单位
//            for (CscpOrgDTO item : orgsList) {
//                List<CscpOrg> cscpOrgs = cscpOrgRepository.selectSelf2RootOrg(item.getId());
//                //如果是需要省直审批
//                List<CscpOrg> collect;
//                if (1 == auditRecordDTO.getApplyType()) {
//                    collect = cscpOrgs.stream().filter(q -> q.getLevel().equals(cscpOrgs.size() - 1)).collect(Collectors.toList());
//                } else {
//                    //collect = cscpOrgs.stream().filter(q -> q.getLevel().equals(0)).collect(Collectors.toList());
//                    collect = Lists.newArrayList(cscpOrgs.get(0));
//                }
//                if (CollectionUtil.isNotEmpty(collect)) {
//                    regionRoleOrgs.addAll(collect);
//                }
//            }
//            //获取该上级/省直、市区单位下是否有区划管理员
//            if (CollectionUtils.isNotEmpty(regionRoleOrgs)) {
//                CscpUserDTO cscpUserDTO;
//                List<Long> regionRoleIds = new ArrayList<>();
//                for (CscpOrg item : regionRoleOrgs) {
//                    cscpUserDTO = new CscpUserDTO();
//                    cscpUserDTO.setCompanyId(item.getId());
//                    //获取该单位下的所有人员
//                    //TODO 这里在获取单位所有人员的时候可以获取到他们对应的角色集合
//                    List<CscpUserDTO> cscpUserDTOList = userRepository.selectUserByCompanyId(cscpUserDTO);
//                    List<CscpRoles> cscpRolesList;
//                    Map<Long, List<String>> userIdRolesMap;
//                    if (CollectionUtils.isNotEmpty(cscpUserDTOList)) {
//                        List<Long> cscpUserIds = cscpUserDTOList.stream()
//                                .map(CscpUserDTO::getId)
//                                .collect(Collectors.toList());
//                        //获取人员所有角色
//                        cscpRolesList = userRoleRepository.queryRoleByUserIds(cscpUserIds);
//                        userIdRolesMap = cscpRolesList.stream()
//                                .collect(Collectors.toMap(
//                                        CscpRoles::getUserId,  // key：以UserId作为分组依据
//                                        p -> {           // value：初次遇到key时，创建包含当前name的List
//                                            List<String> names = new ArrayList<>();
//                                            names.add(p.getRoleCode());
//                                            return names;
//                                        },
//                                        (existingList, newList) -> {  // 合并函数：key重复时，将新元素添加到已有List
//                                            existingList.addAll(newList);
//                                            return existingList;
//                                        }
//                                ));
//                        //筛选出有区域管理员角色的用户
//                        userIdRolesMap.forEach((key, value) -> {
//                            if (value.contains("region_role")) {
//                                regionRoleIds.add(key);
//                            }
//                        });
//                    }
//                }
//                if (CollectionUtil.isEmpty(regionRoleIds)) {
//                    throw new BusinessException("没有找到对应的区划管理人员");
//                }
//                List<CscpUser> cscpUsers = userRepository.selectBatchIds(regionRoleIds);
//                List<CscpUserDeleteAuditRecord> submitUserList = new ArrayList<>();
//                CscpUserDeleteAuditRecord auditRecord;
//                for (CscpUser item : cscpUsers) {
//                    List<CscpUserDTO> deleteUserOrgs = cscpUserOtherMap.get(deleteUser.getId());
//
//                    auditRecord = new CscpUserDeleteAuditRecord();
//                    auditRecord.setApplyLoginName(SecurityUtils.getCurrentUserName());
//                    auditRecord.setApplyRealName(auditRecordDTO.getApplyRealName());
//                    auditRecord.setApplyMobile(auditRecordDTO.getApplyMobile());
//                    auditRecord.setApplyType(auditRecordDTO.getApplyType());
//                    auditRecord.setApplyDeleteExplain(auditRecordDTO.getApplyDeleteExplain());
//                    auditRecord.setDeleteUserId(deleteUserId);
//                    auditRecord.setApplyDeleteTime(LocalDateTime.now());
//                    auditRecord.setDeleteLoginName(deleteUser.getLoginName());
//                    auditRecord.setDeleteRealName(deleteUser.getRealName());
//                    auditRecord.setDeleteMobile(deleteUser.getMobile());
//                    auditRecord.setAuditStatus(0);
//                    auditRecord.setAuditUserId(item.getId());
//                    auditRecord.setAuditLoginName(item.getLoginName());
//                    auditRecord.setAuditRealName(item.getRealName());
//                    auditRecord.setDeleteIdCardNo(deleteUser.getIdCardNo());
//                    auditRecord.setDeleteUserDepartmentId(deleteUserOrgs.get(0).getDepartmentId());
//                    auditRecord.setDeleteUserDepartmentName(StringUtils.join(deleteUserOrgs.stream().map(CscpUserDTO::getDepartmentName).collect(Collectors.toSet()), ","));
//                    auditRecord.setDeleteUserCompanyId(deleteUserOrgs.get(0).getCompanyId());
//                    auditRecord.setDeleteUserCompanyName(StringUtils.join(deleteUserOrgs.stream().map(CscpUserDTO::getCompanyName).collect(Collectors.toSet()), ","));
//                    submitUserList.add(auditRecord);
//                }
//                this.saveBatch(submitUserList);
//            }
//        }
//    }


    @Transactional
    @Override
    public void submitDeleteAudit(CscpUserDeleteAuditRecordDTO auditRecordDTO) {
        // 先查询被删除用户的其他信息（部门相关）
        CscpUserDTO cscpUserDTO1 = new CscpUserDTO();
        cscpUserDTO1.setUserIds(auditRecordDTO.getDeleteUserIds());
        List<CscpUserDTO> cscpUserOtherInfo = userRepository.otherQueryCscpUserListNoAdmin(cscpUserDTO1);
        Map<Long, List<CscpUserDTO>> cscpUserOtherMap = cscpUserOtherInfo.stream().collect(Collectors.groupingBy(CscpUserDTO::getId));
        List<CscpOrgDTO> orgsList;
        for (Long deleteUserId : auditRecordDTO.getDeleteUserIds()) {
            //获取被删除用户的所有相关单位机构
            orgsList = cscpUserOrgService.queryOrgByUserId(deleteUserId);
            CscpUserDTO deleteUser = userRepository.selectUserById(deleteUserId);
            Set<CscpOrg> regionRoleOrgs = new HashSet<>();
            //获取被删除用户的上级/省直、市区单位
            for (CscpOrgDTO item : orgsList) {
                List<CscpOrg> cscpOrgs = cscpOrgRepository.selectSelf2RootOrg(item.getId());
                //如果是需要省直审批
                List<CscpOrg> collect;
                if (1 == auditRecordDTO.getApplyType()) {
                    collect = cscpOrgs.stream().filter(q -> q.getLevel().equals(cscpOrgs.size() - 1)).collect(Collectors.toList());
                } else {
                    //collect = cscpOrgs.stream().filter(q -> q.getLevel().equals(0)).collect(Collectors.toList());
                    collect = Lists.newArrayList(cscpOrgs.get(0));
                }
                if (CollectionUtil.isNotEmpty(collect)) {
                    regionRoleOrgs.addAll(collect);
                }
            }
            //获取该上级/省直、市区单位下是否有区划管理员
            if (CollectionUtils.isNotEmpty(regionRoleOrgs)) {
                List<CscpUserDTO> regionRoleUserList = new ArrayList<>();
                for (CscpOrg item : regionRoleOrgs) {
                    //获取这个单位的所有的区划管理人员信息
                    List<CscpUserDTO> cscpUserDTOS = userRepository.selectUserByCompanyIdAndRegionRoleList(item.getId());
                    //这里要对人员去重
                    regionRoleUserList = Stream.concat(regionRoleUserList.stream(), cscpUserDTOS.stream())
                            // 以id为键，收集到Map（重复时保留先出现的元素）
                            .collect(Collectors.toMap(
                                    CscpUserDTO::getLoginName,  // 键：id属性
                                    user -> user, // 值：对象本身
                                    (existing, replacement) -> existing // 重复时保留原元素
                            ))
                            .values() // 提取值集合
                            .stream()
                            .collect(Collectors.toList());
                }
                if (CollectionUtil.isEmpty(regionRoleUserList)) {
                    throw new BusinessException("没有找到对应的区划管理人员");
                }
                List<CscpUserDeleteAuditRecord> submitUserList = new ArrayList<>();
                CscpUserDeleteAuditRecord auditRecord;
                for (CscpUserDTO item : regionRoleUserList) {
                    List<CscpUserDTO> deleteUserOrgs = cscpUserOtherMap.get(deleteUser.getId());
                    auditRecord = new CscpUserDeleteAuditRecord();
                    auditRecord.setApplyLoginName(SecurityUtils.getCurrentUserName());
                    auditRecord.setApplyRealName(auditRecordDTO.getApplyRealName());
                    auditRecord.setApplyMobile(auditRecordDTO.getApplyMobile());
                    auditRecord.setApplyType(auditRecordDTO.getApplyType());
                    auditRecord.setApplyDeleteExplain(auditRecordDTO.getApplyDeleteExplain());
                    auditRecord.setDeleteUserId(deleteUserId);
                    auditRecord.setApplyDeleteTime(LocalDateTime.now());
                    auditRecord.setDeleteLoginName(deleteUser.getLoginName());
                    auditRecord.setDeleteRealName(deleteUser.getRealName());
                    auditRecord.setDeleteMobile(deleteUser.getMobile());
                    auditRecord.setAuditStatus(0);
                    auditRecord.setAuditUserId(item.getId());
                    auditRecord.setAuditLoginName(item.getLoginName());
                    auditRecord.setAuditRealName(item.getRealName());
                    auditRecord.setDeleteIdCardNo(deleteUser.getIdCardNo());
                    auditRecord.setDeleteUserDepartmentId(deleteUserOrgs.get(0).getDepartmentId());
                    auditRecord.setDeleteUserDepartmentName(StringUtils.join(deleteUserOrgs.stream().map(CscpUserDTO::getDepartmentName).collect(Collectors.toSet()), ","));
                    auditRecord.setDeleteUserCompanyId(deleteUserOrgs.get(0).getCompanyId());
                    auditRecord.setDeleteUserCompanyName(StringUtils.join(deleteUserOrgs.stream().map(CscpUserDTO::getCompanyName).collect(Collectors.toSet()), ","));
                    submitUserList.add(auditRecord);
                }
                this.saveBatch(submitUserList);
            }
        }
    }

    @Override
    public void updateAuditStatus(CscpUserDeleteAuditRecordDTO auditRecordDTO) {
        //把最新的审批状态结果更新到该被删除人的所有记录上
        LambdaUpdateWrapper<CscpUserDeleteAuditRecord> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.in(CscpUserDeleteAuditRecord::getDeleteUserId, auditRecordDTO.getDeleteUserIds());
        updateWrapper.eq(CscpUserDeleteAuditRecord::getAuditStatus, 0);
        updateWrapper.set(StringUtils.isNotBlank(auditRecordDTO.getAuditRefuseExplain()), CscpUserDeleteAuditRecord::getAuditRefuseExplain, auditRecordDTO.getAuditRefuseExplain());
        updateWrapper.set(CscpUserDeleteAuditRecord::getAuditStatus, auditRecordDTO.getAuditStatus());
        updateWrapper.set(CscpUserDeleteAuditRecord::getAuditTime, LocalDateTime.now());
        updateWrapper.set(CscpUserDeleteAuditRecord::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(CscpUserDeleteAuditRecord::getUpdateBy, SecurityUtils.getCurrentUserId());
        updateWrapper.set(CscpUserDeleteAuditRecord::getUpdateName, SecurityUtils.getCurrentUserName());
        this.update(updateWrapper);
    }

    @Override
    public PageResult<CscpUserDeleteAuditRecordDTO> selectApprovalList(CscpUserDeleteAuditRecordDTO auditRecordDTO, BasePageForm basePageForm) {
        LambdaQueryWrapper<CscpUserDeleteAuditRecord> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserDeleteAuditRecord::getAuditUserId, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(StringUtils.isNotBlank(auditRecordDTO.getApplyRealName()), CscpUserDeleteAuditRecord::getApplyRealName, auditRecordDTO.getApplyRealName());
        queryWrapper.eq(StringUtils.isNotBlank(auditRecordDTO.getApplyMobile()), CscpUserDeleteAuditRecord::getApplyMobile, auditRecordDTO.getApplyMobile());
        queryWrapper.eq(!Objects.isNull(auditRecordDTO.getAuditStatus()), CscpUserDeleteAuditRecord::getAuditStatus, auditRecordDTO.getAuditStatus());
        queryWrapper.orderByDesc(CscpUserDeleteAuditRecord::getCreateTime);
        IPage<CscpUserDeleteAuditRecord> userIPage = userDeleteAuditRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        List<CscpUserDeleteAuditRecordDTO> data = ListCopyUtil.copy(userIPage.getRecords(), CscpUserDeleteAuditRecordDTO.class);
        long count = 0L;
        if (CollectionUtils.isNotEmpty(data)) {
            count = userIPage.getTotal();
        }
        return new PageResult<>(data, count, count);
    }

    @Override
    public PageResult<CscpUserDeleteAuditRecordDTO> selectDeleteRecordByUserId(Long userId, BasePageForm basePageForm) {
        LambdaQueryWrapper<CscpUserDeleteAuditRecord> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserDeleteAuditRecord::getAuditUserId, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(CscpUserDeleteAuditRecord::getDeleteUserId, userId);
        queryWrapper.orderByDesc(CscpUserDeleteAuditRecord::getCreateTime);
        IPage<CscpUserDeleteAuditRecord> userIPage = userDeleteAuditRecordMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        List<CscpUserDeleteAuditRecordDTO> data = ListCopyUtil.copy(userIPage.getRecords(), CscpUserDeleteAuditRecordDTO.class);
        long count = 0L;
        if (CollectionUtils.isNotEmpty(data)) {
            count = userIPage.getTotal();
        }
        return new PageResult<>(data, count, count);
    }

    //分割字符串加密
    public String division(String realName) {
        String realNames = "";
        if (StringUtils.isNotBlank(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                realNames = realNames.concat(KeyCenterUtils.encrypt(realName.substring(i, i + 1)));
                realNames = realNames.concat(",");
            }
            realNames = realNames.substring(0, realNames.length() - 1);
        }
        return realNames;
    }

    public static void main(String[] args) {
        ArrayList<Long> ids = new ArrayList<>();
        ids.add(1L);
        ids.add(3L);
        ids.add(2L);
        ids.add(4L);

        List<BatchUserDisableResultVO> deleteIdInfo = new ArrayList<>();
        BatchUserDisableResultVO a = new BatchUserDisableResultVO();
        a.setDeleteId(2L);
        a.setReason("ABC");
        deleteIdInfo.add(a);

        a = new BatchUserDisableResultVO();
        a.setDeleteId(3L);
        a.setReason("CDE");
        deleteIdInfo.add(a);

        List<Long> collect = ids.stream().filter(userInfo ->
                !deleteIdInfo.stream().map(BatchUserDisableResultVO::getDeleteId).collect(Collectors.toList()).contains(userInfo)
        ).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(collect));

    }
}
