package com.ctsi.ssdc.admin.domain.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname CscpUserDeleteAuditRecordDTO
 * @Description
 * @Date 2025/8/14 17:02
 */
@Data
@ApiModel("用户删除审批记录")
public class CscpUserDeleteAuditRecordDTO extends BaseDtoEntity {
    @ApiModelProperty("申请人用户名")
    private String applyLoginName;

    @ApiModelProperty("申请人姓名")
    private String applyRealName;

    @ApiModelProperty("申请人手机")
    private String applyMobile;

    @ApiModelProperty("申请类型(省直、市区划管理员审批=0，上级区划管理员审批=1)")
    private Integer applyType;

    @ApiModelProperty("申请删除原因")
    private String applyDeleteExplain;

    @ApiModelProperty("申请时间")
    private LocalDateTime applyDeleteTime;

    @ApiModelProperty(value = "删除用户id(集合)")
    private List<Long> deleteUserIds;

    @ApiModelProperty(value = "删除用户id")
    private Long deleteUserId;

    @ApiModelProperty("删除用户名")
    private String deleteLoginName;

    @ApiModelProperty("删除用户姓名")
    private String deleteRealName;

    @ApiModelProperty("删除用户手机")
    private String deleteMobile;

    @ApiModelProperty("删除用户身份证")
    private String deleteIdCardNo;

    @ApiModelProperty("删除用户部门id")
    private Long deleteUserDepartmentId;

    @ApiModelProperty("删除用户部门名称")
    private String deleteUserDepartmentName;

    @ApiModelProperty("删除用户单位id")
    private Long deleteUserCompanyId;

    @ApiModelProperty("删除用户单位名称")
    private String deleteUserCompanyName;

    @ApiModelProperty("审批状态（审批中=0审批通过=1审批驳回=2）")
    private Integer auditStatus;

    @ApiModelProperty("审批拒绝原因")
    private String auditRefuseExplain;

    @ApiModelProperty("审批人用户名")
    private String auditLoginName;

    @ApiModelProperty("审批人姓名")
    private String auditRealName;

    @ApiModelProperty("审批时间")
    private LocalDateTime auditTime;
}
