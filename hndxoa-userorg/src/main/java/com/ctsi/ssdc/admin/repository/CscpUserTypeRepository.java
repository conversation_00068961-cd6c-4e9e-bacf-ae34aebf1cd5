package com.ctsi.ssdc.admin.repository;

import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.admin.domain.CscpUserType;
import com.ctsi.ssdc.database.annotation.InjectByDataBaseType;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;

/**
 * <AUTHOR> Generator
 */
@InjectByDataBaseType(includes = {EnumDatabaseName.ORACLE})
public interface CscpUserTypeRepository extends MybatisBaseMapper<CscpUserType> {


}