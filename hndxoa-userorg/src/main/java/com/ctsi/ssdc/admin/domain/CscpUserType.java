package com.ctsi.ssdc.admin.domain;


import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> Generator
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "cscp_user_type", description = "人员类型")
public class CscpUserType extends BaseEntity {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户类型")
    private String userType;

    @ApiModelProperty(value = "类型级别")
    private String typeLevel;

}