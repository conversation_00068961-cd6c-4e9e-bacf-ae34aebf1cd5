package com.ctsi.ssdc.admin.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.database.annotation.InjectByDataBaseType;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;
import com.ctsi.ssdc.dto.QueryUserExamineDTO;
import com.ctsi.ssdc.dto.QueryUserLockDTO;
import com.ctsi.ssdc.security.CscpUserNumberDTO;
import com.ctsi.ssdc.vo.QueryUserLockVO;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Generator
 */
@InjectByDataBaseType(includes = {EnumDatabaseName.ORACLE})
public interface CscpUserRepository extends MybatisBaseMapper<CscpUser> {


    /**
     * 通过用户ID，查看该用户能查看的用户
     *
     * @param id
     * @return
     */
    List<CscpUser> selectAllCscpUserList(Long id);

    Integer getUserMaxSorted();

    /**
     * 条件查询
     *
     * @param iPage
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> criteriaQueryCscpUserList(IPage iPage, @Param("user") CscpUserDTO dto);

    /**
     * 条件查询
     *
     * @param iPage
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> queryCscpUserList(IPage iPage, @Param("user") CscpUserDTO dto, @Param("deptIDList") List<Long> deptIDList);



    /**
     * 条件查询
     *
     * @param iPage
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> criteriaQueryCscpUserListNoAdmin(IPage iPage, @Param("user") CscpUserDTO dto);


    /**
     * 通过查询条件查询所有用户
     *
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> queryCscpUserOrgList(@Param("user") CscpUserDTO dto);

    /**
     * 查询租户下所有用户
     *
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> selectUserByTenantId(@Param("user") CscpUserDTO dto);

    /**
     * 查询租户下所有用户
     *
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserExportDTO> selectUserExport(@Param("user") CscpUserDTO dto);

    /**
     * 查询单位下所有用户,或者本部门下面的所有用户
     *
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> selectUserByCompanyId(@Param("user") CscpUserDTO dto);

    /**
     * 根据单位id获取单位下面的所有用户的单位id、单位名称、部门id、部门名称
     *
     * @param dto 单位id
     * @return CscpUserDTO
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> selectUserDetailByCompanyId(@Param("user") CscpUserDTO dto);


    /**
     * @param iPage
     * @param queryCscpUserNotInListDTO
     * @return
     */
    IPage<CscpUserDTO> queryCscpUserNotInListDTO(IPage iPage, @Param("ew") QueryCscpUserNotInListDTO queryCscpUserNotInListDTO);

    /**
     * 查询指定机构下的用户信息
     *
     * @param iPage
     * @param orgId
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<CscpUserDTO> pageQueryUserList(IPage iPage, @Param("orgId") Long orgId, @Param("realName") String realName, @Param("userIds") List<Long> userIds);

    /**
     * 查询指定机构下的用户信息(不区分是否显示和是否停用)
     *
     * @param iPage
     * @param orgId
     * @param realName
     * @return
     */
    IPage<CscpUserDTO> pageSelectUsers(IPage iPage, @Param("orgId") Long orgId, @Param("realName") String realName);


    /**
     * 获取本单位未登录的用户
     */
    List<CscpUserNumberDTO> notSingInUser(@Param("userId") List<Long> singInUserIdList);


    /**
     * 查询用户对应的机构信息
     */
    List<CscpUserDTO> selectUserOrg(@Param("org") CscpSelectUserlistDTO cscpSelectUserlist);

    /**
     * 查询用户基本属性
     *
     * @param cscpBaseUserDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpBaseUserDTO> selectBaseUserInfo(@Param("user") CscpBaseUserDTO cscpBaseUserDTO);

//    /**
//     * 查询用户基本属性
//     *
//     * @param cscpDistributeBaseUserDTO
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "true")
//    List<CscpDistributeBaseUserDTO> selectDistributeBaseUserInfo(@Param("user") CscpDistributeBaseUserDTO cscpDistributeBaseUserDTO);

    /**
     * 查询用户组织机构信息
     *
     * @param cscpBaseUserDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpBaseUserDTO> queryBaseUserInfo(@Param("user") CscpBaseUserDTO cscpBaseUserDTO);

    /**
     * 租户选人模块模糊搜索
     *
     * @param cscpSelectFuzzySearchUserList
     * @return
     */
    List<CscpUserDTO> selectFuzzySearchUserList(@Param("param") CscpSelectFuzzySearchUserList cscpSelectFuzzySearchUserList);


    /**
     * 查询具有指定权限码的用户
     *
     * @param
     * @param code
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> selectCodeUser(@Param("companyId") Long companyId, @Param("code") String code);

    /**
     * 查询指定租户下的用户
     *
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> selectTenantUsers(Long tenantId);

    /**
     * 获取某个机构下的用户
     *
     * @param parentId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> selectOrgUsers(@Param("orgId") Long parentId);


    /**
     * 通过人员id集合，和单位id集合，获取人员对应的单位部门人员信息集合
     *
     * @param
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpDistributeBaseUserDTO> selectDistributeBaseUserInfo(@Param("user") CscpDistributeBaseUserDTO cscpDistributeBaseUserDTO);

    @InterceptorIgnore(tenantLine = "true")
    void updateAppVersionName(@Param("id") Long id,@Param("versionName") String versionName);

    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> getWorkUserList(IPage iPage, @Param("groupId") Long groupId, @Param("realName") String realName);

    @InterceptorIgnore(tenantLine="true")
    IPage<CscpUserDTO> selectCommCompanyUserList(IPage iPage, @Param("orgId") Long orgId, @Param("realName") String realName, @Param("userIds") List<Long> userIds);


    int updateSxDataSyncFlag(@Param("strId") String strId);
    @InterceptorIgnore(tenantLine="true")
    IPage<QueryUserLockDTO> queryUserLock(IPage page, @Param("vo") QueryUserLockVO vo, @Param("userId") Long userId);

    @InterceptorIgnore(tenantLine="true")
    IPage<QueryUserExamineDTO> queryUserExamine(IPage page, @Param("vo") QueryUserLockVO vo);

    @InterceptorIgnore(tenantLine="true")
    CscpUserDTO selectUserIsDisable(@Param("userId") Long id);

    @InterceptorIgnore(tenantLine="true")
    List<CscpUserDTO> selectListUserIsNotDisable(@Param("userIds") List<Long> userIds);
    /**
     * 根据单位 ID 查询用户信息
     * @param orgIdList 单位 ID
     * @return 符合条件的用户信息列表
     */
    List<CscpUser> selectUsersByOrgIdList(List<Long> orgIdList);

    /**
     * 分页查询用户基础信息
     * @param page
     * @param cscpUserDTO
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<CscpUserDTO> baseQueryCscpUserListNoAdmin(IPage page, @Param("user") CscpUserDTO cscpUserDTO,@Param("contextIndex") String contextIndex);

    /**
     * 查询部门信息和用户部门关联关系
     * @param cscpUserDTO
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    List<CscpUserDTO> otherQueryCscpUserListNoAdmin(@Param("user") CscpUserDTO cscpUserDTO);

    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> queryUserByTreeIdAndRoleIdPage(IPage mPlusPageByBasePage , @Param("orgId")  Long orgId , @Param("roleId")  Long roleId);

    /**
     * 根据角色及其他条件分页查询用户信息
     *
     * @param page 分页请求对象
     * @param params 查询条件参数Map
     * @return 分页的用户信息
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> queryUsersByRolePage(IPage<CscpUser> page, @Param("params") Map<String, Object> params);

    @InterceptorIgnore(tenantLine = "true")
    int updateUserCompanyId(@Param("orgId") Long orgId, @Param("companyId") Long companyId,
                            @Param("companyName") String companyName, @Param("orgAbbreviation") String orgAbbreviation);

    @InterceptorIgnore(tenantLine = "true")
    List<Long> selectUserCompanyId(@Param("orgId") Long orgId, @Param("companyId") Long companyId);

    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> queryCscpUserByRole(IPage iPage, @Param("ew") QueryCscpUserNotInListDTO queryCscpUserNotInListDTO);

    @InterceptorIgnore(tenantLine = "true")
    IPage<CscpUserDTO> queryUserByTreeIdAndRoleCodePage(IPage mPlusPageByBasePage, @Param("orgId") Long orgId, @Param("roleCodes") List<String> roleCodes);

    @InterceptorIgnore(tenantLine = "true")
    Integer selectUserLoginCount(@Param("userId") Long id);

    @InterceptorIgnore(tenantLine = "true")
    Integer selectUserListCount(@Param("user") CscpUserDTO dto);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserExportDTO> selectUserListByPage(@Param("user")CscpUserDTO dto,@Param("pageNo") Integer pageNo,@Param("pageSize") Integer pageSize);

    @InterceptorIgnore(tenantLine = "true")
    CscpUserDTO selectUserById(@Param("userId") Long userId);

    @InterceptorIgnore(tenantLine = "true")
    Integer getMaxSuffixByBaseName(@Param("baseName") String baseName);

    @InterceptorIgnore(tenantLine = "true")
    void updateAppCodesById(@Param("userId") Long userId, @Param("appCodes") String appCodes);

    /**
     * 获取版主管理的应用ID列表
     * @param moderatorId 版主ID
     * @return 应用ID列表
     */

    @InterceptorIgnore(tenantLine = "true")
    List<Long> getModeratorManageAppIds(@Param("moderatorId") Long moderatorId);


    /**
     * 查询单位下所有拥有区划管理员角色的用户(region_role)
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> selectUserByCompanyIdAndRegionRoleList( Long companyId);
}