package com.ctsi.ssdc.admin.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.ssdc.admin.domain.CscpUserType;
import com.ctsi.ssdc.admin.repository.CscpUserTypeRepository;
import com.ctsi.ssdc.admin.service.CscpUserTypeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service Implementation for managing CscpUserRole.
 *
 * <AUTHOR> biyi generator
 */
@Service
public class CscpUserTypeServiceImpl extends SysBaseServiceImpl<CscpUserTypeRepository, CscpUserType> implements CscpUserTypeService {

    private final Logger log = LoggerFactory.getLogger(CscpUserTypeServiceImpl.class);

    @Autowired
    private CscpUserTypeRepository cscpUserTypeRepository;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUserTypeList(Long id, List<CscpUserType> userTypeList) {
        try {
            LambdaQueryWrapper<CscpUserType> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CscpUserType::getUserId, id);
            cscpUserTypeRepository.delete(queryWrapper);
            if (CollectionUtils.isNotEmpty(userTypeList)) {
                for (CscpUserType userType : userTypeList) {
                    userType.setUserId(id);
                    if (StringUtils.isEmpty(userType.getUserType())) {
                        continue;
                    }
                    if (StringUtils.isEmpty(userType.getTypeLevel())) {
                        userType.setTypeLevel(StringUtils.substringBefore(userType.getUserType(), "_"));
                    }
                    cscpUserTypeRepository.insert(userType);
                }
            }
        } catch (Exception e) {
            log.error("保存用户类型失败：{}", e);
            return false;
        }
        return true;
    }
}
