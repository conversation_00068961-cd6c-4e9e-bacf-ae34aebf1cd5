package com.ctsi.hndx.addrbook.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.addrbook.constant.AddrBookConstant;
import com.ctsi.hndx.addrbook.entity.TAddressBook;
import com.ctsi.hndx.addrbook.entity.TAddressBookAuthorize;
import com.ctsi.hndx.addrbook.entity.TAddressBookLabel;
import com.ctsi.hndx.addrbook.entity.TLabelOrg;
import com.ctsi.hndx.addrbook.entity.dto.*;
import com.ctsi.hndx.addrbook.mapper.TAddressBookAuthorizeMapper;
import com.ctsi.hndx.addrbook.mapper.TAddressBookLabelMapper;
import com.ctsi.hndx.addrbook.mapper.TAddressBookMapper;
import com.ctsi.hndx.addrbook.mapper.TLabelOrgMapper;
import com.ctsi.hndx.addrbook.service.ITAddressBookAuthorizeService;
import com.ctsi.hndx.addrbook.service.ITAddressBookLabelService;
import com.ctsi.hndx.addrbook.service.ITAddressBookService;
import com.ctsi.hndx.addrbook.service.ITLabelOrgService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.RedisKeyConstant;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.systenant.entity.TSysTenant;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.RedisUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */

@Slf4j
@Service
public class TAddressBookLabelServiceImpl extends SysBaseServiceImpl<TAddressBookLabelMapper, TAddressBookLabel> implements ITAddressBookLabelService {

    @Autowired
    private TAddressBookLabelMapper tAddressBookLabelMapper;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private TLabelOrgMapper tLabelOrgMapperl;

    @Autowired
    private ITLabelOrgService itLabelOrgService;

    @Autowired
    private TAddressBookMapper tAddressBookMapper;

    @Autowired
    private TAddressBookServiceImpl tAddressBookService;

    @Autowired
    private ITAddressBookAuthorizeService itAddressBookAuthorizeService;

    @Autowired
    private TAddressBookAuthorizeMapper tAddressBookAuthorizeMapper;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private ITAddressBookService itAddressBookService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;


    /**
     * 用于通讯录可视范围结尾可能会出现，问题
     */
    private static final String CHARACTER = ",";

    // 定义线程池作为类的静态成员，避免重复创建
    private static final ExecutorService EXECUTOR = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("address-book-pool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    /**
     * 翻页
     *
     * @param labelName
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TAddressBookLabelDTO> queryTAddressBookLabelPc(String labelName, BasePageForm basePageForm) {
        IPage<TAddressBookLabel> pageData = new Page<>();

        //普通用户，查询本单位的标签和租户授权给他的标签
        if (SecurityUtils.isGeneralName() && !SecurityUtils.isRegionAdmin()) {
            TAddressBookLabelDTO build = TAddressBookLabelDTO.builder()
                    .companyId(SecurityUtils.getCurrentCompanyId())
                    // 同时查出授权给本单位的标签
                    //.departmentId(SecurityUtils.getCurrentCscpUserDetail().getDepartmentId())
                    .labelName(labelName).build();
            pageData = tAddressBookLabelMapper.selectPageAddressBookLabel(PageHelperUtil.getMPlusPageByBasePage(basePageForm), build);
            log.info("查询isGeneralName queryTAddressBookLabelPc.IPage<TAddressBookLabel> pageData：{}", pageData.getRecords());
        }

        //admin管理员，查询admin管理员建的标签
        if (SecurityUtils.isSystemName() || SecurityUtils.isRegionAdmin()) {
            pageData = tAddressBookLabelMapper.selectPageNoAdd(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                    new LambdaQueryWrapper<TAddressBookLabel>()
                            .eq(TAddressBookLabel::getCreateBy, SecurityUtils.getCurrentUserId())
                            .eq(TAddressBookLabel::getDefaultLabel, AddrBookConstant.NOT_DEFAULT_LABEL)
                            .orderByAsc(TAddressBookLabel::getSort)
                            .like(StringUtils.isNotEmpty(labelName), TAddressBookLabel::getLabelName, labelName)
            );
            log.info("查询isSystemName queryTAddressBookLabelPc.IPage<TAddressBookLabel> pageData：{}", pageData.getRecords());
        }

        //返回
        IPage<TAddressBookLabelDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TAddressBookLabelDTO.class));

        //如果是普通用户无法编辑租户的标签
        data.getRecords().stream().filter(i -> !Objects.isNull(i.getDefaultLabel())).forEach(i -> {
            if (!(SecurityUtils.isSystemName() || SecurityUtils.isRegionAdmin())
                    && i.getDefaultLabel().equals(AddrBookConstant.NOT_DEFAULT_LABEL)) {
                i.setLabelAddType(AddrBookConstant.NON_EDITABLE);
            } else {
                i.setLabelAddType(AddrBookConstant.EDITABLE);
            }
        });

        return new PageResult(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 获取通讯录应用标签列表 (带缓存)
     *
     * @return 标签列表
     */
    public List<TAddressBookLabelDTO> queryTAddressBookLabelApp2() {
        ObjectMapper objectMapper = new ObjectMapper();
        if ((SecurityUtils.isSystemName() && !SecurityUtils.isPlatformAdmin()) || SecurityUtils.isRegionAdmin()) {
            throw new BusinessException("该接口不允许租户访问！");
        }

        Long companyId = SecurityUtils.getCurrentCompanyId();
        if (companyId == null) {
            companyId = SecurityUtils.getCurrentBackupCompanyId();
        }
        if (companyId == null) {
            throw new BusinessException("该用户无访问权限！");
        }
        String cacheKey = RedisKeyConstant.CACHE_KEY_PREFIX + companyId;

        // 从缓存获取数据
        try {
            String cachedData = (String) redisUtil.get(cacheKey);
            if (cachedData != null) {
                log.info("成功命中通讯录标签缓存，Key: {}", cacheKey);
                // 反序列化并返回
                return objectMapper.readValue(cachedData, new TypeReference<List<TAddressBookLabelDTO>>() {});
            }
        } catch (Exception e) {
            log.error("获取或解析通讯录标签缓存失败，将执行数据库查询。Key: {}, 错误: {}", cacheKey, e.getMessage());
        }

        // 缓存未命中，执行数据库查询和业务逻辑
        log.info("未命中通讯录标签缓存，开始执行数据库查询，Key: {}", cacheKey);
        List<TAddressBookLabelDTO> resultLabels = queryTAddressBookLabelApp();

        // 将生成的结果存入缓存
        try {
            String jsonResult = objectMapper.writeValueAsString(resultLabels);
            redisUtil.set(cacheKey, jsonResult, 60, TimeUnit.MINUTES);
            log.info("已将通讯录标签结果存入缓存，Key: {}, 过期时间: {} 分钟", cacheKey, 60);
        } catch (Exception e) {
            log.error("序列化或存储通讯录标签到缓存失败，Key: {}, 错误: {}", cacheKey, e.getMessage());
        }

        return resultLabels;
    }

    /**
     * 列表查询
     *
     * @return
     */
    @Override
    public List<TAddressBookLabelDTO> queryTAddressBookLabelApp() {
        if ((SecurityUtils.isSystemName() && !SecurityUtils.isPlatformAdmin()) || SecurityUtils.isRegionAdmin()) {
            throw new BusinessException("该接口不允许租户访问！");
        }

        Long companyId = SecurityUtils.getCurrentCompanyId();
        if (companyId == null) {
            companyId = SecurityUtils.getCurrentBackupCompanyId();
        }
        if (companyId == null) {
            throw new BusinessException("该用户无访问权限！");
        }
        //查询标签列表
        //获取当前登录人的机构的orgCode取其前12位，根据这个12位可以获取名称，点击这个标签，和全省组织机构数一样，里面获取子节点了，下面的人员
        //行政区划12位      43 00000 00000
        //43 XX000 00000 14个地市   市值单位  43 XX000 00000  +  0000002
        //43 XX XX 00 0000     某个地市下面的区县  区县直属单位      43 XX XX 00 0000  +  0000002
        //3  XX XX XXX  000    某个区县下面乡镇
        List<CscpUserOrgDTO> cscpUserOrgDTOS = cscpUserOrgService.qryUserOrgByUserId(SecurityUtils.getCurrentUserId());
        List<Long> orgIds = cscpUserOrgDTOS.stream().map(CscpUserOrgDTO::getOrgId).collect(Collectors.toList());
        if (CollUtil.isEmpty(orgIds)){
            return ListUtil.empty();
        }
        List<CscpOrgDTO> cscpOrgDTOS = cscpOrgService.selectLabelOrg(orgIds);
        List<String> orgCodePaths = cscpOrgDTOS.stream().map(CscpOrgDTO::getOrgCode).collect(Collectors.toList());
        List<TAddressBookLabelDTO> tAddressBookLabelDTOS = new ArrayList<>(10);
        // 构建当前用户本单位标签
        TAddressBookLabelDTO currentTABEntity = new TAddressBookLabelDTO();
        currentTABEntity.setId(companyId);
        currentTABEntity.setSort(0);
        currentTABEntity.setDefaultLabel(1);
        currentTABEntity.setLabelName("本单位");
        currentTABEntity.setDepartmentId(companyId);
        currentTABEntity.setCompanyId(companyId);
        tAddressBookLabelDTOS.add(currentTABEntity);

        TAddressBookLabelDTO tAddressBookLabelDTO = new TAddressBookLabelDTO();
        tAddressBookLabelDTO.setSort(1);
        tAddressBookLabelDTO.setDefaultLabel(0);

        tAddressBookLabelDTO.setDepartmentId(companyId);
        tAddressBookLabelDTO.setCompanyId(companyId);
        tAddressBookLabelDTOS.add(tAddressBookLabelDTO);
        String level = OrgUtil.getLevelDescription(orgCodePaths);

        switch (level) {
            case "1":
                tAddressBookLabelDTO.setId(0L);
                tAddressBookLabelDTO.setLabelName("湖南省");
                break;
            case "2":
                CscpOrgDTO cscpOrgDTO = cscpOrgDTOS.stream().filter(req ->
                        OrgUtil.isCityLevel(req.getOrgCode())).findFirst().orElse(new CscpOrgDTO());
                this.setLabelName(cscpOrgDTO, tAddressBookLabelDTO);
                break;
            case "3":
                CscpOrgDTO DistrCscpOrgDTO = cscpOrgDTOS.stream().filter(req ->
                        OrgUtil.isDistrictLevel(req.getOrgCode())).findFirst().orElse(new CscpOrgDTO());
                this.setLabelName(DistrCscpOrgDTO, tAddressBookLabelDTO);
                break;
            case "4":
                CscpOrgDTO townOrVillagCscpOrgDTO = cscpOrgDTOS.stream().filter(req ->
                        OrgUtil.isTownLevel(req.getOrgCode())).findFirst().orElse(new CscpOrgDTO());
                this.setLabelNameTown(townOrVillagCscpOrgDTO, tAddressBookLabelDTO);
                break;
            default:
                throw new BusinessException("该用户机构编码错误！");

        }
        return tAddressBookLabelDTOS;
    }

    private void setLabelName(CscpOrgDTO cscpOrgDTO, TAddressBookLabelDTO tAddressBookLabelDTO) {
        String substring = cscpOrgDTO.getOrgCode().substring(0, 12);
        CscpOrgDTO orgDTO = cscpOrgService.selectOrgCode(substring);
        if (ObjectUtil.isNull(orgDTO)){
            throw new BusinessException("查询机构为空");
        }
        tAddressBookLabelDTO.setLabelName(orgDTO.getOrgName());
        tAddressBookLabelDTO.setId(orgDTO.getId());
    }

    private void setLabelNameTown(CscpOrgDTO cscpOrgDTO, TAddressBookLabelDTO tAddressBookLabelDTO) {
        String substring = cscpOrgDTO.getOrgCode().substring(0, 6);
        //将substring后面 补齐12位，后面补0
        String result = StrUtil.padAfter(substring, 12, '0');
        CscpOrgDTO orgDTO = cscpOrgService.selectOrgCode(result);
        if (ObjectUtil.isNull(orgDTO)){
            throw new BusinessException("查询机构为空");
        }
        tAddressBookLabelDTO.setLabelName(orgDTO.getOrgName());
        tAddressBookLabelDTO.setId(orgDTO.getId());
    }


    /**
     * 处理相同标签排序问题
     * */
    private void sortTAddressBookLabel(List<TAddressBookAuthorize> addressBookAuthorizes, List<TAddressBookLabel> tAddressBookLabels) {
        if (addressBookAuthorizes == null || tAddressBookLabels == null) {
            log.warn("参数不能为 null: addressBookAuthorizes={}, tAddressBookLabels={}",
                    addressBookAuthorizes, tAddressBookLabels);
            return;
        }
        // 1. 先按 labelId 和 id 排序
        List<TAddressBookAuthorize> sortedAuthorizes = addressBookAuthorizes.stream()
                .sorted(Comparator.comparing(TAddressBookAuthorize::getLabelId)
                        .thenComparing(TAddressBookAuthorize::getId))
                .collect(Collectors.toList());

        // 2. 遍历并计算 sort 值
        Map<Long, Integer> labelIdToLastSort = new HashMap<>();  // 记录每个 labelId 最后分配的 sort 值
        Map<Long, Integer> authorizeIdToSort = new HashMap<>();  // 存储最终的 sort 值

        sortedAuthorizes.forEach(authorize -> {
            Long labelId = authorize.getLabelId();
            int baseSort = authorize.getSort() != null ? authorize.getSort() : 9999;

            // 如果 labelId 已存在，则在前一个 sort 基础上 +1
            if (labelIdToLastSort.containsKey(labelId)) {
                baseSort = labelIdToLastSort.get(labelId) + 1;
            }

            labelIdToLastSort.put(labelId, baseSort);
            authorizeIdToSort.put(authorize.getId(), baseSort);
        });

        // 3. 设置标签的排序值
        tAddressBookLabels.forEach(label -> {
            label.setSort(authorizeIdToSort.getOrDefault(label.getId(), 9999));
        });
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TAddressBookLabelDTO create(TAddressBookLabelDTO entityDTO) {
        //处理排序
        //tAddressBookLabelMapper.updataSort(SortEnum.builder().sort(entityDTO.getSort()).tableName("t_address_book_label").sortName("SORT").additionOrsubtraction("+").build());

        TAddressBookLabel tAddressBookLabel = BeanConvertUtils.copyProperties(entityDTO, TAddressBookLabel.class);
        tAddressBookLabel.setSort(2);
        save(tAddressBookLabel);

        //如果是单位管理员，并且不能是区划管理员填充默认数据
        if (SecurityUtils.isGeneralName() && !SecurityUtils.isRegionAdmin()) {
            fillRelationData(entityDTO);
        }

        //标签关联机构
        if (!Objects.isNull(entityDTO.getLabelOrgList())) {
            //List<TLabelOrgDTO> tLabelOrgDTOS = filterUnitOrgLabelList(entityDTO.getLabelOrgList());
            entityDTO.getLabelOrgList().forEach(i -> {
                i.setDisplayRangeId(tAddressBookLabel.getId());
                i.setDisplayRangeName(entityDTO.getLabelName());
            });
            //entityDTO.setLabelOrgList(tLabelOrgDTOS);
        }
        addLabelRelationOrgService(entityDTO.getLabelOrgList());

        //标签授权机构
        if (!Objects.isNull(entityDTO.getAddressBookAuthorizeList())) {
            // 只保留单位数据
            //List<TAddressBookAuthorizeDTO> authorizeDTOList = filterUnitOrgAuthorizList(entityDTO.getAddressBookAuthorizeList());
            entityDTO.getAddressBookAuthorizeList().forEach(i -> {
                i.setLabelId(tAddressBookLabel.getId());
                i.setLabelName(entityDTO.getLabelName());
                i.setSort(tAddressBookLabel.getSort());
            });
            //entityDTO.setAddressBookAuthorizeList(authorizeDTOList);
        }
        addLabelAuthorizeOrgService(entityDTO.getAddressBookAuthorizeList(), entityDTO.getLabelName());

        TAddressBookLabelDTO tAddressBookLabelDTO = BeanConvertUtils.copyProperties(tAddressBookLabel, TAddressBookLabelDTO.class);
        tAddressBookLabelDTO.setAddressBookAuthorizeList(entityDTO.getAddressBookAuthorizeList());
        tAddressBookLabelDTO.setLabelOrgList(entityDTO.getLabelOrgList());
        return tAddressBookLabelDTO;
    }

    /**
     * 只保留单位数据
     * */
    private List<TAddressBookAuthorizeDTO> filterUnitOrgAuthorizList(List<TAddressBookAuthorizeDTO> addressBookAuthorizeList) {
        if (CollUtil.isEmpty(addressBookAuthorizeList)) {
            return Collections.emptyList();
        }

        // 获取所有orgId
        List<Long> idList = addressBookAuthorizeList.stream()
                .map(TAddressBookAuthorizeDTO::getUnitId)
                .collect(Collectors.toList());

        // 查询符合条件的CscpOrg记录
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectList(new LambdaQueryWrapper<CscpOrg>()
                .eq(CscpOrg::getType, 2)
                .in(CscpOrg::getId, idList));

        // 获取cscpOrgList中存在的id集合
        Set<Long> validOrgIds = cscpOrgList.stream()
                .map(CscpOrg::getId)
                .collect(Collectors.toSet());

        // 过滤labelOrgList，只保留orgId存在于cscpOrgList中的项
        return addressBookAuthorizeList.stream()
                .filter(dto -> validOrgIds.contains(dto.getUnitId()))
                .collect(Collectors.toList());
    }


    private List<TLabelOrgDTO> filterUnitOrgLabelList(List<TLabelOrgDTO> labelOrgList) {
        if (CollUtil.isEmpty(labelOrgList)) {
            return Collections.emptyList();
        }

        // 获取所有orgId
        List<Long> idList = labelOrgList.stream()
                .map(TLabelOrgDTO::getOrgId)
                .collect(Collectors.toList());

        // 查询符合条件的CscpOrg记录
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectList(new LambdaQueryWrapper<CscpOrg>()
                .eq(CscpOrg::getType, 2)
                .in(CscpOrg::getId, idList));

        // 获取cscpOrgList中存在的id集合
        Set<Long> validOrgIds = cscpOrgList.stream()
                .map(CscpOrg::getId)
                .collect(Collectors.toSet());

        // 过滤labelOrgList，只保留orgId存在于cscpOrgList中的项
        return labelOrgList.stream()
                .filter(dto -> validOrgIds.contains(dto.getOrgId()))
                .collect(Collectors.toList());
    }



    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TAddressBookLabelDTO crmCreate(TAddressBookLabelDTO entityDTO, TSysTenant tSysTenant) {
        //处理排序
        //tAddressBookLabelMapper.updataSort(SortEnum.builder().sort(entityDTO.getSort()).tableName("t_address_book_label").sortName("SORT").additionOrsubtraction("+").build());

        TAddressBookLabel tAddressBookLabel = BeanConvertUtils.copyProperties(entityDTO, TAddressBookLabel.class);
        tAddressBookLabel.setCreateBy(tSysTenant.getTenantLoginId());
        tAddressBookLabel.setTenantId(tSysTenant.getId());
        tAddressBookLabel.setSort(0);
        save(tAddressBookLabel);

        //如果是单位管理员填充默认数据
//        if (SecurityUtils.isGeneralName()) {
//            fillRelationData(entityDTO);
//        }

        //标签关联机构
        if (!Objects.isNull(entityDTO.getLabelOrgList())) {
            entityDTO.getLabelOrgList().forEach(i -> i.setDisplayRangeId(tAddressBookLabel.getId()));
        }
        crmAddLabelRelationOrgService(entityDTO.getLabelOrgList(),tSysTenant);

        //标签授权机构
        if (!Objects.isNull(entityDTO.getAddressBookAuthorizeList())) {
            entityDTO.getAddressBookAuthorizeList().forEach(i -> {
                i.setLabelId(tAddressBookLabel.getId());
                i.setSort(tAddressBookLabel.getSort());
            });
        }
        crmAddLabelAuthorizeOrgService(entityDTO.getAddressBookAuthorizeList(),tSysTenant);

        TAddressBookLabelDTO tAddressBookLabelDTO = BeanConvertUtils.copyProperties(tAddressBookLabel, TAddressBookLabelDTO.class);
        tAddressBookLabelDTO.setAddressBookAuthorizeList(entityDTO.getAddressBookAuthorizeList());
        tAddressBookLabelDTO.setLabelOrgList(entityDTO.getLabelOrgList());
        return tAddressBookLabelDTO;
    }

    /**
     * 标签关联机构
     *
     * @param labelOrgList
     * @return
     */
    public boolean addLabelRelationOrgService(List<TLabelOrgDTO> labelOrgList) {
        if (!Objects.isNull(labelOrgList) && !labelOrgList.isEmpty()) {
            //1.标签关联机构
            List<TLabelOrg> l1 = ListCopyUtil.copy(labelOrgList, TLabelOrg.class);
            return itLabelOrgService.saveBatch(l1);
        }
        return false;
    }
    public boolean crmAddLabelRelationOrgService(List<TLabelOrgDTO> labelOrgList,TSysTenant tSysTenant) {
        if (!Objects.isNull(labelOrgList) && !labelOrgList.isEmpty()) {
            //1.标签关联机构
            List<TLabelOrg> l1 = ListCopyUtil.copy(labelOrgList, TLabelOrg.class);
            List<TLabelOrg> tLabelOrg = new ArrayList<>();
            for(TLabelOrg ent : l1){
                ent.setCreateBy(tSysTenant.getTenantLoginId());
                ent.setTenantId(tSysTenant.getId());
                tLabelOrg.add(ent);
            }
            return itLabelOrgService.saveBatch(tLabelOrg);
        }
        return false;
    }

    /**
     * 标签授权机构
     *
     * @param addressBookAuthorizeList
     * @return
     */
    public boolean addLabelAuthorizeOrgService(List<TAddressBookAuthorizeDTO> addressBookAuthorizeList, String labelName) {
        //新增该授权标签，排序号追加在单位已有标签之后
        if (!Objects.isNull(addressBookAuthorizeList) && !addressBookAuthorizeList.isEmpty() && (!SecurityUtils.isGeneralName() || SecurityUtils.isRegionAdmin())) {
            //获取所有授权单位id
            List<Long> unitIdList = addressBookAuthorizeList.stream().map(i -> i.getUnitId()).collect(Collectors.toList());
            //查询这些授权单位id所有的数据
            List<TAddressBookAuthorize> labelSortMax = tAddressBookAuthorizeMapper.selectListNoAdd(
                    new LambdaQueryWrapper<TAddressBookAuthorize>().in(TAddressBookAuthorize::getUnitId, unitIdList));
            //不等于空给每个单位分组，并获取最大值
            if (!labelSortMax.isEmpty()) {
                Map<Long, List<TAddressBookAuthorize>> abaGroup = labelSortMax.stream().collect(Collectors.groupingBy(TAddressBookAuthorize::getUnitId));
                addressBookAuthorizeList.forEach(i -> {
                    if(StrUtil.isNotBlank(labelName)){
                        i.setLabelName(labelName);
                    }
                    List<TAddressBookAuthorize> addressBookAuthorizes = abaGroup.get(i.getUnitId());
                    if (!Objects.isNull(addressBookAuthorizes)) {
                        List<Integer> sortList = addressBookAuthorizes.stream().map(k -> k.getSort()).collect(Collectors.toList());
                        sortList.removeAll(Collections.singleton(null));

                        if (!sortList.isEmpty()) {
                            //获取这个标签的所有排序号中最大的那个
                            Integer reduce = sortList.stream().reduce(sortList.get(0), Integer::max);
                            i.setSort(reduce + 2);
                        }
                    }
                });
            }
        }
        //如果是普通用户添加，需要修改本单位的排序号
        if (SecurityUtils.isGeneralName()) {
            //出现一样的排序好就把大于等于的排序号都加一
            Integer count = tAddressBookAuthorizeMapper.selectCountNoAdd(
                    new LambdaQueryWrapper<TAddressBookAuthorize>()
                            .select(TAddressBookAuthorize::getId)
                            .eq(TAddressBookAuthorize::getUnitId, SecurityUtils.getCurrentCompanyId())
                            .eq(TAddressBookAuthorize::getSort, addressBookAuthorizeList.get(0).getSort())
            );
            if (count > 0) {
                tAddressBookAuthorizeMapper.updataSort(SortEnum.builder()
                        .tableName("t_address_book_authorize")
                        .sortName("sort")
                        .sort(addressBookAuthorizeList.get(0).getSort())
                        .additionOrsubtraction("+")
                        .unitId(SecurityUtils.getCurrentCompanyId())
                        .build()
                );
            }
        }

        //2.标签授权单位
        List<TAddressBookAuthorize> l1 = ListCopyUtil.copy(addressBookAuthorizeList, TAddressBookAuthorize.class);
        return itAddressBookAuthorizeService.saveBatch(l1);
    }

    public boolean crmAddLabelAuthorizeOrgService(List<TAddressBookAuthorizeDTO> addressBookAuthorizeList,TSysTenant tSysTenant) {
        //新增该授权标签，排序号追加在单位已有标签之后
        if (!Objects.isNull(addressBookAuthorizeList) && !addressBookAuthorizeList.isEmpty()) {
            //获取所有授权单位id,过滤掉本单位标签
            List<Long> unitIdList = addressBookAuthorizeList.stream().filter(i -> !StrUtil.equals(i.getLabelName(),"本单位")).map(TAddressBookAuthorizeDTO::getUnitId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(unitIdList)){
                //查询这些授权单位id所有的数据
                List<TAddressBookAuthorize> labelSortMax = tAddressBookAuthorizeMapper.selectListNoAdd(
                        new LambdaQueryWrapper<TAddressBookAuthorize>().in(TAddressBookAuthorize::getUnitId, unitIdList));
                //不等于空给每个单位分组，并获取最大值
                if (!labelSortMax.isEmpty()) {
                    Map<Long, List<TAddressBookAuthorize>> abaGroup = labelSortMax.stream().collect(Collectors.groupingBy(TAddressBookAuthorize::getUnitId));
                    addressBookAuthorizeList.forEach(i -> {

                        List<TAddressBookAuthorize> addressBookAuthorizes = abaGroup.get(i.getUnitId());
                        if (!Objects.isNull(addressBookAuthorizes)) {
                            List<Integer> sortList = addressBookAuthorizes.stream().map(k -> k.getSort()).collect(Collectors.toList());
                            sortList.removeAll(Collections.singleton(null));

                            if (!sortList.isEmpty()) {
                                //获取这个标签的所有排序号中最大的那个
                                Integer reduce = sortList.stream().reduce(sortList.get(0), Integer::max);
                                i.setSort(reduce + 1);
                            }
                        }
                    });
                }
            }
        }

        //2.标签授权单位
        List<TAddressBookAuthorize> l1 = ListCopyUtil.copy(addressBookAuthorizeList, TAddressBookAuthorize.class);
        List<TAddressBookAuthorize> tAddressBookAuthorize = new ArrayList<>();
        for(TAddressBookAuthorize ent : l1){
            ent.setCreateBy(tSysTenant.getTenantLoginId());
            tAddressBookAuthorize.add(ent);
        }

        return itAddressBookAuthorizeService.saveBatch(tAddressBookAuthorize);
    }

    /**
     * 单位管理员新增标签默认授权给本单位
     *
     * @param addressBookLabel
     */
    public void fillRelationData(TAddressBookLabelDTO addressBookLabel) {
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        //授权单位
        List<TAddressBookAuthorizeDTO> addressBookAuthorizeList = new LinkedList<>();
        TAddressBookAuthorizeDTO build = TAddressBookAuthorizeDTO.builder()
                .labelName(addressBookLabel.getLabelName())
                .unitId(currentCscpUserDetail.getCompanyId())
                .unitName(currentCscpUserDetail.getCompanyName()).build();
        addressBookAuthorizeList.add(build);
        addressBookLabel.setAddressBookAuthorizeList(addressBookAuthorizeList);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TAddressBookLabelDTO entity) {
        // 删除缓存的标签关联机构
        CompletableFuture.runAsync(() -> {
            try {
                // 删除标签关联机构的缓存
                String cacheKey = "sys:label:org:parent:" + entity.getId();
                redisUtil.del(cacheKey);
            } catch (Exception e) {
                log.error("异步删除APP标签关联机构缓存失败", e);
            }
        });

        TAddressBookLabel t1 = tAddressBookLabelMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TAddressBookLabel>()
                        .eq(TAddressBookLabel::getId, entity.getId())
        );
        TAddressBookLabel tAddressBookLabel = BeanConvertUtils.copyProperties(entity, TAddressBookLabel.class);

        //管理员，普通用户编辑逻辑
        if (SecurityUtils.isGeneralName() && !SecurityUtils.isRegionAdmin()) {
            //如果是自己建的修改标签信息
            if (!this.isTenantName(BeanConvertUtils.copyProperties(t1, TAddressBookLabelDTO.class))) {
                tAddressBookLabelMapper.updateById(tAddressBookLabel);
            }

            //修改标签排序
            TAddressBookAuthorize t2 = new TAddressBookAuthorize();
            t2.setSort(entity.getSort());


            //判断是否需要修改排序
            TAddressBookAuthorize tAddressBookAuthorize = tAddressBookAuthorizeMapper.selectOneNoAdd(new LambdaQueryWrapper<TAddressBookAuthorize>()
                    .eq(TAddressBookAuthorize::getUnitId, SecurityUtils.getCurrentCompanyId())
                    .eq(TAddressBookAuthorize::getLabelId, entity.getId())
            );
            Boolean b = !Objects.isNull(tAddressBookAuthorize) && !Objects.isNull(tAddressBookAuthorize.getSort()) && !tAddressBookAuthorize.getSort().equals(entity.getSort());
            if (b) {
                tAddressBookAuthorizeMapper.updataSort(SortEnum.builder()
                        .tableName("t_address_book_authorize")
                        .sortName("sort")
                        .sort(entity.getSort())
                        .additionOrsubtraction("+")
                        .unitId(SecurityUtils.getCurrentCompanyId())
                        .build()
                );
            }

            return tAddressBookAuthorizeMapper.updateTenantId(t2,
                    new LambdaQueryWrapper<TAddressBookAuthorize>()
                            .eq(TAddressBookAuthorize::getLabelId, entity.getId())
                            .eq(TAddressBookAuthorize::getUnitId, SecurityUtils.getCurrentCompanyId())
            );

        }

        //admin编辑逻辑 区划管理员
        if (SecurityUtils.isSystemName() || SecurityUtils.isRegionAdmin()) {
            //删除之前关联的机构
            tLabelOrgMapperl.deleteRelationData(entity.getId());
            List<TLabelOrgDTO> labelOrgList = entity.getLabelOrgList();
            labelOrgList.forEach(i -> {
                i.setDisplayRangeId(entity.getId());
                i.setId(null);
            });
            itLabelOrgService.saveBatch(ListCopyUtil.copy(labelOrgList, TLabelOrg.class));

            //删除之前授权的机构
            tAddressBookAuthorizeMapper.deleteRelationData(entity.getId());
            List<TAddressBookAuthorizeDTO> addressBookAuthorizeList = entity.getAddressBookAuthorizeList();
            addressBookAuthorizeList.forEach(i -> {
                i.setLabelId(entity.getId());
                i.setId(null);
            });
            this.addLabelAuthorizeOrgService(addressBookAuthorizeList, entity.getLabelName());

            //修改通讯录中关联了这个标签的数据
            List<TAddressBook> tAddressBooks = tAddressBookMapper.selectListNoAdd(
                    new LambdaQueryWrapper<TAddressBook>()
                            .select(TAddressBook::getUserId, TAddressBook::getId, TAddressBook::getAddressBookLableId)
                            .like(TAddressBook::getAddressBookLableId, entity.getId())
            );
            if (!tAddressBooks.isEmpty()) {
                this.selectUpdateData(entity.getAddressBookAuthorizeList(), tAddressBooks, entity.getId());
            }

            //判断是否需要修改排序
            TAddressBookLabel tAddressBookLabel1 = tAddressBookLabelMapper.selectOneNoAdd(new LambdaQueryWrapper<TAddressBookLabel>().select(TAddressBookLabel::getSort).eq(TAddressBookLabel::getId, entity.getId()));
            Boolean b = !Objects.isNull(tAddressBookLabel1) && !Objects.isNull(tAddressBookLabel1.getSort()) && !tAddressBookLabel1.getSort().equals(entity.getSort());
            if (b) {
                //tAddressBookLabelMapper.updataSort(SortEnum.builder()
                //        .tableName("t_address_book_label")
                //        .sortName("sort")
                //        .sort(entity.getSort())
                //        .additionOrsubtraction("+")
                //        .createBy(SecurityUtils.getCurrentUserId())
                //        .build()
                //);
            }

            int result = tAddressBookLabelMapper.updateById(tAddressBookLabel);
            // 异步更新缓存
            asyncUpdateCache(entity.getId());
            return result;

        }
        return 0;
    }

    // 异步更新缓存的方法
    private void asyncUpdateCache(Long labelId) {
        CompletableFuture.runAsync(() -> {
            try {
                // 重新查询数据并更新缓存
                List<CscpOrgDTO> orgList = this.queryTaddressBookLabelOrgParent(labelId);
                // 这里queryTaddressBookLabelOrgParent方法已经包含了缓存逻辑
                // 所以只需要调用它就会自动更新缓存
            } catch (Exception e) {
                log.error("异步更新APP标签关联机构缓存失败", e);
            }
        });
    }

    /**
     * 修改通讯录可视范围
     *
     * @param addressBooks
     * @param labelId
     */
    public void updateTaddressBookLabelId(List<TAddressBook> addressBooks, Long labelId) {
        addressBooks.forEach(i -> {
            i.setAddressBookLableId(i.getAddressBookLableId().replace(String.valueOf(labelId), ""));
            if (i.getAddressBookLableId().charAt(i.getAddressBookLableId().length() - 1) == ',') {
                i.setAddressBookLableId(i.getAddressBookLableId().substring(0, i.getAddressBookLableId().length() - 1));
            }
            i.setAddressBookLableId(i.getAddressBookLableId().replace(",,", ","));
        });
        itAddressBookService.saveOrUpdateBatch(addressBooks);
    }

    /**
     * 查询那些通讯录的可视范围需要修改
     *
     * @param addressBookAuthorizeList
     * @param tAddressBooks
     * @param labelId
     */
    public void selectUpdateData(List<TAddressBookAuthorizeDTO> addressBookAuthorizeList, List<TAddressBook> tAddressBooks, Long labelId) {
        //如果授权单位为空把通讯录中包含该标签的id全部清除
        if (addressBookAuthorizeList.isEmpty()) {
            this.updateTaddressBookLabelId(tAddressBooks, labelId);
        } else {
            List<Long> collect = addressBookAuthorizeList.stream().map(i -> i.getUnitId()).collect(Collectors.toList());
            List<CscpUserOrg> userOrgList = cscpUserOrgRepository.selectListNoAdd(
                    new LambdaQueryWrapper<CscpUserOrg>().in(CscpUserOrg::getCompanyId, collect)
            );
            Set<Long> u1 = userOrgList.stream().map(i -> i.getUserId()).collect(Collectors.toSet());
            List<TAddressBook> tAddressBookList = tAddressBooks.stream().filter(i -> !u1.contains(i.getUserId())).collect(Collectors.toList());
            if (!tAddressBookList.isEmpty()) {
                this.updateTaddressBookLabelId(tAddressBookList, labelId);
            }
        }
    }

    /**
     * 删除标签
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        //单位管理员不能删除租户授权的标签。
        TAddressBookLabel tAddressBookLabel = tAddressBookLabelMapper.selectOneNoAdd(new LambdaQueryWrapper<TAddressBookLabel>().eq(TAddressBookLabel::getId, id));
        if (this.isTenantName(BeanConvertUtils.copyProperties(tAddressBookLabel, TAddressBookLabelDTO.class))
                && SecurityUtils.isGeneralName() && !SecurityUtils.isRegionAdmin()) {
            throw new BusinessException("该标签单位管理员无法删除");
        }

        //删除对应的标签
        int addressBookLabelCount = tAddressBookLabelMapper.deleteById(id);

        //删除标签同时也要把通讯录的可视范围对应的标签id也删除
        List<TAddressBook> collect = tAddressBookMapper
                .selectList(new LambdaQueryWrapper<TAddressBook>()
                        .like(TAddressBook::getAddressBookLableId, id)
                ).stream().map(i -> {
                    //将可视范围中对应的标签id替换为空
                    i.setAddressBookLableId(i.getAddressBookLableId().replace(String.valueOf(id), ""));
                    //去除结尾可能出现，的问题
                    if (i.getAddressBookLableId().endsWith(CHARACTER)) {
                        i.setAddressBookLableId(i.getAddressBookLableId().substring(0, i.getAddressBookLableId().length() - 1));
                    }
                    i.setAddressBookLableId(i.getAddressBookLableId().replace(",,", ","));
                    return i;
                }).collect(Collectors.toList());
        //批量修改
        tAddressBookService.updateBatchById(collect);

        //删除授权的单位
        tAddressBookAuthorizeMapper.delete(new LambdaQueryWrapper<TAddressBookAuthorize>().eq(TAddressBookAuthorize::getLabelId, id));
        //删除关联单位
        tLabelOrgMapperl.delete(new LambdaQueryWrapper<TLabelOrg>().eq(TLabelOrg::getDisplayRangeId, id));
        // 删除缓存的标签关联机构
        CompletableFuture.runAsync(() -> {
            try {
                // 删除标签关联机构的缓存
                String cacheKey = "sys:label:org:parent:" + id;
                redisUtil.del(cacheKey);
            } catch (Exception e) {
                log.error("异步删除APP标签缓存数据", e);
            }
        });
        return 0 < addressBookLabelCount ? 1 : 0;
    }

    /**
     * 查询标签对应的机构信息
     *
     * @param labelId
     * @return
     */
    @Override
    public List<CscpOrgDTO> queryTaddressBookLabelOrgParent(Long labelId) {
        //由于机构都是默认摄入的数据，不需要直接通过标签id（顶级机构id）查询子集
        // 0 本单位标签直接返回结果
        if (SecurityUtils.isGeneralName() && labelId.equals(SecurityUtils.getCurrentCompanyId())) {
            List<CscpUserOrgDTO> cscpUserOrgDTOS = cscpUserOrgService.qryUserOrgByUserId(SecurityUtils.getCurrentUserId());
            if (CollUtil.isNotEmpty(cscpUserOrgDTOS) && cscpUserOrgDTOS.size() != 1){
                // 获取所有单位ID
                List<Long> orgIds = cscpUserOrgDTOS.stream()
                        .map(CscpUserOrgDTO::getCompanyId)
                        .collect(Collectors.toList());
                // 检查所有ID是否相同
                boolean allIdsSame = orgIds.stream()
                        .allMatch(id -> id.equals(orgIds.get(0)));
                if (!allIdsSame){
                    //根据path_code排序
                    cscpUserOrgDTOS.sort(Comparator.comparing(CscpUserOrgDTO::getOrderBy));
                    return cscpUserOrgDTOS.stream().map(req -> {
                        CscpOrg cscpOrg = cscpOrgRepository.selectById(req.getCompanyId());
                        CscpOrgDTO orgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
                        if(StrUtil.isNotBlank(orgDTO.getAliasName())){
                            orgDTO.setOrgName(orgDTO.getAliasName());
                        }
                        orgDTO.setIsValuable(1);
                        return orgDTO;
                    }).collect(Collectors.toList());
                }
            }

            CscpOrg cscpOrg = cscpOrgRepository.selectById(SecurityUtils.getCurrentCompanyId());
            CscpOrgDTO orgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
            if(StrUtil.isNotBlank(orgDTO.getAliasName())){
                orgDTO.setOrgName(orgDTO.getAliasName());
            }
            orgDTO.setIsValuable(1);
            return Collections.singletonList(orgDTO);
        }
        return cscpOrgService.selectOrgByParentIdAddressBook(labelId);
    }

    /**
     * 根据指定标签id查询对应的子机机构信息
     *
     * @param labelId
     * @param orgId
     * @return
     */
    @Override
    public List<CscpOrgDTO> queryTaddressBookLabelOrgChildren(Long labelId, Long orgId) {
        // 创建线程池用于异步任务
        ExecutorService executor = Executors.newFixedThreadPool(
                Runtime.getRuntime().availableProcessors() // 可根据需要调整线程池大小
        );

        try {
            // 查询标签ID列表（同步执行，因为通常较快）
            List<Long> labelIdList = this.queryTAddressBookLabelApp().stream()
                    .map(i -> i.getId())
                    .collect(Collectors.toList());

            // 判断用户是否有预览权限
            if (labelIdList.contains(labelId)) {
                // 异步查询子机构列表
                CompletableFuture<List<CscpOrgDTO>> tLabelOrgListFuture = CompletableFuture.supplyAsync(() ->
                        cscpOrgService.selectSubOrgList(orgId, executor), executor);

                // 创建映射（同步执行，因为是CPU密集型且快速）
                //CompletableFuture<Map<Long, Integer>> collectFuture = tLabelOrgListFuture.thenApply(tLabelOrgList ->
                //        tLabelOrgList.stream()
                //                .filter(i -> !Objects.isNull(i.getOrderBy()))
                //                .collect(Collectors.toMap(
                //                        CscpOrgDTO::getId,
                //                        CscpOrgDTO::getOrderBy,
                //                        (k1, k2) -> k1,
                //                        LinkedHashMap::new
                //                ))
                //);

                // 异步查询机构信息并处理
                //CompletableFuture<List<CscpOrgDTO>> processedListFuture = tLabelOrgListFuture.thenCompose(tLabelOrgList -> {
                //    List<Long> orgIds = tLabelOrgList.stream()
                //            .map(CscpOrgDTO::getId)
                //            .collect(Collectors.toList());
                //
                //    // 异步查询机构信息
                //    return CompletableFuture.supplyAsync(() ->
                //            cscpOrgService.selectLabelOrg(orgIds), executor
                //    )
                CompletableFuture<List<CscpOrgDTO>> processedListFuture =  tLabelOrgListFuture.thenCompose(orgList -> {
                    // 为每个CscpOrgDTO创建异步任务
                    List<CompletableFuture<CscpOrgDTO>> futures = orgList.stream().map(dto ->
                            CompletableFuture.supplyAsync(() -> {
                                // 异步检查下级机构
                                List<TLabelOrg> labList = cscpOrgService.selectParentIdOrg(dto.getId(), labelId);
                                if (labList != null && !labList.isEmpty()) {
                                    if (dto.getType() == 3) {
                                        dto.setIsValuable(2);
                                    } else {
                                        dto.setIsValuable(1);
                                    }
                                } else {
                                    dto.setIsValuable(0);
                                }
                                // 异步获取collect映射（确保已完成）
                                //dto.setOrderBy(collectFuture.join().get(dto.getId()));
                                return dto;
                            }, executor)
                    ).collect(Collectors.toList());

                    // 合并所有异步任务
                    return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                            .thenApply(v -> futures.stream()
                                    .map(CompletableFuture::join)
                                    .collect(Collectors.toList()));
                });
                //});

                // 异步处理人员计数
                CompletableFuture<List<CscpOrgDTO>> finalListFuture = processedListFuture.thenCompose(list -> {
                    List<CompletableFuture<Void>> countFutures = list.stream().map(dto ->
                            CompletableFuture.runAsync(() -> {
                                if (ObjectUtil.equals(dto.getType(), 1)) {
                                    // 异步获取虚拟机构下的单位ID
                                    List<CscpOrg> ids = cscpOrgRepository.selectListNoAdd(
                                            new LambdaQueryWrapper<CscpOrg>()
                                                    .select(CscpOrg::getId)
                                                    .eq(CscpOrg::getParentId, dto.getId())
                                    );
                                    List<Long> idLong = ids.stream()
                                            .map(id -> id.getId())
                                            .collect(Collectors.toList());

                                    // 异步获取标签关联的单位列表
                                    List<Long> labelList = cscpOrgRepository.getLabelList(labelId);

                                    // 过滤匹配的ID
                                    List<Long> resultList = labelList.stream()
                                            .filter(item -> idLong.contains(item))
                                            .collect(Collectors.toList());

                                    // 异步统计人员数量
                                    Integer count = cscpOrgRepository.countParentId(dto.getId(), resultList);
                                    dto.setPersonCount(count);
                                } else if (ObjectUtil.equals(dto.getType(), 2)) {
                                    Integer count = cscpOrgRepository.countUserByCompany(dto.getId());
                                    dto.setPersonCount(count);
                                } else if (ObjectUtil.equals(dto.getType(), 3)) {
                                    Integer subDeptOrgCount = cscpOrgRepository.countUserByParentId(dto.getId());
                                    Integer count = cscpOrgRepository.countUserByOrg(dto.getId());
                                    dto.setPersonCount(count+subDeptOrgCount);
                                }
                            }, executor)
                    ).collect(Collectors.toList());

                    // 等待所有计数操作完成
                    return CompletableFuture.allOf(countFutures.toArray(new CompletableFuture[0]))
                            .thenApply(v -> list);
                });

                // 获取最终结果并排序
                List<CscpOrgDTO> list = finalListFuture.join();
                //Map<Long, Integer> collect = collectFuture.join();
                //
                //if (!collect.isEmpty()) {
                //    List<CscpOrgDTO> l1 = list.stream()
                //            .filter(i -> Objects.isNull(i.getOrderBy()))
                //            .collect(Collectors.toList());
                //    List<CscpOrgDTO> l2 = list.stream()
                //            .filter(i -> !Objects.isNull(i.getOrderBy()))
                //            .sorted(Comparator.comparing(CscpOrgDTO::getOrderBy))
                //            .collect(Collectors.toList());
                //    l2.addAll(l1);
                //    return l2;
                //}

                return list;
            } else {
                throw new BusinessException("你没有预览该标签下数据的权限");
            }
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 根据指定标签id查询对应的子机机构信息和人员信息
     *
     * @param labelId
     * @param orgId
     * @return
     */
    @Override
    public TAddressOrgUserDTO queryTaddressBookLabelOrgUserChildren(Long labelId, Long orgId) {
//        // 判断是否为本单位
//        boolean isCurrentCompany = ObjectUtil.equal(SecurityUtils.getCurrentCompanyId(), labelId);
//
//        // 如果不是本单位，才尝试从缓存获取数据
//        if (!isCurrentCompany) {
//            // 构造缓存key
//            String cacheKey = "sys:address:label:org:" + labelId + ":" + orgId;
//            // 尝试从缓存获取数据
//            String cachedData = (String) redisUtil.get(cacheKey);
//            if (StrUtil.isNotEmpty(cachedData)) {
//                try {
//                    // 解压缩并解析缓存数据
//                    String uncompressedJson = GzipUtil.uncompress(cachedData);
//                    return JSON.parseObject(uncompressedJson, TAddressOrgUserDTO.class);
//                } catch (Exception e) {
//                    log.error("解析缓存数据失败，将重新查询", e);
//                }
//            }
//        }

        TAddressOrgUserDTO dto = new TAddressOrgUserDTO();
        //只查询部门下面的用户
        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(orgId);
        List<TAddressBookUserDTO> tAddressBookUserDTOS = new ArrayList<>();
        if (ObjectUtil.equals(cscpOrgDTO.getType(), 3)){
            tAddressBookUserDTOS = tAddressBookMapper.selectCurrentOrgTAddressBookList(orgId);
        }
        this.dealTAddressBookList(tAddressBookUserDTOS);
        dto.setCscpOrgDTOList(cscpOrgService.selectOrgByParentIdAddressBook(orgId));
        dto.setCurrentOrgUserList(tAddressBookUserDTOS);
        return dto;


//        // 查询标签ID列表
//        List<Long> labelIdList = this.queryTAddressBookLabelApp().stream().map(i -> i.getId()).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(labelIdList)) {
//            // 如果标签ID列表中不包含传入的labelId，则抛出异常
//            if (!labelIdList.contains(labelId)) {
//                throw new BusinessException("你没有预览" + labelId + "该标签下数据的权限");
//            }
//
//            try {
//                Long currentUnitLabel;
//                // 判断是否为本单位标签
//                if (labelId.equals(SecurityUtils.getCurrentCompanyId())) {
//                    // 如果是本单位标签，则不需要过滤机构
//                    currentUnitLabel = null;
//                } else {
//                    // 非本单位标签需要去除未勾选的机构
//                    currentUnitLabel = labelId;
//                }
//
//                // 异步查询子机构列表
//                CompletableFuture<List<CscpOrgDTO>> tLabelOrgListFuture = CompletableFuture.supplyAsync(() ->
//                        cscpOrgService.selectSubOrgList(orgId,currentUnitLabel),  EXECUTOR);
//
//                // 异步查询本级机构下的人员
//                CompletableFuture<List<TAddressBookUserDTO>> tAddressBookListFuture = CompletableFuture.supplyAsync(
//                        () -> tAddressBookMapper.selectCurrentOrgTAddressBookList(orgId), EXECUTOR);
//                //在查询完成后，异步处理人员列表（依赖 tAddressBookListFuture 的结果）
//                CompletableFuture<Void> dealAddressBookFuture = tAddressBookListFuture.thenAcceptAsync(
//                       list -> this.dealTAddressBookList(list), EXECUTOR);
//
//                // 统计机构下的人数
//                //CompletableFuture<List<CscpOrgDTO>> finalListFuture = getListCompletableFuture(labelId, processedListFuture, executor);
//
//                // 等待所有异步任务完成并组合结果
//                TAddressOrgUserDTO result = CompletableFuture.allOf(tLabelOrgListFuture, tAddressBookListFuture, dealAddressBookFuture)
//                        .thenApplyAsync(v -> {
//                            try {
//                                dto.setCscpOrgDTOList(tLabelOrgListFuture.get(30, TimeUnit.SECONDS));
//                                dto.setCurrentOrgUserList(tAddressBookListFuture.get(30, TimeUnit.SECONDS));
//                                return dto;
//                            } catch (InterruptedException e) {
//                                Thread.currentThread().interrupt();
//                                throw new CompletionException(e);
//                            } catch (TimeoutException | ExecutionException e) {
//                                throw new CompletionException(e);
//                            }
//                        }, EXECUTOR)
//                        .exceptionally(throwable -> {
//                            log.error("查询机构人员数据失败", throwable);
//                            throw new BusinessException("系统繁忙，请稍后重试");
//                        }).join(); // 最后调用 join() 获取结果
//
//                // 只有在非本单位的情况下才缓存结果
//                if (!isCurrentCompany) {
//                    // 将结果存入缓存，设置10小时过期时间
//                    try {
//                        String cacheKey = RedisKeyConstant.CACHE_SYS_ADDRESS_LABEL_ORG + labelId + ":" + orgId;
//                        ObjectMapper objectMapper = new ObjectMapper();
//                        String value = objectMapper.writeValueAsString(result);
//                        String compressedJson = GzipUtil.compress(value);
//                        redisUtil.set(cacheKey, compressedJson, 5, TimeUnit.MINUTES);
//                    } catch (Exception e) {
//                        log.error("数据压缩或缓存失败", e);
//                    }
//                }
//
//                return result;
//            } catch (Exception e) {
//                log.error("查询机构人员数据失败", e);
//                throw new BusinessException("系统繁忙，请稍后重试");
//            }
//        } else {
//           return dto;
//        }
    }

    private void dealTAddressBookList(List<TAddressBookUserDTO> tAddressBookOrgUserDTOList) {
        if(CollectionUtil.isNotEmpty(tAddressBookOrgUserDTOList)) {
            tAddressBookOrgUserDTOList.stream().forEach(addressBook -> {
                addressBook.setRealName(KeyCenterUtils.decrypt(addressBook.getRealName()));
                addressBook.setTelephone(KeyCenterUtils.decrypt(addressBook.getTelephone()));
                if (AddrBookConstant.NOT_WHETHER_SHOW.equals(addressBook.getWhetherShow())) {
                    //app查询的时候判断这条通讯录数据是否显示 不显示就不把默认电话放回
                    addressBook.setDefaultPhone(null);
                } else {
                    addressBook.setDefaultPhone(KeyCenterUtils.decrypt(addressBook.getDefaultPhone()));
                }
            });
        }
    }

    private CompletableFuture<List<CscpOrgDTO>> processOrgList(CompletableFuture<List<CscpOrgDTO>> orgListFuture,Long labelId) {
        return orgListFuture.thenComposeAsync(orgList -> {
            if (CollUtil.isEmpty(orgList)) {
                return CompletableFuture.completedFuture(Collections.emptyList());
            }

            // 批量查询关联数据
            Map<Long, TLabelOrg> labelOrgMap = cscpOrgService.selectBatchParentIdOrg(
                    orgList.stream() .map(CscpOrgDTO::getId).collect(Collectors.toList()),labelId );

            // 处理每个机构数据
            List<CompletableFuture<CscpOrgDTO>> futures = orgList.stream()
                    .map(orgDTO -> CompletableFuture.supplyAsync(() -> {
                        try {
                            TLabelOrg labelOrg = labelOrgMap.get(orgDTO.getId());
                            orgDTO.setIsValuable(labelOrg != null ? orgDTO.getIsValuable() : 0);
                            return orgDTO;
                        } catch (Exception e) {
                            log.error("处理机构数据失败: orgId={}", orgDTO.getId(), e);
                            throw new CompletionException(e);
                        }
                    }, EXECUTOR))
                    .collect(Collectors.toList());

            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream() .map(CompletableFuture::join).collect(Collectors.toList()));
        }, EXECUTOR);
    }

    /**
     * 统计机构下的人数
     * */
    private CompletableFuture<List<CscpOrgDTO>> getListCompletableFuture(Long labelId, CompletableFuture<List<CscpOrgDTO>> processedListFuture, ExecutorService executor) {
        // 异步处理人员计数
        CompletableFuture<List<CscpOrgDTO>> finalListFuture = processedListFuture.thenCompose(list -> {
            List<CompletableFuture<Void>> countFutures = list.stream().map(dto ->
                    CompletableFuture.runAsync(() -> {
                        if (ObjectUtil.equals(dto.getType(), 1)) {
                            // 异步获取虚拟机构下的单位ID
                            List<CscpOrg> ids = cscpOrgRepository.selectListNoAdd(
                                    new LambdaQueryWrapper<CscpOrg>()
                                            .select(CscpOrg::getId)
                                            .eq(CscpOrg::getParentId, dto.getId())
                            );
                            List<Long> idLong = ids.stream()
                                    .map(id -> id.getId())
                                    .collect(Collectors.toList());

                            // 异步获取标签关联的单位列表
                            List<Long> labelList = cscpOrgRepository.getLabelList(labelId);

                            // 过滤匹配的ID
                            List<Long> resultList = labelList.stream()
                                    .filter(item -> idLong.contains(item))
                                    .collect(Collectors.toList());

                            // 异步统计人员数量
                            Integer count = cscpOrgRepository.countParentId(dto.getId(), resultList);
                            dto.setPersonCount(count);
                        } else if (ObjectUtil.equals(dto.getType(), 2)) {
                            Integer count = cscpOrgRepository.countUserByCompany(dto.getId());
                            dto.setPersonCount(count);
                        } else if (ObjectUtil.equals(dto.getType(), 3)) {
                            Integer subDeptOrgCount = cscpOrgRepository.countUserByParentId(dto.getId());
                            Integer count = cscpOrgRepository.countUserByOrg(dto.getId());
                            dto.setPersonCount(count+subDeptOrgCount);
                        }
                    }, executor)
            ).collect(Collectors.toList());

            // 等待所有计数操作完成
            return CompletableFuture.allOf(countFutures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> list);
        });
        return finalListFuture;
    }

    @Override
    public TAddressBookLabelDTO queryLabelOrg(Long labelId) {
        //查询这条数据的详情
        TAddressBookLabel tAddressBookLabel = tAddressBookLabelMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TAddressBookLabel>()
                        .eq(TAddressBookLabel::getId, labelId)
        );

        TAddressBookLabelDTO tAddressBookLabelDTO = BeanConvertUtils.copyProperties(tAddressBookLabel, TAddressBookLabelDTO.class);

        //关联单位
        List<TLabelOrg> labelOrgList = tLabelOrgMapperl.selectListNoAdd(
                new LambdaQueryWrapper<TLabelOrg>()
                        .eq(TLabelOrg::getDisplayRangeId, labelId)
        );
        tAddressBookLabelDTO.setLabelOrgList(ListCopyUtil.copy(labelOrgList, TLabelOrgDTO.class));

        //授权单位
        List<TAddressBookAuthorize> addressBookAuthorizes = tAddressBookAuthorizeMapper.selectListNoAdd(
                new LambdaQueryWrapper<TAddressBookAuthorize>()
                        .eq(TAddressBookAuthorize::getLabelId, labelId)
        );
        tAddressBookLabelDTO.setAddressBookAuthorizeList(ListCopyUtil.copy(addressBookAuthorizes, TAddressBookAuthorizeDTO.class));

        //获取该标签的排序
        if (!SecurityUtils.isTenantName()) {
            Map<Long, Integer> orgSortMap = addressBookAuthorizes.stream().collect(Collectors.toMap(TAddressBookAuthorize::getUnitId, TAddressBookAuthorize::getSort));
            Integer integer = orgSortMap.get(SecurityUtils.getCurrentCompanyId());
            if (!Objects.isNull(orgSortMap) && !Objects.isNull(integer)) {
                tAddressBookLabelDTO.setSort(integer);
            }
        }


        return tAddressBookLabelDTO;
    }


    public Boolean isTenantName(TAddressBookLabelDTO tAddressBookLabel) {
        return Objects.isNull(tAddressBookLabel.getCompanyId()) && Objects.isNull(tAddressBookLabel.getDepartmentId());
    }

    /**
     * 查询通讯录标签,用于可视范围
     *
     * @param addressBookId
     * @return
     */
    @Override
    public QueryLabelVisualRangePcDTO queryLabelVisualRangePc(Long addressBookId) {
        String useSysLabelFlag = sysConfigService.getSysConfigValueByCode(SysConfigConstant.APP_SYS_LABEL_FLAG);
        QueryLabelVisualRangePcDTO q1 = new QueryLabelVisualRangePcDTO();

        TAddressBook tAddressBook = tAddressBookMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TAddressBook>().select(TAddressBook::getAddressBookLableId, TAddressBook::getUserId).eq(TAddressBook::getId, addressBookId));

        //获取用户可用的标签
        List<CscpUserOrg> userOrgs = cscpUserOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpUserOrg>()
                        .eq(CscpUserOrg::getUserId, tAddressBook.getUserId())
        );

        if (userOrgs == null || userOrgs.isEmpty()) {
            throw new BusinessException("数据异常!");
        }

        List<Long> companyIds = userOrgs.stream().map(CscpUserOrg::getCompanyId).collect(Collectors.toList());

        List<TAddressBookAuthorize> addressBookAuthorizes = tAddressBookAuthorizeMapper.selectListNoAdd(new LambdaQueryWrapper<TAddressBookAuthorize>()
                .select(TAddressBookAuthorize::getLabelId, TAddressBookAuthorize::getId, TAddressBookAuthorize::getSort).in(TAddressBookAuthorize::getUnitId, companyIds)
                .ne(StrUtil.isNotBlank(useSysLabelFlag) && useSysLabelFlag.equals("1"), TAddressBookAuthorize::getLabelName,"本单位"));


        if (StrUtil.isNotBlank(useSysLabelFlag) && useSysLabelFlag.equals("1")) {
            // 构建当前用户本单位标签
            Set<Long> processedCompanyIds = new HashSet<>();

            for (CscpUserOrg userOrg : userOrgs) {
                // 如果这个companyId已经处理过，则跳过
                if (!processedCompanyIds.add(userOrg.getCompanyId())) {
                    continue;
                }

                TAddressBookAuthorize authorize = new TAddressBookAuthorize();
                authorize.setId(userOrg.getCompanyId());
                authorize.setLabelId(userOrg.getCompanyId());
                authorize.setSort(0);
                authorize.setLabelName("本单位");
                authorize.setDepartmentId(userOrg.getOrgId());
                authorize.setCompanyId(userOrg.getCompanyId());
                addressBookAuthorizes.add(authorize);
            }
        }

        List<Long> authorizesLabelId = addressBookAuthorizes.stream().map(i -> i.getLabelId()).collect(Collectors.toList());



        if (authorizesLabelId.isEmpty()) {
            return q1;
        }

        List<TAddressBookLabel> tAddressBookLabels = tAddressBookLabelMapper.selectListNoAdd(new LambdaQueryWrapper<TAddressBookLabel>().in(TAddressBookLabel::getId, authorizesLabelId)
                .eq(StrUtil.isNotBlank(useSysLabelFlag) && useSysLabelFlag.equals("1"), TAddressBookLabel::getDefaultLabel, AddrBookConstant.NOT_DEFAULT_LABEL));

        if (StrUtil.isNotBlank(useSysLabelFlag) && useSysLabelFlag.equals("1")) {
            // 构建当前用户本单位标签
            Set<Long> tadbLabelSet = new HashSet<>();

            for (CscpUserOrg userOrg : userOrgs) {
                // 如果这个companyId已经处理过，则跳过
                if (!tadbLabelSet.add(userOrg.getCompanyId())) {
                    continue;
                }

                TAddressBookLabel currentTABEntity = new TAddressBookLabel();
                currentTABEntity.setId(userOrg.getCompanyId());
                currentTABEntity.setSort(0);
                currentTABEntity.setLabelName("本单位");
                currentTABEntity.setDepartmentId(userOrg.getOrgId());
                currentTABEntity.setCompanyId(userOrg.getCompanyId());
                tAddressBookLabels.add(currentTABEntity);
            }
        }

        //填充排序(处理多机构同一标签问题)
        this.sortTAddressBookLabel(addressBookAuthorizes, tAddressBookLabels);

        List<TAddressBookLabel> c1 = tAddressBookLabels.stream().sorted(Comparator.comparing(TAddressBookLabel::getSort)).collect(Collectors.toList());
        q1.setAvailableLabel(ListCopyUtil.copy(c1, TAddressBookLabelDTO.class));

        List<Long> collect1 = tAddressBookLabels.stream().map(i -> i.getId()).collect(Collectors.toList());

        //如果通讯录表中的可见范围不为空就拿自己的标签id，如果为空获取本单位的
        if (!Objects.isNull(tAddressBook) && StringUtils.isNotEmpty(tAddressBook.getAddressBookLableId())) {
            String[] labelId = tAddressBook.getAddressBookLableId().split(",");
            List<Long> collect = Arrays.stream(labelId).map(i -> Long.valueOf(i)).collect(Collectors.toList());
            q1.setSelectedLabelId(collect);
        } else {
            q1.setSelectedLabelId(collect1);
        }

        return q1;
    }



    /**
     * 查询某个标签关联的单位
     *
     * @param labelId
     */
    @Override
    public List<Tree<String>> queryAffiliatedUnit(Long labelId) {
        List<CscpOrg> orgList = tAddressBookLabelMapper.selectAffiliatedUnit(labelId);
        List<Tree<String>> trees = this.assembledTreeNodeList(orgList);
        return trees;
    }

    /**
     * 关联单位调整排序
     *
     * @param updateAdjustSort
     * @return
     */
    @Override
    public Integer updateAdjustSort(UpdateAdjustSortDTO updateAdjustSort) {
        //通过关联机构表找到标签对应的机构id
        List<TLabelOrg> labelOrgs = tLabelOrgMapperl.selectListNoAdd(
                new LambdaQueryWrapper<TLabelOrg>()
                        .eq(TLabelOrg::getDisplayRangeId, updateAdjustSort.getDisplayRangeId())
        );
        List<Long> labelIdList = labelOrgs.stream().map(i -> i.getOrgId()).collect(Collectors.toList());

        //找到所有和标签绑定的机构
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>().in(CscpOrg::getId, labelIdList));
        Map<Long, CscpOrg> orgMap = orgList.stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg -> CscpOrg));
        //获取当前的机构
        CscpOrg cscpOrg = orgMap.get(updateAdjustSort.getOrgId());

        List<Long> orgIdList = new LinkedList<>();
        //判断是否有父级，没有父级说明是第一级，就把所有第一级找出来，如果不是父级根据parentId找到它的子级
        CscpOrg org = orgMap.get(cscpOrg.getParentId());
        if (!Objects.isNull(org)) {
            List<CscpOrg> o1 = cscpOrgRepository.selectListNoAdd(
                    new LambdaQueryWrapper<CscpOrg>()
                            .eq(CscpOrg::getParentId, cscpOrg.getParentId())
                            .in(CscpOrg::getId, labelIdList)
            );
            List<Long> l1 = o1.stream().map(i -> i.getId()).collect(Collectors.toList());
            orgIdList.addAll(l1);
        } else {
            orgList.forEach(i -> {
                CscpOrg cscpOrg2 = orgMap.get(i.getParentId());
                if (Objects.isNull(cscpOrg2)) {
                    orgIdList.add(i.getId());
                }
            });
        }

        Map<Long, TLabelOrg> labelOrgMap = labelOrgs.stream().collect(Collectors.toMap(TLabelOrg::getOrgId, TLabelOrg -> TLabelOrg));
        TLabelOrg tLabelOrg1 = labelOrgMap.get(updateAdjustSort.getOrgId());
        //处理排序
        if (!Objects.isNull(tLabelOrg1) && !Objects.isNull(tLabelOrg1.getSort()) && tLabelOrg1.getSort() != updateAdjustSort.getSort()) {
            tAddressBookLabelMapper.updataSort(
                    SortEnum.builder()
                            .displayRangeId(updateAdjustSort.getDisplayRangeId())
                            .sort(updateAdjustSort.getSort())
                            .tableName("t_label_org")
                            .sortName("sort")
                            .orgIdList(orgIdList)
                            .additionOrsubtraction("+").build());
        }
        TLabelOrg tLabelOrg = new TLabelOrg();
        tLabelOrg.setSort(updateAdjustSort.getSort());

        return tLabelOrgMapperl.update(tLabelOrg,
                new LambdaQueryWrapper<TLabelOrg>().eq(TLabelOrg::getDisplayRangeId, updateAdjustSort.getDisplayRangeId()).eq(TLabelOrg::getOrgId, updateAdjustSort.getOrgId()));
    }

    /**
     * 标签添加关联机构
     *
     * @param labelOrgList
     */
    @Override
    public boolean addLabelRelationOrg(List<TLabelOrgDTO> labelOrgList) {
        if (!labelOrgList.isEmpty()) {
            tLabelOrgMapperl.deleteRelationData(labelOrgList.get(0).getDisplayRangeId());
        }
        return this.addLabelRelationOrgService(labelOrgList);
    }

    /**
     * 标签添加授权机构
     *
     * @param addressBookAuthorizeList
     * @return
     */
    @Override
    public boolean addLabelAuthorizeOrg(List<TAddressBookAuthorizeDTO> addressBookAuthorizeList) {
        if (!addressBookAuthorizeList.isEmpty()) {
            tAddressBookAuthorizeMapper.deleteRelationData(addressBookAuthorizeList.get(0).getLabelId());
        }
        return this.addLabelAuthorizeOrgService(addressBookAuthorizeList,null);
    }

    /**
     * 删除标签关联的机构
     *
     * @param deleteLabelOrg
     * @return
     */
    @Override
    public int deleteLabelRelationOrg(DeleteLabelOrgDTO deleteLabelOrg) {
        int delete = tLabelOrgMapperl.delete(
                new LambdaQueryWrapper<TLabelOrg>()
                        .eq(TLabelOrg::getDisplayRangeId, deleteLabelOrg.getLabelId())
                        .in(TLabelOrg::getOrgId, deleteLabelOrg.getOrgId())
        );
        return delete;
    }

    /**
     * 删除标签授权的机构
     *
     * @param deleteLabelOrg
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteLabelAuthorizeOrg(DeleteLabelOrgDTO deleteLabelOrg) {
        int delete = tAddressBookAuthorizeMapper.delete(
                new LambdaQueryWrapper<TAddressBookAuthorize>()
                        .eq(TAddressBookAuthorize::getLabelId, deleteLabelOrg.getLabelId())
                        .in(TAddressBookAuthorize::getUnitId, deleteLabelOrg.getOrgId())
        );
        //修改通讯录可视范围
        List<TAddressBook> tAddressBooks = tAddressBookMapper.selectListNoAdd(
                new LambdaQueryWrapper<TAddressBook>()
                        .select(TAddressBook::getUserId, TAddressBook::getId, TAddressBook::getAddressBookLableId)
                        .like(TAddressBook::getAddressBookLableId, deleteLabelOrg.getLabelId())
        );

        if (!tAddressBooks.isEmpty()) {
            List<TAddressBookAuthorize> a1 = tAddressBookAuthorizeMapper.selectListNoAdd(
                    new LambdaQueryWrapper<TAddressBookAuthorize>()
                            .notIn(TAddressBookAuthorize::getUnitId, deleteLabelOrg.getOrgId())
                            .eq(TAddressBookAuthorize::getLabelId, deleteLabelOrg.getLabelId())
            );

            this.selectUpdateData(ListCopyUtil.copy(a1, TAddressBookAuthorizeDTO.class), tAddressBooks, deleteLabelOrg.getLabelId());
        }
        return delete;
    }

    /**
     * 查询某个标签授权的单位
     *
     * @param labelId
     * @return
     */
    @Override
    public List<Tree<String>> queryAuthorizeUnit(Long labelId) {
        List<CscpOrg> orgList = tAddressBookLabelMapper.selectAuthorizeUnit(labelId);
        List<Tree<String>> trees = assembledTreeNodeList(orgList);
        return trees;
    }

    /**
     * 新增标签判断排序是否存在
     *
     * @param sort
     * @return
     */
    @Override
    public Boolean querySort(Integer sort) {
        //如果是admin新增标签，直接查询标签中的排序
        if (SecurityUtils.isSystemName() || SecurityUtils.isRegionAdmin()) {
            List<TAddressBookLabel> tAddressBookLabels = tAddressBookLabelMapper.selectListNoAdd(
                    new LambdaQueryWrapper<TAddressBookLabel>()
                            .eq(TAddressBookLabel::getCreateBy, SecurityUtils.getCurrentUserId())
                            .eq(TAddressBookLabel::getSort, sort)
                            .eq(TAddressBookLabel::getDefaultLabel, AddrBookConstant.NOT_DEFAULT_LABEL));
            if (!tAddressBookLabels.isEmpty()) {
                return true;
            }
        }

        //如果是普通用户需要查询授权表中的排序
        if (SecurityUtils.isGeneralName()) {
            List<TAddressBookAuthorize> addressBookAuthorizes = tAddressBookAuthorizeMapper.selectListNoAdd(
                    new LambdaQueryWrapper<TAddressBookAuthorize>()
                            .eq(TAddressBookAuthorize::getSort, sort)
                            .eq(TAddressBookAuthorize::getUnitId, SecurityUtils.getCurrentCompanyId()));
            if (!addressBookAuthorizes.isEmpty()) {
                return true;
            }
        }
        return false;
    }


    /**
     * 组装机构树
     *
     * @param cscpOrgList
     * @return
     */
    public List<Tree<String>> assembledTreeNodeList(List<CscpOrg> cscpOrgList) {

        // 构建的整个树数据
        List<TreeNode<String>> treeNodeList = cscpOrgList.stream().map(org -> {
            Map<String, Object> extraMap = new HashMap<>();
            extraMap.put("orderBy", org.getOrderBy());
            extraMap.put("type", org.getType());
            // 单个树数据构建
            TreeNode<String> treeNode = new TreeNode<String>()
                    .setId(String.valueOf(org.getId()))
                    .setParentId(String.valueOf(org.getParentId()))
                    .setName(org.getOrgName())
                    .setWeight(org.getOrderBy())
                    .setExtra(extraMap);

            //处理模糊查询的机构父id不是0,就不组装树问题
            if (org.getParentId() != 0) {
                List<CscpOrg> collect = cscpOrgList.stream().filter(i -> i.getId().equals(org.getParentId())).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    treeNode.setParentId(String.valueOf(org.getParentId()));
                } else {
                    treeNode.setParentId("0");
                }
            } else {
                treeNode.setParentId(String.valueOf(org.getParentId()));
            }

            return treeNode;
        }).collect(Collectors.toList());

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setNameKey("title");
        treeNodeConfig.setChildrenKey("children");
        // 最大递归深度
        treeNodeConfig.setDeep(6);

        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(treeNodeList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    tree.putExtra("orderBy", treeNode.getExtra().getOrDefault("orderBy", null));
                    tree.putExtra("type", treeNode.getExtra().getOrDefault("type", null));
                });
        return treeNodes;
    }

    /**
     * 遍历查询所有下级统计每一个的人数
     *
     * @param orgId
     * @return
     */
    public Integer countUserByOrg(Long orgId,Integer count){
        count=count+cscpOrgRepository.countUserByOrg(orgId);
        QueryWrapper<CscpOrg> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("parent_id",orgId);
        List<CscpOrg> cscpOrgs = cscpOrgRepository.selectList(queryWrapper);
        for (CscpOrg cscpOrg : cscpOrgs) {
            countUserByOrg(cscpOrg.getId(),count);
        }
        return count;
    }


}
