<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.ssdc.admin.repository.CscpUserRepository">
    <resultMap id="BaseResultMap" type="com.ctsi.ssdc.admin.domain.CscpUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="password" jdbcType="VARCHAR" property="password" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullValueFromPlaintextDesHandler"/>
    </resultMap>

    <resultMap id="CscpUserBaseResultMap" type="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result column="email" jdbcType="VARCHAR" property="email" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullValueFromPlaintextDesHandler"/>
        <result column="password" jdbcType="VARCHAR" property="password" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullValueFromPlaintextDesHandler"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="last_login" jdbcType="TIMESTAMP" property="lastLogin"/>
        <result column="default_department" jdbcType="BIGINT" property="defaultDepart"/>
        <result column="branch_leader" jdbcType="INTEGER" property="branchLeader"/>
        <result column="department_head" jdbcType="INTEGER" property="departmentHead"/>
        <result column="order_by" jdbcType="INTEGER" property="orderBy"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="org_sort" jdbcType="INTEGER" property="orgSort"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="department_code" jdbcType="VARCHAR" property="departmentCode"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="company_name" jdbcType="BIGINT" property="companyName"/>
        <result column="org_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="display" jdbcType="INTEGER" property="display"/>
        <result column="statistics" jdbcType="INTEGER" property="statistics"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="post" jdbcType="VARCHAR" property="post"/>
        <result column="app_version_name" jdbcType="VARCHAR" property="appVersionName"/>
        <result column="cuo_id" jdbcType="BIGINT" property="cuoId"/>
        <result column="person_label" jdbcType="VARCHAR" property="personLabel"/>
        <result column="ID_CARD_NO" jdbcType="VARCHAR" property="idCardNo" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="secretary_name" jdbcType="VARCHAR" property="secretaryName"/>
        <result column="secretary_phone" jdbcType="VARCHAR" property="secretaryPhone"/>
        <result column="push_app_code" jdbcType="VARCHAR" property="pushAppCode"/>
    </resultMap>

    <resultMap id="notSingInUserMap" type="com.ctsi.ssdc.security.CscpUserNumberDTO">
        <id column="id" jdbcType="BIGINT" property="userId"/>
        <result column="real_name" jdbcType="VARCHAR" property="userName" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="orgOrderBy" property="orgOrderBy"/>
        <result column="userOrderBy" property="userOrderBy"/>
    </resultMap>

    <resultMap id="BaseUserMap" type="com.ctsi.ssdc.admin.domain.dto.CscpBaseUserDTO">
        <id column="id" jdbcType="BIGINT" property="userId"/>
        <result column="real_name" jdbcType="VARCHAR" property="userName"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="post" jdbcType="VARCHAR" property="post"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>

    <resultMap id="DistributeBaseUserMap" type="com.ctsi.ssdc.admin.domain.dto.CscpDistributeBaseUserDTO">
        <id column="id" jdbcType="BIGINT" property="userId"/>
        <result column="real_name" jdbcType="VARCHAR" property="userName"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="dept_sort" property="deptSort"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="company_sort" property="companySort"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="post" jdbcType="VARCHAR" property="post"/>
    </resultMap>

    <resultMap id="UserLockMap" type="com.ctsi.ssdc.dto.QueryUserLockDTO">
        <id column="id" jdbcType="BIGINT" property="userId"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentOrgName"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyOrgName"/>
    </resultMap>

    <resultMap id="UserExamineMap" type="com.ctsi.ssdc.dto.QueryUserExamineDTO">
        <id column="id" jdbcType="BIGINT" property="userId"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentOrgName"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyOrgName"/>
    </resultMap>


    <sql id="Base_Column_List">
        cu.id, cu.login_name, cu.password, cu.real_name, cu.last_login, cu.email, cu.mobile, cu.status,cu.examine_status,cu.app_version_name,cu.security_classification_code,cu.security_classification_code_name,cu.str_id,cu.person_label
            ,cu.ID_CARD_NO,cu.secretary_name,cu.secretary_phone
    </sql>


    <select id="selectAllCscpUserList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from cscp_user_role as cur
        INNER JOIN cscp_roles as cr ON cur.role_id = cr.id
        INNER JOIN cscp_user_org as cuo ON cr.org_id = cuo.org_id
        INNER JOIN cscp_user as cu ON cuo.user_id = cu.id
        where cur.user_id = #{id, jdbcType=BIGINT}
    </select>

    <select id="getUserMaxSorted" resultType="java.lang.Integer">










                                                                                        select MAX(order_by)
                                                                                        from cscp_user










    </select>


    <select id="criteriaQueryCscpUserList" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap">
        select  <include refid="Base_Column_List">
    </include> ,
        co.order_by org_sort, cu.order_by, cuo.order_by sort, co.id department_id, co.org_name department_name,
        cuo.company_id company_id, cuo.post post, co.org_name company_name, cu.HMAC_MOBILE,cu.created_time
        FROM cscp_user cu
        inner JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        left join cscp_org co
        on cuo.org_id = co.id
        where cu.deleted = 0
        and cuo.deleted = 0
        and co.deleted = 0
        <if test="user.status != null">
            and cu.status = #{user.status,jdbcType=INTEGER}
        </if>
        <if test="user.id != null">
            and cu.id = #{user.id,jdbcType=BIGINT}
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            <!--            and ( cu.real_name = #{user.realName} or cu.real_name_start = #{user.realName} or-->
            <!--            cu.real_name_end =-->
            <!--            #{user.realName})-->
            and cu.real_name_start like concat('%',concat(#{user.realName},'%'))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            <!--            and (cu.mobile = #{user.mobile} or cu.mobile_start = #{user.mobile} or cu.mobile_middle = #{user.mobile} or-->
            <!--            cu.mobile_end =-->
            <!--            #{user.mobile})-->
            and cu.mobile_start like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.tenantId != null and user.tenantId != ''">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        <if test="user.idCardNo != null and user.idCardNo != ''">
            and cu.id_Card_No = #{user.idCardNo}
        </if>
        <if test="user.companyIdList !=null and user.companyIdList.size>0">
            and cuo.company_id in
            <foreach collection="user.companyIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="user.tenantIdList!=null">
                and cu.tenant_id in
                <foreach collection="user.tenantIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <when test="user.tenantId!=null">
                and cu.tenant_id = #{user.tenantId}
            </when>
        </choose>
        <if test="user.securityClassificationCode != null and user.securityClassificationCode != ''">
            and cu.security_classification_code = #{user.securityClassificationCode}
        </if>
        order by co.order_by, cuo.order_by, cu.order_by
    </select>


    <select id="criteriaQueryCscpUserListNoAdmin" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap">
        select <include refid="Base_Column_List">
    </include> ,
        co.order_by org_sort, cu.order_by, cuo.order_by sort, co.id department_id, co.org_name department_name, cu.WESTONE_USER_ID,cu.create_time,
        cuo.company_id company_id,co2.org_name as company_name,cuo.post,cu.push_app_code
        FROM cscp_user cu
        left JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        left join cscp_org co
        on cuo.org_id = co.id
        left join cscp_org co2
        on cuo.company_id=co2.id  and co2.deleted=0
        where cu.deleted = 0
        and cuo.deleted = 0
        <!-- 用户改禁用要求能查出数据、机构要求逻辑删除;
             用户管理下分页查询的机构deleted条件去掉;
            and co.deleted = 0
        -->
        <if test="user.status != null">
            and cu.status = #{user.status,jdbcType=INTEGER}
        </if>
        <if test="user.id != null">
            and cu.id = #{user.id,jdbcType=BIGINT}
        </if>
        <if test="user.idCardNo != null and user.idCardNo != ''">
            and cu.id_Card_No = #{user.idCardNo}
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            <!--            and ( cu.real_name = #{user.realName} or cu.real_name_start = #{user.realName} or-->
            <!--            cu.real_name_end =-->
            <!--            #{user.realName})-->
            and REGEXP_LIKE(cu.real_name_start, concat('.*',concat(#{user.realName},'.*')))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            <!--            and (cu.mobile = #{user.mobile} or cu.mobile_start = #{user.mobile} or cu.mobile_middle = #{user.mobile} or-->
            <!--            cu.mobile_end =-->
            <!--            #{user.mobile})-->
            and cu.mobile_start like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.orgId != null">
            and co.id = #{user.orgId}
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.tenantId != null and user.tenantId != ''">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        <if test="user.companyIdList !=null and user.companyIdList.size>0">
            and cuo.company_id in
            <foreach collection="user.companyIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="user.rootOrgCodePath != null and user.rootOrgCodePath.size()>0">
            and
            <foreach collection="user.rootOrgCodePath" item="orgCodePath" open="(" close=")" separator=" or ">
                co.org_code_path like concat(#{orgCodePath},'%')
            </foreach>
        </if>
        <choose>
            <when test="user.tenantIdList!=null">
                and cu.tenant_id in
                <foreach collection="user.tenantIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <when test="user.tenantId!=null">
                and cu.tenant_id = #{user.tenantId}
            </when>
        </choose>
        <if test="user.securityClassificationCode != null and user.securityClassificationCode != ''">
            and cu.security_classification_code = #{user.securityClassificationCode}
        </if>
        <!-- 未推送 -->
        <if test="user.westoneUserPushedStatus != null and user.westoneUserPushedStatus == 'unPushed'">
            and (cu.WESTONE_USER_ID IS NULL OR cu.WESTONE_USER_ID = '')
        </if>
        <!-- 已推送 -->
        <if test="user.westoneUserPushedStatus != null and user.westoneUserPushedStatus == 'pushed'">
            and cu.WESTONE_USER_ID IS NOT NULL
        </if>
        <if test="user.strId != null and user.strId != ''">
            and cu.str_id like concat('%',concat(#{user.strId},'%'))
        </if>
        <if test="user.status != null and user.status != ''">
            and cu.status = #{user.status}
        </if>
        order by co.order_by, cuo.order_by, cu.order_by
    </select>

    <select id="queryCscpUserList" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap">
        select <include refid="Base_Column_List">
    </include> ,
        cu.create_time,co.order_by org_sort, cu.order_by, cuo.order_by sort, co.id department_id, co.org_name department_name, co.org_code department_code, cu.WESTONE_USER_ID,cu.push_app_code,
        cuo.company_id company_id,(select co2.org_name from cscp_org co2 where co2.id = cuo.company_id and co2.deleted=0) as company_name
        from ( SELECT id,
        org_name,
        org_code,
        order_by
        FROM cscp_org
        where deleted = 0
        START WITH id = #{user.companyId}
        CONNECT BY PRIOR id = parent_id) co
        inner join cscp_user_org cuo
        on cuo.org_id = co.id
        and cuo.deleted=0
        inner join cscp_user cu
        on cu.id = cuo.user_id
        where cu.deleted=0
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            <!--            and ( cu.real_name = #{user.realName} or cu.real_name_start = #{user.realName} or-->
            <!--            cu.real_name_end =-->
            <!--            #{user.realName})-->
            and REGEXP_LIKE(cu.real_name_start, concat('.*',concat(#{user.realName},'.*')))
        </if>
        <if test="user.tenantId != null and user.tenantId != ''">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.orgId != null">
            and co.id = #{user.orgId}
        </if>
        <if test="null != deptIDList and deptIDList.size() > 0">
            and cuo.org_id in
            <foreach collection="deptIDList" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            and cu.mobile_start like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <!-- 未推送 -->
        <if test="user.westoneUserPushedStatus != null and user.westoneUserPushedStatus == 'unPushed'">
            and (cu.WESTONE_USER_ID IS NULL OR cu.WESTONE_USER_ID = '')
        </if>
        <!-- 已推送 -->
        <if test="user.westoneUserPushedStatus != null and user.westoneUserPushedStatus == 'pushed'">
            and cu.WESTONE_USER_ID IS NOT NULL
        </if>
        <if test="user.strId != null and user.strId != ''">
            and cu.str_id like concat('%',concat(#{user.strId},'%'))
        </if>
        <if test="user.status != null">
            and cu.status = #{user.status}
        </if>
        order by co.order_by, cuo.order_by, cu.order_by
    </select>

    <select id="queryCscpUserOrgList" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap">
        select  <include refid="Base_Column_List"></include> ,
        cuo.company_sort org_sort, cu.order_by, cuo.order_by sort,cuo.org_id, cuo.org_id department_id, cuo.org_name department_name,cuo.company_id,cuo.company_name,cuo.post
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        where cu.deleted = 0
        and cuo.deleted = 0
        and cu.statistics = 1
        <if test="user.status != null">
            and cu.status = #{user.status,jdbcType=INTEGER}
        </if>
        <if test="user.id != null">
            and cu.id = #{user.id,jdbcType=BIGINT}
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            and cu.real_name_start like concat('%',concat(#{user.realName},'%'))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            and cu.mobile_start like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.departmentId != null and user.departmentId != ''">
            and cuo.org_id = #{user.departmentId}
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.tenantId != null and user.tenantId != ''">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        order by cuo.company_sort, cuo.order_by, cu.order_by
    </select>

    <select id="selectUserByTenantId" resultMap="CscpUserBaseResultMap">
        select  <include refid="Base_Column_List">
    </include> , cu.display, cu.statistics, cuo.post, cuo.department_head department_head,
        co.order_by org_sort, cu.order_by, cuo.order_by sort, co.id department_id, co.org_name department_name,
        cuo.company_id company_id
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        <if test="user.companyId != null and user.companyId != ''">
            left join ( SELECT id,
            org_name,
            order_by,
            deleted,
            company_id
            FROM cscp_org
            where deleted = 0
            START WITH id = #{user.companyId}
            CONNECT BY PRIOR id = parent_id) co
        </if>
        <if test="user.companyId == null or user.companyId == ''">
            left join cscp_org co
        </if>
        on cuo.org_id = co.id
        where cu.deleted = 0 and cuo.deleted = 0 and co.deleted = 0
        <if test="null != user.tenantId and '' != user.tenantId">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            and cu.mobile like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            and REGEXP_LIKE(cu.real_name_start, concat('.*',concat(#{user.realName},'.*')))
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
    </select>

    <select id="selectUserExport" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserExportDTO">
        select
        cu.id as id,
        cu."login_name" as loginName,
        cu."login_name" as userName,
        cu."real_name" as realName,
        cu."mobile" as mobile,
        cu.order_by as orderBy,
        cu.email as email,
        cu.display,cu.statistics,
        cuo.department_head as departmentHead,
        IF(cu.display != 0, '否', '是') as displayStr,
        IF(cu.statistics != 0, '否', '是') as statisticsStr,
        IF（cuo.department_head != 0, '否', '是') as departmentHeadStr,
        cuo."org_name" as departmentName,
        cuo."company_name" as companyName,
        cuo."order_by" as sort,
        cuo.post as post
        from cscp_user cu
        left join cscp_user_org cuo on cu.id = cuo.user_id
        <if test="user.companyId != null and user.companyId != ''">
            left join ( SELECT id,
            org_name,
            order_by,
            deleted,
            company_id
            FROM cscp_org
            where deleted = 0
            START WITH id = #{user.companyId}
            CONNECT BY PRIOR id = parent_id) co
        </if>
        <if test="user.companyId == null or user.companyId == ''">
            left join cscp_org co
        </if>
        on cuo.org_id = co.id
        where cu.deleted = 0 and cuo.deleted = 0 and co.deleted = 0
        <if test="null != user.tenantId and '' != user.tenantId">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            and cu.mobile like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            and REGEXP_LIKE(cu.real_name_start, concat('.*',concat(#{user.realName},'.*')))
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
    </select>

    <!--TODO:Json加入了用户锁定字段-->
    <select id="pageQueryUserList" resultMap="CscpUserBaseResultMap">
        SELECT * FROM
        (
        SELECT ROW_NUMBER() OVER(PARTITION BY cu.id ORDER BY cu.order_by,cuo.order_by ) RN ,
        cu.id, cu.login_name, cu.real_name, cu.mobile, cu.email, cu.status, cu.last_login,
        cuo.default_department, cuo.branch_leader, cuo.department_head, cuo.org_id, co1.id company_id,
        co.id department_id,co.org_name department_name,co1.org_name company_name,co.code,cu.tenant_id
        FROM cscp_user cu
        INNER JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        left join cscp_org co
        on cuo.org_id = co.id
        left JOIN cscp_org co1
        ON co1.id = cuo.company_id
        where
        cu.deleted = 0
        and cuo.deleted = 0
        and co.deleted = 0
        and co1.deleted = 0
        <!---->
        and (cuo.company_id = #{orgId, jdbcType=BIGINT} or cuo.org_id = #{orgId, jdbcType=BIGINT} )
        and cu.display = 1
        and cu.status = 1
        <if test="realName != null and realName != ''">
            <!--            and (cu.real_name = #{realName} or cu.real_name_start = #{realName} or cu.real_name_end =-->
            <!--            #{realName})-->
            and cu.real_name like concat('%',concat(#{realName},'%'))
        </if>
        <if test="userIds != null and userIds.size > 0">
            and cu.id not in
            <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
                #{userId}
            </foreach>
        </if>
        order by cu.order_by,cuo.order_by
        )
        where rn = 1
    </select>


    <select id="pageSelectUsers" resultMap="CscpUserBaseResultMap">
        SELECT * FROM
        (
        SELECT ROW_NUMBER() OVER(PARTITION BY cu.id ORDER BY cu.id ) RN ,
        cu.id, cu.login_name, cu.real_name, cu.real_name_start, cu.mobile, cu.email, cu.status, cu.last_login,
        cuo.default_department, cuo.branch_leader, cuo.department_head, cuo.org_id, cuo.company_id,cuo."order_by",cuo."id" cuo_id
        FROM cscp_user cu
        INNER JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        where
        cu.deleted = 0
        and cu."status" = 1
        and cuo.deleted = 0
        and (cuo.company_id = #{orgId, jdbcType=BIGINT} or cuo.org_id = #{orgId, jdbcType=BIGINT})
        <if test="realName != null and realName != ''">
            <!--            and (cu.real_name = #{realName} or cu.real_name_start = #{realName} or cu.real_name_end =-->
            <!--            #{realName})-->
            and cu.real_name_start like concat('%',concat(#{realName},'%'))
        </if>
        order by cuo."order_by",cuo."id"
        )
        where rn = 1

    </select>


    <select id="queryCscpUserNotInListDTO" resultMap="CscpUserBaseResultMap"
            parameterType="com.ctsi.ssdc.admin.domain.dto.QueryCscpUserNotInListDTO">
        select
        <include refid="Base_Column_List">
        </include>
        FROM cscp_user cu
        LEFT JOIN cscp_user_org org
        on cu.id = org.user_id
        where cu.deleted = 0
        AND org.deleted = 0
        AND cu.status = 1
        AND cu.display = 1
        <if test="null != ew.userIdList and ew.userIdList.size > 0">
            and org.user_id not in
            <foreach collection="ew.userIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="null!=ew.tenantId and ''!=ew.tenantId">
            and cu.tenant_id = #{ew.tenantId}
        </if>
        <if test="null!=ew.companyId and ''!=ew.companyId">
            and org.company_id = #{ew.companyId}
        </if>
        <if test="ew.realName != null and ew.realName != ''">
            <!--            and (cu.real_name = #{ew.realName} or cu.real_name_start = #{ew.realName} or cu.real_name_end =-->
            <!--            #{ew.realName})-->
            and cu.real_name_start like concat('%',concat(#{ew.realName},'%'))
        </if>
        <if test="ew.loginName != null and ew.loginName != ''">
            and cu.login_name like concat('%',concat(#{ew.loginName},'%'))
        </if>
    </select>


    <select id="queryUserDetailDTOByUserId" resultMap="BaseResultMap">
        select cu.loginName,
        <include refid="Base_Column_List">
        </include>
        from cscp_user_role as cur
        INNER JOIN cscp_roles as cr ON cur.role_id = cr.id
        INNER JOIN cscp_user_org as cuo ON cr.org_id = cuo.org_id
        INNER JOIN cscp_user as cu ON cuo.user_id = cu.id
        INNER JOIN cscp_user_detail as cud ON cu.id = cud.user_id
        where cur.user_id = #{loginId,jdbcType=BIGINT}
        and cu.id = #{userId,jdbcType=BIGINT}
    </select>


    <select id="notSingInUser" parameterType="java.lang.Long" resultMap="notSingInUserMap">
        SELECT cu.id,cu.real_name, co.org_name,cu.order_by AS userOrderBy,co.order_by AS orgOrderBy
        FROM cscp_user cu
        INNER JOIN cscp_user_org cuo
        ON cu.id = cuo.user_id
        INNER JOIN cscp_org co
        ON cuo.org_id = co.id
        WHERE
        cuo.default_department = 1
        AND cu.status = 1
        AND cu.deleted = 0
        AND cuo.deleted = 0
        AND co.deleted = 0
        <if test="userId!=null and userId.size>0">
            AND cu.id
            NOT IN
            <foreach collection="userId" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="selectUserOrg" parameterType="java.lang.Long" resultMap="CscpUserBaseResultMap">
        SELECT cu.id, cu.real_name, cuo.org_id, cuo.company_id
        FROM cscp_user cu
        INNER JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        <where>
            AND cuo.default_department = 1
            AND cu.status = 1
            AND cu.deleted = 0
            AND cuo.deleted = 0
            AND cuo.company_id = #{org.companyId}
            <if test="org.departmentId != 0">
                and cuo.org_id = #{org.departmentId}
            </if>
        </where>
    </select>

    <select id="selectUserByCompanyId" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap">
        select distinct <include refid="Base_Column_List">
    </include> , cu.display, cu.statistics,
        co.order_by org_sort, cu.order_by, cuo.order_by sort, co.id department_id, co.org_name department_name,
        co.company_id company_id
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        left join cscp_org co
        on cuo.org_id = co.id
        where cu.deleted = 0
        and cuo.deleted = 0
        and cu.display = 1
        and co.deleted = 0
        and cu.status = 1
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        <if test="user.departmentId != null and user.departmentId != ''">
            and cuo.org_id = #{user.departmentId}
        </if>

        <if test="null != user.tenantId and '' != user.tenantId">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.realName != null and user.realName != ''">
            <!--            and (cu.real_name = #{user.realName} or cu.real_name_start = #{user.realName} or cu.real_name_end =-->
            <!--            #{user.realName})-->
            and cu.real_name like concat('%',concat(#{user.realName},'%'))
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
    </select>

    <select id="selectUserDetailByCompanyId" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap">
        select distinct <include refid="Base_Column_List">
    </include> , cu.display, cu.statistics,
        co.order_by org_sort, cu.order_by, cuo.order_by sort, co.id department_id, co.org_name department_name,
        co.company_id company_id, com.org_name AS company_name
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        LEFT JOIN cscp_org co
        on cuo.org_id = co.id
        LEFT JOIN cscp_org com
        on com.id = cuo.company_id
        where cu.deleted = 0
        and cuo.deleted = 0
        and cu.display = 1
        and co.deleted = 0
        and cu.status = 1
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        <if test="user.departmentId != null and user.departmentId != ''">
            and cuo.org_id = #{user.departmentId}
        </if>

        <if test="null != user.tenantId and '' != user.tenantId">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.realName != null and user.realName != ''">
            <!--            and (cu.real_name = #{user.realName} or cu.real_name_start = #{user.realName} or cu.real_name_end =-->
            <!--            #{user.realName})-->
            and cu.real_name like concat('%',concat(#{user.realName},'%'))
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
    </select>

    <select id="selectBaseUserInfo" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpBaseUserDTO"
            resultMap="BaseUserMap">
        select distinct cu.id, cu.real_name, co.id department_id, co.org_name department_name,
        co.company_id company_id, co.tenant_id tenant_id
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        left join cscp_org co
        on cuo.org_id = co.id
        where cu.deleted = 0
        and cuo.deleted = 0
        and co.deleted = 0
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        <if test="user.userName != null and user.userName != ''">
            <!--            and (cu.real_name = #{user.userName} or cu.real_name_start = #{user.userName} or cu.real_name_end =-->
            <!--            #{user.userName})-->
            and cu.real_name like concat('%',concat(#{user.userName},'%'))
        </if>
        <if test="null != user.companyIdList and user.companyIdList.size() > 0">
            and cuo.company_id in
            <foreach collection="user.companyIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
    </select>

    <select id="queryBaseUserInfo" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpBaseUserDTO"
            resultMap="BaseUserMap">
        select distinct cu.id, cu.real_name, cuo.post, co.id department_id, co.org_name department_name,
        co.company_id company_id, co.tenant_id tenant_id
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        left join cscp_org co
        on cuo.org_id = co.id
        where cu.deleted = 0
        and cuo.deleted = 0
        and co.deleted = 0
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        <if test="user.userName != null and user.userName != ''">
            <!--            and (cu.real_name = #{user.userName} or cu.real_name_start = #{user.userName} or cu.real_name_end =-->
            <!--            #{user.userName})-->
            and cu.real_name like concat('%',concat(#{user.userName},'%'))
        </if>
        <if test="null != user.companyIdList and user.companyIdList.size() > 0">
            and cuo.company_id in
            <foreach collection="user.companyIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != user.userIdList and user.userIdList.size() > 0">
            and cu.id in
            <foreach collection="user.userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
    </select>

    <select id="selectFuzzySearchUserList" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpSelectFuzzySearchUserList"
            resultMap="CscpUserBaseResultMap">
        SELECT * FROM
        (
        SELECT ROW_NUMBER() OVER(PARTITION BY cu.id ORDER BY cu.order_by,cuo.order_by ) RN ,
        cu.id, cu.real_name, cuo.org_id department_id, cuo.company_id company_id, cu.tenant_id tenant_id,cu.mobile mobile,cuo.post post
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        WHERE cu.tenant_id
        IN
        <foreach collection="param.tenantList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        <if test="param.realName != null and param.realName != ''">
            <!--            and (cu.real_name = #{param.realName}-->
            <!--            or cu.real_name_end = #{param.realNameEnd}-->
            <!--            or cu.real_name_start= #{param.realNameStart})-->
            and cu.real_name_start like concat('%',concat(#{param.realName},'%'))
        </if>
        and cu.deleted = 0
        and cuo.deleted = 0
        and cu.display = 1

        order by cu.order_by, cuo.order_by
        )
        where rn = 1
    </select>


    <select id="selectCodeUser" resultMap="CscpUserBaseResultMap">
        SELECT
        cu.id,cu.mobile
        FROM
        cscp_menus cm
        INNER JOIN cscp_role_menu crm ON cm.id = crm.menu_id
        INNER JOIN cscp_user_role cuo ON cuo.role_id = crm.role_id
        INNER JOIN cscp_user cu ON cu.id = cuo.user_id
        INNER JOIN cscp_user_org cuog ON cuog.user_id = cu.id
        WHERE
        cuog.company_id = #{companyId}
        AND cm.permission_code = #{code}
        and cu.deleted = 0
        and cuo.deleted = 0
        and cuog.deleted = 0
        and cu.display = 1
        and cu.status=1
        GROUP BY cu.id
    </select>

    <select id="selectTenantUsers" resultMap="CscpUserBaseResultMap" parameterType="java.lang.Long">
        SELECT
        cu.id, cu.login_name, cu.real_name, cu.last_login, cu.email, cu.mobile, cu.status,
        co.org_name AS company_name, co1.org_name AS department_name, cuo.org_id AS department_id, cuo.company_id AS
        company_id
        FROM
        cscp_user cu
        INNER JOIN cscp_user_org cuo ON cu.id = cuo.user_id
        INNER JOIN cscp_org co ON co.id = cuo.company_id
        INNER JOIN cscp_org co1 ON co1.id = cuo.org_id
        WHERE
        cu.tenant_id = #{tenantId} and
        cuo.default_department = 1 and
        cu.deleted = 0 and
        cuo.deleted = 0 and
        cu.display = 1
        order by cu.order_by, cuo.order_by
    </select>

    <select id="selectOrgUsers" resultMap="CscpUserBaseResultMap" parameterType="java.lang.Long">
        SELECT
        	cu.id,
        	cu.real_name,
        	cuo.company_id,
        	co.org_name AS company_name,
        	co1.id AS department_id,
        	co1.org_name AS department_name
        FROM
        	cscp_user cu
        	INNER JOIN cscp_user_org cuo ON  cu.id = cuo.user_id
        	INNER JOIN cscp_org co ON co.id = cuo.company_id
        	INNER JOIN cscp_org co1 ON co1.id = cuo.org_id
        WHERE
        	cuo.org_id = #{orgId}
        	AND cuo.default_department = 1
        	AND cuo.deleted = 0
        	AND cu.deleted =0
        	AND co.deleted = 0
        	AND co1.deleted = 0
        	AND cu.display = 1
            AND cu.status=1
        GROUP BY cu.id
    </select>

    <!--通过人员id集合，和单位id集合，获取人员对应的单位部门人员信息集合-->
    <select id="selectDistributeBaseUserInfo" parameterType="com.ctsi.ssdc.admin.domain.dto.CscpDistributeBaseUserDTO"
            resultMap="DistributeBaseUserMap">
<!--        select distinct cu.id, cu.real_name,cu.mobile, cuo.org_id department_id, cuo.org_name department_name,-->
<!--        cuo.company_id company_id,cuo.company_name company_name, cuo.tenant_id tenant_id,cuo.post,cuo.dept_sort,cuo.company_sort-->
<!--        FROM cscp_user cu-->
<!--        LEFT JOIN cscp_user_org cuo-->
<!--        on cu.id = cuo.user_id-->
<!--        where cu.deleted = 0-->
<!--        and cuo.deleted = 0-->
<!--        AND cu.display = 1-->
<!--        AND cu.status=1-->
<!--        <if test="companyId != null and companyId != ''">-->
<!--            and cuo.company_id = #{user.companyId}-->
<!--        </if>-->
<!--        <if test="null != user.companyIdList and user.companyIdList.size() > 0">-->
<!--            and cuo.company_id in-->
<!--            <foreach collection="user.companyIdList" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="user.userId != null and user.userId != ''">-->
<!--            and cu.id = #{user.userId}-->
<!--        </if>-->
<!--        <if test="null != user.userIdList and user.userIdList.size() > 0">-->
<!--            and cu.id in-->
<!--            <foreach collection="user.userIdList" item="id" open="(" separator="," close=")">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        order by co.order_by, cuo.order_by-->

        SELECT * FROM
        (
        SELECT ROW_NUMBER() OVER(PARTITION BY cu.id ORDER BY cu.id) RN ,
        cu.id,
        cu.real_name,
        cu.mobile,
        co1.id department_id,
        co1.org_name department_name,
        co1.order_by dept_sort,
        co.id company_id,
        co.org_name company_name,
        co.order_by company_sort,
        cu.tenant_id tenant_id,
        cuo.post
        FROM
        cscp_user cu
        INNER JOIN cscp_user_org cuo ON  cu.id = cuo.user_id
        INNER JOIN cscp_org co ON co.id = cuo.company_id
        INNER JOIN cscp_org co1 ON co1.id = cuo.org_id
        WHERE
        cuo.default_department = 1
        AND cuo.deleted = 0
        AND cu.deleted =0
        AND co.deleted = 0
        AND co1.deleted = 0
        AND cu.display = 1
        AND cu.status=1
                <if test="user.companyId != null and user.companyId != ''">
                    and cuo.company_id = #{user.companyId}
                </if>
                <if test="null != user.companyIdList and user.companyIdList.size() > 0">
                    and cuo.company_id in
                    <foreach collection="user.companyIdList" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="user.userId != null and user.userId != ''">
                    and cu.id = #{user.userId}
                </if>
                <if test="null != user.userIdList and user.userIdList.size() > 0">
                    and cu.id in
                    <foreach collection="user.userIdList" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                )
                where rn = 1
            </select>

    <update id="updateAppVersionName">
        update cscp_user set app_version_name = #{versionName} where id = #{id}
    </update>
    <update id="updateSxDataSyncFlag">
        update SX_SYNC_USER_ORG_DATA set IS_SYNC=1 where STR_ID=#{strId}
    </update>
    <update id="updateUserCompanyId">
        update cscp_user_org set "company_id" = #{companyId}
        <if test = "companyName != null and companyName != ''">
            ,"company_name" = #{companyName}
        </if>
        <if test = "orgAbbreviation != null and orgAbbreviation != ''">
            ,"org_abbreviation" = #{orgAbbreviation}
        </if>
        where "org_id" = #{orgId} and "deleted" = 0
    </update>
    <update id="updateAppCodesById">
        update cscp_user set push_app_code = #{appCodes} where id = #{userId}
    </update>

    <select id="getWorkUserList" resultMap="CscpUserBaseResultMap">

        select cu.id, cu.login_name, cu.real_name, cu.mobile, cu.email, cu.status, cu.last_login
        FROM cscp_user_work_group uw
        INNER JOIN cscp_user cu
        on uw.user_id = cu.id
        INNER JOIN cscp_user_org cuo
        on uw.user_id = cuo.user_id and uw.org_id = cuo.org_id
        left join cscp_org co
        on cuo.org_id = co.id
        where
        uw.deleted = 0
        and cu.deleted = 0
        and cuo.deleted = 0
        and cu.display = 1
        and cu.status = 1
        and uw.group_id = #{groupId}
        <if test="realName != null and realName != ''">
            and cu.real_name_start like concat('%',concat(#{realName},'%'))
        </if>
        order by cu.order_by,co.path_code,cuo.order_by
    </select>

    <select id="pageQueryCompanyUserList" resultMap="CscpUserBaseResultMap">

        select cu.id, cu.login_name, cu.real_name, cu.mobile, cu.email, cu.status, cu.last_login,cu.tenant_id
        FROM cscp_user cu
        INNER JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        where
        cu.deleted = 0
        and cuo.deleted = 0
        and cuo.department_head = 1
        and (cuo.company_id = #{orgId, jdbcType=BIGINT} or cuo.org_id = #{orgId, jdbcType=BIGINT} )
        and cu.display = 1
        and cu.status = 1
        <if test="realName != null and realName != ''">
            and cu.real_name_start like concat('%',concat(#{realName},'%'))
        </if>
        <if test="userIds != null and userIds.size > 0">
            and cu.id in
            <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
                #{userId}
            </foreach>
        </if>
        order by cu.order_by,cuo.path_code,cuo.order_by
    </select>

    <select id="selectCommCompanyUserList" resultMap="CscpUserBaseResultMap">

        select cu.id, cu.login_name, cu.real_name, cu.mobile, cu.email, cu.status, cu.last_login,cu.tenant_id
        FROM cscp_user cu
        INNER JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        where
        cu.deleted = 0
        and cuo.deleted = 0
        and cuo.department_head = 1
        and (cuo.company_id = #{orgId, jdbcType=BIGINT} or cuo.org_id = #{orgId, jdbcType=BIGINT} )
        and cu.display = 1
        and cu.status = 1
        <if test="orgId != null and orgId != ''">
            and (cuo.company_id = #{orgId, jdbcType=BIGINT} or cuo.org_id = #{orgId, jdbcType=BIGINT} )
        </if>
        <if test="realName != null and realName != ''">
            and cu.real_name_start like concat('%',concat(#{realName},'%'))
        </if>
        <if test="userIds != null and userIds.size > 0">
            and cu.id in
            <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
                #{userId}
            </foreach>
        </if>
        order by cu.order_by,cuo.path_code,cuo.order_by
    </select>
    <select id="queryUserLock" resultMap="UserLockMap">
        select
            cu.id, cu.login_name userName, cu.real_name, cu.mobile, cu.status,co.org_name AS company_name,co1.org_name AS department_name,CULR.LOCK_TIME,CULR.LOCK_IP
        FROM
            cscp_user cu
        LEFT JOIN cscp_user_org cuo ON cu.id = cuo.user_id AND cuo.deleted = 0
        LEFT JOIN cscp_org co ON co.id = cuo.company_id AND co.deleted = 0
        LEFT JOIN cscp_org co1 ON co1.id = cuo.org_id AND co1.deleted = 0
        left join (select * from (SELECT ROW_NUMBER() OVER(PARTITION BY USER_ID ORDER BY LOCK_TIME desc) RN,USER_ID,LOCK_IP,LOCK_TIME from CSCP_USER_LOCK_RECORD) where RN = 1) CULR on CULR.USER_ID = cu.id
        where cu.deleted = 0
        and cu.status = 0
        <if test="vo.realName != null and vo.realName != ''">
            and cu.real_name_start like concat('%',concat(#{vo.realName},'%'))
        </if>
        <if test="vo.userName != null and vo.userName != ''">
            and cu.login_name like concat('%',concat(#{vo.userName},'%'))
        </if>
        <if test="vo.mobile != null and vo.mobile != ''">
            and cu.mobile_start like concat('%',concat(#{vo.mobile},'%'))
        </if>
        <if test="vo.departmentOrgName != null and vo.departmentOrgName != ''">
            and co1.org_name like concat('%',concat(#{vo.departmentOrgName},'%'))
        </if>
        <if test="userId == 2">
            and cu.id not in(1,2)
        </if>
        <if test="userId == 1">
            and cu.id in(2)
        </if>
        <if test="userId == 3">
            and cu.id in(1)
        </if>
        order by CULR.LOCK_TIME desc
    </select>

    <select id="queryUserExamine" resultMap="UserExamineMap">
        select
        cu.id, cu.login_name userName,cu.sex userSex, cu.real_name, cu.mobile, cu.status,co.org_name AS company_name,co1.org_name AS department_name,co.order_by org_sort,cu.security_classification_code_name,cr.name roleName
        FROM
        cscp_user cu
        LEFT JOIN cscp_user_org cuo on cu.id = cuo.user_id and cuo.deleted = 0
        LEFT JOIN cscp_org co ON co.id = cuo.company_id
        LEFT JOIN cscp_org co1 ON co1.id = cuo.org_id
        LEFT JOIN cscp_user_role cur on cur.user_id = cu.id and cur.deleted = 0
        LEFT JOIN cscp_roles cr on cr.id = cur.role_id and cr.deleted = 0
        where cu.deleted = 0
        and cur.id is not null
        and cu.examine_status = 0
        and cu.security_classification_code_name is not null
        <if test="vo.realName != null and vo.realName != ''">
            and cu.real_name_start like concat('%',concat(#{vo.realName},'%'))
        </if>
        <if test="vo.userName != null and vo.userName != ''">
            and cu.login_name like concat('%',concat(#{vo.userName},'%'))
        </if>
        <if test="vo.mobile != null and vo.mobile != ''">
            and cu.mobile_start like concat('%',concat(#{vo.mobile},'%'))
        </if>
        <if test="vo.departmentOrgName != null and vo.departmentOrgName != ''">
            and co1.org_name like concat('%',concat(#{vo.departmentOrgName},'%'))
        </if>
        order by cu.create_time desc
    </select>

    <select id="selectUserIsDisable" resultMap="CscpUserBaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cscp_user cu
        where cu.id = #{userId} AND cu.deleted = 0 AND cu.status = 0
    </select>

    <select id="selectListUserIsNotDisable" resultMap="CscpUserBaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cscp_user cu
        where cu.deleted = 0 AND cu.status = 1
        AND cu.id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    <select id="selectUsersByCompanyId" resultType="com.ctsi.ssdc.admin.domain.CscpUser">
        SELECT DISTINCT
            u.id,
            u.mobile,
            u.real_name
        FROM
            cscp_user u
        INNER JOIN
            cscp_user_org uo
        ON
            u.id = uo.user_id
        WHERE
            u.deleted = 0
            and uo.deleted = 0
            <if test="companyId != null">
                and uo.company_id = #{companyId}
            </if>
            AND u.status = 1
            AND u.display = 1
    </select>
    <select id="selectUsersByOrgIdList" resultType="com.ctsi.ssdc.admin.domain.CscpUser">
       SELECT DISTINCT   u.id, u.mobile, u.real_name
        FROM
            cscp_user_bark u
        INNER JOIN
            cscp_user_org_bark uo
        ON
            u.id = uo.user_id
        WHERE
            u.deleted = 0
            and uo.deleted = 0
            <if test="orgIdList != null and orgIdList.size() > 0">
                and uo.org_id in
                <foreach item="orgId" collection="orgIdList" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            AND u.status = 1
            AND u.display = 1
    </select>
    <select id="baseQueryCscpUserListNoAdmin" resultMap="CscpUserBaseResultMap">
        select  <include refid="Base_Column_List"/>
            , cu.WESTONE_USER_ID, cu.HMAC_MOBILE,cu.create_time,cu.push_app_code
        FROM cscp_user cu
        <if test="user.orgId != null">
        inner join (select cuo."user_id",MIN(cuo."order_by") as order_by from "cscp_user_org" cuo
        where cuo."deleted" = 0 and cuo."org_id" = #{user.orgId} group by cuo."user_id") cuo2
        on cuo2.user_id = cu."id"
        </if>
        where cu.deleted = 0
        <if test="user.status != null">
            and cu.status = #{user.status,jdbcType=INTEGER}
        </if>
        <if test="user.id != null">
            and cu.id = #{user.id,jdbcType=BIGINT}
        </if>
        <if test="user.currentUserId != null and user.currentUserId != 2">
            and cu.id > 3
        </if>
        <if test="user.userIds != null and user.userIds.size() > 0">
            and cu.id in
            <foreach item="id" collection="user.userIds" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.realName != null and user.realName != '' and contextIndex != null and contextIndex == 'yes' ">
            <!-- 使用全文索引搜索 -->
            and CONTAINS(cu.real_name_start, #{user.realName}) > 0
        </if>

        <if test="user.realName != null and user.realName != ''">

            and REGEXP_LIKE(cu.real_name_start, concat('.*',concat(#{user.realName},'.*')))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            and cu.mobile_start like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.idCardNo != null and user.idCardNo != ''">
            and cu.id_Card_No = #{user.idCardNo}
        </if>
        <if test="user.tenantId != null and user.tenantId != ''">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <choose>
            <when test="user.tenantIdList!=null">
                and cu.tenant_id in
                <foreach collection="user.tenantIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <when test="user.tenantId!=null">
                and cu.tenant_id = #{user.tenantId}
            </when>
        </choose>
        <if test="user.securityClassificationCode != null and user.securityClassificationCode != ''">
            and cu.security_classification_code = #{user.securityClassificationCode}
        </if>
        <!-- 未推送 -->
        <if test="user.westoneUserPushedStatus != null and user.westoneUserPushedStatus == 'unPushed'">
            and (cu.WESTONE_USER_ID IS NULL OR cu.WESTONE_USER_ID = '')
        </if>
        <!-- 已推送 -->
        <if test="user.westoneUserPushedStatus != null and user.westoneUserPushedStatus == 'pushed'">
            and cu.WESTONE_USER_ID IS NOT NULL
        </if>
        <if test="user.strId != null and user.strId != ''">
            and cu.str_id like concat('%',concat(#{user.strId},'%'))
        </if>
        <if test="user.status != null and user.status != ''">
            and cu.status = #{user.status}
        </if>
        <if test="user.orgId == null">
            order by cu."order_by"
        </if>
        <if test="user.orgId != null">
            order by cuo2.order_by
        </if>
    </select>
    <select id="otherQueryCscpUserListNoAdmin" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO">
        select cuo.user_id id,
        co.order_by org_sort,
        co.create_time create_time,
        cuo.order_by sort,
        co.id department_id,
        co.org_code department_code,
        co.org_name department_name,
        cuo.company_id company_id,
        co2.org_name as company_name,
        cuo.post
        FROM cscp_org co
        INNER JOIN cscp_user_org cuo
        on cuo.org_id = co.id
        and cuo.deleted = 0
        left join cscp_org co2
        on cuo.company_id=co2.id
        and co2.deleted = 0
        where co.deleted = 0
        <if test="user.userIds != null and user.userIds.size() > 0">
            and cuo.user_id in
            <foreach item="id" collection="user.userIds" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.companyId != null and user.companyId != ''">
            and cuo.company_id = #{user.companyId}
        </if>
        <if test="user.orgId != null">
            and cuo.org_id = #{user.orgId}
        </if>
        <if test="user.companyIdList !=null and user.companyIdList.size>0">
            and cuo.company_id in
            <foreach collection="user.companyIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="user.rootOrgCodePath != null and user.rootOrgCodePath.size()>0">
            and
            <foreach collection="user.rootOrgCodePath" item="orgCodePath" open="(" close=")" separator=" or ">
                co.org_code_path like concat(#{orgCodePath},'%')
            </foreach>
        </if>
        order by cuo.default_department desc, cuo.order_by
    </select>
    <select id="queryUserByTreeIdAndRoleIdPage" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap" >

        select
            u.real_name, u.login_name
        from cscp_user_role r
                 join cscp_user u on r.user_id = u.id
                 join cscp_user_org uorg on uorg.user_id = u.id
        where r.role_id = #{roleId}  and uorg.org_id = #{orgId}
        group  by  u.real_name, u.login_name

    </select>

    <select id="queryUsersByRolePage" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO" resultMap="CscpUserBaseResultMap" >
        SELECT DISTINCT u.* FROM cscp_user u
        JOIN cscp_user_role ur ON u.id = ur.user_id
        <if test="params.orgCodePath != null and params.orgCodePath != ''">
            left join "cscp_user_org" cuo on cuo."user_id" = ur.user_id
            left join cscp_org co on co.id = cuo.org_id
        </if>
        <where>
            ur.deleted=0
            and u.deleted=0
            <if test="params.roleId != null">
                AND ur.role_id = #{params.roleId}
            </if>

            <if test="params.companyId != null">
                AND ur.company_id = #{params.companyId}
            </if>
            <if test="params.companyId == null and params.tenantId != null">
                AND (ur.tenant_id = #{params.tenantId} OR ur.create_by = 2 OR ur.create_by = 5) </if>
            <if test="params.status != null and params.status != ''">
                AND u.status = #{params.status}
            </if>
            <if test="params.display != null and params.display != ''">
                AND u.display = #{params.display}
            </if>
            <if test="params.orgCodePath != null and params.orgCodePath != ''">
                AND co.org_code_path LIKE concat(#{params.orgCodePath},'%')
            </if>
            <if test="params.realNameProcessed != null and params.realNameProcessed != ''">
                AND u.real_name_start LIKE '%' || #{params.realNameProcessed} || '%'
            </if>
            <if test="params.loginName != null and params.loginName != ''">
                AND u.login_name LIKE '%' || #{params.loginName} || '%'
            </if>
        </where>
    </select>
    <select id="selectUserCompanyId" resultType="java.lang.Long">
        select "user_id" from cscp_user_org
        where "org_id" = #{orgId} and "deleted" = 0
    </select>
    <select id="queryCscpUserByRole" resultMap="CscpUserBaseResultMap"
            parameterType="com.ctsi.ssdc.admin.domain.dto.QueryCscpUserNotInListDTO">
        select DISTINCT
        <include refid="Base_Column_List">
        </include>
        FROM cscp_user cu
        LEFT JOIN cscp_user_org org
        on cu.id = org.user_id
        left join cscp_org co on co.id = org.org_id
        where cu.deleted = 0
        AND org.deleted = 0
        AND cu.status = 1
        AND cu.display = 1
        <if test="null != ew.userIdList and ew.userIdList.size > 0">
            and org.user_id not in
            <foreach collection="ew.userIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="null!=ew.tenantId and ''!=ew.tenantId">
            and cu.tenant_id = #{ew.tenantId}
        </if>
        <if test="null!=ew.companyId and ''!=ew.companyId">
            and org.company_id = #{ew.companyId}
        </if>
        <if test="ew.realName != null and ew.realName != ''">
            <!--            and (cu.real_name = #{ew.realName} or cu.real_name_start = #{ew.realName} or cu.real_name_end =-->
            <!--            #{ew.realName})-->
            and cu.real_name_start like concat('%',concat(#{ew.realName},'%'))
        </if>
        <if test="ew.loginName != null and ew.loginName != ''">
            and cu.login_name like concat('%',concat(#{ew.loginName},'%'))
        </if>
        <if test="ew.roleId != null">
            AND NOT EXISTS (select 1 from cscp_user_role cur where cur."user_id" = cu.id AND cur."role_id" = #{ew.roleId} AND cur.deleted = 0)
        </if>
        <if test="ew.orgCodePath != null and ew.orgCodePath != ''">
            AND co.org_code_path LIKE concat(#{ew.orgCodePath},'%')
        </if>
    </select>
    <select id="queryUserByTreeIdAndRoleCodePage" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO"
            resultMap="CscpUserBaseResultMap">
        select
            u.real_name, u.login_name
        from cscp_user_role r
        join cscp_user u on r.user_id = u.id
        join cscp_user_org uorg on uorg.user_id = u.id
        join cscp_roles cr on cr.id = r.role_id
        where uorg.org_id = #{orgId}
          and uorg.deleted = 0
          and u.deleted = 0
        and cr.role_code in
        <foreach item="roleCode" collection="roleCodes" open="(" close=")" separator=",">
            #{roleCode}
        </foreach>
        group by u.real_name, u.login_name
    </select>
    <select id="selectUserLoginCount" resultType="java.lang.Integer">
        select count(1) from "cscp_log_login" where "create_by" = #{userId} and "status" = 'success'
    </select>
    <select id="selectUserById" resultMap="CscpUserBaseResultMap">
        select
        <include refid="Base_Column_List"/>
            ,cu.push_app_code
        from cscp_user cu
        where cu.id = #{userId}
    </select>

    <select id="selectUserListCount" resultType="java.lang.Integer">
        select count(1) from cscp_user cu
        left join cscp_user_org cuo on cu.id = cuo.user_id
        <if test="user.companyId != null and user.companyId != ''">
            left join ( SELECT id,
            org_name,
            order_by,
            deleted,
            company_id
            FROM cscp_org
            where deleted = 0
            START WITH id = #{user.companyId}
            CONNECT BY PRIOR id = parent_id) co
        </if>
        <if test="user.companyId == null or user.companyId == ''">
            left join cscp_org co
        </if>
        on cuo.org_id = co.id
        where cu.deleted = 0 and cuo.deleted = 0 and co.deleted = 0
        <if test="null != user.tenantId and '' != user.tenantId">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            and cu.mobile like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            and REGEXP_LIKE(cu.real_name_start, concat('.*',concat(#{user.realName},'.*')))
        </if>
        <if test="user.status != null">
            and cu.status = #{user.status}
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
    </select>

    <resultMap id="UserExportMap" type="com.ctsi.ssdc.admin.domain.dto.CscpUserExportDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="loginName" jdbcType="VARCHAR" property="loginName"/>
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="realName" jdbcType="VARCHAR" property="realName"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result column="email" jdbcType="VARCHAR" property="email" typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullValueFromPlaintextDesHandler"/>
        <result column="orderBy" jdbcType="INTEGER" property="orderBy"/>
        <result column="display" jdbcType="INTEGER" property="display"/>
        <result column="statistics" jdbcType="INTEGER" property="statistics"/>
        <result column="department_head" jdbcType="INTEGER" property="departmentHead"/>
        <result column="displayStr" jdbcType="TIMESTAMP" property="displayStr"/>
        <result column="statisticsStr" jdbcType="BIGINT" property="statisticsStr"/>
        <result column="departmentHeadStr" jdbcType="INTEGER" property="departmentHeadStr"/>
        <result column="departmentName" jdbcType="VARCHAR" property="departmentName"/>
        <result column="companyName" jdbcType="BIGINT" property="companyName"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="post" jdbcType="VARCHAR" property="post"/>
        <result column="rank" jdbcType="VARCHAR" property="rank"/>
    </resultMap>

    <select id="selectUserListByPage" resultMap="UserExportMap">
        select
        cu.id as id,
        cu."login_name" as loginName,
        cu."login_name" as userName,
        cu."real_name" as realName,
        cu."mobile" as mobile,
        cu.order_by as orderBy,
        cu.email as email,
        cu.display,cu.statistics,
        cuo.department_head as departmentHead,
        IF(cu.display != 0, '否', '是') as displayStr,
        IF(cu.statistics != 0, '否', '是') as statisticsStr,
        IF(cuo.department_head != 0, '否', '是') as departmentHeadStr,
        IF(cu.id_card_no is not null and cu.id_card_no != '', '否', '是') as idCardStr,
        cuo."org_name" as departmentName,
        cuo."company_name" as companyName,
        cuo."order_by" as sort,
        cuo.post as post,
        r.name as rank
        from cscp_user cu
        left join cscp_user_org cuo on cu.id = cuo.user_id
        left join t_sys_dict_record r on cu.rank = r.code and dict_id = 1896455771131469825
        <if test="user.companyId != null and user.companyId != ''">
            left join ( SELECT id,
            org_name,
            order_by,
            deleted,
            company_id
            FROM cscp_org
            where deleted = 0
            START WITH id = #{user.companyId}
            CONNECT BY PRIOR id = parent_id) co
        </if>
        <if test="user.companyId == null or user.companyId == ''">
            left join cscp_org co
        </if>
        on cuo.org_id = co.id
        where cu.deleted = 0 and cuo.deleted = 0 and co.deleted = 0
        <if test="null != user.tenantId and '' != user.tenantId">
            and cu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.loginName != null and user.loginName != ''">
            and cu.login_name like concat('%',concat(#{user.loginName},'%'))
        </if>
        <if test="user.departmentName != null and user.departmentName != ''">
            and co.org_name like concat('%',concat(#{user.departmentName},'%'))
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            and cu.mobile like concat('%',concat(#{user.mobile},'%'))
        </if>
        <if test="user.realName != null and user.realName != ''">
            and REGEXP_LIKE(cu.real_name_start, concat('.*',concat(#{user.realName},'.*')))
        </if>
        <if test="user.status != null">
            and cu.status = #{user.status}
        </if>
        order by cu.order_by, co.order_by, cuo.order_by
        limit #{pageNo}, #{pageSize}
    </select>
    <select id="getMaxSuffixByBaseName" resultType="java.lang.Integer">
        SELECT MAX(TO_NUMBER(SUBSTR(login_name, INSTR(login_name, '_', -1, 1) + 1))) AS max_suffix
        FROM cscp_user
        WHERE login_name LIKE concat(#{baseName}, '%')
          AND SUBSTR(login_name, INSTR(login_name, '_', -1, 1) + 1) IS NOT NULL
          AND REGEXP_LIKE(SUBSTR(login_name, INSTR(login_name, '_', -1, 1) + 1), '^[0-9]+$')
    </select>

    <!-- 获取版主管理的应用ID列表 -->
    <select id="getModeratorManageAppIds" resultType="java.lang.Long">
        SELECT DISTINCT app_id
        FROM T_SYNC_APP_MODERATOR_MANAGE
        <where>
             deleted = 0
            <if test="moderatorId != null">
                AND moderator_id = #{moderatorId}
            </if>
        </where>
    </select>


    <select id="selectUserByCompanyIdAndRegionRoleList" parameterType="java.lang.Long"
            resultMap="CscpUserBaseResultMap">
        select  <include refid="Base_Column_List">
    </include> , cr.role_code
        FROM cscp_user     cu
        LEFT JOIN cscp_user_org cuo
        on cu.id = cuo.user_id
        LEFT JOIN cscp_org co
        on cuo.org_id = co.id
        LEFT JOIN cscp_user_role cur
        on cu.id = cur.user_id
        LEFT JOIN cscp_roles cr
        on cur.role_id = cr.id
        where
        cu.deleted = 0
        and cuo.deleted = 0
        and cur.deleted = 0
        and cr.deleted = 0
        and cu.display = 1
        and co.deleted = 0
        and cu.status = 1
        and cr.role_code = 'region_role'
        and cuo.company_id = #{companyId}
    </select>
</mapper>
