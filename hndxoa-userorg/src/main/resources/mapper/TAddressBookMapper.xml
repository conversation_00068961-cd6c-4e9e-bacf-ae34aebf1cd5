<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndx.addrbook.mapper.TAddressBookMapper">

    <resultMap id="queryAddressBookUser" type="com.ctsi.hndx.addrbook.entity.dto.QueryAddressBookUserDTO">
        <id property="id" column="id"/>
        <result property="realName" column="REAL_NAME"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result property="defaultPhone" column="default_phone"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result property="telephone" column="telephone"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>

        <result property="secretaryPhone" column="secretaryPhone"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result property="secretaryName" column="secretaryName"
        />
        <result property="orgId" column="org_id"/>
        <result property="companyName" column="companyName"/>
        <result property="companyId" column="companyId"/>
        <result property="userId" column="user_id"/>
        <result property="jobTitle" column="post"/>
        <result property="sort"  jdbcType="INTEGER" column="sort"/>
        <result property="orgSort"  jdbcType="INTEGER" column="orgSort"/>

        <result property="whetherShow"  jdbcType="INTEGER" column="whetherShow"/>
        <result property="addressBookLableId"   column="addressBookLableId" javaType="java.lang.String" />
    </resultMap>

    <resultMap id="queryListPageResultMap" type="com.ctsi.hndx.addrbook.entity.dto.QueryListPageDTO">
        <result property="realName" column="REAL_NAME"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneStrFullFromBase64ValueDesHandler"/>
        <result property="defaultPhone" column="default_phone"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result property="secretaryPhone" column="SECRETARY_PHONE"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <result property="telephone" column="telephone"
                typeHandler="com.ctsi.hndx.mybatisplus.typerhander.WestoneNumberFullValueFromBase64DesHandler"/>
        <!--typeHandler="com.ctsi.hndx.mybatisplus.typerhander.FullValueDesHandler"/>-->


    </resultMap>


    <select id="querytaddressbookuser" resultMap="queryAddressBookUser"
            parameterType="com.ctsi.hndx.addrbook.entity.dto.QueryTAddressBookConditionDTO">
        SELECT tab.id,tab.REAL_NAME,tab.default_phone,tlo.org_id,tab.user_id,cuo.post,tab.JOB_TITLE AS jobTitle, tab.HMAC_DEFAULT_PHONE,
        tab.IS_SHOW AS isShow
        FROM t_address_book tab
        INNER JOIN cscp_user_org cuo ON tab.USER_ID = cuo.user_id
        INNER JOIN t_label_org tlo ON tlo.org_id = cuo.org_id
        INNER JOIN cscp_org co ON cuo.org_id = co.id
        WHERE tab.deleted = 0 and tab.status = 1
        and cuo.deleted = 0 and tlo.deleted = 0  and co.deleted = 0
          and tlo.display_range_id in
        <foreach collection="parm.labelIdList" index="index" item="labelId" open="(" separator="," close=")">
            #{labelId}
        </foreach>
        <if test="parm.nameOrPhone!=null and parm.nameOrPhone!=''">
            and (
            tab.REAL_PIN_YIN like concat('%',#{parm.realPinYin},'%')
            or
            tab.REAL_NAME like concat('%',#{parm.nameOrPhone},'%')
            or
            tab.LAST_NAME_ENCRYPT like concat('%',#{parm.nameOrPhone},'%')
            or
            tab.FIRST_NAME_ENCRYPT  like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE  like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_1 like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_2 like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_3 like concat('%',#{parm.nameOrPhone},'%')
            <if test="parm.orgIdCondition!=null and parm.orgIdCondition.size()>0">
                or tlo.org_id in
                <foreach collection="parm.orgIdCondition" index="index" item="orgConditionId" open="(" separator=","
                         close=")">
                    #{orgConditionId}
                </foreach>
            </if>
            )
        </if>
        and tlo.org_id in
        <foreach collection="parm.orgId" index="index" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
        GROUP BY tab.USER_ID
        ORDER BY co.order_by,cuo.order_by
    </select>


    <select id="queryListPage" resultMap="queryListPageResultMap">
        SELECT
            tab.id,
            tab.sort,
            tab.address_book_label_id AS addressBookLableId,
            tab.SECRETARY_NAME,
            tab.SECRETARY_PHONE,
            tab.TELEPHONE,
            cu.id AS userId,
            cuo.org_id,
            cuo.order_by AS orgSort,
            tab.HMAC_DEFAULT_PHONE,
            tab.REAL_NAME,
            tab.JOB_TITLE,
            tab.DEFAULT_PHONE,
            co.org_name,
            tab.IS_SHOW AS whetherShow,
            tab.display
        FROM t_address_book tab
        INNER JOIN cscp_user cu ON cu.id = tab.USER_ID
        INNER JOIN (
            SELECT
                user_id,
                org_id,
                order_by,
                company_id  -- 添加company_id避免回表
            FROM cscp_user_org
            WHERE
                deleted = 0
                AND default_department = 1
                -- 优化OR条件为UNION
                <if test="null != condition.companyId and condition.companyId != ''">
                    AND (company_id = #{condition.companyId} OR org_id = #{condition.companyId})
                </if>
        ) cuo ON tab.USER_ID = cuo.user_id
        INNER JOIN cscp_org co ON
            cuo.org_id = co.id
            AND co.deleted = 0
            <if test="null != condition.orgName and condition.orgName != ''">
                AND co.org_name LIKE CONCAT('%', #{condition.orgName}, '%')
            </if>
            <if test="condition.rootOrgCodePath != null and condition.rootOrgCodePath.size() > 0">
                AND (
                <foreach collection="condition.rootOrgCodePath" item="orgCodePath" separator=" OR ">
                    co.org_code_path LIKE CONCAT(#{orgCodePath}, '%')
                </foreach>
                )
            </if>
        WHERE
            tab.deleted = 0
            AND tab.status = 1
            AND cu.deleted = 0
            <if test="null != condition.realName and condition.realName != ''">
                AND (
                tab.REAL_PIN_YIN LIKE CONCAT('%', #{condition.realPinYin}, '%')
                OR tab.REAL_NAME LIKE CONCAT('%', #{condition.realName}, '%')
                OR tab.LAST_NAME_ENCRYPT LIKE CONCAT('%', #{condition.realName}, '%')
                OR tab.FIRST_NAME_ENCRYPT LIKE CONCAT('%', #{condition.realName}, '%')
                )
            </if>
            <if test="null != condition.defaultPhone and condition.defaultPhone != ''">
                AND tab.DEFAULT_PHONE_ENCRYPT_1 LIKE CONCAT('%', #{condition.defaultPhone}, '%')
            </if>
            <choose>
                <when test="null != condition.companyId and condition.companyId != ''">
                    ORDER BY cuo.order_by
                </when>
                <otherwise>
                    ORDER BY co.order_by, cuo.order_by
                </otherwise>
            </choose>

    </select>



    <select id="querytaddressbookuserPc" resultMap="queryAddressBookUser"
            parameterType="com.ctsi.hndx.addrbook.entity.dto.QueryTAddressBookConditionDTO">
        SELECT tab.id,tab.REAL_NAME,tab.default_phone,tlo.org_id,tab.user_id,cuo.post
        ,oo.org_name AS companyName,oo.id AS companyId
        ,tab.sort ,org.order_by AS org_sort ,org.org_name AS org_name ,tab.JOB_TITLE AS job_title
        ,tab.display ,tab.telephone as telephone, a.sex
        ,tab.ADDRESS_BOOK_LABEL_ID as addressBookLableId, tab.HMAC_DEFAULT_PHONE
        , tab.IS_SHOW as whetherShow ,tab.SECRETARY_NAME as secretaryName,tab.SECRETARY_PHONE as secretaryPhone
        FROM t_address_book tab
        INNER JOIN cscp_user_org cuo ON tab.USER_ID = cuo.user_id
        INNER JOIN t_label_org tlo ON tlo.org_id = cuo.org_id
        LEFT JOIN cscp_org org ON org.id = tlo.org_id
        LEFT JOIN cscp_user a  ON  a.id = tab.USER_ID

        LEFT JOIN  (  SELECT aa.id,aa.org_name   FROM cscp_org aa WHERE aa.type=2 AND aa.id= aa.company_id  ) oo
        ON oo.id =org.company_id

        WHERE
        cuo.deleted=0 AND tlo.deleted=0
        AND tab.display=1 AND a.deleted=0
        AND tab.deleted=0 and tab.status = 1
        <if test="parm.labelIdList != null and parm.labelIdList.size > 0">
            and  tlo.display_range_id in
            <foreach collection="parm.labelIdList" index="index" item="labelId" open="(" separator="," close=")">
                #{labelId}
            </foreach>
        </if>
        <if test="parm.nameOrPhone!=null and parm.nameOrPhone!=''">
            and (
            binary tab.REAL_NAME   like concat('%',#{parm.nameOrPhone},'%')
            or
            binary   tab.LAST_NAME_ENCRYPT  like concat('%',#{parm.nameOrPhone},'%')
            or
            binary   tab.FIRST_NAME_ENCRYPT  like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE   like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_1    like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_2    like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_3    like concat('%',#{parm.nameOrPhone},'%')
            )
        </if>
        <if test="parm.realName!=null and parm.realName!=''">
            and (
            tab.REAL_PIN_YIN like concat('%',#{parm.realName},'%')
            or
            binary  tab.REAL_NAME    like concat('%',#{parm.realName},'%')
            or
            binary tab.LAST_NAME_ENCRYPT   like concat('%',#{parm.realName},'%')
            or
            binary tab.FIRST_NAME_ENCRYPT    like concat('%',#{parm.realName},'%')

            )
        </if>
        <if test="parm.defaultPhone!=null and parm.defaultPhone!=''">
            and (
            DEFAULT_PHONE   like concat('%',#{parm.defaultPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_1   like concat('%',#{parm.defaultPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_2   like concat('%',#{parm.defaultPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_3   like concat('%',#{parm.defaultPhone},'%')
            )
        </if>
        <if test="parm.orgName!=null and parm.orgName!=''">
            and  org.org_name like  concat('%',#{parm.orgName},'%')
        </if>



        and tlo.org_id in
        <foreach collection="parm.orgId" index="index" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
        AND NOT EXISTS (
        SELECT 1
        FROM t_address_book tab2
        WHERE tab2.USER_ID = tab.USER_ID
        AND tab2.id > tab.id
        ) order by org.order_by,cuo.order_by
    </select>


    <select id="querytaddressbookuserapp" resultMap="queryAddressBookUser"
            parameterType="com.ctsi.hndx.addrbook.entity.dto.QueryTAddressBookConditionDTO">
        SELECT
            tab.id,
            tab.REAL_NAME,
            tab.default_phone,
            cuo.org_id,
            tab.user_id,
            cuo.post,
            cuo.company_name AS companyName,
            cuo.company_id AS companyId,
            tab.sort,
            org.order_by AS org_sort,
            org.org_name AS org_name,
            tab.JOB_TITLE AS job_title,
            a.display,
            tab.telephone,
            a.sex,
            tab.ADDRESS_BOOK_LABEL_ID AS addressBookLableId,
            tab.HMAC_DEFAULT_PHONE,
            tab.IS_SHOW AS whetherShow,
            SECRETARY_NAME AS secretaryName,
            SECRETARY_PHONE AS secretaryPhone
        FROM t_address_book tab
        INNER JOIN cscp_user_org cuo ON tab.USER_ID = cuo.user_id
        <!--INNER JOIN t_label_org tlo ON tlo.org_id = cuo.org_id-->
        LEFT JOIN cscp_org org ON org.id = cuo.org_id
        LEFT JOIN cscp_user a ON a.id = tab.USER_ID
        <!--LEFT JOIN (-->
        <!--    SELECT aa.id, aa.org_name-->
        <!--    FROM cscp_org aa-->
        <!--    WHERE aa.type = 2 AND aa.id = aa.company_id-->
        <!--) oo ON oo.id = org.company_id-->
        WHERE
        cuo.deleted=0 AND org.deleted=0 AND a.deleted=0
        AND tab.display=1  AND tab.status = 1 AND tab.deleted=0

        <!--<if test="parm.labelIdList != null and parm.labelIdList.size > 0">-->
        <!--    and  tlo.display_range_id in-->
        <!--    <foreach collection="parm.labelIdList" index="index" item="labelId" open="(" separator="," close=")">-->
        <!--        #{labelId}-->
        <!--    </foreach>-->
        <!--</if>-->
        <if test="parm.nameOrPhone!=null and parm.nameOrPhone!=''">
            and (
            <if test="parm.realPinYin!=null and parm.nameOrPhone!=''">
                tab.REAL_PIN_YIN LIKE concat( '%',#{parm.realPinYin},'%' )
                or
            </if>

            <if test="parm.orgName!=null and parm.orgName!=''">
                org.org_name like  concat('%',#{parm.orgName},'%')
                or
            </if>

            <!--binary tab.REAL_NAME   like concat('%',#{parm.nameOrPhone},'%')-->
            REGEXP_LIKE(a.REAL_NAME, REPLACE(#{parm.nameOrPhone}, '+', '\+'), 'c')
            or
            binary   tab.LAST_NAME_ENCRYPT  like concat('%',#{parm.nameOrPhone},'%')
            or
            binary   tab.FIRST_NAME_ENCRYPT  like concat('%',#{parm.nameOrPhone},'%')
            or
            <!--DEFAULT_PHONE   like concat('%',#{parm.nameOrPhone},'%')-->
            REGEXP_LIKE(DEFAULT_PHONE, concat('.*',concat(#{parm.nameOrPhone},'.*')), 'c')
            or
            DEFAULT_PHONE_ENCRYPT_1    like concat('%',#{parm.nameOrPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_2    like concat('%',#{parm.nameOrPhone},'%')
            or
            REGEXP_LIKE(a.real_name_start, REPLACE(#{parm.nameOrPhone}, '+', '\+'), 'c')
            or
            REGEXP_LIKE(a.mobile_start, concat('.*',concat(#{parm.nameOrPhone},'.*')), 'c')
            )
        </if>
        <if test="parm.orgCodeList != null and parm.orgCodeList.size() > 0">
            and
            <foreach collection="parm.orgCodeList" item="prefix" open="(" separator=" OR " close=")">
                org.org_code LIKE concat(#{prefix},'%')
            </foreach>
        </if>

        <if test="parm.realName!=null and parm.realName!=''">
            and (
            tab.REAL_PIN_YIN like concat('%',#{parm.realName},'%')
            or
            binary  tab.REAL_NAME    like concat('%',#{parm.realName},'%')
            or
            binary tab.LAST_NAME_ENCRYPT   like concat('%',#{parm.realName},'%')
            or
            binary tab.FIRST_NAME_ENCRYPT    like concat('%',#{parm.realName},'%')

            )
        </if>
        <if test="parm.defaultPhone!=null and parm.defaultPhone!=''">
            and (
            DEFAULT_PHONE   like concat('%',#{parm.defaultPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_1   like concat('%',#{parm.defaultPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_2   like concat('%',#{parm.defaultPhone},'%')
            or
            DEFAULT_PHONE_ENCRYPT_3   like concat('%',#{parm.defaultPhone},'%')
            )
        </if>

        <if test="parm.orgId != null and parm.orgId.size() > 0">
            and org.id in
            <foreach collection="parm.orgId" index="index" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        AND NOT EXISTS (
        SELECT 1
        FROM t_address_book tab2
        WHERE tab2.USER_ID = tab.USER_ID
        AND tab2.id > tab.id
        ) order by org.order_by,cuo.order_by
    </select>


    <select id="selectAdressCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        t_address_book tab
        INNER JOIN cscp_user_org cuo
        ON tab.USER_ID = cuo.user_id and cuo.deleted = 0 AND cuo.org_id = #{orgId}
        WHERE
        tab.display = 1
        AND tab.deleted = 0
        AND tab.status = 1
        AND EXISTS (
            SELECT 1
            FROM cscp_user a
            WHERE a.id = tab.USER_ID
            AND a.deleted = 0
            AND a.real_name NOT LIKE '%admin'
        )
        <if test="encrypt !=null and encrypt !=''">
            and (
            tab.REAL_NAME = #{encrypt}
            or
            tab.LAST_NAME_ENCRYPT = #{encrypt}
            or
            tab.FIRST_NAME_ENCRYPT = #{encrypt}
            )
        </if>
    </select>

    <select id="selectAdressInfo" resultType="com.ctsi.hndx.addrbook.entity.TAddressBook">
        SELECT
        tab.*,tab.IS_SHOW AS whetherShow
        FROM
        t_address_book tab
        INNER JOIN cscp_user_org cuo
        ON tab.USER_ID = cuo.user_id AND cuo.deleted = 0 AND cuo.org_id = #{orgId}
        WHERE
        tab.display = 1
        AND tab.deleted = 0
        AND tab.status = 1
        AND EXISTS (
            SELECT 1
            FROM cscp_user a
            WHERE a.id = tab.USER_ID
            AND a.deleted = 0
            AND a.real_name NOT LIKE '%admin'
        )
        <if test="encrypt !=null and encrypt !=''">
            and (
            tab.REAL_NAME = #{encrypt}
            or
            tab.LAST_NAME_ENCRYPT = #{encrypt}
            or
            tab.FIRST_NAME_ENCRYPT = #{encrypt}
            )
        </if>
        ORDER BY cuo.order_by
        limit #{start},#{pageSize}
    </select>

    <select id="selectCurrentOrgTAddressBookList" resultType="com.ctsi.hndx.addrbook.entity.dto.TAddressBookUserDTO">
        SELECT  tab.id, tab.user_id, tab.real_name, tab.default_phone, tab.telephone, tab.sort, tab.sex,
                tab.hmac_default_phone,  a.str_id, a.backup_mobile, tab.IS_SHOW AS whetherShow,
                b.post AS job_title, b.org_id, b.org_name, b.company_id, b.company_name
        FROM cscp_user a
        inner JOIN
        cscp_user_org b  ON a.id=b.user_id
        inner JOIN
        t_address_book tab ON tab.USER_ID = b.user_id
        WHERE  b.deleted=0 AND tab.display = 1  AND a.deleted = 0  AND b.org_id=#{orgId} AND tab.deleted = 0 and a.status = 1 and tab.status = 1
        AND a.real_name NOT LIKE '%admin'
        order by b.order_by
    </select>

    <select id="selectUserLabelIdList" resultType="com.ctsi.hndx.addrbook.entity.dto.TAddressBookAuthorizeDTO">
        select aba.id,aba.label_id,aba.label_name, co.org_code, co.org_id_path, co.id as unit_id, co.org_name as unit_name, aba.sort
        from t_address_book_authorize aba
            left join T_Label_Org tlo on tlo.display_range_id = aba.label_id
            left join cscp_org co on co.id = tlo.org_id
        where
            aba.deleted = 0 and tlo.deleted = 0 and co.deleted = 0
        <if test="ogrIdList != null and ogrIdList.size > 0">
            and  aba.unit_id in
            <foreach collection="ogrIdList" index="index" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
    </select>

    <select id="selectAddressBookUserApp" resultMap="queryAddressBookUser"
            parameterType="com.ctsi.hndx.addrbook.entity.dto.QueryTAddressBookConditionDTO">
        SELECT
            tab.id,
            tab.REAL_NAME,
            tab.default_phone,
            cuo.org_id,
            org.org_code,
            tab.user_id,
            cuo.post,
            cuo.company_name AS companyName,
            cuo.company_id AS companyId,
            tab.sort,
            org.order_by AS org_sort,
            org.org_name AS org_name,
            tab.JOB_TITLE AS job_title,
            tab.display,
            tab.telephone,
            tab.sex,
            tab.ADDRESS_BOOK_LABEL_ID AS addressBookLableId,
            tab.HMAC_DEFAULT_PHONE,
            tab.IS_SHOW AS whetherShow,
            SECRETARY_NAME AS secretaryName,
            SECRETARY_PHONE AS secretaryPhone
        FROM t_address_book tab
        INNER JOIN cscp_user_org cuo ON tab.USER_ID = cuo.user_id
        LEFT JOIN cscp_org org ON org.id = cuo.org_id
        <!--LEFT JOIN cscp_user a ON a.id = tab.USER_ID-->
        WHERE cuo.deleted=0 AND org.deleted=0 AND   tab.deleted = 0  and  tab.display=1
        <if test="searchPhone!=null and searchPhone!=''">
            and (
                REGEXP_LIKE(tab.DEFAULT_PHONE_ENCRYPT_1, REPLACE(#{searchPhone}, '+', '\+'), 'c')
                or  REGEXP_LIKE(tab.TELEPHONE_ENCRYPT_1, REPLACE(#{searchPhone}, '+', '\+'), 'c')
            )
        </if>

        <if test="searchName!=null and searchName!=''">
            and (
                REGEXP_LIKE(tab.LAST_NAME_ENCRYPT, REPLACE(#{searchName}, '+', '\+'), 'c')
                <!--or  REGEXP_LIKE(a.real_name_start, REPLACE(#{searchName}, '+', '\+'), 'c')-->
                <if test="orgName!=null and orgName !=''">
                    or org.org_name like concat('%',#{orgName},'%')
                </if>
            )
        </if>
        order by org.order_by,cuo.order_by
        limit #{limit}
    </select>

    <select id="selectAddressBookUserBasic" resultMap="queryAddressBookUser"
            parameterType="com.ctsi.hndx.addrbook.entity.dto.QueryTAddressBookConditionDTO">
        SELECT
        tab.id,
        tab.REAL_NAME,
        tab.default_phone,
        tab.user_id,
        cuo.post,
        org.org_name AS companyName,
        org.id AS companyId,
        org.org_id_path,
        tab.sort,
        org.order_by AS org_sort,
        cuo.org_id,
        org.org_code,
        cuo.org_name AS org_name,
        tab.JOB_TITLE AS job_title,
        tab.display,
        tab.telephone,
        tab.sex,
        tab.ADDRESS_BOOK_LABEL_ID AS addressBookLableId,
        tab.HMAC_DEFAULT_PHONE,
        tab.IS_SHOW AS whetherShow,
        SECRETARY_NAME AS secretaryName,
        SECRETARY_PHONE AS secretaryPhone
        FROM t_address_book tab
        INNER JOIN cscp_user_org cuo ON tab.USER_ID = cuo.user_id and cuo.deleted=0
        LEFT JOIN cscp_org org  ON org.id = cuo.company_id AND org.deleted=0
        <!--LEFT JOIN cscp_user a ON a.id = tab.USER_ID-->
        WHERE  tab.deleted = 0  and  tab.display=1
        <!--使用EXISTS来高效判断用户是否有效-->
        AND EXISTS (
            SELECT 1
            FROM cscp_user a
            WHERE a.id = tab.USER_ID AND a.deleted = 0 and a.status = 1
        )
        <if test="currentOrgCode != null and currentOrgCode != '' or highestOrgCode != null and highestOrgCode != ''">
            AND (
            <if test="currentOrgCode != null and currentOrgCode != ''">
                org.org_code LIKE CONCAT(#{currentOrgCode}, '|%')
                <if test="highestOrgCode != null and highestOrgCode != ''">
                    OR org.org_code_path LIKE CONCAT(#{highestOrgCode}, '|%')
                </if>
            </if>
            <if test="currentOrgCode == null or currentOrgCode == ''">
                org.org_code_path LIKE CONCAT(#{highestOrgCode}, '|%')
            </if>
            )
        </if>
        <if test="searchPhone!=null and searchPhone!=''">
            and (
            REGEXP_LIKE(tab.DEFAULT_PHONE_ENCRYPT_1, REPLACE(#{searchPhone}, '+', '\+'), 'c')
            or  REGEXP_LIKE(tab.TELEPHONE_ENCRYPT_1, REPLACE(#{searchPhone}, '+', '\+'), 'c')
            )
        </if>

        <if test="searchName!=null and searchName!=''">
            and (
            REGEXP_LIKE(tab.LAST_NAME_ENCRYPT, REPLACE(#{searchName}, '+', '\+'), 'c')
            <if test="orgName!=null and orgName !=''">
                or org.org_name like concat('%',#{orgName},'%')
            </if>
            )
        </if>
        order by org.org_code, org.order_by, cuo.order_by
    </select>

    <select id="selectAddressBookUserFullTextSearch" resultMap="queryAddressBookUser"
            parameterType="com.ctsi.hndx.addrbook.entity.dto.QueryTAddressBookConditionDTO">
        SELECT
            tab.id,
            tab.REAL_NAME,
            tab.default_phone,
            tab.user_id,
            cuo.post,
            org.org_name AS companyName,
            org.org_id_path,
            tab.sort,
            org.order_by AS org_sort,
            cuo.org_id,
            org.org_code,
            cuo.org_name AS org_name,
            tab.JOB_TITLE AS job_title,
            tab.display,
            tab.telephone,
            tab.sex,
            tab.ADDRESS_BOOK_LABEL_ID AS addressBookLableId,
            tab.HMAC_DEFAULT_PHONE,
            tab.IS_SHOW AS whetherShow,
            SECRETARY_NAME AS secretaryName,
            SECRETARY_PHONE AS secretaryPhone
        FROM t_address_book tab
        INNER JOIN cscp_user_org cuo ON tab.USER_ID = cuo.user_id AND cuo.deleted = 0
        INNER JOIN cscp_org org ON org.id = cuo.company_id AND org.deleted = 0
        WHERE tab.deleted = 0 AND tab.display = 1
        AND EXISTS (
            SELECT 1
            FROM cscp_user a
            WHERE a.id = tab.USER_ID AND a.deleted = 0 AND a.status = 1
        )
        <if test="currentOrgCode != null and currentOrgCode != '' or highestOrgCode != null and highestOrgCode != ''">
            AND (
            <if test="currentOrgCode != null and currentOrgCode != ''">
                org.org_code LIKE CONCAT(#{currentOrgCode}, '|%')
                <if test="highestOrgCode != null and highestOrgCode != ''">
                    OR org.org_code_path LIKE CONCAT(#{highestOrgCode}, '|%')
                </if>
            </if>
            <if test="currentOrgCode == null or currentOrgCode == ''">
                org.org_code_path LIKE CONCAT(#{highestOrgCode}, '|%')
            </if>
            )
        </if>
        <if test="searchPhone!=null and searchPhone!=''">
            and ((
                <!-- 使用全文索引搜索 -->
                CONTAINS(tab.DEFAULT_PHONE_ENCRYPT_1, #{searchPhone}) > 0
                AND REGEXP_LIKE(tab.DEFAULT_PHONE_ENCRYPT_1, REPLACE(#{searchPhone}, '+', '\+'), 'c'))
            or  (<!-- 使用全文索引搜索 -->
                CONTAINS(tab.TELEPHONE_ENCRYPT_1, #{searchPhone}) > 0
                AND REGEXP_LIKE(tab.TELEPHONE_ENCRYPT_1, REPLACE(#{searchPhone}, '+', '\+'), 'c')
            ))
        </if>

        <if test="searchName!=null and searchName!=''">
            and (
                <!-- 使用全文索引搜索 -->
                CONTAINS(tab.LAST_NAME_ENCRYPT, #{searchName}) > 0
                and REGEXP_LIKE(tab.LAST_NAME_ENCRYPT, REPLACE(#{searchName}, '+', '\+'), 'c')
            <if test="orgName!=null and orgName !=''">
                or org.org_name like concat('%',#{orgName},'%')
            </if>
            )
        </if>
        order by org.org_code, org.order_by, cuo.order_by
    </select>

</mapper>
