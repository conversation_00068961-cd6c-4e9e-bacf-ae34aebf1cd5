//package com.ctsi.service;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.ctsi.hndx.common.BasePageForm;
//import com.ctsi.hndx.utils.BeanConvertUtils;
//import com.ctsi.hndx.utils.PageHelperUtil;
//import com.ctsi.hndx.utils.StringUtils;
//import com.ctsi.ssdc.admin.domain.CscpOrg;
//import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
//import com.ctsi.ssdc.admin.service.CscpOrgService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.DisposableBean;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.context.event.ApplicationStartedEvent;
//import org.springframework.cache.Cache;
//import org.springframework.cache.CacheManager;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.UUID;
//import java.util.concurrent.*;
//
//@Slf4j
//@Component
//@EnableAsync
//@SuppressWarnings("all")
//public class OrgCacheService implements DisposableBean, ApplicationListener<ApplicationStartedEvent> {
//
//    @Autowired
//    private CscpOrgService cscpOrgService;
//
//    @Autowired
//    private CacheManager cacheManager;
//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//
//    // 缓存名称
//    private static final String CACHE_NAME = "orgCache-details";
//    // 分布式锁的key
//    private static final String CACHE_INIT_LOCK_KEY = "org:cache:init:lock";
//    // 锁的初始过期时间（用于续期基础）
//    private static final long LOCK_INIT_EXPIRE_SECONDS = 60L;
//    // 续期间隔（必须小于初始过期时间）
//    private static final long LOCK_RENEW_INTERVAL_SECONDS = 50L;
//    // 尝试获取锁的最大等待时间
//    private static final long MAX_WAIT_MILLIS = 5000L;
//    // 分页查询重试次数
//    private static final int QUERY_RETRY_COUNT = 3;
//    // 优化：查询间隔（毫秒），控制查询速率
//    private static final long QUERY_INTERVAL_MILLIS = 200;
//    // 保存当前持有的锁值
//    private String currentLockValue;
//    // 标记是否正在执行缓存初始化
//    private volatile boolean isCacheInitializing = false;
//    // 锁续期执行器
//    private ScheduledExecutorService lockRenewalExecutor;
//    // 锁续期任务引用
//    private volatile ScheduledFuture<?> lockRenewalFuture;
//    // 新增：数据库连接并发控制信号量（最多3个连接）
//    private final Semaphore dbConnectionSemaphore = new Semaphore(3);
//    private static final int MAX_DB_CONNECTIONS = 3;
//
//    public OrgCacheService() {
//        // 初始化续期线程池（单线程足够）
//        lockRenewalExecutor = Executors.newSingleThreadScheduledExecutor(
//                r -> {
//                    Thread thread = new Thread(r, "Lock-Renewal-Thread");
//                    thread.setDaemon(true);
//                    return thread;
//                }
//        );
//    }
//
//    // 修改监听的事件，确保数据源完全初始化后再执行
//    @Override
//    @Async("orgCacheExecutor")
//    public void onApplicationEvent(ApplicationStartedEvent event) {
//        // 额外延迟几秒确保数据源完全就绪
//        try {
//            Thread.sleep(5000);
//            log.info("确保数据源初始化完成，开始异步初始化机构缓存...");
//            this.triggerCacheInit();
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            log.error("缓存加载延迟被中断", e);
//        }
//    }
//
//    // 触发缓存初始化
//    public void triggerCacheInit() {
//        currentLockValue = UUID.randomUUID().toString();
//
//        try {
//            boolean locked = tryAcquireLock(currentLockValue, MAX_WAIT_MILLIS);
//            if (locked) {
//                log.info("当前节点获取到分布式锁，开始执行机构缓存初始化");
//                this.cacheAllOrgsAsync();
//            } else {
//                log.info("其他节点已获取分布式锁，当前节点跳过机构缓存初始化");
//            }
//        } catch (Exception e) {
//            log.error("分布式锁处理异常", e);
//            releaseLock(currentLockValue);
//        }
//    }
//
//    // 异步批量缓存所有机构（限制数据库连接数）
//    @Async("orgCacheExecutor")
//    public CompletableFuture<Void> cacheAllOrgsAsync() {
//        isCacheInitializing = true;
//        try {
//            int pageSize = 500;
//            LambdaQueryWrapper<CscpOrg> queryWrapper = this.orgLqw();
//
//            // 先获取一个连接来查询总数
//            if (!dbConnectionSemaphore.tryAcquire(1, 30, TimeUnit.SECONDS)) {
//                log.error("获取数据库连接超时，无法查询总条数");
//                return CompletableFuture.completedFuture(null);
//            }
//
//            try {
//                int totalCount = cscpOrgService.selectCountNoAdd(queryWrapper);
//                int totalPages = (totalCount + pageSize - 1) / pageSize;
//
//                log.info("开始异步缓存机构数据，查询条件过滤后总条数：{}，分{}页，每页{}条，最大并发连接数：{}",
//                        totalCount, totalPages, pageSize, MAX_DB_CONNECTIONS);
//
//                // 使用 CountDownLatch 控制分页查询的并发度
//                CountDownLatch pageLatch = new CountDownLatch(totalPages);
//                ExecutorService pageExecutor = Executors.newFixedThreadPool(MAX_DB_CONNECTIONS);
//
//                for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
//                    final int currentPage = pageNum;
//                    pageExecutor.submit(() -> {
//                        if (!acquireDbConnection()) {
//                            log.warn("页码{}: 获取数据库连接失败，跳过", currentPage);
//                            pageLatch.countDown();
//                            return;
//                        }
//
//                        try {
//                            processPage(currentPage, pageSize, queryWrapper, totalPages);
//                        } catch (Exception e) {
//                            log.error("页码{}处理异常", currentPage, e);
//                        } finally {
//                            releaseDbConnection();
//                            pageLatch.countDown();
//                        }
//                    });
//
//                    // 控制提交速度，避免瞬间提交太多任务
//                    if (currentPage % MAX_DB_CONNECTIONS == 0) {
//                        Thread.sleep(100);
//                    }
//                }
//
//                // 等待所有分页处理完成
//                pageLatch.await();
//                pageExecutor.shutdown();
//                pageExecutor.awaitTermination(1, TimeUnit.MINUTES);
//
//            } finally {
//                dbConnectionSemaphore.release();
//            }
//
//            log.info("所有机构数据缓存完成");
//            return CompletableFuture.completedFuture(null);
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            log.error("缓存加载被中断", e);
//            return failedFuture(e);
//        } catch (Exception e) {
//            log.error("缓存加载异常", e);
//            return failedFuture(e);
//        } finally {
//            isCacheInitializing = false;
//            releaseLock(currentLockValue);
//        }
//    }
//
//    // 获取数据库连接（带超时和重试）
//    private boolean acquireDbConnection() {
//        try {
//            return dbConnectionSemaphore.tryAcquire(1, 60, TimeUnit.SECONDS);
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            log.warn("获取数据库连接被中断");
//            return false;
//        }
//    }
//
//    // 释放数据库连接
//    private void releaseDbConnection() {
//        dbConnectionSemaphore.release();
//    }
//
//    // 处理单个分页
//    private void processPage(int pageNum, int pageSize,
//                             LambdaQueryWrapper<CscpOrg> queryWrapper, int totalPages) {
//        for (int retry = 1; retry <= QUERY_RETRY_COUNT; retry++) {
//            try {
//                BasePageForm basePageForm = new BasePageForm(pageNum, pageSize);
//                log.debug("查询机构数据，页码：{}，页大小：{}，重试次数：{}，可用连接：{}",
//                        pageNum, pageSize, retry, dbConnectionSemaphore.availablePermits());
//
//                IPage<CscpOrg> pageResult = cscpOrgService.selectPageNoAdd(
//                        PageHelperUtil.getMPlusPageByBasePage(basePageForm),
//                        queryWrapper
//                );
//                List<CscpOrg> orgList = pageResult.getRecords();
//
//                log.debug("页码{}查询完成，实际返回条数：{}", pageNum, orgList.size());
//
//                Cache orgCache = cacheManager.getCache(CACHE_NAME);
//                orgList.forEach(org -> {
//                    CscpOrgDTO orgDTO = BeanConvertUtils.copyProperties(org, CscpOrgDTO.class);
//                    orgCache.putIfAbsent(orgDTO.getId(), orgDTO);
//                });
//
//                if (pageNum % 20 == 0) {
//                    log.info("机构数据缓存进度：{}/{}页，可用连接：{}",
//                            pageNum, totalPages, dbConnectionSemaphore.availablePermits());
//                }
//
//                Thread.sleep(QUERY_INTERVAL_MILLIS);
//                break; // 成功则跳出重试循环
//            } catch (Exception e) {
//                log.error("缓存机构数据第{}页失败（第{}次重试）", pageNum, retry, e);
//                if (retry == QUERY_RETRY_COUNT) {
//                    log.error("第{}页达到最大重试次数，数据未缓存！", pageNum);
//                }
//                try {
//                    Thread.sleep(100 * retry);
//                } catch (InterruptedException ie) {
//                    Thread.currentThread().interrupt();
//                    break;
//                }
//            }
//        }
//    }
//
//    // 缓存单个机构信息
//    @Cacheable(value = CACHE_NAME, key = "#id")
//    public CscpOrgDTO getOrgById(Long id) {
//        return BeanConvertUtils.copyProperties(cscpOrgService.getById(id), CscpOrgDTO.class);
//    }
//
//    // 清除单个机构的缓存
//    @CacheEvict(value = CACHE_NAME, key = "#id")
//    public void clearOrgCache(Long id) {
//        log.info("清除机构ID为{}的缓存", id);
//    }
//
//
//    // 清除所有机构的缓存
//    @CacheEvict(value = CACHE_NAME, allEntries = true)
//    public void clearAllOrgCache() {
//        log.info("清除所有机构的缓存");
//    }
//
//
//    // 清除所有缓存（带分布式锁）
//    @Async("orgCacheExecutor")
//    public CompletableFuture<Void> clearAllOrgCacheDistributed() {
//        String lockValue = UUID.randomUUID().toString();
//
//        try {
//            boolean locked = tryAcquireLock(lockValue, MAX_WAIT_MILLIS);
//            if (locked) {
//                log.info("获取到分布式锁，开始清除所有机构缓存");
//                clearAllOrgCache();
//                // 清除后重新加载缓存（根据业务需求开启）
//                // this.cacheAllOrgsAsync().get();
//            } else {
//                log.info("其他节点正在执行清除操作，当前节点跳过");
//            }
//        } catch (Exception e) {
//            log.error("分布式清除缓存异常", e);
//        } finally {
//            releaseLock(lockValue);
//        }
//        return CompletableFuture.completedFuture(null);
//    }
//
//
//    // 尝试获取分布式锁
//    private boolean tryAcquireLock(String lockValue, long maxWaitMillis) throws InterruptedException {
//        long start = System.currentTimeMillis();
//        while (System.currentTimeMillis() - start < maxWaitMillis) {
//            // 设置初始过期时间（较短），后续通过续期维持锁
//            Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(
//                    CACHE_INIT_LOCK_KEY,
//                    lockValue,
//                    LOCK_INIT_EXPIRE_SECONDS,
//                    TimeUnit.SECONDS
//            );
//            if (Boolean.TRUE.equals(success)) {
//                // 启动锁续期任务
//                startLockRenewal(lockValue);
//                return true;
//            }
//            Thread.sleep(100);
//        }
//        return false;
//    }
//
//
//    // 启动锁续期任务
//    private void startLockRenewal(String lockValue) {
//        // 取消可能存在的旧任务
//        if (lockRenewalFuture != null && !lockRenewalFuture.isCancelled()) {
//            lockRenewalFuture.cancel(false);
//        }
//
//        // 定时续期任务：每隔LOCK_RENEW_INTERVAL_SECONDS刷新过期时间
//        lockRenewalFuture = lockRenewalExecutor.scheduleAtFixedRate(() -> {
//            try {
//                // 只有当前锁还是自己持有时才续期
//                String currentValue = stringRedisTemplate.opsForValue().get(CACHE_INIT_LOCK_KEY);
//                if (lockValue.equals(currentValue)) {
//                    stringRedisTemplate.expire(CACHE_INIT_LOCK_KEY, LOCK_INIT_EXPIRE_SECONDS, TimeUnit.SECONDS);
//                    log.debug("分布式锁续期成功，锁值：{}，剩余过期时间：{}秒",
//                            lockValue, LOCK_INIT_EXPIRE_SECONDS);
//                } else {
//                    log.warn("分布式锁已被其他节点持有或释放，停止续期");
//                    lockRenewalFuture.cancel(false);
//                }
//            } catch (Exception e) {
//                log.error("分布式锁续期失败", e);
//                // 续期失败时取消任务，避免无限重试
//                if (lockRenewalFuture != null) {
//                    lockRenewalFuture.cancel(false);
//                }
//            }
//        }, 0, LOCK_RENEW_INTERVAL_SECONDS, TimeUnit.SECONDS);
//    }
//
//
//    // 释放分布式锁
//    private void releaseLock(String lockValue) {
//        if (StringUtils.isEmpty(lockValue)) {
//            return;
//        }
//
//        // 取消续期任务
//        if (lockRenewalFuture != null && !lockRenewalFuture.isCancelled()) {
//            lockRenewalFuture.cancel(false);
//            lockRenewalFuture = null;
//        }
//
//        // 确保只有持有锁的线程才能释放
//        String currentValue = stringRedisTemplate.opsForValue().get(CACHE_INIT_LOCK_KEY);
//        if (lockValue.equals(currentValue)) {
//            stringRedisTemplate.delete(CACHE_INIT_LOCK_KEY);
//            log.info("分布式锁已释放，锁值：{}", lockValue);
//        }
//    }
//
//
//    private LambdaQueryWrapper<CscpOrg> orgLqw() {
//        return Wrappers.lambdaQuery(CscpOrg.class)
//                .orderByAsc(CscpOrg::getLevel)
//                .orderByAsc(CscpOrg::getOrgCode)
//                .orderByAsc(CscpOrg::getOrderBy);
//    }
//
//
//    @Bean
//    public Executor orgCacheExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(5);
//        executor.setMaxPoolSize(10);
//        executor.setQueueCapacity(25);
//        executor.setThreadNamePrefix("OrgCache-");
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        executor.setWaitForTasksToCompleteOnShutdown(true);
//        executor.setAwaitTerminationSeconds(60);
//        executor.initialize();
//        return executor;
//    }
//
//    private static <T> CompletableFuture<T> failedFuture(Throwable ex) {
//        CompletableFuture<T> future = new CompletableFuture<>();
//        future.completeExceptionally(ex);
//        return future;
//    }
//
//    // 容器销毁时执行的清理操作
//    @Override
//    public void destroy() throws Exception {
//        log.info("容器开始销毁，执行OrgCacheService资源清理");
//
//        // 1. 释放分布式锁
//        if (StringUtils.isNotEmpty(currentLockValue)) {
//            releaseLock(currentLockValue);
//        }
//
//        // 2. 关闭续期线程池
//        if (lockRenewalExecutor != null) {
//            lockRenewalExecutor.shutdown();
//            if (!lockRenewalExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
//                lockRenewalExecutor.shutdownNow();
//            }
//        }
//
//        // 3. 中断缓存初始化任务
//        if (isCacheInitializing) {
//            log.warn("容器销毁时缓存初始化任务仍在执行，将尝试中断");
//            Executor executor = orgCacheExecutor();
//            if (executor instanceof ThreadPoolTaskExecutor) {
//                ThreadPoolExecutor threadPoolExecutor = ((ThreadPoolTaskExecutor) executor).getThreadPoolExecutor();
//                if (threadPoolExecutor != null) {
//                    threadPoolExecutor.shutdownNow();
//                    log.info("线程池已强制关闭，中断所有任务");
//                }
//            }
//        }
//
//        log.info("OrgCacheService资源清理完成");
//    }
//}
