package com.ctsi.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableCaching
public class RedisConfiguration {
    /**
     * 注入 lettuceConnectionFactory
     */
    @Resource
    private LettuceConnectionFactory lettuceConnectionFactory;


    /**
     * 实例化 RedisTemplate 对象
     */
    @Bean
    public RedisTemplate<String, Object> functionDomainRedisTemplate() {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        initDomainRedisTemplate(redisTemplate, lettuceConnectionFactory);
        return redisTemplate;
    }


    /**
     * 设置数据存入 redis 的序列化方式
     */
    private void initDomainRedisTemplate(RedisTemplate<String, Object> redisTemplate, RedisConnectionFactory factory) {

        // 设置序列化
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = this.SerializerJson();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setConnectionFactory(factory);
    }


    /**
     * 缓存配置管理器
     */
//    @Bean
//    public CacheManager cacheManager(LettuceConnectionFactory factory) {
//        // 以锁写入的方式创建RedisCacheWriter对象
//        RedisCacheWriter writer = RedisCacheWriter.lockingRedisCacheWriter(factory);
//        /**
//         * 设置CacheManager的Value序列化方式为JdkSerializationRedisSerializer,
//         * 但其实RedisCacheConfiguration默认就是使用 StringRedisSerializer序列化key，
//         * JdkSerializationRedisSerializer序列化value, 所以以下注释代码就是默认实现，没必要写，直接注释掉
//         */
//        // 创建默认缓存配置对象
//        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
//                .entryTtl(Duration.ofHours(1)) // 设置缓存有效期一小时;
//                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
//                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(this.SerializerJson()));
//        RedisCacheManager cacheManager = new RedisCacheManager(writer, config);
//        return cacheManager;
//    }

    /**
     * 新增：自定义 CacheManager（为 orgCache-details 配置专属策略）
     * - orgCache-details：缓存有效期调整为 8 小时（可按需修改）
     * - 其他缓存：默认 1 小时（保持原有默认配置）
     */
    @Bean
    public CacheManager cacheManager(LettuceConnectionFactory factory) {
        // 1. 定义 Redis 缓存写入器（带锁，保证线程安全）
        RedisCacheWriter cacheWriter = RedisCacheWriter.lockingRedisCacheWriter(factory);

        // 2. 配置 JSON 序列化器（与原有 SerializerJson 保持一致）
        Jackson2JsonRedisSerializer<Object> jsonSerializer = this.SerializerJson();
        RedisSerializationContext.SerializationPair<Object> valuePair =
                RedisSerializationContext.SerializationPair.fromSerializer(jsonSerializer);

        // 3. 构建默认缓存配置（1 小时过期，复用原有逻辑）
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1)) // 默认过期时间：1小时
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(valuePair);

        // 4. 构建 缓存名-专属配置 的映射（重点：配置 orgCache-details）
        Map<String, RedisCacheConfiguration> cacheConfigMap = new HashMap<>();
        // 为 orgCache-details 配置 8 小时过期（可根据需求修改 Duration 参数，如 ofDays(1) 表示1天）
        cacheConfigMap.put("orgCache-details", defaultConfig.entryTtl(Duration.ofHours(3)));

        // 5. 创建 CacheManager（传入默认配置 + 专属配置映射）
        return RedisCacheManager.builder(cacheWriter)
                .cacheDefaults(defaultConfig) // 未匹配的缓存使用默认配置
                .withInitialCacheConfigurations(cacheConfigMap) // 匹配的缓存使用专属配置
                .build();
    }


    @Bean
    public RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory) {
        //Redis消息监听器
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        //设置Redis连接工厂
        container.setConnectionFactory(connectionFactory);
        return container;
    }

    public Jackson2JsonRedisSerializer<Object> SerializerJson() {
        // 设置序列化
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<Object>(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.activateDefaultTyping(
                LaissezFaireSubTypeValidator.instance,
                ObjectMapper.DefaultTyping.NON_FINAL,
                JsonTypeInfo.As.PROPERTY);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        return jackson2JsonRedisSerializer;
    }
}
