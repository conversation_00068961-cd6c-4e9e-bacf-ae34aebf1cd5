-- SQL提交规范
-- 格式：日期，姓名，功能作用说明

-- 案例：
-- 1、张三 2024年12月02日 新建XXX表，XX某块
-- 2、张三 2024年12月02日 XX表新增XX字段


-- 2025-04-14 机构表新增code_path和id_path字段
ALTER TABLE "cscp_org" add "org_code_path" VARCHAR(2000) NULL;
COMMENT ON COLUMN "cscp_org"."org_code_path" IS '存储从根节点到当前节点的完整层级code路径（如：A|B|C）';
ALTER TABLE "cscp_org" add "org_id_path" VARCHAR(2000) NULL;
COMMENT ON COLUMN "cscp_org"."org_id_path" IS '存储从根节点到父节点的完整层级id路径（如：1|2）';

CREATE INDEX idx_org_code_path ON cscp_org(org_code_path);
CREATE INDEX idx_org_id_path ON cscp_org(org_id_path);

-- 2025-05-06 机构表新增org_code_backup字段备份org_code
ALTER TABLE "cscp_org" ADD org_code_backup VARCHAR(128);
COMMENT ON COLUMN "cscp_org"."org_code_backup" IS '机构编码备份';
UPDATE "cscp_org" set "org_code_backup" = "org_code";

-- 2025-05-08 角色表新增role_code、description
ALTER TABLE cscp_roles ADD ( role_code VARCHAR(100), description VARCHAR(400) );
update cscp_roles set "role_code" = 'platform_role' where id = 1424729081995304962;
update cscp_roles set "role_code" = 'tenant_role' where id = 1424729500020613121;
update cscp_roles set "role_code" = 'unit_role' where id = 1453911658682986498;
update cscp_roles set "role_code" = "name" where role_code is null;

insert into "HN_SWOA"."cscp_roles" ("id", "name", "role_type", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "department_id", "company_id", "tenant_id", "deleted", "main_tariff", "surcharge", "range_type", "HMAC_ID", "HMAC_NAME", "role_code", "description") values (1920647131882123265, '区划管理员角色', 2, 1, '安全保密管理员', '2025-05-09 09:10:15', null, null, '2025-06-19 09:59:17', null, null, null, 0, null, null, 10, null, null, 'region_role', '例如管理整个地市/区县的数据，用户需要配置在“某某地市”/“某某区县”下');

-- 2025-05-14
ALTER TABLE "cscp_org" MODIFY org_abbreviation VARCHAR(200);

-- 2025-05-21
CREATE TABLE cscp_org_change_history
(
    id            BIGINT PRIMARY KEY,
    old_org_code  VARCHAR(2000),
    new_org_code  VARCHAR(2000),
    new_org_name  VARCHAR(100),
    old_parent_id BIGINT,
    new_parent_id BIGINT,
    posterity     VARCHAR(500),
    create_by     BIGINT,
    create_time   TIMESTAMP DEFAULT SYSDATE,
    create_name   VARCHAR(250),
    update_by     BIGINT,
    update_time   TIMESTAMP,
    update_name   VARCHAR(250)
);

COMMENT ON TABLE cscp_org_change_history
IS
    '组织机构变更历史记录表';
    COMMENT ON COLUMN cscp_org_change_history.id
IS
    '主键ID';
    COMMENT ON COLUMN cscp_org_change_history.old_org_code
IS
    '变更前机构编码';
    COMMENT ON COLUMN cscp_org_change_history.new_org_code
IS
    '变更后机构编码';
    COMMENT ON COLUMN cscp_org_change_history.new_org_name
IS
    '机构名称';
    COMMENT ON COLUMN cscp_org_change_history.old_parent_id
IS
    '变更前父节点ID';
    COMMENT ON COLUMN cscp_org_change_history.new_parent_id
IS
    '变更后父节点ID';
    COMMENT ON COLUMN cscp_org_change_history.posterity
IS
    '影响的后代节点路径（用于级联操作追踪）';
    COMMENT ON COLUMN cscp_org_change_history.create_by
IS
    '创建人ID';
    COMMENT ON COLUMN cscp_org_change_history.create_time
IS
    '创建时间（默认系统时间）';
    COMMENT ON COLUMN cscp_org_change_history.create_name
IS
    '创建人姓名';
    COMMENT ON COLUMN cscp_org_change_history.update_by
IS
    '更新人ID';
    COMMENT ON COLUMN cscp_org_change_history.update_time
IS
    '更新时间';
    COMMENT ON COLUMN cscp_org_change_history.update_name
IS
    '更新人姓名';
CREATE INDEX idx_old_parent ON cscp_org_change_history
    (old_parent_id
        );

CREATE INDEX idx_new_parent ON cscp_org_change_history
    (new_parent_id
        );

ALTER TABLE cscp_org_change_history ADD push_kz_flag TINYINT DEFAULT 0 NOT NULL;

COMMENT ON COLUMN cscp_org_change_history.push_kz_flag
IS
    '自动推送标记快照';

ALTER TABLE cscp_org_change_history
    ADD (
    department_id BIGINT,
    company_id    BIGINT,
    tenant_id     BIGINT,
    deleted      TINYINT DEFAULT 0 NOT NULL
);
ALTER TABLE cscp_org_change_history ADD org_id BIGINT;

ALTER TABLE t_sync_org_histroy_record ADD source_id BIGINT NULL;

COMMENT ON COLUMN t_sync_org_histroy_record.source_id IS '关联数据来源标识';

-- 2025-05-22
ALTER TABLE "cscp_org_change_history" MODIFY "posterity" VARCHAR(4000);

-- 2025-05-23
ALTER TABLE "cscp_user_org" MODIFY "org_abbreviation" VARCHAR(200);
-- 系统配置参数
insert into "t_sys_config" ("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values (1925562621109473281, 'push:delete:user:appCode', 'c2Z3', '', 2, '系统管理员', '2025-05-22 22:41:19', null, null, '2025-05-22 23:13:07', 0);


-- 2025-05-27
alter table cscp_org add order_by_path varchar(2000);

-- 2025-06-03
CREATE INDEX "idx_cscp_org_order_by" ON cscp_org('order_by');
CREATE INDEX "idx_cscp_user_order_by" ON cscp_user('order_by');
CREATE INDEX "idx_cscp_user_org_order_by" ON cscp_user_org('order_by');


-- 2025-06-05
ALTER TABLE "cscp_org" MODIFY "org_name" VARCHAR(255);
ALTER TABLE "cscp_org_change_history" MODIFY "new_org_name" VARCHAR(255);
-- 2025-06-06
insert into "t_sys_config" ("id", "code", "value", "remark", "create_by", "create_name", "create_time", "update_by", "update_name", "update_time", "deleted") values (1930823845357719554, 'sync:mqToHttp', 'MQ==', '同步机构用户是否启用MQ去保证HTTP请求', 2, '系统管理员', '2025-06-06 11:07:32', null, null, null, 0);


-- 2025-06-09
ALTER TABLE "cscp_user" MODIFY "WESTONE_USER_ID" VARCHAR(128);
ALTER TABLE "cscp_org" MODIFY "description" VARCHAR(600);

-- 2025-06-10
create table "t_mq_dlx" (
                            "id" bigint not null primary key,
                            "business_queue" varchar(255) not null,
                            "message_id" varchar(255) not null,
                            "message_body" clob not null,
                            "fail_reason" varchar(2000),
                            "retry_count" int default 3,
                            "status" varchar(10) default '0' not null,
                            "create_time" datetime default sysdate not null,

                            constraint "uk_message_id" unique("message_id")
);

create index "idx_queue_status" on "t_mq_dlx"("business_queue", "status");
create index "idx_create_time" on "t_mq_dlx"("create_time");

comment on table "t_mq_dlx" is 'MQ死信队列消费记录表';

comment on column "t_mq_dlx"."id" is '主键ID';
comment on column "t_mq_dlx"."business_queue" is '业务队列名称';
comment on column "t_mq_dlx"."message_id" is '消息唯一标识';
comment on column "t_mq_dlx"."message_body" is '消息体内容';
comment on column "t_mq_dlx"."fail_reason" is '消费失败原因';
comment on column "t_mq_dlx"."retry_count" is '重试次数';
comment on column "t_mq_dlx"."status" is '处理状态：0-未处理 1-已处理';
comment on column "t_mq_dlx"."create_time" is '创建时间';


ALTER TABLE "cscp_user" ADD push_app_code VARCHAR(255) NULL;
alter table "cscp_user" add column(SECRETARY_NAME VARCHAR(1000));
alter table "cscp_user" add column(SECRETARY_PHONE VARCHAR(1000));

-- 2025-07
ALTER TABLE "t_top_org_relation" MODIFY org_name VARCHAR(200);
ALTER TABLE "cscp_user" MODIFY push_app_code VARCHAR(1000);
ALTER TABLE "t_sync_user_histroy_record" ADD event_id VARCHAR(50);
ALTER TABLE "cscp_org" ADD duty_phone VARCHAR(120);
ALTER TABLE "cscp_org" ADD fax_phone VARCHAR(120);

-- 2025-07-07
CREATE TABLE t_app_auth_history
(
    id                NUMBER(32,0) NOT NULL,
    app_id            NUMBER(32,0),
    app_name          VARCHAR(255),
    app_code          VARCHAR(100),
    org_id            NUMBER(32,0),
    org_name          VARCHAR(255),
    user_id           NUMBER(32,0),
    user_name         VARCHAR(255),
    department_id     NUMBER(32,0),
    tenant_id         NUMBER(32,0),
    deleted           CHAR(5) DEFAULT '0' NOT NULL,
    create_by         NUMBER(32,0),
    create_name       VARCHAR(255),
    create_time       TIMESTAMP(6),
    update_by         NUMBER(32,0),
    update_name       VARCHAR(255),
    update_time       TIMESTAMP(6),
    PRIMARY KEY (id)
);

COMMENT ON TABLE "t_app_auth_history" IS 'APP授权记录';
COMMENT ON COLUMN "t_app_auth_history"."app_code" IS '应用编码';
COMMENT ON COLUMN "t_app_auth_history"."app_id" IS '应用id';
COMMENT ON COLUMN "t_app_auth_history"."app_name" IS '应用名称';
COMMENT ON COLUMN "t_app_auth_history"."user_id" IS '用户id';
COMMENT ON COLUMN "t_app_auth_history"."user_name" IS '用户名';
COMMENT ON COLUMN "t_app_auth_history"."create_by" IS '创建人id';
COMMENT ON COLUMN "t_app_auth_history"."create_name" IS '创建人名称';
COMMENT ON COLUMN "t_app_auth_history"."create_time" IS '创建时间';
COMMENT ON COLUMN "t_app_auth_history"."deleted" IS '是否删除[0:否;1:是]';
COMMENT ON COLUMN "t_app_auth_history"."department_id" IS '部门id';
COMMENT ON COLUMN "t_app_auth_history"."id" IS '主键';
COMMENT ON COLUMN "t_app_auth_history"."org_id" IS '机构id';
COMMENT ON COLUMN "t_app_auth_history"."org_name" IS '机构名称';
COMMENT ON COLUMN "t_app_auth_history"."tenant_id" IS '租户id';
COMMENT ON COLUMN "t_app_auth_history"."update_by" IS '更新人id';
COMMENT ON COLUMN "t_app_auth_history"."update_name" IS '更新人名称';
COMMENT ON COLUMN "t_app_auth_history"."update_time" IS '更新时间';

-- 为app_id创建普通索引
CREATE INDEX t_app_auth_history_n1 ON t_app_auth_history(app_id);

-- 为org_id创建普通索引
CREATE INDEX t_app_auth_history_n2 ON t_app_auth_history(org_id);

-- 为user_id创建普通索引
CREATE INDEX t_app_auth_history_n3 ON t_app_auth_history(user_id);

ALTER TABLE "t_app_auth_history" ADD document_reason VARCHAR(250);
ALTER TABLE "t_app_auth_history" ADD company_id NUMBER(32,0);
ALTER TABLE "t_app_auth_history" ADD opt_type VARCHAR(30);
ALTER TABLE "t_app_auth_history" ADD status NUMBER(2) DEFAULT 1;
ALTER TABLE "cscp_user" ADD entry_time TIMESTAMP(6) DEFAULT SYSDATE;
ALTER TABLE "cscp_user_org" ADD entry_time TIMESTAMP(6) DEFAULT SYSDATE;


ALTER TABLE "t_sync_app_system_manage" ADD passive_url VARCHAR(200);

ALTER TABLE "cscp_user" ADD offer_time TIMESTAMP(6);

-- 职级调整
ALTER TABLE "cscp_user" ADD "rank" VARCHAR(100);
update t_sys_dict_record set code = '1' where name = '省部级正职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '2' where name = '省部级副职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '3' where name = '厅局级正职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '4' where name = '厅局级副职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '5' where name = '一级巡视员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '6' where name = '二级巡视员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '7' where name = '县处级正职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '8' where name = '县处级副职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '9' where name = '一级调研员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '10' where name = '二级调研员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '11' where name = '三级调研员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '12' where name = '四级调研员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '13' where name = '乡科级正职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '14' where name = '乡科级副职' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '15' where name = '一级主任科员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '16' where name = '二级主任科员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '17' where name = '三级主任科员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '18' where name = '四级主任科员' and "dict_id" = 1896455771131469825;
update t_sys_dict_record set code = '19' where name = '科员及以下' and "dict_id" = 1896455771131469825;

UPDATE "cscp_user_org" cuo JOIN "t_sys_dict_record" r on r."name" = cuo."rank"
    set cuo."rank" = r."code"
where cuo.rank is not null and cuo.rank != '' and r."dict_id" = 1896455771131469825;

update cscp_user cu join "cscp_user_org" cuo on cu.id = cuo."user_id"
    set cu.rank = cuo."rank"
where cu."deleted" = 0 and cuo."deleted" = 0
  and cuo.rank is not null and cuo.rank != '';

-- 办公号码
update "cscp_user" set office_phone = '' where office_phone = '18900000000';

-- 用户类型
CREATE TABLE cscp_user_type
(
    id                NUMBER(32,0) NOT NULL,
    user_id           NUMBER(32,0),
    user_type         VARCHAR(255),
    deleted           CHAR(5) DEFAULT '0' NOT NULL,
    create_by         NUMBER(32,0),
    create_name       VARCHAR(255),
    create_time       TIMESTAMP(6),
    update_by         NUMBER(32,0),
    update_name       VARCHAR(255),
    update_time       TIMESTAMP(6),
    PRIMARY KEY (id)
);
CREATE INDEX idx_user_type_user_id ON cscp_user_type(user_id);
CREATE INDEX idx_user_type_type ON cscp_user_type(user_type);
ALTER TABLE "cscp_user_type" ADD type_level VARCHAR(50);

-- 系统条线
CREATE TABLE cscp_org_line
(
    id                NUMBER(32,0) NOT NULL,
    org_id            NUMBER(32,0),
    line_name         VARCHAR(255),
    deleted           CHAR(5) DEFAULT '0' NOT NULL,
    create_by         NUMBER(32,0),
    create_name       VARCHAR(255),
    create_time       TIMESTAMP(6),
    update_by         NUMBER(32,0),
    update_name       VARCHAR(255),
    update_time       TIMESTAMP(6),
    PRIMARY KEY (id)
);
CREATE INDEX idx_org_line_org_id ON cscp_org_line(org_id);
CREATE INDEX idx_org_line_name ON cscp_org_line(line_name);