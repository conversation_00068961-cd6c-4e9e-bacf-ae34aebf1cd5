package com.ctsi.ssdc.security;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ctsi.hndx.enums.RoleEnum;
import com.ctsi.hndx.utils.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.CollectionUtils;

import java.util.Optional;

/**
 * Utility class for Spring Security.
 */
public final class SecurityUtils {

    private SecurityUtils() {
    }

    /**
     * Get the username of the current user.
     *
     * @return the username of the current user
     */
    public static Optional<String> getOptionalCurrentUserName() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication()).map(authentication -> {
            if (authentication.getPrincipal() instanceof UserDetails) {
                UserDetails springSecurityUser = (UserDetails) authentication.getPrincipal();
                return springSecurityUser.getUsername();
            } else if (authentication.getPrincipal() instanceof String) {
                return (String) authentication.getPrincipal();
            }
            return null;
        });
    }

    /**
     * 获取当前登录用户的登录名
     *
     * @return
     */
    public static String getCurrentUserName() {
        return getOptionalCurrentUserName().map(name -> {
            return name;
        }).orElse(null);
    }


    /**
     * 获取当前登录用户的真实姓名
     *
     * @return
     */
    public static String getCurrentRealName() {
        return getCurrentCscpUserDetail().getRealName();
    }

    public static Optional<Long> getOptionalCurrentUserId() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication()).map(authentication -> {
            if (authentication.getPrincipal() instanceof CscpUserDetail) {
                CscpUserDetail springSecurityUser = (CscpUserDetail) authentication.getPrincipal();
                return springSecurityUser.getId();
            }
            return null;
        });
    }

    public static long getCurrentUserId() {
        return getOptionalCurrentUserId().map(id -> {
            return id;
        }).orElse((long) 0);

    }

    public static Optional<CscpUserDetail> getCurrentUser() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication()).map(authentication -> {
            if (authentication.getPrincipal() instanceof CscpUserDetail) {
                return (CscpUserDetail) authentication.getPrincipal();
            }
            return null;
        });
    }

    /**
     * 获取当前登录的单位编号
     *
     * @return
     */
    public static Long getCurrentCompanyId() {
        return getCurrentCscpUserDetail().getCompanyId();
    }

    public static Long getCurrentBackupCompanyId() {
        return getCurrentCscpUserDetail().getBackupCompanyId();
    }

    public static CscpUserDetail getCurrentCscpUserDetail() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Optional<CscpUserDetail> optionalCscpUserDetail = Optional.ofNullable(securityContext.getAuthentication()).
                map(authentication -> {
                    if (authentication.getPrincipal() instanceof CscpUserDetail) {
                        return (CscpUserDetail) authentication.getPrincipal();
                    }
                    return null;
                });
        return optionalCscpUserDetail.orElse(null);
    }

    /**
     * 系统默认的单位编号是1;
     *
     * @return
     */
    public static Long getAdminCompany() {
        return 1L;
    }

    public static Optional<Authentication> getCurrentAuthentication() {

        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication());

    }

    /**
     * Get the JWT of the current user.
     *
     * @return the JWT of the current user
     */
    public static Optional<String> getCurrentUserJWT() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
                .filter(authentication -> authentication.getCredentials() instanceof String)
                .map(authentication -> (String) authentication.getCredentials());
    }

    /**
     * Check if a user is authenticated.
     *
     * @return true if the user is authenticated, false otherwise
     */
    public static boolean isAuthenticated() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
                .map(authentication -> authentication.getAuthorities().stream().noneMatch(
                        grantedAuthority -> grantedAuthority.getAuthority().equals(AuthoritiesConstants.ANONYMOUS)))
                .orElse(false);
    }

    /**
     * If the current user has a specific authority (security role).
     * <p>
     * The name of this method comes from the isUserInRole() method in the
     * Servlet API
     *
     * @param authority the authority to check
     * @return true if the current user has the authority, false otherwise
     */
    public static boolean isCurrentUserInRole(String authority) {
        SecurityContext securityContext = SecurityContextHolder.getContext();

        return Optional
                .ofNullable(securityContext.getAuthentication()).map(authentication -> authentication.getAuthorities()
                        .stream().anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authority)))
                .orElse(false);
    }


    /**
     * 判断当前登录用户是否平台登录用户
     *
     * @return true 平台管理员用户
     */
    public static boolean isSystemName() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        Long tenantId = cscpUserDetail.getTenantId();
        return tenantId == null;
    }

    /**
     * 判断当前登录用户是否租户管理员登录用户
     *
     * @return true 租户管理员登录用户
     */
    public static boolean isTenantName() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        Long tenantId = cscpUserDetail.getTenantId();
        Long complayId = cscpUserDetail.getCompanyId();
        return tenantId != null && complayId == null;
    }

    /**
     * 判断当前登录用户普通用户登录用户
     *
     * @return true 普通用户登录用户
     */
    public static boolean isGeneralName() {
        return !isSystemName() && !isTenantName();
    }

    /**
     * 判断当前登录用户普通用户登录用户
     *
     * @return true 普通用户登录用户(且非管理员)
     */
    public static boolean isNormalName() {
        return !isSystemName() && !isTenantName() && !isUnitAdmin() && !isRegionAdmin() && !isDepartAdmin();
    }

    /**
     * 判断当前登录用户单位管理员
     *
     * @return true 单位管理员用户登录用户
     */
    public static boolean isUnitAdmin() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        return cscpUserDetail.getRoleCodes().contains(RoleEnum.COMPANY_ROLE.getCode());
    }

    /**
     * 判断当前登录用户区划机构管理员
     *
     * @return true 单位管理员用户登录用户
     */
    public static boolean isRegionAdmin() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if("安全保密管理员".equals(cscpUserDetail.getRealName())){
            return false;
        }
        return CollUtil.isNotEmpty(cscpUserDetail.getRoleCodes()) &&
                cscpUserDetail.getRoleCodes().contains(RoleEnum.REGION_NAME.getCode());
    }

    /**
     * 判断当前登录用户部门（内设机构）管理员
     *
     * @return true 部门（内设机构）管理员用户登录用户
     */
    public static boolean isDepartAdmin() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if("安全保密管理员".equals(cscpUserDetail.getRealName())){
            return false;
        }
        return CollUtil.isNotEmpty(cscpUserDetail.getRoleCodes()) &&
                cscpUserDetail.getRoleCodes().contains(RoleEnum.DEPTMENT_ROLE.getCode());
    }

    /**
     * 判断是否包含权限
     *
     * @param permission  权限字符串
     * @return 用户是否具备某权限
     */
    public static boolean hasPermissions(String permission) {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (cscpUserDetail == null){
            return false;
        }else {
           if (CollectionUtils.isEmpty(cscpUserDetail.getPermissions())){
                return  false;
           }
            return cscpUserDetail.getPermissions().contains(StringUtils.trim(permission));
        }
    }

    public static boolean isAdmin() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        return cscpUserDetail.getId() ==2 || (CollUtil.isNotEmpty(cscpUserDetail.getRoleCodes()) &&
                cscpUserDetail.getRoleCodes().contains(RoleEnum.SYSTEM_ROLE.getCode()));
    }

    public static boolean isPlatformAdmin() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        return cscpUserDetail.getId() !=2 && CollUtil.isNotEmpty(cscpUserDetail.getRoleCodes()) &&
                cscpUserDetail.getRoleCodes().contains(RoleEnum.SYSTEM_ROLE.getCode());
    }
}
