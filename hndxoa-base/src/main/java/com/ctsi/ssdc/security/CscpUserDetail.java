package com.ctsi.ssdc.security;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.enums.UserType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;

@Getter
@Setter
public class CscpUserDetail implements UserDetails {

    private long id;
    /**
     * 用户真实姓名
     */
    private String realName;
    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 登录的部门名称
     */
    private String departmentName;
    /**
     * 单位id
     */
    private Long companyId;

    private Long backupCompanyId;

    /**
     * 登录的单位名称
     */
    private String companyName;

    /**
     * 是否加水印
     */
    private Integer hasWatermark;

    /**
     * 正文编辑是否分屏，1表示分屏，0表示不分屏
     */
    private Integer splitview;

    /**
     * 单位共享云盘空间大小，单位GB，默认2G
     */
    private Integer cloudDiskSpaceSize;

    /**
     * 租户id
     */
    private Long tenantId;

    private Long backupTenantId;

    /**
     * 用户手机号码
     */
    private String mobile;


    /**
     * 用户的类别
     */
    private String userType;

    /**
     * 登录时间
     */
    private Long loginTime;


    private String userName;

    private String passWord;

    /**
     * token
     */
    private String token;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 排序
     */
    private Integer orderBy;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * sms名
     */
    private String smsName;

    /**
     * sms的key
     */
    private String smsKey;

    private String code;

    private String pathCode;

    private String dossierNumber;

    private String orgIdPath;

    private String orgCodePath;

    private String strId;


    /**
     * 公文OID
     */
    private String oid;

    /**
     * 档案标识-统一信用代码
     */
    private String creditCode;

    /**
     * 档案标识-代字编码
     */
    private String dzCode;

    /**
     * 档案标识-组织机构编码
     */
    private String organizationCode;

    @ApiModelProperty(value = "盖章图片地址")
    private String stampUrl;

    @ApiModelProperty(value = "起始号")
    private Integer startNo;

    @ApiModelProperty("角色编码")
    private List<String> roleCodes = new ArrayList<>();
    @ApiModelProperty("版主管理的应用ID列表")
    private List<Long> moderatorAppIdList;

    /**
     *
     */
    private static final long serialVersionUID = 3899399998765880053L;

    public CscpUserDetail() {

    }

    public CscpUserDetail(long id, String username, String password,
                          Set<String> permissions) {
        this.id = id;
        this.userName = username;
        this.passWord = password;
        this.permissions = permissions;
    }

    @JsonIgnore
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @JsonIgnore
    @Override
    public String getPassword() {
        return this.passWord;
    }

    @JsonIgnore
    @Override
    public String getUsername() {
        return this.userName;
    }

    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @JsonIgnore
    @Override
    public boolean isEnabled() {
        return true;
    }
}
