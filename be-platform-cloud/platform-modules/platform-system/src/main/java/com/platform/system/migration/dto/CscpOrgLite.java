package com.platform.system.migration.dto;

import lombok.Data;

/**
 * 最小化的 CscpOrg 输入快照，用于预检/干跑转换
 */
@Data
public class CscpOrgLite {
    private Long id;
    /** 1=虚拟, 2=单位, 3=部门 */
    private Integer type;
    private Long parentId;

    private String orgName;
    private Integer orderBy;

    private String code;
    private String pathCode;
    private Integer level;

    private String lan;
    private String lanId;

    private Long tenantId;
    private Integer deleted; // 0=正常, 其他=删除

    private Long companyId; // 旧模型字段
    private Long branchLeaderId; // 旧部门领导
}

