package com.platform.system.migration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 扁平源组织节点（来自 CscpOrg 的必要子集）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SourceOrgNode {
    /** 源ID（CscpOrg.id） */
    private Long id;
    /** 父ID（CscpOrg.parentId） */
    private Long parentId;

    /** 名称（CscpOrg.orgName） */
    private String name;

    /** 类型：1=虚拟机构；2=单位；3=部门（CscpOrg.type） */
    private Integer type;

    /** 编码（CscpOrg.code） */
    private String code;
    /** 完整编码（CscpOrg.pathCode） */
    private String pathCode;
    /** 级别（CscpOrg.level） */
    private Integer level;

    /** 顺序（CscpOrg.orderBy） */
    private Integer orderBy;

    /** 地区编码/名称 */
    private String lanId;
    private String lan;

    /** 状态：删除标记等（0=正常，其它=删除） */
    private Integer deleted;

    /** 租户/单位ID（若有） */
    private Long tenantId;
    private Long companyId;

    /** 其他保留信息（可选） */
    private String orgAbbreviation;
    private Boolean checkUp;
    private Integer unitType;
}

